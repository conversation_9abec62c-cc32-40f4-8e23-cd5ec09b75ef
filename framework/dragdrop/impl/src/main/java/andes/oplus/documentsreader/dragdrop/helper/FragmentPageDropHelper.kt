/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/24, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package andes.oplus.documentsreader.dragdrop.helper

import andes.oplus.documentsreader.core.common.CustomToast
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.SelectItemLayout
import andes.oplus.documentsreader.dragdrop.action.DropDispatchAction
import andes.oplus.documentsreader.dragdrop.action.DropFolderAction
import andes.oplus.documentsreader.dragdrop.interfaze.R
import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.view.DragEvent
import android.view.View

class FragmentPageDropHelper(private val context: Context) {

    companion object {
        const val TAG = "FragmentPageDropHelper"
    }

    private val dropHandler = DropHandler()

    init {
        dropHandler.dragInCallback = { view, tag ->
            setItemDragIn(view, context)
        }
        dropHandler.dragOutCallback = { view, tag ->
            setItemDragOut(view, context)
        }
    }

    fun handleDragEvent(activity: Activity, view: View?, event: DragEvent?) {
        when (event?.action) {
            DragEvent.ACTION_DROP -> {
                Log.d(TAG, "handleDragEvent -> ACTION_DROP itemDropTag ${dropHandler.itemDropTag}" +
                        " fragmentDropTag ${dropHandler.fragmentDropTag}")
                if (dropHandler.itemDropTag != null) {
                    DropFolderAction.handleDropFolderAction(activity, event, dropHandler.itemDropTag?.filePath)
                    setItemDragOut(dropHandler.targetItemView, context)
                } else if (dropHandler.fragmentDropTag != null) {
                    val categoryType = dropHandler.fragmentDropTag?.categoryType
                    if (categoryType != null) {
                        DropDispatchAction.handleDragDrop(activity, categoryType, event, false)
                    } else {
                        CustomToast.showShort(R.string.oplus_doc_drag_not_support_position)
                    }
                } else {
                    CustomToast.showShort(R.string.oplus_doc_drag_not_support_position)
                }
                dropHandler.resetView()
            }

            DragEvent.ACTION_DRAG_STARTED -> Log.d(TAG, "handleDragEvent ACTION_DRAG_STARTED")

            DragEvent.ACTION_DRAG_ENTERED -> Log.d(TAG, "handleDragEvent ACTION_DRAG_ENTERED")

            DragEvent.ACTION_DRAG_LOCATION -> dropHandler.handleDropLocation(view, view, event)

            DragEvent.ACTION_DRAG_EXITED -> {
                Log.d(TAG, "handleDragEvent ACTION_DRAG_EXITED")
                setItemDragOut(dropHandler.targetItemView, context)
                dropHandler.resetView()
            }

            DragEvent.ACTION_DRAG_ENDED -> Log.d(TAG, "handleDragEvent ACTION_DRAG_ENDED")

            else -> {}
        }
    }

    private fun setItemDragIn(itemView: View?, context: Context) {
        //列表
        if (itemView is SelectItemLayout) {
            itemView.isPressed = true
            return
        }

        //宫格
        itemView?.setBackgroundColor(context.resources.getColor(com.support.appcompat.R.color.coui_color_hover))
    }

    private fun setItemDragOut(itemView: View?, context: Context) {
        //列表
        if (itemView is SelectItemLayout) {
            itemView.isPressed = false
            return
        }

        //宫格
        itemView?.setBackgroundColor(Color.TRANSPARENT)
    }
}