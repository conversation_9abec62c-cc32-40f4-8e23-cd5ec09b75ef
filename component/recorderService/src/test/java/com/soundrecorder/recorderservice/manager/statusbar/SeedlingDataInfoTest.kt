/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SeedlingDataInfoTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/7/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.manager.statusbar

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.recorderservice.manager.RecordStatusManager
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption
import com.soundrecorder.recorderservice.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.recorderservice.shadows.ShadowOplusCompactUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowOplusCompactUtil::class]
)
class SeedlingDataInfoTest {

    @Test
    fun should_equals_when_getCurTimeInfo() {
        var newData = SeedlingDataInfo(curTime = 1000, saveFileState = RecorderViewModelAction.saveFileState)
        val result = newData.getCurTimeInfo(false)
        Assert.assertNotNull(result)

        newData = SeedlingDataInfo(curTime = 2000, saveFileState = RecorderViewModelAction.saveFileState)
        val newResult = newData.getCurTimeInfo(true)
        Assert.assertNotEquals(result, newResult)
    }

    @Test
    fun should_equals_when_getCurLabelInfo() {
        var newData = SeedlingDataInfo(curMarkTime = 1000, saveFileState = RecorderViewModelAction.saveFileState)
        val result = newData.getCurLabelInfo(false)
        Assert.assertNotNull(result)

        newData = SeedlingDataInfo(curMarkTime = 2000, saveFileState = RecorderViewModelAction.saveFileState)
        val newResult = newData.getCurLabelInfo(true)
        Assert.assertNotEquals(result, newResult)
    }

    @Test
    fun should_equals_when_getMarkBtnInfo() {
        var newData = SeedlingDataInfo(curMarkTime = 1000, saveFileState = RecorderViewModelAction.saveFileState)
        val result = newData.getMarkBtnInfo(false)
        Assert.assertNotNull(result)

        newData = SeedlingDataInfo(curMarkTime = 2000, saveFileState = RecorderViewModelAction.saveFileState)
        val newResult = newData.getMarkBtnInfo(true)
        Assert.assertNotEquals(result, newResult)
    }

    @Test
    fun should_equals_when_getRecordBtnInfo() {
        var newData = SeedlingDataInfo(curStatus = RecordStatusManager.RECORDING, recordEnable = true)
        val result = newData.getRecordBtnInfo(false)
        Assert.assertEquals("\$r('images.recording')", result!![0])

        newData = SeedlingDataInfo(curStatus = RecordStatusManager.PAUSED, recordEnable = true, saveFileState = RecorderViewModelAction.saveFileState)
        val newResult = newData.getRecordBtnInfo(true)
        Assert.assertNotEquals(result, newResult)
    }

    @Test
    fun should_not_empty_when_getChangedDataSimple() {
        val newData = SeedlingDataInfo(curTime = 1000, saveFileState = RecorderViewModelAction.saveFileState)
        val result = newData.getChangedDataSimple()
        Assert.assertNotEquals(0, result.length())
    }

    @Test
    fun should_not_empty_when_getChangedData() {
        val newData = SeedlingDataInfo(curTime = 1000, curStatus = RecordStatusManager.PAUSED, saveFileState = RecorderViewModelAction.saveFileState)
        val result = newData.getChangedData(false)
        Assert.assertNotEquals(0, result.length())
    }

    @Test
    fun should_not_empty_when_equals() {
        val oldData = SeedlingDataInfo(curTime = 1000, saveFileState = RecorderViewModelAction.saveFileState)
        val newData = SeedlingDataInfo(curTime = 1100, saveFileState = RecorderViewModelAction.saveFileState)
        Assert.assertEquals(oldData, newData)
    }

    @Test
    fun should_correct_when_getMarkTextInternal() {
        val newData = SeedlingDataInfo(curTime = 1000, curStatus = RecordStatusManager.PAUSED, saveFileState = RecorderViewModelAction.saveFileState)
        var statePair =
            Whitebox.invokeMethod<Pair<String, String>?>(newData, "getMarkTextInternal")
        Assert.assertNull(statePair)

        newData.curMarkTime = 1000
        statePair = Whitebox.invokeMethod(newData, "getMarkTextInternal")
        Assert.assertNotNull(statePair)
    }

    fun should_not_empty_when_getRecordStatusTextInternal() {
        // paused
        val newData = SeedlingDataInfo(curStatus = RecordStatusManager.PAUSED, saveFileState = RecorderViewModelAction.saveFileState)
        var statePair =
            Whitebox.invokeMethod<Pair<String, String>>(newData, "getRecordStatusTextInternal")
        Assert.assertNotEquals("", statePair.first)

        // recording
        newData.curStatus = RecordStatusManager.RECORDING
        statePair = Whitebox.invokeMethod(newData, "getRecordStatusTextInternal")
        Assert.assertNotEquals("", statePair.first)

        // not init
        newData.curStatus = RecordStatusManager.INIT
        statePair = Whitebox.invokeMethod(newData, "getRecordStatusTextInternal")
        Assert.assertEquals("", statePair.first)
    }
}