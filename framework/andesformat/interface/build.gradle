plugins {
    id 'com.android.library'
    id 'kotlin-kapt'
}
apply from: rootProject.file("script/compile.gradle")
apply from: rootProject.file("script/unitTest.gradle")

android {
    namespace 'andes.oplus.documents.preview.interfaces'
}

dependencies {
    implementation project(":foundation:common")
    implementation project(":framework:database")

    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.material

    implementation(libs.gson)

    // Room
    implementation(libs.androidx.room.runtime)
    kapt(libs.androidx.room.compiler)

    // Koin
    implementation(libs.koin.android)
}