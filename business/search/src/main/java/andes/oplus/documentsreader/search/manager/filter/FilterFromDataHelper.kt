/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date: 2020/8/3
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package andes.oplus.documentsreader.search.manager.filter

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.CommonConstants
import andes.oplus.documentsreader.core.common.Constants
import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.VolumeEnvironment
import andes.oplus.documentsreader.core.common.compat.FeatureCompat
import andes.oplus.documentsreader.core.common.utils.BrandUtil
import andes.oplus.documentsreader.search.bean.SearchRecycleWrapper
import andes.oplus.documentsreader.search.manager.filter.FilterConstants.FILTER_THIRD_APP_DINGTALK
import andes.oplus.documentsreader.search.manager.filter.FilterConstants.FILTER_THIRD_APP_FEISHU
import andes.oplus.documentsreader.search.manager.filter.FilterConstants.FILTER_THIRD_APP_OR_OLD_FROM_APP_QQ
import andes.oplus.documentsreader.search.manager.filter.FilterConstants.FILTER_THIRD_APP_OR_OLD_FROM_APP_WECHAT
import andes.oplus.documentsreader.search.manager.filter.FilterConstants.FILTER_THIRD_APP_QQ
import andes.oplus.documentsreader.search.manager.filter.FilterConstants.FILTER_THIRD_APP_WECHAT
import andes.oplus.documentsreader.search.manager.filter.FilterConstants.FILTER_THIRD_APP_WEWORK
import andes.oplus.documentsreader.superapp.ISuperApp
import java.io.File
import java.util.Locale

class FilterFromDataHelper : FilterDataHelper {

    private val mVolumeList: ArrayList<String> = ArrayList()

    companion object {
        const val TAG = "FilterFromDataHelper"
        fun getTransferPath(flavorBrand: String): Array<String> {
            return when (flavorBrand) {
                BrandUtil.BRAND_REALME -> Constants.CATEGORY_PATH_REALME_SHARE
                BrandUtil.BRAND_ONEPLUS -> Constants.CATEGORY_PATH_ONEPLUS_SHARE
                else -> Constants.CATEGORY_PATH_OPPO_SHARE
            }
        }
    }

    private val thirdAppDataHelper: FilterThirdAppDataHelper = FilterThirdAppDataHelper()
    private val thirdAppAndOldFromHelper: FilterThirdAppCommonFromHelper = FilterThirdAppCommonFromHelper()

    init {
        val array = arrayOf(
                VolumeEnvironment.getInternalSdPath(ContextGetter.context),
                VolumeEnvironment.getExternalSdPath(ContextGetter.context),
                if (FeatureCompat.sIsSupportMultiApp) {
                    KtConstants.LOCAL_VOLUME_MULTI_APP_PATH
                } else ""
        )
        array.forEach {
            if (!VolumeEnvironment.isNeedLoadPath(ContextGetter.context, it)) {
                mVolumeList.add(if (it!!.endsWith(File.separator)) {
                    it
                } else {
                    it.plus(File.separator)
                })
            }
        }
        thirdAppAndOldFromHelper.filterFromHelper = this
    }

    override fun filterData(filterItem: FilterItem, dataList: MutableList<BaseFileBean>): MutableList<BaseFileBean> {
        return when (filterItem.id) {
            FilterConstants.FILTER_FROM_SUPER_APP_ONE,
            FilterConstants.FILTER_FROM_SUPER_APP_TWO -> {
                val index = when (filterItem.id) {
                    FilterConstants.FILTER_FROM_SUPER_APP_ONE -> 0
                    FilterConstants.FILTER_FROM_SUPER_APP_TWO -> 1
                    else -> 0
                }
                Injector.injectFactory<ISuperApp>()?.getCategoryItems(ContextGetter.context)?.let {
                    if (it.size > index) {
                        filterDataByFrom(it[index].fileList, dataList)
                    } else {
                        dataList
                    }
                } ?: run {
                    dataList
                }
            }
            FilterConstants.FILTER_FROM_DOWNLOAD -> {
                filterDataByFrom(Constants.CATEGORY_PATH_DOWNLOAD, dataList)
            }
            FilterConstants.FILTER_FROM_TRANSFER -> {
                filterDataByFrom(if (BrandUtil.isRealMe()) {
                    Constants.CATEGORY_PATH_REALME_SHARE
                } else {
                    Constants.CATEGORY_PATH_OPPO_SHARE
                }, dataList)
            }
            FilterConstants.FILTER_FROM_BLUETOOTH -> {
                filterDataByFrom(Constants.CATEGORY_PATH_BT, dataList)
            }
            FilterConstants.FILTER_FROM_OWORK -> {
                if (Injector.injectFactory<ISuperApp>()?.checkOWorkSuperAppCondition(ContextGetter.context) == true) {
                    filterDataByFrom(CommonConstants.OWORK_DEFAULT_PATHS, dataList)
                } else dataList
            }
            FilterConstants.FILTER_FROM_CURRENT -> {
                var list = dataList
                filterItem.packageName?.let {
                    var pathName = it
                    mVolumeList.forEach { root ->
                        if (root.contains(pathName)) {
                            pathName = ""
                        }
                        pathName = pathName.replace(root, "")
                    }
                    list = filterDataByFrom(arrayOf(pathName), dataList)
                }
                list
            }

            FILTER_THIRD_APP_QQ,
            FILTER_THIRD_APP_WECHAT,
            FILTER_THIRD_APP_WEWORK,
            FILTER_THIRD_APP_DINGTALK,
            FILTER_THIRD_APP_FEISHU -> thirdAppDataHelper.filterData(filterItem, dataList)

            FILTER_THIRD_APP_OR_OLD_FROM_APP_QQ,
            FILTER_THIRD_APP_OR_OLD_FROM_APP_WECHAT ->
                thirdAppAndOldFromHelper.filterData(filterItem, dataList)
            else -> dataList
        }
    }

    fun filterDataByFrom(pathArray: Array<String>, dataList: MutableList<BaseFileBean>): MutableList<BaseFileBean> {
        val list = arrayListOf<String>()
        pathArray.forEach { path ->
            mVolumeList.forEach { root ->
                list.add(root.plus(path).uppercase(Locale.getDefault()))
            }
        }
        return dataList.filter {
            list.forEach { ext ->
                val path = if (it is SearchRecycleWrapper) {
                    it.originPath
                } else it.mData ?: return@filter false
                if (path.uppercase(Locale.getDefault()).startsWith(ext)) {
                    return@filter true
                }
            }
            false
        } as MutableList<BaseFileBean>
    }
}