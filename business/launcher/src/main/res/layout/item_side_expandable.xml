<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/divider_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/toolbar_divider_height"
        android:layout_gravity="center_horizontal"
        android:layout_marginVertical="8dp"
        android:layout_marginLeft="@dimen/common_margin"
        android:layout_marginRight="@dimen/common_margin"
        android:background="?attr/couiColorDivider"
        android:forceDarkAllowed="false" />

    <LinearLayout
        android:id="@+id/ll_expand"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginVertical="16dp"
        android:layout_marginHorizontal="24dp">

        <TextView
            android:id="@+id/expand_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:textAlignment="viewStart"
            android:textFontWeight="600"
            android:fontFamily="sans-serif"
            android:textAppearance="?attr/couiTextAppearanceHeadline5"
            android:textColor="?attr/couiColorPrimaryNeutral"
            android:maxLines="2"
            android:ellipsize="end"
            tools:ignore="RtlCompat" />

        <com.coui.appcompat.rotateview.COUIRotateView
            android:id="@+id/iv_arrow"
            android:layout_gravity="center_vertical"
            android:src="@drawable/coui_line_arrow"
            app:supportExpanded="false"
            android:layout_marginStart="8dp"
            android:layout_width="24dp"
            android:layout_height="24dp"/>
    </LinearLayout>
</LinearLayout>