/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryServiceManager
 * Description: AI录音摘要service管理
 * Version: 1.0
 * Date: 2025/5/14
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/14      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.playback.aisummary

import android.content.ServiceConnection
import com.soundrecorder.common.databean.Record
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.suffix
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.R
import com.soundrecorder.common.constant.RecorderConstant
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.modulerouter.aisummary.IAISummaryCallback
import com.soundrecorder.convertservice.aisummary.AISummaryService
import com.soundrecorder.convertservice.aisummary.AISummaryTaskManager
import com.soundrecorder.convertservice.convert.ConvertCheckUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class AISummaryManager {

    companion object {
        private const val TAG = "AISummaryServiceManager"

        private var manager: AISummaryManager? = null

        fun getInstance(): AISummaryManager {
            if (manager == null) {
                synchronized(AISummaryManagerImpl::class.java) {
                    manager = AISummaryManager()
                }
            }
            return manager!!
        }
    }

    private var mSelectRecordList: MutableList<Record>? = null

    private var mAISummaryCallback: IAISummaryCallback? = object : IAISummaryCallback {

        override fun onAISummaryError(mediaId: Long, errorCode: Int, errorMsg: String?) {
            DebugUtil.d(TAG, "onAISummaryError, mediaId:$mediaId, errorCode:$errorCode, errorMsg:$errorMsg")
            unregisterAISummaryCallback(mediaId)
        }

        override fun onAISummaryFinished(
            mediaId: Long,
            jsonResult: String,
            extras: Map<String, Any>?
        ) {
            DebugUtil.d(TAG, "onAISummaryFinished, mediaId:$mediaId")
            unregisterAISummaryCallback(mediaId)
        }
    }

    private fun unregisterAISummaryCallback(mediaId: Long) {
        mAISummaryCallback?.let { AISummaryTaskManager.unregisterAISummaryCallback(mediaId, it) }
    }

    private fun toastMessage(text: String?) {
        ToastManager.showShortToast(BaseApplication.getAppContext(), text)
    }

    /**
     * 开始AI摘要
     */
    fun startOrResumeAISummary(selectedMediaIdList: MutableList<Long>?) {
        if (selectedMediaIdList.isNullOrEmpty()) {
            return
        }
        DebugUtil.d(TAG, "startOrResumeAISummary, selectedMediaIdList:${selectedMediaIdList.size}")
        val idList = arrayOfNulls<String?>(selectedMediaIdList.size)
        selectedMediaIdList.forEachIndexed { index, id ->
            idList[index] = id.toString()
        }
        CoroutineScope(Dispatchers.IO).launch {
            mSelectRecordList = MediaDBUtils.getMediaRecordsById(BaseApplication.getAppContext(), idList)
            mSelectRecordList?.forEach {
                val mediaId = it.id
                val canSummary = checkCanSummary(it)
                DebugUtil.d(TAG, "startOrResumeAISummary, canSummary:$canSummary")
                if (canSummary) {
                    mAISummaryCallback?.let { callback ->
                        AISummaryTaskManager.registerAISummaryCallback(mediaId, callback)
                    }
                    AISummaryTaskManager.startAISummary(mediaId, null)
                }
            }
        }
    }

    private fun isSuffixSupport(suffix: String?): Boolean {
        DebugUtil.d(TAG, "isSuffixSupport, suffix:$suffix")
        if (suffix.isNullOrEmpty()) {
            return false
        }
        return when (suffix) {
            RecorderConstant.MP3_FILE_SUFFIX,
            RecorderConstant.AAC_FILE_SUFFIX,
            RecorderConstant.WAV_FILE_SUFFIX,
            RecorderConstant.AMR_FILE_SUFFIX,
            RecorderConstant.AMR_WB_FILE_SUFFIX -> true
            else -> false
        }
    }

    private fun checkCanSummary(mediaRecord: Record): Boolean {
        val mediaId = mediaRecord.id
        val context = BaseApplication.getAppContext()
        if (NetworkUtils.isNetworkInvalid(context)) {
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.network_disconnect)
            return false
        }
        DebugUtil.i(TAG, "CheckAISummaryTask start. record:$mediaRecord")
        val suffix = mediaRecord.data.suffix()
        if (!isSuffixSupport(suffix)) {
            toastMessage("当前文件不支持转文本，生成摘要失败")
            return false
        }

        var fileFormat: String? = null
        var fileDuration: Long? = null
        val fileSize: Long? = mediaRecord.mFileSize

        if (PermissionUtils.hasReadAudioPermission()) {
            ConvertDbUtil.updateRecordIdByMediaPath(mediaRecord.data, mediaId)
        }
        val convertRecord = ConvertDbUtil.selectByRecordId(mediaId)
        if (convertRecord == null) {
            DebugUtil.i(TAG, "MediaMetadataRetriever start.}")
            val mLocalUri = MediaDBUtils.genUri(mediaId)
            val pairFormatAndDuration = MediaDBUtils.getFileFormatAndDurationFromUri(mLocalUri)
            fileFormat = pairFormatAndDuration.first
            fileDuration = pairFormatAndDuration.second
            DebugUtil.i(TAG, "MediaMetadataRetriever end. mFileFormat:$fileFormat, mFileDuration:$fileDuration")
        }

        fileSize?.let {
            if (!ConvertCheckUtils.isFileSizeMinMet(it)) {
                DebugUtil.d(TAG, "isFileConditionMet, upload_status_exception")
                toastMessage("当前文件不支持转文本，生成摘要失败")
                return false
            }
            if (!ConvertCheckUtils.isFileSizeMaxMet(fileSize)) {
                toastMessage("仅支持 500 MB 内的文件，生成摘要失败")
                return false
            }
        }

        if (!isFileDurationMet(mediaId, fileDuration)) {
            return false
        }
        if (!isFileFormatMet(mediaId, fileFormat)) {
            return false
        }
        return true
    }

    private fun isFileDurationMet(
        mediaId: Long,
        fileDuration: Long?
    ): Boolean {
        fileDuration?.let {
            if (!ConvertCheckUtils.isFileDurationMinMet(it)) {
                DebugUtil.w(TAG, "mFileDuration <= 0!")
                toastMessage("当前文件不支持转文本，生成摘要失败")
                return false
            }
            if (!ConvertCheckUtils.isFileDurationMaxMet(it)) {
                DebugUtil.d(TAG, "isFileConditionMet, fileDuration max")
                toastMessage("录音时长超过 5 小时，生成摘要失败")
                return false
            }
        }
        return true
    }

    private fun isFileFormatMet(
        mediaId: Long,
        fileFormat: String?
    ): Boolean {
        fileFormat?.let {
            return ConvertCheckUtils.isFileFormatMet(it).apply {
                if (!this) {
                    toastMessage("不支持该文件格式，生成摘要失败")
                }
            }
        }
        return true
    }

    fun release() {
        mSelectRecordList?.clear()
        AISummaryTaskManager.releaseAll()
    }
}