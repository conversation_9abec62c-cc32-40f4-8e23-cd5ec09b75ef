/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ActivityTaskUtils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.task

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.BrowseFileAction
import com.soundrecorder.modulerouter.HomeAction
import com.soundrecorder.modulerouter.SettingAction
import java.lang.ref.WeakReference
import java.util.UUID

object ActivityTaskUtils {

    private val TAG by lazy {
        ActivityTaskUtils::class.java.simpleName
    }

    /**
     * Activity重建标识UUID
     */
    private val KEY_ACTIVITY_UUID by lazy {
        "${BaseApplication.getAppContext().packageName}.key.activity.uuid"
    }

    /**
     * 录音App所有Activity的堆栈数据结构对象
     */
    private val activityTask = LinkedHashMap<Int, MutableList<ActivityTaskInfo>>()

    /**
     * 记录前台taskId
     */
    private var frontTaskId = -1

    /**
     * 主栈 ID
     */
    private var mainTaskId = -1

    /**
     * activity 计数
     */
    private var mFinalCount = 0
    private val mActivityNames = mutableSetOf<String>()

    /**
     * 保存应用不同栈前后台
     */
    private val mAppIsForeground = HashMap<Int, Boolean>()

    private val activityLifecycleCallbacks by lazy {
        object : Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                if (activity.hasExclude()) return
                frontTaskId = activity.taskId
                activityTask.addOrReplaceActivity(activity, savedInstanceState)
                DebugUtil.d(TAG, "onActivityCreated - $activity")
                DebugUtil.d(TAG, "savedInstanceState is null  = ${savedInstanceState == null}")
                DebugUtil.d(TAG, "${activityTask[frontTaskId]}")
            }

            override fun onActivityStarted(activity: Activity) {
                if (activity.hasExclude()) return
                activityTask.move2TopActivity(activity)
                DebugUtil.d(TAG, "onActivityStarted - $activity")
                if (activity.componentName.className != HomeAction.getTransParentActivityClass()?.name) {
                    mActivityNames.add(activity.componentName.className)
                    mFinalCount++

                    DebugUtil.d(TAG, "onActivityStarted - $mActivityNames  mFinalCount $mFinalCount")
                    if (mFinalCount == 1) {
                        mAppIsForeground[activity.taskId] = true
                    }
                }
            }

            override fun onActivityResumed(activity: Activity) {
                if (activity.hasExclude()) return
                activityTask.move2TopActivity(activity)
                DebugUtil.d(TAG, "onActivityResumed - $activity - $activityTask")
                if (frontTaskId != activity.taskId) {
                    frontTaskId = activity.taskId
                    DebugUtil.d(TAG, "${activityTask[frontTaskId]}")
                }
            }

            override fun onActivityPaused(activity: Activity) {
                if (activity.hasExclude()) return
                DebugUtil.d(TAG, "onActivityPaused - $activity")
            }

            override fun onActivityStopped(activity: Activity) {
                if (activity.hasExclude()) return
                DebugUtil.d(TAG, "onActivityStopped - $activity")
                if (mActivityNames.contains(activity.componentName.className)
                    && activity.componentName.className != HomeAction.getTransParentActivityClass()?.name) {
                    mFinalCount--
                    DebugUtil.d(
                        TAG, "onActivityStopped - $mActivityNames  mFinalCount $mFinalCount"
                    )
                    if (mFinalCount == 0) {
                        mAppIsForeground[activity.taskId] = false
                    }
                }
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
                if (activity.hasExclude()) return
                activityTask.saveInstanceState(activity, outState)
            }

            override fun onActivityDestroyed(activity: Activity) {
                DebugUtil.d(TAG, "onActivityDestroyed, activity = $activity, isFinishing = ${activity.isFinishing}, ${activityTask[frontTaskId]}")
                if (activity.hasExclude()) return
                if (activity.isFinishing) {
                    activityTask.removeActivity(activity)
                    BaseApplication.getApplication().onDestroyedRelease(activity)
                }
            }
        }
    }

    @JvmStatic
    fun isForeground(): Boolean {
        DebugUtil.d(TAG, "isForeground, mFinalCount:$mFinalCount")
        return mFinalCount > 0
    }

    /**
     * ActivityTaskManager初始化
     */
    @JvmStatic
    fun Application.initActivityTaskManager() {
        this.registerActivityLifecycleCallbacks(activityLifecycleCallbacks)
    }

    /**
     * 从activityTask获取Activity的uuid，并保存到Bundle中
     * @param activity 当前正在onActivitySaveInstanceState的Activity
     */
    @JvmStatic
    private fun LinkedHashMap<Int, MutableList<ActivityTaskInfo>>.saveInstanceState(activity: Activity, outState: Bundle) {
        val uuid = this[activity.taskId]?.firstOrNull { it.activity() == activity }?.ud
        DebugUtil.d(TAG, "onSaveInstanceState - ud = $uuid")
        if (!uuid.isNullOrEmpty()) {
            outState.putString(KEY_ACTIVITY_UUID, uuid)
        }
    }

    /**
     * 向activityTask对应的taskId列表替换或者移除Activity
     * @param activity 当前正在onActivityCreated的Activity
     */
    @JvmStatic
    private fun LinkedHashMap<Int, MutableList<ActivityTaskInfo>>.addOrReplaceActivity(activity: Activity, savedInstanceState: Bundle?) {
        var uuid = savedInstanceState?.getString(KEY_ACTIVITY_UUID)
        if (uuid.isNullOrEmpty()) {
            uuid = UUID.randomUUID().toString()
        }
        val taskId = activity.taskId
        var v = this[taskId]
        if (v == null) {
            v = mutableListOf()
            put(taskId, v)
        }
        if (v.any { it.ud == uuid }) {
            v.forEach {
                if (it.ud == uuid) {
                    it.replace(activity)
                }
            }
        } else {
            v.add(ActivityTaskInfo(uuid, WeakReference(activity)))
        }
    }

    @JvmStatic
    private fun LinkedHashMap<Int, MutableList<ActivityTaskInfo>>.move2TopActivity(activity: Activity) {
        val v = this[activity.taskId] ?: return
        if (v.lastOrNull()?.activity() == activity) {
            return
        }
        val ati = v.lastOrNull { it.activity() == activity }
        if (ati != null) {
            v.remove(ati)
            v.add(ati)
        }
    }

    /**
     * 从activityTask对应的taskId列表移除
     * @param activity 当前正在onActivityDestroyed的Activity
     */
    private fun LinkedHashMap<Int, MutableList<ActivityTaskInfo>>.removeActivity(activity: Activity) {
        val taskId = activity.taskId
        val v = this[taskId]
        if (!v.isNullOrEmpty()) {
            v.removeAll {
                it.activity() == activity
            }
        }
        if (v.isNullOrEmpty()) {
            this.remove(taskId)
        }
    }

    /**
     * @param taskId 默认不传，代表获取整个进程的topActivity，不为空，获取当前task的topActivity
     * @return 返回topActivity可能为空
     */
    @JvmStatic
    fun topActivity(taskId: Int): Activity? {
        val shouldRemoveKey = mutableListOf<Int>()
        //遍历hashmap
        activityTask.forEach { (k, v) ->
            v.removeAll {
                it.activity() == null
            }
            if (v.isEmpty()) {
                shouldRemoveKey.add(k)
            }
        }
        shouldRemoveKey.forEach {
            activityTask.remove(it)
        }
        return activityTask[taskId]?.lastOrNull()?.activity()
    }

    /**
     * 获取当前Activity Task的topActivity
     */
    fun Activity.topActivity() = topActivity(taskId)

    /**
     * 获取前台Task的topActivity
     */
    @Suppress("unused")
    fun topActivityOnFrontTask() = topActivity(frontTaskId)

    @JvmStatic
    fun topActivityWithOutFinishingActivity(): Activity? {
        return activityTask[frontTaskId]?.map { it.activity() }?.lastOrNull { it?.isFinishing == false }
    }

    /**
     * 获取整个应用的Activity数组
     */
    @Suppress("unused")
    fun getAppActivities() = mutableListOf<Activity>().apply {
        activityTask.values.forEach { list ->
            list.forEach { ai ->
                ai.activity()?.let {
                    add(it)
                }
            }
        }
    }

    /**
     * 回到首页
     */
    @JvmStatic
    fun backToBrowse(taskId: Int) {
        val actList = activityTask[taskId]
        if (actList.isNullOrEmpty() || actList.size < 1) return
        val browseClass = BrowseFileAction.getBrowseFileClass()
        actList.forEach {
            val act = it.activity()
            if ((act != null) && (act::class.java != browseClass)) {
                act.finish()
            }
        }
    }

    @JvmStatic
    fun secondActivity(taskId: Int): Activity? {
        val shouldRemoveKey = mutableListOf<Int>()
        //遍历hashmap
        activityTask.forEach { (k, v) ->
            v.removeAll {
                it.activity() == null
            }
            if (v.isEmpty()) {
                shouldRemoveKey.add(k)
            }
        }
        shouldRemoveKey.forEach {
            activityTask.remove(it)
        }
        val size = activityTask[taskId]?.size
        if (size != null && size > 1) {
            val index = size.minus(2)
            val second = activityTask[taskId]?.get(index)?.activity()
            DebugUtil.i(TAG, "secondActivity: $second")
            return second
        }
        return null
    }

    @JvmStatic
    fun getLastActivity(taskId: Int): Activity? {
        val shouldRemoveKey = mutableListOf<Int>()
        //遍历hashmap
        activityTask.forEach { (k, v) ->
            v.removeAll {
                it.activity() == null
            }
            if (v.isEmpty()) {
                shouldRemoveKey.add(k)
            }
        }
        shouldRemoveKey.forEach {
            activityTask.remove(it)
        }
        return activityTask[taskId]?.firstOrNull()?.activity()
    }

    @JvmStatic
    fun isTaskEmpty(taskId: Int): Boolean {
        val isEmpty = activityTask[taskId].isNullOrEmpty()
        DebugUtil.i(TAG, "isTaskEmpty isEmpty:$isEmpty")
        return isEmpty
    }

    @JvmStatic
    fun isAllTaskEmpty(): Boolean {
        var isEmpty = true
        activityTask.forEach { (t, u) ->
            isEmpty = isEmpty && u.isNullOrEmpty()
        }
        DebugUtil.i(TAG, "isAllTaskEmpty isEmpty:$isEmpty")
        return isEmpty
    }

    @JvmStatic
    fun isTaskEmptyExceptActivity(taskId: Int, activity: Activity): Boolean {
        var isEmpty = true
        DebugUtil.i(TAG, "isTaskEmptyExceptActivity:${activityTask[taskId]}")
        activityTask[taskId]?.forEach {
            if (it.activity() != null && it.activity() != activity) {
                DebugUtil.i(TAG, "isTaskEmptyExceptActivity:other activity")
                isEmpty = false
                return@forEach
            }
        }
        DebugUtil.i(TAG, "isTaskEmptyExceptActivity:taskId:$taskId,isEmpty: $isEmpty")
        return isEmpty
    }

    @JvmStatic
    fun isFirstTaskBrowseFileActivity(taskId: Int, browseName: String, func: ((act: Activity) -> Unit)? = null): Boolean {
        var isTaskTopBrowseFile = false
        val activity = topActivity(taskId)
        if (activity != null) {
            if (activity.javaClass.name.equals(browseName, ignoreCase = true)) {
                isTaskTopBrowseFile = true
                func?.invoke(activity)
            } else if (activity.javaClass.name.equals(HomeAction.getTransParentActivityClass()?.name, ignoreCase = true)) {
                val secondActivity = secondActivity(taskId)
                if (secondActivity != null && secondActivity.javaClass.name.equals(browseName, ignoreCase = true)) {
                    isTaskTopBrowseFile = true
                    func?.invoke(secondActivity)
                }
            }
        }
        DebugUtil.i(TAG, "isFirstTaskBrowseFileActivity isTopBrowse:$isTaskTopBrowseFile")
        return isTaskTopBrowseFile
    }

    @JvmStatic
    fun isFirstTaskRecorderActivity(taskId: Int, activityName: String?): Boolean {
        if (activityName == null) {
            return false
        }
        var isTaskTopRecorder = false
        val activity = topActivity(taskId)
        if (activity != null) {
            if (activity.javaClass.name.equals(activityName, ignoreCase = true)) {
                isTaskTopRecorder = true
            } else if (activity.javaClass.name.equals(HomeAction.getTransParentActivityClass()?.name, ignoreCase = true)) {
                val secondActivity = secondActivity(taskId)
                if (secondActivity != null && secondActivity.javaClass.name.equals(activityName, ignoreCase = true)) {
                    isTaskTopRecorder = true
                }
            }
        }
        DebugUtil.i(TAG, "isFirstTaskRecorderActivity, taskId:$taskId, isTopRecorder:$isTaskTopRecorder")
        return isTaskTopRecorder
    }

    /**
     * 在设置或设置二三级页面
     */
    @JvmStatic
    fun isSettingOrSecondaryActivity(taskId: Int): Boolean {
        val settingPages = SettingAction.getPageNameInSetting() ?: return false
        var hasSettingPage = false
        activityTask[taskId]?.forEach {
            it.activity()?.let { activity ->
                if (settingPages.contains(activity.javaClass.name)) {
                    DebugUtil.i(TAG, "isSettingOrSecondaryActivity")
                    hasSettingPage = true
                    return@forEach
                }
            }
        }
        return hasSettingPage
    }

    @JvmStatic
    fun isFirstTaskPlaybackActivityOfAllTask(func: ((act: Activity) -> Boolean)): Boolean {
        if (activityTask.size > 0) {
            var isPlayBackActivity = false
            val keys: Set<Int> = activityTask.keys
            val iterator: Iterator<Int> = keys.iterator()
            while (iterator.hasNext()) {
                val key = iterator.next()
                val topActivity = topActivity(key)
                if (topActivity != null) {
                    isPlayBackActivity = func.invoke(topActivity)
                }
                if (isPlayBackActivity) {
                    return true
                }
            }
            return false
        }
        return false
    }

    @JvmStatic
    fun isFirstTaskEditRecordActivityOfAllTask(editorName: String): Boolean {
        if (activityTask.size > 0) {
            var isEditorActivity = false
            val keys: Set<Int> = activityTask.keys
            val iterator: Iterator<Int> = keys.iterator()
            while (iterator.hasNext()) {
                val key = iterator.next()
                val topActivity = topActivity(key)
                if (topActivity != null) {
                    isEditorActivity = topActivity.javaClass.name.equals(editorName, true)
                }
                if (isEditorActivity) {
                    return true
                }
            }
            return false
        }
        return false
    }

    @JvmStatic
    fun getActivity(taskId: Int, actClass: Class<*>?): Activity? {
        val activities = activityTask[taskId]
        if (activities.isNullOrEmpty().not()) {
            val iterator: Iterator<ActivityTaskInfo> = activities!!.iterator()
            while (iterator.hasNext()) {
                val act = iterator.next().activity()
                if (act != null && act.javaClass.name == actClass?.name) {
                    return act
                }
            }
        }
        return null
    }

    @JvmStatic
    fun getMainTaskId(): Int {
        DebugUtil.i(TAG, "getMainTaskId $mainTaskId")
        return mainTaskId
    }

    @JvmStatic
    fun setMainTaskId(taskId: Int) {
        mainTaskId = taskId
    }

    /**
     * 获取主栈是否在前台
     */
    @JvmStatic
    fun isMainForeground(): Boolean {
        val foreground = mAppIsForeground[mainTaskId]
        DebugUtil.d(TAG, "isMainForeground == $foreground")
        return foreground ?: false
    }

    @JvmStatic
    fun Activity.isEmptyExcludeSelf(): Boolean {
        return activityTask[taskId]?.none { it.activity() != this } != false
    }

    @JvmStatic
    fun Activity.any(recorderActivity: Class<out Activity>): Boolean {
        return activityTask[taskId]?.any { it.activity()?.javaClass == recorderActivity } == true
    }

    @JvmStatic
    fun Activity.anyExcludeSelf(activity: Class<out Activity>): Boolean {
        return activityTask[taskId]?.any { (it.activity()?.javaClass == activity) && (it.activity() != this) } == true
    }

    @JvmStatic
    fun Any.hasExclude(): Boolean {
        return (this as? ExcludeActivityTask)?.hasExclude() ?: false
    }

    @JvmStatic
    fun clearAllTask() {
        getAppActivities().forEach { it.finishAndRemoveTask() }
    }

    @JvmStatic
    fun clearAllTaskExclude(activityClass: Class<out Activity>) {
        getAppActivities().filterNot { it.javaClass == activityClass }
            .forEach { it.finishAndRemoveTask() }
    }
}

private class ActivityTaskInfo(val ud: String = UUID.randomUUID().toString(), var weakActivity: WeakReference<Activity>) {
    fun replace(activity: Activity) {
        weakActivity = WeakReference(activity)
    }

    fun activity(): Activity? {
        return weakActivity.get()
    }

    override fun toString(): String {
        return "{ud = $ud,activity = ${activity()},taskId = ${activity()?.taskId}}"
    }
}

interface ExcludeActivityTask {
    /**
     * ActivityTaskUtils的不加入Task标志
     */
    fun hasExclude(): Boolean
}