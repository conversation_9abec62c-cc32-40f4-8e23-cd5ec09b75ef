/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SummaryStaticUtil
 * Description:
 * Version: 1.0
 * Date: 2024/3/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/3/18 1.0 create
 */

package com.soundrecorder.common.buryingpoint

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.modulerouter.summary.SummaryAction

object SummaryStaticUtil {
    private const val EVENT_GROUP_SUMMARY = "summary"

    /*点击查看摘要*/
    private const val EVENT_ID_CLICK_VIEW_SUMMARY = "click_view_summary"

    /*点击生成摘要*/
    private const val EVENT_ID_CLICK_START_SUMMARY = "click_start_summary"

    /*展示生成摘要*/
    private const val EVENT_ID_SHOW_START_SUMMARY = "show_start_summary"

    /*展示查看摘要*/
    private const val EVENT_ID_SHOW_VIEW_SUMMARY = "show_view_summary"

    /*事件字段：来源*/
    private const val EVENT_KEY_FROM = "from"

    /*事件字段：错误码，用于点击生成摘要事件，录音预处理判断错误码*/
    private const val EVENT_KEY_ERROR_CODE = "errorCode"

    /*来源：录制页面*/
    const val EVENT_FROM_RECORD = "record"

    /*来源：桌面卡片*/
    const val LAUNCHER_RECORD = "launcher"

    /*来源：音频详情音频页面*/
    const val EVENT_FROM_AUDIO = "audio"

    /*来源：一排详情文本页面*/
    const val EVENT_FROM_CONVERT = "convert"

    /*来源：首页列表页面*/
    const val EVENT_FROM_MAIN = "main"

    /*来源：搜索列表页面*/
    const val EVENT_FROM_SEARCH = "search"

    /*不满足格式要求*/
    const val EVENT_ERROR_FORMAT = "format"

    /*不满足最大时长要求*/
    const val EVENT_ERROR_MAX_DURATION = "max_duration"
    /*不满足最小时长要求*/
    const val EVENT_ERROR_MIN_DURATION = "min_duration"

    /*2个小时以上，警示弹窗，用户点击取消*/
    const val EVENT_ERROR_WARN_DURATION = "warn_duration"

    /*不满足文件size要求*/
    const val EVENT_ERROR_SIZE = "size"

    /*不满足网络要求，无网络*/
    const val EVENT_ERROR_NETWORK = "network"
    /*超级省电模式*/
    const val EVENT_ERROR_SUPER_POWER_MODE = "superPowerMode"

    /**
     * 显示生成摘要事件
     * @param from 来源
     */
    @JvmStatic
    fun addShowStartSummaryEvent(from: String) {
        val eventInfo = HashMap<String, String>()
        eventInfo[EVENT_KEY_FROM] = from
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), EVENT_GROUP_SUMMARY, EVENT_ID_SHOW_START_SUMMARY, eventInfo, false)
    }

    /**
     * 点击生成摘要事件
     * @param from 来源
     */
    @JvmStatic
    fun addClickStartSummaryEvent(from: String, originErrorCode: Int) {
        val eventInfo = HashMap<String, String>()
        eventInfo[EVENT_KEY_FROM] = from
        val error = when (originErrorCode) {
            SummaryAction.ERROR_CODE_DURATION_LARGE -> EVENT_ERROR_MAX_DURATION
            SummaryAction.ERROR_CODE_FORMAT -> EVENT_ERROR_FORMAT
            SummaryAction.ERROR_CODE_DURATION_LARGE_WARN -> EVENT_ERROR_WARN_DURATION
            SummaryAction.ERROR_CODE_SIZE_LARGE -> EVENT_ERROR_SIZE
            SummaryAction.ERROR_CODE_NO_INTERNET -> EVENT_ERROR_NETWORK
            SummaryAction.ERROR_CODE_SUPER_SAVE_MODE -> EVENT_ERROR_SUPER_POWER_MODE
            SummaryAction.ERROR_CODE_DURATION_SMALL -> EVENT_ERROR_MIN_DURATION
            else -> null
        }
        error?.let {
            eventInfo[EVENT_KEY_ERROR_CODE] = error
        }
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), EVENT_GROUP_SUMMARY, EVENT_ID_CLICK_START_SUMMARY, eventInfo, false)
    }

    /**
     * 显示查看摘要事件
     * @param from 来源
     */
    @JvmStatic
    fun addShowViewSummaryEvent(from: String) {
        val eventInfo = HashMap<String, String>()
        eventInfo[EVENT_KEY_FROM] = from
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), EVENT_GROUP_SUMMARY, EVENT_ID_SHOW_VIEW_SUMMARY, eventInfo, false)
    }

    /**
     * 点击查看摘要事件
     * @param from 来源
     */
    @JvmStatic
    fun addClickViewSummaryEvent(from: String) {
        val eventInfo = HashMap<String, String>()
        eventInfo[EVENT_KEY_FROM] = from
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), EVENT_GROUP_SUMMARY, EVENT_ID_CLICK_VIEW_SUMMARY, eventInfo, false)
    }
}