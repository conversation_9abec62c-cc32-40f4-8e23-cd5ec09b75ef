/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * File        :
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/2/14
 * * Author      : W9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */
package andes.oplus.documentsreader.core.common

import android.icu.text.Transliterator
import android.text.TextUtils
import android.util.Log
import java.util.*

class HanziToPinyin private constructor() {
    private var mPinyinTransliterator: Transliterator? = null
    private var mAsciiTransliterator: Transliterator? = null

    data class Token(var type: Int = 0, var source: String? = null, var target: String? = null) {

        /**
         * Type of this token, ASCII, PINYIN or UNKNOWN.
         *
         * var type = 0
         *
         * Original string before translation.
         *
         * var source: String? = null
         *
         * Translated string of source. For Han, target is corresponding Pinyin. Otherwise target is
         * original string in source.
         *
         * var target: String? = null
         */

        companion object {
            /**
             * Separator between target string for each source char
             */
            const val SEPARATOR = " "
            const val LATIN = 1
            const val PINYIN = 2
            const val UNKNOWN = 3
        }
    }

    fun hasChineseTransliterator(): Boolean {
        return mPinyinTransliterator != null
    }

    @SuppressWarnings("NewApi")
    private fun tokenize(character: Char, token: Token) {
        token.source = Character.toString(character)

        // ASCII
        if (character.code < CODE_128) {
            token.type = Token.LATIN
            token.target = token.source
            return
        }

        // Extended Latin. Transcode these to ASCII equivalents
        if (character.code < CODE_250 || character.code in CODE_1E00..CODE_1EFE) {
            token.type = Token.LATIN
            token.target = if (mAsciiTransliterator == null) {
                    token.source
                } else {
                    mAsciiTransliterator!!.transliterate(token.source)
                }
            return
        }
        token.type = Token.PINYIN
        token.target = mPinyinTransliterator!!.transliterate(token.source)
        if (TextUtils.isEmpty(token.target) ||
            TextUtils.equals(token.source, token.target)
        ) {
            token.type = Token.UNKNOWN
            token.target = token.source
        }
    }

    @SuppressWarnings("NewApi")
    fun transliterate(input: String?): String? {
        return if (!hasChineseTransliterator() || TextUtils.isEmpty(input)) {
            null
        } else mPinyinTransliterator!!.transliterate(input)
    }

    /**
     * Convert the input to a array of tokens. The sequence of ASCII or Unknown characters without
     * space will be put into a Token, One Hanzi character which has pinyin will be treated as a
     * Token. If there is no Chinese transliterator, the empty token array is returned.
     */
    fun getTokens(input: String): ArrayList<Token> {

        val tokens = ArrayList<Token>()
        if (!hasChineseTransliterator() || TextUtils.isEmpty(input)) {
            // return empty tokens.
            return tokens
        }
        val inputLength = input.length
        val sb = StringBuilder()
        var tokenType = Token.LATIN
        var token = Token()

         /*Go through the input, create a new token when
          a. Token type changed
          b. Get the Pinyin of current charater.
          c. current character is space.*/
        for (i in 0 until inputLength) {
            val character = input[i]
            if (Character.isSpaceChar(character)) {
                if (sb.isNotEmpty()) {
                    addToken(sb, tokens, tokenType)
                }
            } else {
                tokenize(character, token)
                if (token.type == Token.PINYIN) {
                    if (sb.isNotEmpty()) {
                        addToken(sb, tokens, tokenType)
                    }
                    tokens.add(token)
                    token = Token()
                } else {
                    if (tokenType != token.type && sb.isNotEmpty()) {
                        addToken(sb, tokens, tokenType)
                    }
                    sb.append(token.target)
                }
                tokenType = token.type
            }
        }
        if (sb.isNotEmpty()) {
            addToken(sb, tokens, tokenType)
        }
        return tokens
    }

    fun getGroupTokens(input: String): ArrayList<Token> {
        val tokens: ArrayList<Token> =
            ArrayList<Token>()
        if (!hasChineseTransliterator() || TextUtils.isEmpty(input)) {
            return tokens
        }
        val inputLength = input.length
        val sb = StringBuilder()
        val letterSb = StringBuilder()

        var tokenType = 0
        val token = Token()
        for (i in 0 until inputLength) {
            val character = input[i]
            if (Character.isSpaceChar(character)) {
                if (sb.isNotEmpty()) {
                    addToken(sb, letterSb, tokens, tokenType)
                }
            } else {
                tokenize(character, token)
                if (token.type != tokenType && sb.isNotEmpty()) {
                    addToken(sb, letterSb, tokens, tokenType)
                }
                sb.append(token.source)
                letterSb.append(token.target?.first())
                tokenType = token.type
            }
        }
        if (sb.isNotEmpty()) {
            addToken(sb, letterSb, tokens, tokenType)
        }
        return tokens
    }

    private fun addToken(sb: StringBuilder, tokens: ArrayList<Token>, tokenType: Int) {
        val str = sb.toString()
        tokens.add(Token(tokenType, str, str))
        sb.setLength(0)
    }

    private fun addToken(sb: StringBuilder, letterSb: StringBuilder, tokens: ArrayList<Token>, tokenType: Int) {
        val str = sb.toString()
        tokens.add(Token(tokenType, str, letterSb.toString()))
        sb.setLength(0)
        letterSb.setLength(0)
    }

    companion object {
        private const val TAG = "HanziToPinyin"

        const val CODE_128 = 128
        const val CODE_250 = 0x250
        const val CODE_1E00 = 0x1e00
        const val CODE_1EFE = 0x1efe

        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            HanziToPinyin()
        }

        /**
         * 输入汉字返回拼音的通用方法函数
         */
        fun getPinYin(hanzi: String): String {
            val tokens = instance.getTokens(hanzi)
            val sb = StringBuilder()
            if (tokens.size > 0) {
                for (token in tokens) {
                    if (Token.PINYIN == token.type) {
                        sb.append(token.target)
                    } else {
                        sb.append(token.source)
                    }
                }
            }
            return sb.toString().uppercase(Locale.getDefault())
        }

        fun getPinYinFirstLetter(hanzi: String): String {
            val tokens = instance.getTokens(hanzi)
            val sb = StringBuilder()
            if (tokens.size > 0) {
                for (token in tokens) {
                    if (Token.PINYIN == token.type) {
                        sb.append(token.target?.firstOrNull())
                    } else {
                        sb.append(token.source?.firstOrNull())
                    }
                }
            }
            return sb.toString().uppercase(Locale.getDefault())
        }
    }

    init {
        initTransliterator()
    }

    @SuppressWarnings("NewApi")
    fun initTransliterator() {
        try {
            mPinyinTransliterator = Transliterator.getInstance(
                "Han-Latin/Names; Latin-Ascii; Any-Upper"
            )
            mAsciiTransliterator = Transliterator.getInstance("Latin-Ascii")
        } catch (e: IllegalArgumentException) {
            Log.w(
                TAG, "Han-Latin/Names transliterator data is missing,"
                        + " HanziToPinyin is disabled"
            )
        }
    }
}