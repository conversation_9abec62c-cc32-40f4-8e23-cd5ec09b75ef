/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.controller
 * * Version     : 1.0
 * * Date        : 2020/3/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.documentsreader.emptyfile

import andes.oplus.documentsreader.core.common.BaseLifeController
import andes.oplus.documentsreader.core.common.Utils
import andes.oplus.documentsreader.core.common.noMoreAction
import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.annotation.VisibleForTesting
import androidx.core.view.contains
import androidx.lifecycle.Lifecycle
import com.documentsreader.emptyfile.FileEmptyUtils.FILE_EMPTY_ANIMATION_JSON
import com.documentsreader.emptyfile.FileEmptyUtils.FILE_EMPTY_ANIMATION_JSON_NIGHT
import com.documentsreader.emptyfile.FileEmptyUtils.SEARCH_EMPTY_ANIMATION_FILE
import com.documentsreader.emptyfile.FileEmptyUtils.SEARCH_EMPTY_ANIMATION_FILE_NIGHT
import com.documentsreader.emptyfile.impl.R
import com.documentsreader.emptyfile.view.FileEmptyView

class FileEmptyController : BaseLifeController, IFileEmptyController {
    companion object {
        private const val TAG = "FileEmptyController"
    }

    @VisibleForTesting
    var mFileEmptyView: FileEmptyView? = null
        get() = field.noMoreAction()

    override fun setLifecycle(lifecycle: Lifecycle) {
        lifecycle.addObserver(this)
    }

    @VisibleForTesting
    override fun initFileEmptyView(context: Context, rootView: ViewGroup) {
        if (mFileEmptyView == null || !rootView.contains(mFileEmptyView!!)) {
            mFileEmptyView = FileEmptyView(context)
            val layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            rootView.addView(mFileEmptyView, 0, layoutParams)
        }
    }

    /**
     * 显示空文件视图
     */
    @Suppress("ParameterStyleBracesRule")
    override fun showFileEmptyView(
        context: Context, rootView: ViewGroup,
        asset: String?,
        emptyTitle: Int?
    ): View {
        initFileEmptyView(context, rootView)
        if (emptyTitle == null) {
            setFileEmptyTitle(R.string.empty_file)
        } else {
            setFileEmptyTitle(emptyTitle)
        }
        if (asset == null) {
            setFileEmptyAnimation("empty_file_res.json")
        } else {
            setFileEmptyAnimation(asset)
        }
        setEmptySummaryVisibilityAndContent(View.GONE, "")
        setEmptyGuidVisibilityAndContent(View.GONE, "", null)
        mFileEmptyView?.setEmptyVisible(View.VISIBLE)
        return mFileEmptyView!!
    }

    @VisibleForTesting
    fun setEmptyGuidVisibilityAndContent(
        visible: Int,
        content: String,
        clickListener: View.OnClickListener?
    ) {
        mFileEmptyView?.setEmptyGuidVisibilityAndContent(visible, content, clickListener)
    }

    override fun setEmptySummaryVisibilityAndContent(visible: Int, content: String) {
        mFileEmptyView?.setEmptySummaryVisibilityAndContent(visible, content)
    }


    override fun hideFileEmptyView() {
        mFileEmptyView?.setEmptyVisible(View.GONE)
    }

    @VisibleForTesting
    override fun setFileEmptyAnimation(asset: String) {
        mFileEmptyView?.let {
            when (asset) {
                FILE_EMPTY_ANIMATION_JSON -> {
                    if (Utils.isNightMode(mFileEmptyView?.context)) {
                        it.setEmptyAnimation(FILE_EMPTY_ANIMATION_JSON_NIGHT)
                    } else {
                        it.setEmptyAnimation(FILE_EMPTY_ANIMATION_JSON)
                    }
                }

                SEARCH_EMPTY_ANIMATION_FILE -> {
                    if (Utils.isNightMode(mFileEmptyView?.context)) {
                        it.setEmptyAnimation(SEARCH_EMPTY_ANIMATION_FILE_NIGHT)
                    } else {
                        it.setEmptyAnimation(SEARCH_EMPTY_ANIMATION_FILE)
                    }
                }
            }
        }
    }

    override fun setFileEmptyTitle(titleId: Int) {
        mFileEmptyView?.setEmptyTextViewId(titleId)
    }

    override fun onDestroy() {
        mFileEmptyView?.setAnimationReset()
        mFileEmptyView = null
    }

    /**
     * 在分屏切换到全屏时，要刷新EmptyFile中的控件，把Icon重新显示出来
     * tablet do not need this function
     */
    override fun changeEmptyFileIcon() {
        mFileEmptyView?.let {
            if (it.getEmptyVisibility() == View.VISIBLE) {
                it.setEmptyVisible(View.VISIBLE)
            }
        }
    }

    override fun setSmallCenterLayout(smallLayout: Boolean) {
        mFileEmptyView?.setSmallCenterLayout(smallLayout)
    }

    override fun isEmptyShowing(): Boolean {
        return mFileEmptyView?.visibility == View.VISIBLE
    }
}