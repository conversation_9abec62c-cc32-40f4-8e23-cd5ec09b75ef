package com.soundrecorder.common.utils;

import android.content.Context;
import android.content.Intent;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.constant.RecordModeConstant;


public class RecordFileChangeNotify {

    private static final String TAG = "RecordFileChangeNotify";
    public static final String FILE_UPDATE_ACTION = "com.oplus.soundrecorder.recordFileChanged";
    public static final String FILE_UPDATE_TYPES = "com.oplus.soundrecorder.fileUpdateTypes";
    public static final String BRENO_FRONT_TO_RECORD = "com.oplus.soundrecorder.browse.to.record";

    public static final String CUBE_CLEAR_PLAY_RECORD_DATA = "com.oplus.soundrecorder.clearPlayData";

    public static final String FILE_CUT_NEW_RECORD_ACTION = "com.oplus.soundrecorder.recordFileCutChanged";

    public static final String SMART_NAME_ALL_TASK_FINISH_ACTION = "com.oplus.soundrecorder.smartNameAllTaskFinish";

    private boolean mFilesHasChanged = false;
    private ArrayList<Integer> mRecorderTypes = new ArrayList<>();

    public RecordFileChangeNotify() {

    }

    public void init() {
        mFilesHasChanged = false;
        mRecorderTypes.clear();
    }


    public void setFileChanged(int type) {
        mFilesHasChanged = true;
        if (!mRecorderTypes.contains(type)) {
            DebugUtil.i(TAG, "setFileChanged: add " + type);
            mRecorderTypes.add(type);
        }
    }

    public void notifyBySendBroadcast(Context context) {
        if (mFilesHasChanged) {
            Intent intent = new Intent(FILE_UPDATE_ACTION);
            int[] arrays = getArrayForList(mRecorderTypes);
            if (arrays != null) {
                intent.putExtra(FILE_UPDATE_TYPES, arrays);
            }
            DebugUtil.i(TAG, "notifyBySendBroadcast types: " + Arrays.toString(mRecorderTypes.toArray()));
            BaseUtil.sendLocalBroadcast(context, intent);
            init();
        }
    }

    private int[] getArrayForList(List<Integer> arrayList){
        if (arrayList == null || arrayList.size() == 0) {
            return null;
        } else {
            int[] result = new int[arrayList.size()];
            for (int i = 0 ; i<arrayList.size(); i++ ) {
                result[i] = arrayList.get(i);
            }
            return result;
        }
    }




    public void sendAllChangeBroadCast(Context context) {
        mRecorderTypes.clear();
        mRecorderTypes.add(RecordModeConstant.RECORD_TYPE_STANDARD);
        mRecorderTypes.add(RecordModeConstant.RECORD_TYPE_CONFERENCE);
        mRecorderTypes.add(RecordModeConstant.RECORD_TYPE_INTERVIEW);
        mRecorderTypes.add(RecordModeConstant.RECORD_TYPE_CALL);
        Intent intent = new Intent(FILE_UPDATE_ACTION);
        int[] arrays = getArrayForList(mRecorderTypes);
        if (arrays != null) {
            intent.putExtra(FILE_UPDATE_TYPES, arrays);
        }
        DebugUtil.i(TAG, "sendAllChangeBroadCast types: " + Arrays.toString(mRecorderTypes.toArray()));
        BaseUtil.sendLocalBroadcast(context, intent);
    }






}
