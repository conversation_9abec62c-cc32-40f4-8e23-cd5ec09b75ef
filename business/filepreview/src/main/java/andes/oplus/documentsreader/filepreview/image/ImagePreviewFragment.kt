/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: VideoPreviewFragment
 * * Description: the fragment for image preview
 * * Version: 1.0
 * * Date : 2023/12/06
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2023/12/06       1.0           create
 ****************************************************************/
package andes.oplus.documentsreader.filepreview.image

import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.KtConstants.SHOW_SIDEBAR_PANEL
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.Utils
import andes.oplus.documentsreader.core.common.stringResource
import andes.oplus.documentsreader.core.common.uiconfig.UIConfigMonitor
import andes.oplus.documentsreader.core.common.uiconfig.type.IUIConfig
import andes.oplus.documentsreader.filepreview.BasePreviewFragment
import andes.oplus.documentsreader.filepreview.FilePreviewDialogActivity
import andes.oplus.documentsreader.filepreview.R
import andes.oplus.documentsreader.filepreview.anim.IAnimCallback
import andes.oplus.documentsreader.filepreview.databinding.OplusDocFragmentImagePreviewBinding
import andes.oplus.documentsreader.filepreview.util.MediaUtils
import andes.oplus.documentsreader.filepreview.util.PreviewStatisticsUtil
import andes.oplus.documentsreader.interfaces.CollectPrivacyUtils
import android.graphics.PointF
import android.graphics.drawable.BitmapDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.annotation.VisibleForTesting
import androidx.core.content.ContextCompat
import androidx.core.view.MenuProvider
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.oplus.gallery.olivesdk.view.Event
import kotlinx.coroutines.launch

class ImagePreviewFragment : BasePreviewFragment(), UIConfigMonitor.OnUIConfigChangeListener, IAnimCallback {
    companion object {
        private const val TAG = "ImagePreviewFragment"
    }

    private var binding: OplusDocFragmentImagePreviewBinding? = null
    private val viewModel: ImagePreviewFragmentModel by viewModels { ImagePreviewFragmentModel.Factory }
    private var animationEnd = false
    private var canPlay = false

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (isAdded.not()) return
        if (viewModel.isLivePhoto.value == true) {
            (binding?.olive?.image as ScaleOliveImageView).onUIConfigChanged(configList)
        } else {
            binding?.ivMain?.apply {
                this.onUIConfigChanged(configList)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = OplusDocFragmentImagePreviewBinding.inflate(inflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.initFileData(arguments)
        viewModel.checkIsLivePhoto()
        initView()
        startObserver()
        CollectPrivacyUtils.collectImageInfo(viewModel.filePath)
    }

    private fun initView() {
        (activity as FilePreviewDialogActivity).initMenu(menuProvider)
        binding?.mainLayout?.setOnClickListener {
            (activity as FilePreviewDialogActivity).exitPreview(SHOW_SIDEBAR_PANEL, finishWithAnimator = true)
        }
    }

    private fun startObserver() {
        viewModel.isLivePhoto.observe(viewLifecycleOwner) { livePhoto ->
            Log.d(TAG, "isLivePhoto -> $livePhoto")
            updateUIIfLivePhoto(livePhoto)
        }
    }

    override fun onEnterAnimationStart() {
    }

    override fun onEnterAnimationEnd() {
        Log.d(TAG, "onEnterAnimationEnd canPlay = $canPlay")
        animationEnd = true
        if (canPlay) {
            viewModel.playVideo()
        }
    }

    @VisibleForTesting
    fun updateUIIfLivePhoto(isLivePhoto: Boolean) {
        if (isLivePhoto) {
            binding?.olive?.visibility = View.VISIBLE
            playOlivePhoto(viewModel.filePath)
        } else {
            binding?.ivMain?.visibility = View.VISIBLE
            initPlayPath()
        }
    }

    private fun initPlayPath() {
        binding?.ivMain?.let {
            lifecycle.addObserver(it)
            it.loadBitmap(viewModel.filePath)
        }
    }

    override fun onPause() {
        super.onPause()
        Log.d(TAG, "onPause")
    }

    /**
     * menu:分享、打开
     */
    private val menuProvider = object : MenuProvider {
        override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
            menuInflater.inflate(R.menu.preview_menu, menu)
        }

        override fun onPrepareMenu(menu: Menu) {
            super.onPrepareMenu(menu)
            menu.findItem(R.id.preview_share).also {
                it.isEnabled = true
                it.isVisible = true
                it.icon = ContextCompat.getDrawable(
                    ContextGetter.context,
                    R.drawable.menu_ic_share_normal
                )
            }
            menu.findItem(R.id.preview_open).also {
                it.isEnabled = true
                it.isVisible = true
                it.title = stringResource(
                    R.string.file_preview_menu_open_with,
                    stringResource(R.string.dcim)
                )
            }
        }

        override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
            if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                Log.d(TAG, "onMenuItemSelected -> quickClick")
                return true
            }
            if (pause) {
                Log.d(TAG, "already pause, return")
                return true
            }
            return when (menuItem.itemId) {
                R.id.preview_share -> {
                    //图片分享打开是在同一个堆栈，不需要设置关闭全部，仅关闭当前
                    viewModel.share(baseVMActivity, false)
                    true
                }

                R.id.preview_open -> {
                    if (baseVMActivity?.let { MediaUtils.openMediaFile(it, viewModel.filePath, viewModel.fileOriUri) } == true) {
                        (baseVMActivity as? FilePreviewDialogActivity)?.openByOtherSuccess()
                    }
                    PreviewStatisticsUtil.useOtherWayToOpen(viewModel.fileName)
                    true
                }

                R.id.home -> {
                    (baseVMActivity as FilePreviewDialogActivity).exitPreview(SHOW_SIDEBAR_PANEL, finishWithAnimator = true)
                    true
                }

                else -> false
            }
        }
    }

    private fun playOlivePhoto(livePath: String) {
        Log.d(TAG, "livePath path=$livePath")
        viewModel.onDataSourceCodecGet()
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.CREATED) {
                viewModel.viewProvider.collect {
                    it?.let { binding?.olive?.changeOliveProvider(it) }
                }
            }
        }
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.CREATED) {
                viewModel.updateBitmapFlow.collect { result ->
                    result.bitmap?.let {
                        result.provider?.oliveImage?.setImageDrawable(BitmapDrawable(resources, it))
                    }
                }
            }
        }
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.CREATED) {
                viewModel.layoutUiState.collect { state ->
                    if (state == null) {
                        return@collect
                    }
                    viewModel.viewProvider.value?.oliveVideo?.view?.requestLayout()
                    // 添加自动播放
                    Log.d(TAG, "viewModel.playVideo() : animationEnd = $animationEnd")
                    binding?.olive?.migrateState(Event.COVER_DISAPPEAR_START)
                    if (animationEnd) {
                        viewModel.playVideo()
                    }
                    canPlay = true
                }
            }
        }

        // livePhoto播放完成自动切换到封面
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.CREATED) {
                viewModel.coverUiState.collect {
                    Log.d(TAG, "startObserve  ->collect  coverUiState = $it")
                    binding?.olive?.migrateState(Event.VIDEO_DISAPPEAR_START)
                }
            }
        }
    }

    /**
     * 顶部是否有内容，点击toolbar就不能关闭（图片和视频可以全屏展示）
     */
    fun hasContentInPosition(clickPos: PointF): Boolean {
        binding?.scaleLayout?.let {
            return it.y <= clickPos.y
        }
        return false
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.release()
    }
}