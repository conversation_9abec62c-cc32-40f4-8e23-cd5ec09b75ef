/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PanelToolItemDecoration
 * * Description: PanelToolItemDecoration
 * * Version: 1.0
 * * Date : 2024/07/03
 * * Author:ztf
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *        ztf           2024/07/03       1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.impl.decoration

import andes.oplus.documentsreader.core.common.Utils
import andes.oplus.documentsreader.doctools.impl.R
import andes.oplus.documentsreader.impl.bean.ToolBean
import andes.oplus.documentsreader.impl.constant.ToolConstants
import andes.oplus.documentsreader.impl.enumer.EnumLayout
import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView

class PanelToolItemDecoration(var listData: MutableList<ToolBean>) :
    RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        if (listData.isEmpty()) {
            super.getItemOffsets(outRect, view, parent, state)
            return
        }
        val itemPosition =
            (view.layoutParams as GridLayoutManager.LayoutParams).viewLayoutPosition

        val params = view.layoutParams as GridLayoutManager.LayoutParams
        val spanPosition = params.spanIndex
        val isTitle = listData[itemPosition].layoutEnum == EnumLayout.LAYOUT_TITLE_PANEL
        when (isTitle) {
            true -> {
                outRect.left = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
                outRect.top = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
                outRect.right = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
                outRect.bottom = getDimensionPixelSize(parent, R.dimen.dimen_4dp)
            }

            else -> {
                when {
                    listData.size >= ToolConstants.SIZE_3 -> {
                        if (spanPosition % 2 == 0) { //左列
                            handlerLeftColumn(outRect, parent)
                        } else { //右列
                            handlerRightColumn(outRect, parent)
                        }
                    }

                    else -> {
                        outRect.left = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
                        outRect.top = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
                        outRect.right = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
                        outRect.bottom = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
                    }
                }
            }
        }
    }

    private fun handlerLeftColumn(outRect: Rect, parent: RecyclerView) {
        if (Utils.isRtl()) {
            outRect.left = getDimensionPixelSize(parent, R.dimen.dimen_4dp)
            outRect.top = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
            outRect.right = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
            outRect.bottom = getDimensionPixelSize(parent, R.dimen.dimen_8dp)
        } else {
            outRect.left = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
            outRect.top = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
            outRect.right = getDimensionPixelSize(parent, R.dimen.dimen_4dp)
            outRect.bottom = getDimensionPixelSize(parent, R.dimen.dimen_8dp)
        }
    }

    private fun handlerRightColumn(outRect: Rect, parent: RecyclerView) {
        if (Utils.isRtl()) {
            outRect.left = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
            outRect.top = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
            outRect.right = getDimensionPixelSize(parent, R.dimen.dimen_4dp)
            outRect.bottom = getDimensionPixelSize(parent, R.dimen.dimen_8dp)
        } else {
            outRect.left = getDimensionPixelSize(parent, R.dimen.dimen_4dp)
            outRect.top = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
            outRect.right = getDimensionPixelSize(parent, R.dimen.dimen_0dp)
            outRect.bottom = getDimensionPixelSize(parent, R.dimen.dimen_8dp)
        }
    }

    private fun getDimensionPixelSize(parent: RecyclerView, resId: Int): Int {
        return parent.context.resources.getDimensionPixelSize(resId)
    }
}