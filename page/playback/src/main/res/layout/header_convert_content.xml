<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dp12"
    android:paddingBottom="@dimen/dp10">


    <TextView
        android:id="@+id/tv_play_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp24"
        android:textColor="@color/coui_color_primary_neutral"
        android:textSize="@dimen/dp24"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:textAppearance="@style/couiTextAppearanceHeadline3"
        tools:text="标准录音 1" />

    <TextView
        android:id="@+id/tv_create_time"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp24"
        android:textColor="?attr/couiColorLabelSecondary"
        style="@style/couiTextBodyM"
        android:lines="1"
        android:ellipsize="end"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_speakers"
        app:layout_constraintTop_toTopOf="@id/tv_speakers"
        app:layout_constraintBottom_toBottomOf="@id/tv_speakers"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="tnum"
        tools:text="2021-06-08 14:28" />

    <com.soundrecorder.playback.newconvert.keyword.KeyWordChipGroup
        android:id="@+id/key_word_group"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        app:chipSpacingHorizontal="@dimen/dp6"
        app:chipSpacingVertical="@dimen/dp8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_create_time" />

    <TextView
        android:id="@+id/tv_speakers"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintWidth_max="180dp"
        android:lines="1"
        android:ellipsize="end"
        tools:text="@string/all_speaker"
        style="@style/couiTextBodyXS"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingHorizontal="@dimen/dp8"
        android:paddingVertical="@dimen/dp4"
        android:layout_marginTop="@dimen/dp4"
        android:drawablePadding="@dimen/dp4"
        app:layout_constraintHorizontal_bias="1"
        android:drawableEnd="@drawable/ic_speaker_select_expand"
        app:layout_constraintTop_toBottomOf="@id/tv_play_name"
        android:textColor="?attr/couiColorLabelPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_create_time"
        android:forceDarkAllowed="false"
        android:background="@drawable/bg_shape_speaker_select"/>

</androidx.constraintlayout.widget.ConstraintLayout>