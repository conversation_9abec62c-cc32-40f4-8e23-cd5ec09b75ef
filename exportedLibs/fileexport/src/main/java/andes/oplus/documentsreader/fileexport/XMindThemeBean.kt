/***********************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File:   XMindThemeBean
 * * Description: data class
 * * Version:1.0
 * * Date :2024/12/17
 * * Author:renjiahao
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * *   renjiahao                    1.0           create
 ****************************************************************/
package andes.oplus.documentsreader.fileexport

import androidx.annotation.Keep
import org.json.JSONObject
import java.util.UUID

/**
 * @param map 画布主题
 * @param centralTopic 中心节点主题
 * @param mainTopic 主要节点主题
 * @param subTopic 次要节点主题
 * @param skeletonThemeId 固定值
 * @param colorThemeId 固定值
 */
@Keep
data class XMindThemeBean(
    val map: XMindThemeItem,
    val centralTopic: XMindThemeItem,
    val mainTopic: XMindThemeItem,
    val subTopic: XMindThemeItem,
    val skeletonThemeId: String = "db4a5df4db39a8cd1310ea55ea",
    val colorThemeId: String = "Rainbow-#000229-MULTI_LINE_COLORS",
)

fun XMindThemeBean.toJsonObject(): JSONObject {
    val jsonObject = JSONObject()
    jsonObject.put("map", map.toJsonObject())
    jsonObject.put("centralTopic", centralTopic.toJsonObject())
    jsonObject.put("mainTopic", mainTopic.toJsonObject())
    jsonObject.put("subTopic", subTopic.toJsonObject())
    jsonObject.put("skeletonThemeId", skeletonThemeId)
    jsonObject.put("colorThemeId", colorThemeId)
    return jsonObject
}

val defaultThemeBean = XMindThemeBean(
    map = XMindThemeItem(
        id = UUID.randomUUID().toString(),
        properties = Properties(
            svgFill = "#ffffff",
            multiLineColors = "#FF8B2A #FFBD1B #25DF68 #1FB2F9 #4A86FF #B661FF",
            lineTapered = "none"
        )
    ),
    centralTopic = XMindThemeItem(
        id = UUID.randomUUID().toString(),
        properties = Properties().copy(
            fillPattern = "solid",
            shapeClass = "org.xmind.topicShape.roundedRect",
            lineClass = "org.xmind.branchConnection.elbow",
            borderLineWidth = "0pt",
            fontSize = "28pt",
            fontWeight = "600",
            svgFill = "#000229",
            lineWidth = "1pt",
        )
    ),
    mainTopic = XMindThemeItem(
        id = UUID.randomUUID().toString(),
        properties = Properties().copy(
            borderLineWidth = "5pt",
            fontSize = "18pt",
            fontWeight = "500",
            textAlign = "left",
            lineClass = "org.xmind.branchConnection.elbow",
            lineWidth = "1pt",
        )
    ),
    subTopic = XMindThemeItem(
        id = UUID.randomUUID().toString(),
        properties = Properties().copy(
            fontSize = "14pt",
            fontWeight = "400",
            textAlign = "left",
            lineClass = "org.xmind.branchConnection.elbow",
            lineWidth = "1pt",
        )
    ),
)

@Keep
data class XMindThemeItem(
    val id: String,
    val properties: Properties,
)

fun XMindThemeItem.toJsonObject(): JSONObject {
    val jsonObject = JSONObject()
    jsonObject.put("id", id)
    jsonObject.put("properties", properties.toJsonObject())
    return jsonObject
}

/**
 * @param shapeClass 节点边框形状  org.xmind.topicShape.roundedRect   org.xmind.topicShape.underline   org.xmind.topicShape.ellipserect
 * @param lineClass  分支线形状 org.xmind.branchConnection.roundedElbow  org.xmind.branchConnection.curve  org.xmind.branchConnection.straight
 * @param arrowBeginClass 连接线起点  org.xmind.arrowShape.triangle  org.xmind.arrowShape.none  org.xmind.arrowShape.dot
 * @param arrowEndClass 连接线终点  org.xmind.arrowShape.triangle  org.xmind.arrowShape.none  org.xmind.arrowShape.dot
 * @param svgFill 填充颜色
 * @param fillPattern 填充模式  solid none
 * @param backgroundColor 画布背景色
 * @param multiLineColors 分支颜色循环列表
 */
@Keep
data class Properties(
    //fo:font-family
    val fontFamily: String? = "NeverMind",
    //fo:font-size
    val fontSize: String? = "30pt",
    //fo:font-weight
    val fontWeight: String? = "400",
    //fo:font-style
    val fontStyle: String? = "normal",
    //fo:color
    val color: String? = "inherited",
    //fo:text-transform
    val textTransform: String? = "manual",
    //fo:text-decoration
    val textDecoration: String? = "none",
    //fo:text-align
    val textAlign: String? = "center",
    //svg:fill
    val svgFill: String? = "inherited",
    //fill-pattern
    val fillPattern: String? = "none",
    //line-width
    val lineWidth: String? = "2pt",
    //line-color
    val lineColor: String? = "inherited",
    //line-pattern
    val linePattern: String? = "solid",
    //border-line-color
    val borderLineColor: String? = "inherited",
    //border-line-width
    val borderLineWidth: String? = "inherited",
    //border-line-pattern
    val borderLinePattern: String? = "inherited",
    //shape-class
    val shapeClass: String? = "org.xmind.topicShape.underline",
    //line-class
    val lineClass: String? = "org.xmind.branchConnection.roundedElbow",
    //arrow-begin-class
    val arrowBeginClass: String? = "org.xmind.arrowShape.none",
    //arrow-end-class
    val arrowEndClass: String? = "org.xmind.arrowShape.none",
    //alignment-by-level
    val alignmentByLevel: String? = "inactived",
    //multi-line-colors
    val multiLineColors: String? = null,
    //color-list
    val colorList: String? = null,
    //line-tapered
    val lineTapered: String? = null,
)

fun Properties.toJsonObject(): JSONObject {
    val jsonObject = JSONObject()
    jsonObject.put("fo:font-family", fontFamily)
    jsonObject.put("fo:font-size", fontSize)
    jsonObject.put("fo:font-weight", fontWeight)
    jsonObject.put("fo:font-style", fontStyle)
    jsonObject.put("fo:color", color)
    jsonObject.put("fo:text-transform", textTransform)
    jsonObject.put("fo:text-decoration", textDecoration)
    jsonObject.put("fo:text-align", textAlign)
    jsonObject.put("svg:fill", svgFill)
    jsonObject.put("fill-pattern", fillPattern)
    jsonObject.put("line-width", lineWidth)
    jsonObject.put("line-color", lineColor)
    jsonObject.put("line-pattern", linePattern)
    jsonObject.put("border-line-color", borderLineColor)
    jsonObject.put("border-line-width", borderLineWidth)
    jsonObject.put("border-line-pattern", borderLinePattern)
    jsonObject.put("shape-class", shapeClass)
    jsonObject.put("line-class", lineClass)
    jsonObject.put("arrow-begin-class", arrowBeginClass)
    jsonObject.put("arrow-end-class", arrowEndClass)
    jsonObject.put("alignment-by-level", alignmentByLevel)
    jsonObject.put("multi-line-colors", multiLineColors)
    jsonObject.put("color-list", colorList)
    jsonObject.put("line-tapered", lineTapered)
    return jsonObject
}

const val THEME_TYPE_DEFAULT = 0