/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BrowseFileApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/8/9
 * * Author      : v-yanx<PERSON><PERSON><PERSON>@oppo.com
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.miniapp

import android.content.Context
import android.content.Intent
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object MiniRecorderAction {
    const val COMPONENT_NAME = "MiniRecorder"
    const val CREATE_MINI_APP_INTENT = "createMiniAppIntent"
    const val IS_MINI_RECORDER_ACTIVITY = "isMiniRecorderActivity"
    const val CHECK_MINI_APP_CONTINUE_ACTION = "checkContinueAction"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun createMiniAppIntent(context: Context): Intent? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                CREATE_MINI_APP_INTENT
            ).param(context).build()
            OStitch.execute<Intent>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun isMiniRecorderActivity(context: Context): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                IS_MINI_RECORDER_ACTIVITY
            ).param(context).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else false
    }

    @JvmStatic
    fun checkMiniAppContinueAction(context: AppCompatActivity, intent: Intent, disableDialogFun: ((dialog: AlertDialog) -> Unit)) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME, CHECK_MINI_APP_CONTINUE_ACTION)
                .param(context, intent, disableDialogFun).build()
            OStitch.execute<Boolean>(apiRequest).result
        }
    }
}