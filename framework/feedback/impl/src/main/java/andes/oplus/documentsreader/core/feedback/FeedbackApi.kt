/*********************************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FeedbackApi.kt
 ** Description: feedback interface impl
 ** Version: 1.0
 ** Date: 2024/12/30
 ** Author: zhangxiaojun
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.core.feedback

import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.PreferencesUtils
import andes.oplus.documentsreader.core.common.openid.OpenIdUtils
import andes.oplus.documentsreader.core.feedback.interfaze.IFeedback
import andes.oplus.documentsreader.core.feedback.interfaze.IFeedback.Companion.CONTACT_DEVICES_BRAND
import andes.oplus.documentsreader.core.feedback.interfaze.IFeedback.Companion.CONTACT_DEVICES_TYPE
import andes.oplus.documentsreader.core.feedback.interfaze.IFeedback.Companion.CONTACT_ERROR_REPORT
import andes.oplus.documentsreader.core.feedback.interfaze.IFeedback.Companion.CONTACT_FEEDBACK_PROFILE
import andes.oplus.documentsreader.core.feedback.interfaze.IFeedback.Companion.CONTACT_OS_VERSION
import andes.oplus.documentsreader.core.feedback.interfaze.IFeedback.Companion.CONTACT_STATISTICS_INFO
import andes.oplus.documentsreader.core.feedback.interfaze.IFeedback.Companion.CONTACT_STYLE
import andes.oplus.documentsreader.core.feedback.interfaze.IFeedback.Companion.CONTACT_DEVICE_GUID
import android.app.Activity
import android.content.pm.ActivityInfo
import android.text.SpannableString
import com.customer.feedback.sdk.FeedbackHelper
import com.customer.feedback.sdk.FeedbackHelper.RequestMadeCallback
import com.customer.feedback.sdk.model.RequestData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import andes.oplus.documentsreader.core.common.MainScope
import andes.oplus.documentsreader.core.common.uiconfig.UIConfigMonitor

object FeedbackApi : IFeedback {
    private const val TAG = "FeedBackApi"

    const val SHARED_PREFS_COLLECT_CONDITION_NAME = "collect_condition_preferences"

    const val SHARED_PREFS_INFO_CONTENT_NAME = "info_content_preferences"

   private val fbHelper: FeedbackHelper? by lazy {
       FeedbackHelper.getInstance(ContextGetter.application)
   }

    override fun launchFeedBack(activity: Activity) {
        runCatching {
            FeedbackHelper.setNetworkUserAgree(true)
            if (UIConfigMonitor.isSmallScreen(activity)) {
                fbHelper?.setCommonOrientationType(
                    ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                )
            } else {
                fbHelper?.setCommonOrientationType(
                    ActivityInfo.SCREEN_ORIENTATION_USER
                )
            }
            FeedbackHelper.setDataSavedCountry(FeedbackHelper.FbAreaCode.CN)
            MainScope.launch(Dispatchers.Default) {
                val dUid = OpenIdUtils.getDUID(activity.application)
                withContext(Dispatchers.Main) {
                    Log.d(TAG, "launchFeedBack setId: $dUid")
                    FeedbackHelper.setId(dUid)
                    setFeedbackRequestMadeListener()
                    fbHelper?.openFeedback(activity)
                }
            }
        }.onFailure {
            Log.e(TAG, "launchFeedBack: ${it.message}")
        }
    }

    private fun setFeedbackRequestMadeListener() {
        FeedbackHelper.setRequestMadeListener(object : RequestMadeCallback {
            override fun onRequestMade(list: List<RequestData>) {
                MainScope.launch(Dispatchers.IO) {
                    Log.d(TAG, "feedback collect start ============================")
                    for (item in list) {
                        when (item) {
                            is RequestData.Brand -> {
                                var brandNumber = PreferencesUtils.getInt(SHARED_PREFS_COLLECT_CONDITION_NAME, CONTACT_DEVICES_BRAND, 0)
                                PreferencesUtils.put(SHARED_PREFS_COLLECT_CONDITION_NAME, CONTACT_DEVICES_BRAND, ++brandNumber)
                                PreferencesUtils.put(SHARED_PREFS_INFO_CONTENT_NAME, CONTACT_DEVICES_BRAND, item.content)
                                Log.d(TAG, "launchFeedBack BRAND content: $item.content brandNumber: $brandNumber")
                            }

                            is RequestData.Model -> {
                                var devicesTypeNumber = PreferencesUtils.getInt(SHARED_PREFS_COLLECT_CONDITION_NAME, CONTACT_DEVICES_TYPE, 0)
                                PreferencesUtils.put(SHARED_PREFS_COLLECT_CONDITION_NAME, CONTACT_DEVICES_TYPE, ++devicesTypeNumber)
                                PreferencesUtils.put(SHARED_PREFS_INFO_CONTENT_NAME, CONTACT_DEVICES_TYPE, item.content)
                                Log.d(TAG, "launchFeedBack Model content: $item.content devicesTypeNumber: $devicesTypeNumber")
                            }

                            is RequestData.Os -> {
                                var osVersionNumber = PreferencesUtils.getInt(SHARED_PREFS_COLLECT_CONDITION_NAME, CONTACT_OS_VERSION, 0)
                                PreferencesUtils.put(SHARED_PREFS_COLLECT_CONDITION_NAME, CONTACT_OS_VERSION, ++osVersionNumber)
                                PreferencesUtils.put(SHARED_PREFS_INFO_CONTENT_NAME, CONTACT_OS_VERSION, item.content)
                                Log.d(TAG, "launchFeedBack Os content: $item.content osVersionNumber: $osVersionNumber")
                            }

                            is RequestData.Contact -> {
                                var contactNumber = PreferencesUtils.getInt(SHARED_PREFS_COLLECT_CONDITION_NAME, CONTACT_STYLE, 0)
                                PreferencesUtils.put(SHARED_PREFS_COLLECT_CONDITION_NAME, CONTACT_STYLE, ++contactNumber)
                                PreferencesUtils.put(SHARED_PREFS_INFO_CONTENT_NAME, CONTACT_STYLE, item.content)
                                Log.d(TAG, "launchFeedBack Contact content: $item.content contactNumber: $contactNumber")
                            }

                            is RequestData.Log -> {
                                var errorReportNumber = PreferencesUtils.getInt(SHARED_PREFS_COLLECT_CONDITION_NAME, CONTACT_ERROR_REPORT, 0)
                                PreferencesUtils.put(SHARED_PREFS_COLLECT_CONDITION_NAME, CONTACT_ERROR_REPORT, ++errorReportNumber)
                                PreferencesUtils.put(SHARED_PREFS_INFO_CONTENT_NAME, CONTACT_ERROR_REPORT, item.content)
                                Log.d(TAG, "launchFeedBack Log content: $item.content errorReportNumber: $errorReportNumber")
                            }

                            is RequestData.Statistics -> {
                                var statisticsInfoNumber = PreferencesUtils.getInt(
                                    SHARED_PREFS_COLLECT_CONDITION_NAME, CONTACT_STATISTICS_INFO, 0
                                )
                                PreferencesUtils.put(SHARED_PREFS_COLLECT_CONDITION_NAME, CONTACT_STATISTICS_INFO, ++statisticsInfoNumber)
                                PreferencesUtils.put(SHARED_PREFS_INFO_CONTENT_NAME, CONTACT_STATISTICS_INFO, item.content)
                                Log.d(TAG, "launchFeedBack Statistics content: $item.content statisticsInfoNumber: $statisticsInfoNumber")
                            }

                            is RequestData.Feedback -> {
                                var feedbackNumber = PreferencesUtils.getInt(SHARED_PREFS_COLLECT_CONDITION_NAME, CONTACT_FEEDBACK_PROFILE, 0)
                                PreferencesUtils.put(SHARED_PREFS_COLLECT_CONDITION_NAME, CONTACT_FEEDBACK_PROFILE, ++feedbackNumber)
                                PreferencesUtils.put(SHARED_PREFS_INFO_CONTENT_NAME, CONTACT_FEEDBACK_PROFILE, item.content)
                                Log.d(TAG, "launchFeedBack Feedback content: $item.content feedbackNumber: $feedbackNumber")
                            }

                            is RequestData.OpenId -> Log.d(TAG, "launchFeedBack OpenId content: $item.content")
                        }
                    }
                    Log.d(TAG, "feedback collect end ============================")
                }
            }
        })
    }

    override fun getFeedBackCollectCondition(userType: String, defaultValue: Int): Int {
        Log.d(TAG, "collectCondition userType: $userType defaultValue: $defaultValue")
        val isFeedback = (CONTACT_STYLE == userType
                || CONTACT_FEEDBACK_PROFILE == userType
                || CONTACT_ERROR_REPORT == userType
                || CONTACT_STATISTICS_INFO == userType
                || CONTACT_DEVICE_GUID == userType
                || CONTACT_DEVICES_BRAND == userType
                || CONTACT_OS_VERSION == userType
                || CONTACT_DEVICES_TYPE == userType)
        return if (isFeedback) {
            PreferencesUtils.getInt(SHARED_PREFS_COLLECT_CONDITION_NAME, userType, 0)
        } else {
            defaultValue
        }
    }

    override fun getFeedBackInfoContent(userType: String, defaultValue: SpannableString): SpannableString {
        Log.d(TAG, "infoContent userType: $userType defaultValue: $defaultValue")
        val isFeedback = (CONTACT_STYLE == userType
                || CONTACT_FEEDBACK_PROFILE == userType
                || CONTACT_ERROR_REPORT == userType
                || CONTACT_STATISTICS_INFO == userType
                || CONTACT_DEVICE_GUID == userType
                || CONTACT_DEVICES_BRAND == userType
                || CONTACT_OS_VERSION == userType
                || CONTACT_DEVICES_TYPE == userType)
        return if (isFeedback) {
            SpannableString(PreferencesUtils.getString(SHARED_PREFS_INFO_CONTENT_NAME, userType, ""))
        } else {
            defaultValue
        }
    }
}