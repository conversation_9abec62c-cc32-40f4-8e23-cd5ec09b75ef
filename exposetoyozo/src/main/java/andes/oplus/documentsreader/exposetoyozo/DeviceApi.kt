/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/07/13, 80226902
 ** Author: keweiwei
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package andes.oplus.documentsreader.exposetoyozo

import andes.oplus.documentsreader.core.common.compat.FeatureCompat
import andes.oplus.documentsreader.core.common.utils.BrandUtil
import andes.oplus.documentsreader.core.common.utils.ModelUtils
import androidx.annotation.Keep

@Keep
object DeviceApi {

    /**
     * 当前设备是否是pad
     */
    @JvmStatic
    fun isTablet(): Boolean {
        return ModelUtils.isTablet()
    }

    /**
     * 当前是否是realme的设备
     */
    @JvmStatic
    fun isRealMeDevice(): Boolean {
        return BrandUtil.isRealMe()
    }

    /**
     * 当前是否是一加的设备
     */
    @JvmStatic
    fun isOnePlusDevice(): Boolean {
        return BrandUtil.isOnePlus()
    }

    /**
     * 当前是否是oppo设备
     */
    @JvmStatic
    fun isOppoDevice(): Boolean {
        return BrandUtil.isOPPO()
    }

    /**
     * 当前是否是外销版本
     */
    @JvmStatic
    fun isExpDevice(): Boolean {
        return FeatureCompat.sIsExpRom
    }

    /**
     * 是否是轻量版本
     */
    @JvmStatic
    fun isLightDevice(): Boolean {
        return FeatureCompat.sIsLightVersion
    }

    /**
     * 是否是普通轻量或者轻量低版本版本，非轻量高版本
     */
    @JvmStatic
    fun isNormalLightDevice(): Boolean {
        return FeatureCompat.sIsNormalLightOS
    }

    /**
     * 当前是否是欧洲版本
     */
    @JvmStatic
    fun isEuDevice(): Boolean {
        return ModelUtils.isEURegion()
    }

    /**
     * 当前设备是否支持安全芯片
     */
    @JvmStatic
    fun supportSecurityChip(): Boolean {
        return FeatureCompat.isSupportSecurityChip || (BrandUtil.isRealMe() && FeatureCompat.isSupportNFCSecurityChip)
    }
}