plugins {
    id 'com.android.library'
    id 'kotlin-kapt'
    id 'com.oplus.assistantscreen.plugin.mic'
}
apply from: rootProject.file("script/compile.gradle")
apply from: rootProject.file("script/unitTest.gradle")

android {
    namespace 'andes.oplus.documentsreader.core.cloudconfig'
}

dependencies {
    implementation project(":foundation:common")
    implementation project(":framework:cloudconfig:interface")

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)

    // Cloud Config
    implementation(libs.heytap.nearx.cloudconfig)
    implementation(libs.heytap.nearx.cloudconfigEnvOverSea)
    implementation(libs.heytap.nearx.cloudconfigArea)

    debugImplementation libs.heytap.env  //测试环境的域名包，切换正式环境时需要去掉
    debugImplementation libs.heytap.test.env  //测试环境的域名包，切换正式环境时需要去掉

    implementation(libs.heytap.nearx.utils)

    implementation(libs.gson)

    // Koin
    implementation(libs.koin.android)

    implementation(libs.oplus.assistantscreen.plugin.mic.api)
    implementation(libs.oplus.assistantscreen.plugin.mic.interfaces)
    kapt(libs.oplus.assistantscreen.plugin.mic.processor)
}