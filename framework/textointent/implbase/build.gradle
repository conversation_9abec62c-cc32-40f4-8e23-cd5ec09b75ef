plugins {
    id 'com.android.library'
    id 'kotlin-kapt'
}
apply from: rootProject.file("script/compile.gradle")
apply from: rootProject.file("script/unitTest.gradle")

android {
    namespace 'andes.oplus.documentsreader.textointent.implbase'
}

dependencies {
    implementation project(":foundation:common")
    implementation project(":framework:textointent:interface")
    implementation project(":framework:privacy:interface")

    // 文字意图识别sdk
    // 必须，基础接口库
    implementation(libs.oplus.aiunit.ointent.api)
    // regex库，包含汇率、单位换算、电话、邮箱、网址和书名号
    implementation(libs.oplus.aiunit.ointent.regex)
    // 算法库，包含日程、地址、语种和ner
    implementation(libs.oplus.aiunit.ointent.aiunit)
    // 口令检测，注意如果需要支持口令在线更新则还需要接入online
    implementation(libs.oplus.aiunit.ointent.token)
    // 意图检测
    implementation(libs.oplus.aiunit.ointent.detect)
    //ai sdk
    implementation(libs.oplus.aiunit.open.core)
    implementation(libs.oplus.aiunit.open.nlp)

    // Koin
    implementation(libs.koin.android)
    implementation(libs.oplus.assistantscreen.plugin.mic.api)
    implementation(libs.oplus.assistantscreen.plugin.mic.interfaces)
    kapt(libs.oplus.assistantscreen.plugin.mic.processor)
}