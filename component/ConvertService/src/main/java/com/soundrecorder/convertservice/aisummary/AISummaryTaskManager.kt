/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryTaskManager
 * Description:录音摘要任务队列管理器，多个录音摘要同时进行，建立Task队列进行管理
 * Version: 1.0
 * Date: 2025/5/13
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/13      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.convertservice.aisummary

import android.annotation.SuppressLint
import com.oplus.unified.summary.sdk.UnifiedSummaryKit
import com.oplus.unified.summary.sdk.callback.ISummaryInitCallback
import com.oplus.unified.summary.sdk.common.SummaryInitParam
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.AISummaryBaseParam
import com.soundrecorder.convertservice.convert.IJobManagerLifeCycleCallback
import com.soundrecorder.modulerouter.aisummary.AISummaryAction
import com.soundrecorder.modulerouter.aisummary.IAISummaryCallback
import java.util.LinkedList
import java.util.Queue
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

object AISummaryTaskManager : IAISummaryProcess {

    const val TAG = "AISummaryTaskManager"
    /* 同时转换数量 */
    const val LIMIT_SIZE = 3
    /* 默认为空 */
    const val DEFAULT_NULL = 0
    /* 已在运行 */
    const val ALREADY_RUNNING = 1
    /* 超过限制 */
    const val OVER_LIMIT = 2
    /* 添加新任务 */
    const val CAN_ADD_NEW = 3
    /* 转换进行 */
    const val CONVERT_RUNNING = 4

    /* 任务map */
    var taskMaps: ConcurrentHashMap<Long, IAISummaryRunnable> = ConcurrentHashMap(LIMIT_SIZE)

    /* Ui回调函数 map */
    var mUICallback: ConcurrentHashMap<Long, CopyOnWriteArrayList<IAISummaryCallback>> = ConcurrentHashMap(
        LIMIT_SIZE
    )

    /* 执行多个摘要的sessionId 的list */
    var summarySessionIdList: ConcurrentHashMap<Long, String?> = ConcurrentHashMap()

    /* 任务管理器 */
    var jobManagerLifeCycleCallback: IJobManagerLifeCycleCallback? = null

    /* 等待队列 */
    private var mWaitingTasks: Queue<Long> = LinkedList()

    /* 实例化 摘要sdk */
    @SuppressLint("StaticFieldLeak")
    private var mSummaryKit: UnifiedSummaryKit? = null

    /**
     * 初始化 摘要sdk 接口
     */
    override fun initUnifiedSummary(callback: InitSummarySDKCallback?) {
         if (mSummaryKit == null) {
             DebugUtil.d(TAG, "initUnifiedSummary, start")
             mSummaryKit = UnifiedSummaryKit(BaseApplication.getAppContext())
             mSummaryKit?.initKit(
                 SummaryInitParam(
                     BaseApplication.getAppContext().packageName,
                     true,
                     null
                 ), object : ISummaryInitCallback {
                     override fun onError(sessionId: String, errorCode: Int, errorMsg: String?) {
                         DebugUtil.d(TAG, "initUnifiedSummary, onError:$sessionId, errorCode:$errorCode, errorMsg:$errorMsg")
                         callback?.initSummaryKitError(sessionId, errorCode, errorMsg)
                     }

                     override fun onSuccess(bizTag: String?, extras: Map<String, Any>?) {
                         DebugUtil.d(TAG, "initUnifiedSummary, onSuccess:$bizTag, extras:$extras")
                         if (mSummaryKit != null) {
                             callback?.initSummaryKitSuccess(mSummaryKit, true)
                         } else {
                             callback?.initSummaryKitError("", AISummaryAction.PLUGIN_INIT_ERROR, null)
                         }
                     }
                 }
             )
         } else {
             callback?.initSummaryKitSuccess(mSummaryKit)
         }
    }

    /**
     * 释放摘要sdk实例
     */
    private fun releaseSummaryKit() {
        DebugUtil.d(TAG, "releaseSummaryKit")
        mSummaryKit?.release(BaseApplication.getAppContext().packageName)
        mSummaryKit = null
    }

    fun addSessionId(mediaId: Long, sessionId: String) {
        if (!summarySessionIdList.containsKey(mediaId)) {
            summarySessionIdList[mediaId] = sessionId
        }
    }

    fun removeSessionId(mediaId: Long, sessionId: String) {
        if (summarySessionIdList.containsKey(mediaId)) {
            summarySessionIdList.remove(mediaId)
        }
    }

    fun stopAISummaryTask(mediaId: Long) {
        if (summarySessionIdList.isNullOrEmpty()) {
            return
        }
        if (summarySessionIdList.containsKey(mediaId)) {
            val sessionId = summarySessionIdList[mediaId]
            if (!sessionId.isNullOrEmpty() && sessionId != "") {
                mSummaryKit?.stopTask(sessionId)
            }
        }
    }

    object RunnableProgress : IAISummaryProgressCallback {
        override fun preStartAISummary(mediaId: Long) {
        }

        /**
         * 延迟 生成摘要
         */
        override fun postAISummary(mediaId: Long) {
            DebugUtil.d(TAG, "postAISummary, mediaId:$mediaId")
            removeTask(mediaId)
            waitTaskQueuePoll()
            checkFinalTaskEnd(mediaId)
        }

        override fun postCancelAISummary(mediaId: Long) {
            removeTask(mediaId)
        }
    }

    private fun removeTask(mediaId: Long) {
        if (taskMaps.containsKey(mediaId)) {
            DebugUtil.d(TAG, "removeTask, mediaId:$mediaId")
            taskMaps.remove(mediaId)
        }
    }

    private fun waitTaskQueuePoll() {
        synchronized(AISummaryTaskManager::class) {
            if (!mWaitingTasks.isNullOrEmpty()) {
                val mediaId = mWaitingTasks.poll()
                DebugUtil.d(TAG, "waitTaskQueuePoll, waitRecord:$mediaId")
                if (mediaId != null) {
                    addOrResumeAISummaryTask(mediaId, null)
                }
            }
        }
    }

    /**
     * 检查最终Task是否结束
     */
    fun checkFinalTaskEnd(mediaId: Long) {
        if (taskMaps.isEmpty()) {
            DebugUtil.i(TAG, "checkFinalTaskEnd: onFinalJobEnd $mediaId")
            releaseSummaryKit()
            jobManagerLifeCycleCallback?.onFinalJobEnd(mediaId)
        }
    }

     private fun getConvertUiCallback(): IAISummaryCallback {
         return object : IAISummaryCallback {

             override fun onAISummaryStart(mediaId: Long, extras: Map<String, Any>?) {
                  mUICallback[mediaId]?.forEach {
                      it.onAISummaryStart(mediaId, extras)
                  }
             }

             override fun onAISummaryStop(mediaId: Long, extras: Map<String, Any>?) {
                 mUICallback[mediaId]?.forEach {
                     it.onAISummaryStop(mediaId, extras)
                 }
             }

             override fun onAISummaryFinished(
                 mediaId: Long,
                 jsonResult: String,
                 extras: Map<String, Any>?
             ) {
                 mUICallback[mediaId]?.forEach {
                     it.onAISummaryFinished(mediaId, jsonResult, extras)
                 }
             }

             override fun onAISummaryError(mediaId: Long, errorCode: Int, errorMsg: String?) {
                 mUICallback[mediaId]?.forEach {
                     it.onAISummaryError(mediaId, errorCode, errorMsg)
                 }
             }

             override fun onAISummaryEnd(mediaId: Long) {
                  mUICallback[mediaId]?.forEach {
                      it.onAISummaryEnd(mediaId)
                  }
             }
         }
     }

    /**
     * 取消任务
     */
    private fun cancelTask(mediaId: Long): Boolean {
        val result = if (!taskMaps.containsKey(mediaId)) {
            DebugUtil.i(TAG, "maps not contains mediaId: $mediaId, no need to add task again")
            false
        } else {
            taskMaps[mediaId]?.cancelAISummary()
            true
        }
        return result
    }

    /**
     * 取消所有任务
     */
    fun cancelAllTask() {
        taskMaps.forEach { (_, value) ->
            value.cancelAISummary()
        }
    }

    /**
     * 当前mediaId是否在进行
     */
    fun checkIsTaskRunning(mediaId: Long): Boolean {
        return taskMaps.containsKey(mediaId)
    }

    /**
     * 当前没有Task在进行
     */
    fun checkNoTaskRunning(): Boolean {
        DebugUtil.i(TAG, "checkNoTaskRunning = ${taskMaps.isEmpty()}")
        return taskMaps.isEmpty()
    }

    /**
     * 当前有Task在进行
     */
    fun checkHasTaskRunning(): Boolean {
        DebugUtil.i(TAG, "checkHasTaskRunning = ${taskMaps.isNotEmpty()}")
        return taskMaps.isNotEmpty()
    }

    /**
     * 释t放当前mediaId的ask
     */
    fun release(mediaId: Long) {
        DebugUtil.i(TAG, "release: $mediaId")
        taskMaps[mediaId]?.release()
    }

    fun checkCanAddNewTask(mediaId: Long): Int {
        /* 添加转文本状态 判断？ */

        if (taskMaps.containsKey(mediaId)) {
            DebugUtil.i(TAG, "checkCanAddNewTask: $mediaId already running, no need to add")
            return ALREADY_RUNNING
        } else if (taskMaps.size >= LIMIT_SIZE) {
            DebugUtil.i(TAG, "maps limit reached : runnable $mediaId, add waiting")
            return OVER_LIMIT
        } else {
            return CAN_ADD_NEW
        }
    }

    fun releaseAll() {
        DebugUtil.i(TAG, "release all")
        /* 释放 ui 回调函数 list */
        mUICallback.clear()
        /* 释放 taskMap */
        for (mediaId in taskMaps.keys) {
            release(mediaId)
        }
        /* 释放sessionId List */
        summarySessionIdList.clear()
        /* 释放 等待队列 */
        mWaitingTasks.clear()
        /* 释放 摘要sdk实例 */
        releaseSummaryKit()
    }

    override fun startAISummary(mediaId: Long, params: AISummaryBaseParam?): Boolean {
        return addOrResumeAISummaryTask(mediaId, params)
    }

    /**
     * 添加录音摘要任务
     */
    fun addOrResumeAISummaryTask(mediaId: Long, params: AISummaryBaseParam?): Boolean {
        val result = when (checkCanAddNewTask(mediaId)) {
            ALREADY_RUNNING -> {
                DebugUtil.i(TAG, "maps already contains mediaId : $mediaId, no need to add Task again")
                true
            }
            OVER_LIMIT,
            CONVERT_RUNNING -> {
                DebugUtil.i(TAG, "maps limit reached : runnable $mediaId, add to waiting list")
                addWaitingTasks(mediaId)
                false
            }
            else -> {
                val aiSummaryRunnable = AISummaryRunnable(mediaId, params)
                /* 注册Ui接口回调函数 */
                aiSummaryRunnable.registerAISummaryUiCallback(getConvertUiCallback())

                aiSummaryRunnable.registerProgressCallback(RunnableProgress)
                taskMaps[mediaId] = aiSummaryRunnable
                aiSummaryRunnable.startAISummary()
                DebugUtil.i(TAG, "aiSummaryRunnable $mediaId start run")
                true
            }
        }
        return result
    }

    private fun addWaitingTasks(mediaId: Long) {
        if (!checkIsTaskRunning(mediaId) && !mWaitingTasks.contains(mediaId)) {
            DebugUtil.i(TAG, "addWaitingTasks, mediaId: $mediaId")
            mWaitingTasks.add(mediaId)
        }
    }

    override fun cancelAISummary(mediaId: Long): Boolean {
        return cancelTask(mediaId)
    }

    override fun releaseAISummary(mediaId: Long) {
         release(mediaId)
    }

    override fun registerAISummaryCallback(mediaId: Long, callback: IAISummaryCallback) {
        if (mUICallback[mediaId] == null) {
            mUICallback[mediaId] = CopyOnWriteArrayList()
        }
        mUICallback[mediaId]?.run {
            if (!contains(callback)) {
                add(callback)
            }
        }
        DebugUtil.i(TAG, "register ui callback $callback")
    }

    override fun unregisterAISummaryCallback(mediaId: Long, callback: IAISummaryCallback) {
         mUICallback[mediaId]?.run {
             remove(callback)
             if (isEmpty()) {
                 mUICallback.remove(mediaId)
             }
         }
        DebugUtil.i(TAG, "unregister ui callback $callback, size = ${mUICallback[mediaId]?.size}")
    }
}