/*********************************************************************************
 ** Copyright (C) 2020 Oplus. All rights reserved.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - NavigationType.kt
 ** Description: Navigation Menu Type Define
 **
 ** Version: 1.0
 ** Date: 2021-04-07
 ** Author: W9001165
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ********************************************************************************/
package com.documentsreader.navigation

import android.view.MenuItem
import com.coui.appcompat.bottomnavigation.COUINavigationView

abstract class NavigationType {
    /**
     * Build menu by navigationView
     */
    abstract fun buildMenu(naviTool: COUINavigationView?): HashMap<Int, MenuItem>?

    /**
     * set navigate item able
     */
    abstract fun setNavigateItemAble(
        menuItems: HashMap<Int, MenuItem>,
        isEnable: Boolean,
        mHasDrm: Boolean,
        mHasSelectedMultiLabels: Boolean = false,
        mHasSelectedLabelsAllPin: Boolean = false,
        mHasSelectedFileEmpty: Boolean = false
    )
}