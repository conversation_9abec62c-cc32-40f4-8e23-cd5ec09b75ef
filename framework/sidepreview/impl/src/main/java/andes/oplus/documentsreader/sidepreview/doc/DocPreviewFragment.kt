/*********************************************************************
 * * Copyright (C), 2024, OPlus. All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/10/4
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.sidepreview.doc

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.sidepreview.core.AbsPreviewFragment
import andes.oplus.documentsreader.sidepreview.core.FilePreviewViewModel
import andes.oplus.documentsreader.sidepreview.impl.R
import andes.oplus.documentsreader.sidepreview.utils.FilePreviewTypeCheckHelper
import andes.oplus.documentsreader.sidepreview.widget.PreviewFileInfoSuite
import andes.oplus.documentsreader.sidepreview.widget.PreviewFilePathItem
import android.content.Context
import android.view.View
import androidx.fragment.app.Fragment

internal class DocPreviewFragment : AbsPreviewFragment<FilePreviewViewModel>(R.layout.oplusdoc_fragment_preview_doc) {

    private companion object {
        private const val TAG = "DocPreviewFragment"
    }

    override val fragmentInstance: Fragment = this
    override val logTag: String = TAG
    override val viewModelClass: Class<FilePreviewViewModel> = FilePreviewViewModel::class.java
    override val operationsBarId: Int = R.id.preview_operations_bar
    private var filePreview: IDocFilePreview? = null
    private var containerManager: DocPreviewContainerManager? = null

    override fun isPreviewFileApproved(context: Context, fileBean: BaseFileBean): Boolean =
    FilePreviewTypeCheckHelper(context).isDocType(fileBean, TAG)

    override fun onCreateFilePathItem(view: View): PreviewFilePathItem =
        view.findViewById<PreviewFileInfoSuite>(R.id.preview_container_default).filePathView

    override fun onViewModelCreated(view: View, viewModel: FilePreviewViewModel) {
        operationsBar?.setOpenButtonText(R.string.open)
        filePreview = prepareFilePreview(view, viewModel)
    }

    private fun prepareFilePreview(view: View, viewModel: FilePreviewViewModel): IDocFilePreview {
        val previewImpl = IDocFilePreview.obtain(this, viewModel)
        val containerManager = DocPreviewContainerManager(view.findViewById(R.id.root_view))
        previewImpl.attachToContainer(containerManager)
        this.containerManager = containerManager
        return previewImpl
    }

    override fun refreshPreviewFile(context: Context, fileBean: BaseFileBean) {
        Log.d(TAG, "refreshPreviewFile")
        super.refreshPreviewFile(context, fileBean)
        filePreview?.refresh(fileBean)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        filePreview?.release()
        filePreview = null
        containerManager?.release()
    }
}