/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - GlobalSearchRecycleApiHelper.kt
 * Description:
 *     The helper to obtain recycle bin api implement
 *
 * Version: 1.0
 * Date: 2024-07-12
 * Author: 80241271
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * 80241271    2024-07-12   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.search.utils

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.selection.BaseUiModel
import andes.oplus.documentsreader.core.common.selection.SelectionViewModel
import andes.oplus.documentsreader.core.fileoperate.interfaces.INormalFileOperateController
import androidx.lifecycle.Lifecycle

internal object GlobalSearchRecycleApiHelper {

    @JvmStatic
    fun getNormalFileOperate(
        lifecycle: Lifecycle,
        viewModel: SelectionViewModel<out BaseFileBean, out BaseUiModel<out BaseFileBean>>
    ): INormalFileOperateController? =
        Injector.injectFactory(lifecycle, viewModel)
}