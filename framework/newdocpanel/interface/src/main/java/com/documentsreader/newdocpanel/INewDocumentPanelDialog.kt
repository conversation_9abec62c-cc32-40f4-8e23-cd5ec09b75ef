/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: IDocumentExtensionType.kt
 ** Description: ad tools
 ** Version: 1.0
 ** Date: 2024/06/05
 ** Author: keweiwei
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.documentsreader.newdocpanel

import android.content.Context
import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import com.documentsreader.newdocpanel.IOfficeItemClick

interface INewDocumentPanelDialog {
    fun createAndShow(context: Context, resultCodeLauncher: ActivityResultLauncher<Intent>)
    fun setOfficeItemClick(officeItemClick: IOfficeItemClick)
}