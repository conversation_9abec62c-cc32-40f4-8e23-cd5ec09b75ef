/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: IHeytapAccount.kt
 ** Description: ad tools
 ** Version: 1.0
 ** Date: 2024/06/05
 ** Author: keweiwei
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package andes.oplus.documentsreader.account.interfaces

import android.content.Context

interface IHeytapAccount {
    fun initAccountClient(context: Context)

    fun isLogin(context: Context): Boolean

    fun login(context: Context, isShowAcPage: Boolean, callback: (<PERSON><PERSON><PERSON>) -> Unit)

    fun getUserToken(context: Context): String
}