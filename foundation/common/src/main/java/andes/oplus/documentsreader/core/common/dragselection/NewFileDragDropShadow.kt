/***********************************************************
 ** Copyright (C), 2010-2023 Oplus. All rights reserved.
 ** File:  - NewFileDragDropShadow.kt
 ** Description: Drag Shadow Builder
 ** Version: 1.0
 ** Date : 2023/06/21
 ** Author: ********
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  ********     2023/06/21    1.0        create
 ****************************************************************/

package andes.oplus.documentsreader.core.common.dragselection

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.FileMediaHelper
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.MiddleMultilineTextView
import andes.oplus.documentsreader.core.common.R
import andes.oplus.documentsreader.core.common.TextViewSnippet
import andes.oplus.documentsreader.core.common.dragselection.FileDragDropScanner.Companion.STATUS_GRID
import andes.oplus.documentsreader.core.common.dragselection.FileDragDropScanner.Companion.STATUS_GRID_IMG
import andes.oplus.documentsreader.core.common.dragselection.FileDragDropScanner.Companion.STATUS_LIST
import andes.oplus.documentsreader.core.common.dragselection.FileDragDropScanner.Companion.STATUS_RECENT_IMG
import andes.oplus.documentsreader.core.common.dragselection.FileDragDropScanner.Companion.STATUS_RECENT_SINGLE_IMG
import android.content.ClipData
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Point
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.View.*
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import com.coui.appcompat.reddot.COUIHintRedDot
import com.oplus.dropdrag.SelectionTracker.Companion.DEBUG
import java.lang.ref.WeakReference
import java.util.Locale

class NewFileDragDropShadow(context: Context, code: Int) : DragShadowBuilder() {
    companion object {
        private const val TAG = "NewFileDragDropShadow"

        private const val SHADOW_COUNT_ONE = 1
        private const val SHADOW_COUNT_TWO = 2
        private const val SHADOW_DX = 0f
        private const val SHADOW_DY = 10f
        private const val SHADOW_MOST_LAYER_COUNT = 3
    }

    private var shadowViewWR: WeakReference<View>
    private var width: Int = 0
    private var height: Int = 0
    private var shadowBlur: Float = 0.0f
    private var layoutPadding: Float = 0.0f
    private var layoutMarginTop: Float = 0.0f
    private var layoutMarginEnd: Float = 0.0f
    private var shadowRadius: Float = 0.0f
    private var shadowPaint: Paint
    private val shadowColor: Int
    private val middleMaskColor: Int
    private val bottomMaskColor: Int
    private val roundRectColor: Int
    private val rectStrokeColor: Int
    private val rectStrokeWidth: Float
    private var shadowMarginLeftAndRight: Float = 0.0f
    private var shadowMarginTop: Float = 0.0f
    private var shadowMarginBottom: Float = 0.0f
    private var shadowImgThreeLayerMarginBottom: Float = 0.0f
    private var rootView: View? = null
    private var dropCount = 0
    private val statusCode = code
    private var mIsRtl: Boolean = false

    init {
        layoutMarginEnd = context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_margin_end).toFloat()
        layoutMarginTop = context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_margin_top).toFloat()
        shadowBlur = context.resources.getDimensionPixelOffset(R.dimen.list_drag_shadow_blur_radius).toFloat()
        layoutPadding = context.resources.getDimensionPixelOffset(R.dimen.list_drag_shadow_item_layout_padding).toFloat()
        shadowImgThreeLayerMarginBottom =
            context.resources.getDimensionPixelOffset(R.dimen.img_drag_shadow_three_margin_bottom).toFloat()
        rectStrokeWidth = context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_layout_background_stride_width).toFloat()
        when (statusCode) {
            STATUS_GRID ->
                initGrid(context)
            STATUS_LIST ->
                initList(context)
            STATUS_GRID_IMG, STATUS_RECENT_IMG ->
                initImg(context)
            STATUS_RECENT_SINGLE_IMG ->
                initSingleImg(context)
        }
        shadowColor = context.getColor(R.color.drag_shadow_item_shadow_color)
        middleMaskColor = context.getColor(R.color.drag_shadow_item_middle_color)
        bottomMaskColor = context.getColor(R.color.drag_shadow_item_bottom_color)
        roundRectColor = context.getColor(R.color.drag_shadow_layout_background_color)
        rectStrokeColor = context.getColor(R.color.drag_shadow_item_stride_color)

        mIsRtl = (TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == LAYOUT_DIRECTION_RTL)
        if (DEBUG) {
            Log.d(TAG, "mIsRtl : $mIsRtl")
        }
        shadowViewWR = WeakReference(rootView)
        shadowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        rootView?.setLayerType(LAYER_TYPE_HARDWARE, shadowPaint)
    }

    private fun initList(context: Context) {
        val itemWidth = context.resources.getDimensionPixelOffset(R.dimen.list_drag_shadow_item_layout_width)
        val itemHeight = context.resources.getDimensionPixelOffset(R.dimen.list_drag_shadow_item_layout_height)
        shadowMarginLeftAndRight = context.resources.getDimensionPixelOffset(R.dimen.list_drag_shadow_margin_left_and_right).toFloat()
        shadowMarginTop = context.resources.getDimensionPixelOffset(R.dimen.list_drag_shadow_margin_top).toFloat()
        shadowMarginBottom = context.resources.getDimensionPixelOffset(R.dimen.list_drag_shadow_margin_bottom).toFloat()
        shadowRadius = context.resources.getDimensionPixelOffset(R.dimen.list_drag_shadow_layout_background_radius).toFloat()
        width = (itemWidth + 2 * layoutPadding + layoutMarginEnd).toInt()
        height = (itemHeight + 2 * layoutPadding + layoutMarginTop).toInt()
        rootView = LayoutInflater.from(context).inflate(R.layout.oplus_doc_list_dragshadow_item_layout, null)
    }

    private fun initGrid(context: Context) {
        val itemWidth = context.resources.getDimensionPixelOffset(R.dimen.grid_drag_shadow_item_layout_width)
        val itemHeight = context.resources.getDimensionPixelOffset(R.dimen.grid_drag_shadow_item_layout_height)
        shadowMarginLeftAndRight = context.resources.getDimensionPixelOffset(R.dimen.grid_drag_shadow_margin_left_and_right).toFloat()
        shadowMarginTop = context.resources.getDimensionPixelOffset(R.dimen.grid_drag_shadow_margin_top).toFloat()
        shadowMarginBottom = context.resources.getDimensionPixelOffset(R.dimen.grid_drag_shadow_margin_bottom).toFloat()
        shadowRadius = context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_layout_background_radius).toFloat()
        width = (itemWidth + 2 * layoutPadding + layoutMarginEnd).toInt()
        height = (itemHeight + 2 * layoutPadding + layoutMarginTop).toInt()
        rootView = LayoutInflater.from(context).inflate(R.layout.oplus_doc_grid_dragshadow_item_layout, null)
    }

    private fun initImg(context: Context) {
        val itemWidth = context.resources.getDimensionPixelOffset(R.dimen.img_drag_shadow_one_height)
        val itemHeight = context.resources.getDimensionPixelOffset(R.dimen.img_drag_shadow_one_height)
        shadowRadius = context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_layout_background_radius).toFloat()
        width = (itemWidth + 2 * layoutPadding + layoutMarginEnd).toInt()
        height = (itemHeight + 2 * layoutPadding + shadowImgThreeLayerMarginBottom + layoutMarginTop).toInt()
        rootView = LayoutInflater.from(context).inflate(R.layout.oplus_doc_image_dragshadow_item_layout, null)
    }

    private fun initSingleImg(context: Context) {
        val itemWidth = context.resources.getDimensionPixelOffset(R.dimen.single_img_drag_shadow_one_width)
        val itemHeight = context.resources.getDimensionPixelOffset(R.dimen.single_img_drag_shadow_one_height)
        shadowRadius = context.resources.getDimensionPixelOffset(R.dimen.single_img_drag_shadow_layout_background_radius).toFloat()
        width = (itemWidth + 2 * layoutPadding + layoutMarginEnd).toInt()
        height = (itemHeight + 2 * layoutPadding + shadowImgThreeLayerMarginBottom + layoutMarginTop).toInt()
        rootView = LayoutInflater.from(context).inflate(R.layout.oplus_doc_single_image_dragshadow_item_layout, null)
    }

    private fun drawImg(shadowView: View, canvas: Canvas, r: Rect) {
        shadowPaint.color = Color.TRANSPARENT
        shadowPaint.setShadowLayer(shadowBlur, SHADOW_DX, SHADOW_DY, shadowColor)
            canvas.drawRoundRect(r.left + layoutPadding,
                r.top + layoutPadding + layoutMarginTop,
                r.right - layoutPadding - layoutMarginEnd,
                r.bottom - layoutPadding, shadowRadius, shadowRadius, shadowPaint)
        shadowView.draw(canvas)
    }

    private fun drawListAndGrid(shadowView: View, canvas: Canvas, r: Rect) {
        if (dropCount > SHADOW_COUNT_ONE) {
            shadowPaint.color = roundRectColor
            shadowPaint.setShadowLayer(shadowBlur, SHADOW_DX, SHADOW_DY, shadowColor)

            val roundRectPaint = Paint(Paint.ANTI_ALIAS_FLAG)
            roundRectPaint.style = Paint.Style.FILL

            val roundStrokePaint = Paint(Paint.ANTI_ALIAS_FLAG)
            roundStrokePaint.strokeWidth = rectStrokeWidth
            roundStrokePaint.style = Paint.Style.STROKE
            roundStrokePaint.color = rectStrokeColor

            // item count great than 2, draw two shadow
            if (dropCount > SHADOW_COUNT_TWO) {
                // Shadow 1
                drawLayerThree(r, canvas, roundRectPaint, roundStrokePaint)
            }
            // Shadow 2
            drawLayerTwo(r, canvas, roundRectPaint, roundStrokePaint)
        } else {
            shadowPaint.color = Color.TRANSPARENT
            shadowPaint.setShadowLayer(shadowBlur, SHADOW_DX, SHADOW_DY, shadowColor)
            canvas.drawRoundRect(r.left + layoutPadding,
                r.top + layoutPadding + layoutMarginTop,
                r.right - layoutPadding - layoutMarginEnd,
                r.bottom - layoutPadding, shadowRadius, shadowRadius, shadowPaint)
        }
        shadowView.draw(canvas)
    }

    private fun drawLayerThree(
        r: Rect,
        canvas: Canvas,
        roundRectPaint: Paint,
        roundStrokePaint: Paint
    ) {
        val left: Float
        val top: Float
        val right: Float
        val bottom: Float
        if (mIsRtl) {
            // Shadow 1
            left = r.left + layoutPadding + layoutMarginEnd + 2 * shadowMarginLeftAndRight
            top = r.top + 2 * shadowMarginTop + layoutPadding + layoutMarginTop
            right = r.right - layoutPadding - 2 * shadowMarginLeftAndRight
            bottom = r.bottom - layoutPadding + 2 * shadowMarginBottom

            // Shadow 1,bottom layer
            canvas.drawRoundRect(left, top, right, bottom, shadowRadius, shadowRadius, shadowPaint)

            // Shadow 1 mask
            roundRectPaint.color = bottomMaskColor
            canvas.drawRoundRect(
                left,
                top,
                right,
                bottom,
                shadowRadius,
                shadowRadius,
                roundRectPaint
            )

            // Shadow 1 Rect stroke
            canvas.drawRoundRect(
                left,
                top,
                right,
                bottom,
                shadowRadius,
                shadowRadius,
                roundStrokePaint
            )
        } else {
            // Shadow 1
            left = r.left + layoutPadding + 2 * shadowMarginLeftAndRight
            top = r.top + layoutPadding + layoutMarginTop + 2 * shadowMarginTop
            right = r.right - layoutPadding - layoutMarginEnd - 2 * shadowMarginLeftAndRight
            bottom = r.bottom - layoutPadding + 2 * shadowMarginBottom
            // Shadow 1,bottom layer
            canvas.drawRoundRect(left, top, right, bottom, shadowRadius, shadowRadius, shadowPaint)
            // Shadow 1 mask
            roundRectPaint.color = bottomMaskColor
            canvas.drawRoundRect(
                left,
                top,
                right,
                bottom,
                shadowRadius,
                shadowRadius,
                roundRectPaint
            )
            // Shadow 1 Rect stroke
            canvas.drawRoundRect(
                left,
                top,
                right,
                bottom,
                shadowRadius,
                shadowRadius,
                roundStrokePaint
            )
        }
    }

    @Suppress("LongMethod")
    private fun drawLayerTwo(
        r: Rect,
        canvas: Canvas,
        roundRectPaint: Paint,
        roundStrokePaint: Paint
    ) {
        val left: Float
        val top: Float
        val right: Float
        val bottom: Float
        if (mIsRtl) {
            left = r.left + layoutPadding + layoutMarginEnd + shadowMarginLeftAndRight
            top = r.top + layoutPadding + layoutMarginTop + shadowMarginTop
            right = r.right - layoutPadding - shadowMarginLeftAndRight
            bottom = r.bottom - layoutPadding + shadowMarginBottom
            // Shadow 2, middle layer
            if (dropCount > SHADOW_COUNT_TWO) {
                roundRectPaint.color = roundRectColor
                canvas.drawRoundRect(
                    left,
                    top,
                    right,
                    bottom,
                    shadowRadius,
                    shadowRadius,
                    roundRectPaint
                )
            } else {
                canvas.drawRoundRect(
                    left,
                    top,
                    right,
                    bottom,
                    shadowRadius,
                    shadowRadius,
                    shadowPaint
                )
            }

            // Shadow 2, mask
            roundRectPaint.color = middleMaskColor
            canvas.drawRoundRect(
                left,
                top,
                right,
                bottom,
                shadowRadius,
                shadowRadius,
                roundRectPaint
            )

            // Shadow 2 Rect stroke
            canvas.drawRoundRect(
                left,
                top,
                right,
                bottom,
                shadowRadius,
                shadowRadius,
                roundStrokePaint
            )
        } else {
            left = r.left + layoutPadding + shadowMarginLeftAndRight
            top = r.top + layoutPadding + layoutMarginTop + shadowMarginTop
            right = r.right - layoutPadding - layoutMarginEnd - shadowMarginLeftAndRight
            bottom = r.bottom - layoutPadding + shadowMarginBottom
            // Shadow 2, middle layer
            if (dropCount > SHADOW_COUNT_TWO) {
                roundRectPaint.color = roundRectColor
                canvas.drawRoundRect(
                    left,
                    top,
                    right,
                    bottom,
                    shadowRadius,
                    shadowRadius,
                    roundRectPaint
                )
            } else {
                canvas.drawRoundRect(
                    left,
                    top,
                    right,
                    bottom,
                    shadowRadius,
                    shadowRadius,
                    shadowPaint
                )
            }
            // Shadow 2, mask
            roundRectPaint.color = middleMaskColor
            canvas.drawRoundRect(
                left,
                top,
                right,
                bottom,
                shadowRadius,
                shadowRadius,
                roundRectPaint
            )
            // Shadow 2 Rect stroke
            canvas.drawRoundRect(
                left,
                top,
                right,
                bottom,
                shadowRadius,
                shadowRadius,
                roundStrokePaint
            )
        }
    }

    override fun onDrawShadow(canvas: Canvas) {
        val r = canvas.clipBounds
        val shadowView = shadowViewWR.get() ?: return

        Log.d(TAG, "onDrawShadow")

        // Calling measure is necessary in order for all child views to get correctly laid out.
        shadowView.measure(
            MeasureSpec.makeMeasureSpec(r.right - r.left, MeasureSpec.EXACTLY),
            MeasureSpec.makeMeasureSpec(r.bottom - r.top, MeasureSpec.EXACTLY))
        shadowView.layout(r.left, r.top, r.right, r.bottom)

        when (statusCode) {
            STATUS_GRID_IMG, STATUS_RECENT_SINGLE_IMG, STATUS_RECENT_IMG -> drawImg(shadowView, canvas, r)
            STATUS_LIST, STATUS_GRID -> drawListAndGrid(shadowView, canvas, r)
        }
    }

    override fun onProvideShadowMetrics(outShadowSize: Point, outShadowTouchPoint: Point) {
        super.onProvideShadowMetrics(outShadowSize, outShadowTouchPoint)
        outShadowSize.set(width, height)
        outShadowTouchPoint.set(width / 2, height / 2)
    }

    fun updateShadowTitle(dragHoldDownFile: BaseFileBean?) {
        val dragHoldDownTitle = dragHoldDownFile?.mDisplayName
        Log.d(TAG, "updateShadowTitle: $dragHoldDownTitle")
        val shadowView = shadowViewWR.get() ?: return
        if (!dragHoldDownTitle.isNullOrEmpty()) {
            val titleView: TextView = shadowView.findViewById(R.id.dragshadow_item_title) ?: return
            titleView.text = dragHoldDownTitle
            when (statusCode) {
                STATUS_LIST -> {
                    if (titleView is TextViewSnippet) {
                        titleView.setTextViewStyleForFixedWidth()
                        val constraintLayout = rootView?.findViewById<ConstraintLayout>(R.id.list_dragshadow_item_cons)
                        setIconAlign(constraintLayout, titleView, dragHoldDownFile.mDisplayName)
                    }
                }

                STATUS_GRID -> {
                    if (titleView is MiddleMultilineTextView) {
                        shadowView.addOnLayoutChangeListener(object : View.OnLayoutChangeListener {
                            override fun onLayoutChange(
                                v: View?,
                                l: Int,
                                t: Int,
                                r: Int,
                                b: Int,
                                oL: Int,
                                oT: Int,
                                oR: Int,
                                oB: Int
                            ) {
                                shadowView.removeOnLayoutChangeListener(this)
                                dragHoldDownTitle.apply {
                                    titleView.setMultiText(this)
                                }
                            }
                        })
                    }
                }
            }
        }
    }

    private fun setIconAlign(constraintLayout: ConstraintLayout?, titleView: TextViewSnippet, displayName: String?) {
        val title = displayName ?: return
        val moreOne = titleView.isMoreThanOneLine(title)
        setIconConstraintSet(constraintLayout, moreOne)
    }

    private fun setIconConstraintSet(rootView: ConstraintLayout?, isMoreThanOneLine: Boolean) {
        val constraintSet = ConstraintSet()
        constraintSet.apply {
            clone(rootView)
            clear(R.id.dragshadow_item_cover, ConstraintSet.TOP)
            clear(R.id.dragshadow_item_cover, ConstraintSet.BOTTOM)
            if (isMoreThanOneLine) {
                connect(
                    R.id.dragshadow_item_cover,
                    ConstraintSet.TOP,
                    R.id.rl_item_title,
                    ConstraintSet.TOP
                )
            } else {
                connect(
                    R.id.dragshadow_item_cover,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP
                )
                connect(
                    R.id.dragshadow_item_cover,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )
            }
            applyTo(rootView)
        }
    }

    fun updateShadowTitle(dragHoldTitle: String?) {
        Log.d(TAG, "updateShadowTitle: $dragHoldTitle")
        val shadowView = shadowViewWR.get() ?: return
        if (!dragHoldTitle.isNullOrEmpty()) {
            val titleView: TextView = shadowView.findViewById(R.id.dragshadow_item_title) ?: return
            titleView.text = dragHoldTitle
            when (statusCode) {
                STATUS_LIST -> {
                    if (titleView is TextViewSnippet) {
                        titleView.setTextViewStyleForFixedWidth()
                        val constraintLayout = rootView?.findViewById<ConstraintLayout>(R.id.list_dragshadow_item_cons)
                        setIconAlign(constraintLayout, titleView, dragHoldTitle)
                    }
                }

                STATUS_GRID -> {
                    if (titleView is MiddleMultilineTextView) {
                        shadowView.addOnLayoutChangeListener(object : View.OnLayoutChangeListener {
                            override fun onLayoutChange(
                                v: View?,
                                l: Int,
                                t: Int,
                                r: Int,
                                b: Int,
                                oL: Int,
                                oT: Int,
                                oR: Int,
                                oB: Int
                            ) {
                                shadowView.removeOnLayoutChangeListener(this)
                                dragHoldTitle.apply {
                                    titleView.setMultiText(this)
                                }
                            }
                        })
                    }
                }
            }
        }
    }

    fun updateShadowRedDot(dragDropCount: Int?) {
        val shadowView = shadowViewWR.get() ?: return
        val countView: COUIHintRedDot = shadowView.findViewById(R.id.dragshadow_item_count) ?: return
        dropCount = dragDropCount ?: 0
        countView.pointMode = COUIHintRedDot.POINT_WITH_NUM_MODE
        if (dropCount > 1) {
            countView.pointNumber = dropCount
            countView.visibility = VISIBLE
        } else {
            countView.visibility = GONE
        }
    }

    fun updateShadowDetails(context: Context, dragHoldDownFile: BaseFileBean?, isList: Boolean) {
        val dragHoldDownDetail = DragUtils.getDragDetails(context, dragHoldDownFile, isList)
        Log.d(TAG, "updateShadowDetail : $dragHoldDownDetail")
        if (!dragHoldDownDetail.isNullOrEmpty()) {
            val shadowView = shadowViewWR.get() ?: return
            val detailsView: TextView = shadowView.findViewById(R.id.dragshadow_item_details)
                ?: return
            detailsView.text = dragHoldDownDetail
        }
    }

    fun updateShadowDetails(dragHoldDetails: String?) {
        Log.d(TAG, "updateShadowDetail : $dragHoldDetails")
        if (!dragHoldDetails.isNullOrEmpty()) {
            val shadowView = shadowViewWR.get() ?: return
            val detailsView: TextView = shadowView.findViewById(R.id.dragshadow_item_details)
                ?: return
            detailsView.text = dragHoldDetails
        }
    }

    fun updateDragCover(drawable: Drawable?) {
        drawable?.let {
            val shadowView = shadowViewWR.get() ?: return
            val coverView: ImageView = shadowView.findViewById(R.id.dragshadow_item_cover) ?: return
            coverView.setImageDrawable(it)
        }
    }

    fun updateGridImg(context: Context, data: ClipData?, dragHoldDownFile: BaseFileBean?, code: Int, drawable: Drawable?) {
        if (data == null || data.itemCount < 1 || dropCount != data.itemCount) {
            return
        }
        val shadowView = shadowViewWR.get() ?: return
        val imageOne: ImageView = shadowView.findViewById(R.id.dragshadow_item_image_one) ?: return
        val imageTwo: ImageView = shadowView.findViewById(R.id.dragshadow_item_image_two) ?: return
        val imageThree: ImageView = shadowView.findViewById(R.id.dragshadow_item_image_three) ?: return

        if (dropCount > SHADOW_COUNT_ONE) {
            if (dropCount > SHADOW_COUNT_TWO) {
                updateLayerThree(context, data, imageThree, dragHoldDownFile)
            }
            updateLayerTwo(context, data, imageTwo, dragHoldDownFile)
        }
        val dragHoldDownDrawable = DragUtils.getDragImgDrawable(context, drawable, code)
        dragHoldDownDrawable?.let {
            imageOne.setImageDrawable(it)
        }
    }

    private fun updateLayerTwo(context: Context, data: ClipData, imageTwo: ImageView, dragHoldDownFile: BaseFileBean?) {
        if (dragHoldDownFile == null || dragHoldDownFile.mData.isNullOrEmpty()) {
            return
        }
        imageTwo.visibility = VISIBLE
        val holdDownPath = dragHoldDownFile.mData
        val lastUri = data.getItemAt(dropCount - SHADOW_COUNT_ONE).uri
        val lsatPath = FileMediaHelper.queryPathFromUri(context, lastUri)
        val item = if (holdDownPath.equals(lsatPath)) {
            data.getItemAt(dropCount - SHADOW_COUNT_TWO)
        } else {
            data.getItemAt(dropCount - SHADOW_COUNT_ONE)
        }
//        GlideLoader.display(context, item.uri, imageTwo)
        imageTwo.setImageURI(item.uri)
    }

    private fun updateLayerThree(context: Context, data: ClipData, imageThree: ImageView, dragHoldDownFile: BaseFileBean?) {
        if (dragHoldDownFile == null || dragHoldDownFile.mData.isNullOrEmpty()) {
            return
        }
        imageThree.visibility = VISIBLE
        val holdDownPath = dragHoldDownFile.mData
        val lastUri = data.getItemAt(dropCount - SHADOW_COUNT_ONE).uri
        val lsatPath = FileMediaHelper.queryPathFromUri(context, lastUri)
        val lastTwoUri = data.getItemAt(dropCount - SHADOW_COUNT_TWO).uri
        val lsatTwoPath = FileMediaHelper.queryPathFromUri(context, lastTwoUri)
        val item = if (holdDownPath.equals(lsatPath) || holdDownPath.equals(lsatTwoPath)) {
            data.getItemAt(dropCount - SHADOW_MOST_LAYER_COUNT)
        } else {
            data.getItemAt(dropCount - SHADOW_COUNT_TWO)
        }
//        GlideLoader.display(context, item.uri, imageThree)
        imageThree.setImageURI(item.uri)
    }

    fun updateRecentImg(context: Context, drawable: Drawable?, code: Int) {
        val shadowView = shadowViewWR.get() ?: return
        val imageOne: ImageView = shadowView.findViewById(R.id.dragshadow_item_image_one) ?: return
        val imageTwo: ImageView = shadowView.findViewById(R.id.dragshadow_item_image_two) ?: return
        val imageThree: ImageView = shadowView.findViewById(R.id.dragshadow_item_image_three) ?: return

        if (dropCount > SHADOW_COUNT_ONE) {
            if (dropCount > SHADOW_COUNT_TWO) {
                imageThree.visibility = VISIBLE
            }
            imageTwo.visibility = VISIBLE
        }
        val dragHoldDownDrawable = DragUtils.getDragImgDrawable(context, drawable, code)
        dragHoldDownDrawable?.let {
            imageOne.setImageDrawable(it)
        }
    }
}