// Top-level build file where you can add configuration options common to all sub-projects/modules.
final javaVerProp = System.getProperty("java.version")
println("CI runtime info: java.version=$javaVerProp")
final javaVersion = javaVerProp.split("\\.")[0].toInteger()
final requireJava = prop_targetCompatibility.toInteger()
if (javaVersion < requireJava) {
    final errMsg = "Require to run gradle with java version at least $requireJava, " +
            "but current java version is $javaVersion. Please update project gralde JDK configs!"
    throw new IllegalStateException(errMsg)
}

buildscript {
    repositories {
        mavenLocal()

        maven {
            allowInsecureProtocol true
            url prop_oppoMavenUrlRelease
            credentials {
                username sonatypeUsername
                password sonatypeUsername
            }
        }

        maven {
            allowInsecureProtocol true
            url prop_oppoMavenUrlSnapshot
            credentials {
                username sonatypeUsername
                password sonatypeUsername
            }
        }

        maven {
            allowInsecureProtocol true
            url prop_oppoNexusMavenUrl
        }

        maven {
            allowInsecureProtocol true
            url prop_oppoNexusMavenUrl
        }

        maven {
            allowInsecureProtocol true
            url prop_adcMavenUrlRelease
        }

        maven {
            allowInsecureProtocol true
            url prop_adcMavenUrlSnapshot
        }

        maven {
            allowInsecureProtocol = true
            //need use oppo maven,due to google repo is not available in services
            url prop_cacheMavenUrl
        }

        maven {
            allowInsecureProtocol = true
            url prop_oppoBrowserMavenUrl
            credentials {
                username sonatypeUsername
                password sonatypeUsername
            }
        }
    }

    dependencies {
        classpath(libs.android.gradle.plugin)
        classpath(libs.kotlin.gradle.plugin)
        classpath(libs.kotlin.serialization.plugin)
        classpath(libs.oplus.build.plugin)
        classpath(libs.oplus.assistantscreen.plugin.mic.plugin)
        classpath (libs.autotest.opasm)

        if (prop_enableOapm.toBoolean()) {
            classpath(libs.oapm.perf.plugin)
        }
    }
}

allprojects {
    repositories {
        mavenLocal()

        maven {
            allowInsecureProtocol true
            url prop_oppoMavenUrlRelease
            credentials {
                username sonatypeUsername
                password sonatypeUsername
            }
        }

        maven {
            allowInsecureProtocol true
            url prop_oppoMavenUrlSnapshot
            credentials {
                username sonatypeUsername
                password sonatypeUsername
            }
        }

        maven {
            allowInsecureProtocol true
            url prop_oppoNexusMavenUrl
        }

        maven {
            allowInsecureProtocol true
            url prop_adcMavenUrlRelease
        }

        maven {
            allowInsecureProtocol true
            url prop_adcMavenUrlSnapshot
        }

        maven {
            allowInsecureProtocol = true
            //need use oppo maven,due to google repo is not available in services
            url prop_cacheMavenUrl
        }

        maven {
            allowInsecureProtocol = true
            url prop_oppoBrowserMavenUrl
            credentials {
                username sonatypeUsername
                password sonatypeUsername
            }
        }

        maven {
            url(rootProject.file("extraRepo").absolutePath)
        }

        if (prop_testIntegrateYozosoftAarToApp.toBoolean()) {
            maven {
                url(new File(rootProject.buildDir, prop_localPublishAarRepo).absolutePath)
            }
        }
    }
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
    delete file("${rootDir}/reports")
    delete file("${rootDir}/UnitTestReport")
    delete file("${rootDir}/jacocoinfo.txt")
}

subprojects {
    task printAllDependencies(type: DependencyReportTask) {}
}
apply from: rootProject.file("script/rootTest.gradle")