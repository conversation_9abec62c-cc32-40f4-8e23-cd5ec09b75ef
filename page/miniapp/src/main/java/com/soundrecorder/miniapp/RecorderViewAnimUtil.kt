/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecorderViewAnimUtil
 * Description:
 * Version: 1.0
 * Date: 2023/4/14
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/4/14 1.0 create
 */

package com.soundrecorder.miniapp

import android.os.SystemClock
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import androidx.transition.ChangeBounds
import androidx.transition.Transition
import androidx.transition.Transition.TransitionListener
import androidx.transition.TransitionManager
import androidx.transition.TransitionSet
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.miniapp.transition.AppCardAlphaTransition

class RecorderViewAnimUtil(
    private val playStateView: View,
    private val leftMarkView: View,
    private val rightSaveView: View
) {
    companion object {
        const val DURATION_ANIMATION = 300L
    }

    private var doShowAnimationTime = -1L
    private var doHideAnimationTime = -1L

    fun showMarkAndSaveFileView(should: Boolean = true, onTransitionEndFun: (() -> Unit)? = null) {
        try {
            if (checkAnimatorRunning(doShowAnimationTime)) {
                return
            }
            if (leftMarkView.alpha != 0f) {
                return
            }
            if (should) {
                doShowAnimationTime = SystemClock.elapsedRealtime()
            }
            DebugUtil.i("RecorderViewAnimUtil", "showMarkAndSaveFileView should=$should")
            TransitionManager.beginDelayedTransition(
                playStateView.parent as ViewGroup,
                getTransition(should, onTransitionEndFun)
            )
            leftMarkView.alpha = 1F
            rightSaveView.alpha = 1F
            leftMarkView.updateLayoutParams<ConstraintLayout.LayoutParams> {
                endToEnd = ConstraintLayout.LayoutParams.UNSET
                endToStart = R.id.btnSwitchState
            }
            rightSaveView.updateLayoutParams<ConstraintLayout.LayoutParams> {
                startToStart = ConstraintLayout.LayoutParams.UNSET
                startToEnd = R.id.btnSwitchState
            }
        } catch (_: Exception) {
            TransitionManager.endTransitions(playStateView.parent as ViewGroup)
        }
    }

    fun hideMarkAndSaveFileViewWithAnimation(
        should: Boolean = true,
        onTransitionEndFun: (() -> Unit)? = null
    ) {
        try {
            if (checkAnimatorRunning(doHideAnimationTime)) {
                return
            }
            if (leftMarkView.alpha != 1f) {
                return
            }
            if (should) {
                doHideAnimationTime = SystemClock.elapsedRealtime()
            }
            DebugUtil.i(
                "RecorderViewAnimUtil",
                "hideMarkAndSaveFileViewWithAnimation should=$should"
            )

            TransitionManager.beginDelayedTransition(
                playStateView.parent as ViewGroup,
                getTransition(should, onTransitionEndFun)
            )
            leftMarkView.alpha = 0F
            rightSaveView.alpha = 0F
            leftMarkView.updateLayoutParams<ConstraintLayout.LayoutParams> {
                endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                endToStart = ConstraintLayout.LayoutParams.UNSET
            }
            rightSaveView.updateLayoutParams<ConstraintLayout.LayoutParams> {
                startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                startToEnd = ConstraintLayout.LayoutParams.UNSET
            }
        } catch (_: Exception) {
            TransitionManager.endTransitions(playStateView.parent as ViewGroup)
        }
    }

    fun isAnimRunning(): Boolean {
        return checkAnimatorRunning(doShowAnimationTime) || checkAnimatorRunning(doHideAnimationTime)
    }

    private fun getTransition(
        hasAnimDuration: Boolean,
        onTransitionEndFun: (() -> Unit)? = null
    ): TransitionSet {
        return TransitionSet().apply {
            addTransition(ChangeBounds())
            addTransition(AppCardAlphaTransition(leftMarkView.id, rightSaveView.id))
            if (hasAnimDuration) {
                duration = DURATION_ANIMATION
            }
            addListener(object : TransitionListener {
                override fun onTransitionStart(transition: Transition) {
                }

                override fun onTransitionEnd(transition: Transition) {
                    onTransitionEndFun?.invoke()
                }

                override fun onTransitionCancel(transition: Transition) {
                }

                override fun onTransitionPause(transition: Transition) {
                }

                override fun onTransitionResume(transition: Transition) {
                }
            })
        }
    }

    private fun checkAnimatorRunning(time: Long): Boolean {
        return SystemClock.elapsedRealtime() - time <= DURATION_ANIMATION
    }
}