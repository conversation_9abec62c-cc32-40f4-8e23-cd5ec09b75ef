/*
 * *************************************************************
 * Copyright (C), 2010-2024, Oplus. All rights reserved.
 * VENDOR_EDIT
 * File: SecretUtil.kt
 * Description: Annotation method that needs to be mapped.
 * Version: 1.0
 * Date: 2024/3/26
 * Author: kkkk
 * ---------------------Revision History: ---------------------
 * <author>     <data>      <version >      <desc>
 * kkkk    2024/3/26     1.0          build this file
 * *************************************************************
 */
package andes.oplus.documentsreader.formatconvert.util

import org.apache.commons.codec.binary.Hex
import org.apache.commons.codec.digest.HmacAlgorithms
import org.apache.commons.codec.digest.HmacUtils
import java.security.KeyFactory
import java.security.NoSuchAlgorithmException
import java.security.interfaces.RSAPublicKey
import java.security.spec.X509EncodedKeySpec
import java.util.*
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.GCMParameterSpec
import javax.crypto.spec.SecretKeySpec

object SecretUtils {
    const val AES_TRANSFORMATION = "AES/GCM/NoPadding"
    const val RSA_TRANSFORMATION = "RSA/NONE/OAEPPadding"
    const val AES_ALGORITHM = "AES"
    const val RSA_ALGORITHM = "RSA"
    private const val KEY_SIZE = 128

    /**
     * 根据AES秘钥String创建SecretKey
     */
    @JvmStatic
    fun buildAESSecretKey(encodeKey: String?): SecretKey {
        val decodeKeyBytes = Base64.getDecoder().decode(encodeKey)
        return SecretKeySpec(decodeKeyBytes, 0, decodeKeyBytes.size, AES_ALGORITHM)
    }

    /**
     * 根据String创建RSA公钥
     */
    @JvmStatic
    @Throws(Exception::class)
    fun buildRSAPublicKey(encodeKey: String?): RSAPublicKey {
        val decodeKeyBytes = Base64.getDecoder().decode(encodeKey)
        return KeyFactory.getInstance(RSA_ALGORITHM).generatePublic(X509EncodedKeySpec(decodeKeyBytes)) as RSAPublicKey
    }

    /**
     * 随机生成AES秘钥
     */
    @JvmStatic
    @Throws(NoSuchAlgorithmException::class)
    fun generateSecretKey(): SecretKey {
        val keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM)
        keyGenerator.init(KEY_SIZE)
        return keyGenerator.generateKey()
    }

    /**
     * 根据String创建GCM IV
     */
    @JvmStatic
    fun generateIVByString(ivString: String?): GCMParameterSpec {
        val ivBytes = Base64.getDecoder().decode(ivString)
        return GCMParameterSpec(KEY_SIZE, ivBytes)
    }

    @JvmStatic
    fun getHeaderAuthorization(params: Map<String, String>?, body: String, appId: String, secretKey: String, timeStamp: Long? = null): String {
        // 1. 构建认证字符串前缀，格式为 ai-auth-v1/{appId}/{timestamp}, timestamp为时间戳，精确到毫秒，用以验证请求是否失效
        val time = timeStamp ?: System.currentTimeMillis()
        val authStringPrefix = String.format(Locale.ENGLISH, "ai-auth-v1/%s/%d/", appId, time)
        val sb = java.lang.StringBuilder(authStringPrefix)
        // 2. 构建url参数字符串，按照参数名字典序升序排列
        if (params != null) {
            val treeMap: Map<String, String> = TreeMap(params)
            treeMap.forEach { (key: String?, value: String?) ->
                sb.append(key).append("=").append(value).append("&")
            }
        }
        // 3. 拼接签名原文字符串
        val signStr = sb.toString() + body
        // 4. hmac_sha_256算法签名
        val hmacSign = HmacUtils(HmacAlgorithms.HMAC_SHA_256, secretKey)
        val signature = String(Hex.encodeHex(hmacSign.hmac(signStr)))
        // 5. 拼接认证字符串
        return authStringPrefix + signature
    }
}