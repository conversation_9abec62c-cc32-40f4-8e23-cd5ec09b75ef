/*********************************************************************
 * * Copyright (C), 2010-2020, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.utils
 * * Version     : 1.0
 * * Date        : 2020/8/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.core.common.utils

import andes.oplus.documentsreader.core.common.ContextGetter
import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.app.Activity
import android.graphics.drawable.Drawable
import android.view.MenuItem
import android.view.animation.PathInterpolator
import androidx.core.content.ContextCompat

object KtAnimationUtil {
    private const val GRID_BUTTON_FADE_DURATION = 200L
    private const val ALPHA_TRANSPARENT = 0
    private const val ALPHA_NOT_TRANSPARENT = 255
    private const val CONTROL_X1 = 0.33f
    private const val CONTROL_X2 = 0.67f

    @JvmStatic
    fun updateMenuItemWithFadeAnimate(menuItem: MenuItem?, targetIconResId: Int, activity: Activity? = null) {
        var drawable: Drawable? = menuItem?.icon
        var oldDrawable: Drawable? = null

        val fadeOutAnimator = ValueAnimator.ofInt(ALPHA_NOT_TRANSPARENT, ALPHA_TRANSPARENT).apply {
            addUpdateListener { animation ->
                drawable?.alpha = animation.animatedValue as Int
            }
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animator: Animator) {}
                override fun onAnimationEnd(animator: Animator) {
                    oldDrawable = drawable
                    val context = activity ?: ContextGetter.context
                    drawable = ContextCompat.getDrawable(context, targetIconResId)
                    drawable?.alpha = ALPHA_TRANSPARENT
                    menuItem?.icon = drawable
                    oldDrawable?.alpha = ALPHA_NOT_TRANSPARENT
                }

                override fun onAnimationCancel(animator: Animator) {
                    drawable?.alpha = ALPHA_NOT_TRANSPARENT
                    oldDrawable?.alpha = ALPHA_NOT_TRANSPARENT
                }

                override fun onAnimationRepeat(animator: Animator) {}
            })
            duration = GRID_BUTTON_FADE_DURATION
            interpolator = PathInterpolator(CONTROL_X1, 0f, CONTROL_X2, 1f)
        }

        val fadeInAnimator = ValueAnimator.ofInt(ALPHA_TRANSPARENT, ALPHA_NOT_TRANSPARENT).apply {
            addUpdateListener { animation ->
                drawable?.alpha = animation.animatedValue as Int
            }
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animator: Animator) {}
                override fun onAnimationEnd(animator: Animator) {
                    drawable?.alpha = ALPHA_NOT_TRANSPARENT
                }

                override fun onAnimationCancel(animator: Animator) {
                    drawable?.alpha = ALPHA_NOT_TRANSPARENT
                }

                override fun onAnimationRepeat(animator: Animator) {}
            })
            duration = GRID_BUTTON_FADE_DURATION
            interpolator = PathInterpolator(CONTROL_X1, 0f, CONTROL_X2, 1f)
        }

        AnimatorSet().apply {
            playSequentially(fadeOutAnimator, fadeInAnimator)
            start()
        }
    }
}