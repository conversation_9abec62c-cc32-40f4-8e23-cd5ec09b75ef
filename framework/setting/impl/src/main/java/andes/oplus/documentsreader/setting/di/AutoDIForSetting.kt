/*********************************************************************
 * * Copyright (C), 2024, OPlus. All rights reserved.
 * * File        :  AutoDIForSelectPathController
 * * Description : AutoDIForSelectPathController
 * * Version     : 1.0
 * * Date        : 2024/07/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.setting.di

import andes.oplus.documentsreader.setting.ISetting
import andes.oplus.documentsreader.setting.SettingEntrance
import com.oplus.assistantscreen.mic.interfaces.KoinModuleMarker
import com.oplus.assistantscreen.mic.interfaces.MarkField
import org.koin.dsl.module

@KoinModuleMarker
class AutoDIForSetting {

    @MarkField
    val settingModule = module {
        single<ISetting>(createdAtStart = true) {
            SettingEntrance
        }
    }
}