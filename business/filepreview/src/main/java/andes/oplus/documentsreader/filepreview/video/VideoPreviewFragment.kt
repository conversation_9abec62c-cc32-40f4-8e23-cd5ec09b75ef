/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: VideoPreviewFragment
 * * Description: the fragment for video preview
 * * Version: 1.0
 * * Date : 2023/12/06
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2023/12/06       1.0           create
 ****************************************************************/
package andes.oplus.documentsreader.filepreview.video

import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.KtConstants.SHOW_SIDEBAR_PANEL
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.Utils
import andes.oplus.documentsreader.core.common.stringResource
import andes.oplus.documentsreader.core.common.uiconfig.UIConfigMonitor
import andes.oplus.documentsreader.core.common.uiconfig.type.IUIConfig
import andes.oplus.documentsreader.filepreview.BasePreviewFragment
import andes.oplus.documentsreader.filepreview.FilePreviewDialogActivity
import andes.oplus.documentsreader.filepreview.PreviewFragmentModel
import andes.oplus.documentsreader.filepreview.R
import andes.oplus.documentsreader.filepreview.anim.IAnimCallback
import andes.oplus.documentsreader.filepreview.archive.ArchivePreviewFragment
import andes.oplus.documentsreader.filepreview.databinding.OplusDocFragmentVideoPreviewBinding
import andes.oplus.documentsreader.filepreview.util.MediaUtils
import andes.oplus.documentsreader.filepreview.util.PreviewStatisticsUtil
import andes.oplus.documentsreader.interfaces.CollectPrivacyUtils
import android.graphics.PointF
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.MenuProvider
import androidx.fragment.app.viewModels

class VideoPreviewFragment : BasePreviewFragment(), UIConfigMonitor.OnUIConfigChangeListener, IAnimCallback {

    private var binding: OplusDocFragmentVideoPreviewBinding? = null
    private val viewModel: PreviewFragmentModel by viewModels { PreviewFragmentModel.Factory }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = OplusDocFragmentVideoPreviewBinding.inflate(inflater, container, false)
        Log.e("VideoPreviewFragment", "onCreate")
        viewModel.initFileData(arguments)
        initPlayPath()
        (activity as FilePreviewDialogActivity).initMenu(menuProvider)
        return binding?.root
    }

    override fun onEnterAnimationStart() {
    }

    override fun onEnterAnimationEnd() {
        Log.d(TAG, "onEnterAnimationEnd")
        binding?.videoPlayer?.play()
    }

    private fun initPlayPath() {
        binding?.let {
            lifecycle.addObserver(it.videoPlayer)
            it.videoPlayer.setMediaUrl(viewModel.filePath)
            binding?.videoPlayer?.prepare()
        }
        CollectPrivacyUtils.collectVideoInfo(viewModel.filePath)
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (isAdded.not()) return
        binding?.videoPlayer?.apply {
            this.onUIConfigChanged(configList)
        }
    }

    /**
     * menu:分享、打开
     */
    private val menuProvider = object : MenuProvider {
        override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
            menuInflater.inflate(R.menu.preview_menu, menu)
        }

        override fun onPrepareMenu(menu: Menu) {
            super.onPrepareMenu(menu)
            menu.findItem(R.id.preview_share).also {
                it.isEnabled = true
                it.isVisible = true
                it.icon = ContextCompat.getDrawable(
                    ContextGetter.context,
                    R.drawable.menu_ic_share_normal
                )
            }
            menu.findItem(R.id.preview_open).also {
                it.isEnabled = true
                it.isVisible = true
                it.title = stringResource(
                    R.string.file_preview_menu_open_with,
                    stringResource(R.string.video)
                )
            }
        }

        override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
            if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                Log.d(ArchivePreviewFragment.TAG, "onMenuItemSelected -> quickClick")
                return true
            }
            if (pause) {
                Log.d(TAG, "already pause, return")
                return true
            }
            return when (menuItem.itemId) {
                R.id.preview_share -> {
                    viewModel.share(baseVMActivity)
                    true
                }
                R.id.preview_open -> {
                    if (baseVMActivity?.let { MediaUtils.openMediaFile(it, viewModel.filePath) } == true) {
                        (baseVMActivity as? FilePreviewDialogActivity)?.openByOtherSuccess()
                    }
                    PreviewStatisticsUtil.useOtherWayToOpen(viewModel.fileName)
                    true
                }

                R.id.home -> {
                    (baseVMActivity as FilePreviewDialogActivity).exitPreview(SHOW_SIDEBAR_PANEL, finishWithAnimator = true)
                    true
                }

                else -> false
            }
        }
    }

    /**
     * 顶部是否有内容，点击toolbar就不能关闭（图片和视频可以全屏展示）
     */
    fun hasContentInPosition(clickPos: PointF): Boolean {
        binding?.scaleLayout?.let {
            Log.d(TAG, "hasContentInPosition  ->${it.y}  ${clickPos.y}")
            return it.y <= clickPos.y
        }
        return false
    }

    companion object {
        const val TAG = "VideoPreviewFragment"
    }
}