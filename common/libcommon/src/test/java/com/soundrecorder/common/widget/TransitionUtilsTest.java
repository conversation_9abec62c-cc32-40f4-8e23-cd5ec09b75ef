package com.soundrecorder.common.widget;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Build;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.util.HashMap;

import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class TransitionUtilsTest {

    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void should_shadowMapSize_when_runExitAnimation() throws Exception {
        ViewGroup viewGroup = Mockito.mock(ViewGroup.class);
        ViewGroup rootview = Mockito.mock(ViewGroup.class);
        Whitebox.invokeMethod(TransitionUtils.INSTANCE, "initContainer");
        TransitionUtils.INSTANCE.runExitAnimation(viewGroup, rootview, Mockito.mock(Animator.AnimatorListener.class));
        Assert.assertEquals(viewGroup.getChildCount(), 0);
    }

    @Test
    public void should_shadowMapSize_when_runEnterAnimation() throws Exception {
        ViewGroup viewGroup = Mockito.mock(ViewGroup.class);
        ViewGroup rootview = Mockito.mock(ViewGroup.class);
        Whitebox.invokeMethod(TransitionUtils.INSTANCE, "initContainer");
        TransitionUtils.INSTANCE.runEnterAnimation(viewGroup, rootview, Mockito.mock(Animator.AnimatorListener.class));
        Assert.assertEquals(viewGroup.getChildCount(), 0);
    }

    @Test
    public void should_notNull_when_getView() throws Exception {
        View rootview = new View(mContext);
        ValueAnimator valueAnimator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createShadowCurrentModeLayoutAnim", rootview);
        Assert.assertNotNull(valueAnimator);
    }

    @Test
    public void should_Null_when_getView() throws Exception {
        ViewGroup viewGroup = Mockito.mock(ViewGroup.class);
        View rootview = Mockito.mock(View.class);
        ImageView imageView = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "addShadowView", rootview, viewGroup);
        Assert.assertNull(imageView);
        ValueAnimator value = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createSharedViewAnim");
        Assert.assertNull(value);
        ValueAnimator toolBarAlphaAnim = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createToolBarAlphaAnim");
        Assert.assertNull(toolBarAlphaAnim);
    }

    @Test
    public void should_NotNull_when_createShareViewAlphaAnim() throws Exception {
        ValueAnimator animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createShareViewAlphaAnim");
        Assert.assertNull(animator);

        Whitebox.setInternalState(TransitionUtils.INSTANCE.getClass(), "mSharedView", Mockito.spy(View.class));
        animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createShareViewAlphaAnim");
        Assert.assertNotNull(animator);
    }

    @Test
    public void should_NotNull_when_createMarkViewAnim() throws Exception {
        ValueAnimator animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createMarkViewAnim", Mockito.mock(View.class));
        Assert.assertNull(animator);

        View mMarkView = Mockito.spy(View.class);
        Whitebox.setInternalState(TransitionUtils.INSTANCE.getClass(), "mMarkView", mMarkView);
        animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createMarkViewAnim", Mockito.mock(View.class));
        Assert.assertNull(animator);

        mMarkView = Mockito.mock(View.class);
        AnimatedCircleButton redCircleView = Mockito.mock(AnimatedCircleButton.class);
        ViewGroup parent = Mockito.mock(ViewGroup.class);
        Mockito.when(redCircleView.getParent()).thenReturn(parent);
        Mockito.when(mMarkView.getLeft()).thenReturn(1);
        Mockito.when(mMarkView.getWidth()).thenReturn(10);
        Mockito.when(parent.getLeft()).thenReturn(0);
        Mockito.when(parent.getWidth()).thenReturn(100);
        Whitebox.setInternalState(TransitionUtils.INSTANCE.getClass(), "mMarkView", mMarkView);
        Whitebox.setInternalState(TransitionUtils.INSTANCE.getClass(), "mRedCircleView", redCircleView);
        animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createMarkViewAnim", Mockito.mock(View.class));
        Assert.assertNotNull(animator);
    }

    @Test
    public void should_NotNull_when_createTimerAlphaAnim() throws Exception {
        ValueAnimator animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createTimerAlphaAnim");
        Assert.assertNull(animator);

        Whitebox.setInternalState(TransitionUtils.INSTANCE.getClass(), "mTimerView", Mockito.spy(View.class));
        animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createTimerAlphaAnim");
        Assert.assertNotNull(animator);
    }

    @Test
    public void should_NotNull_when_createTimerTransAnim() throws Exception {
        ValueAnimator animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createTimerTransAnim");
        Assert.assertNull(animator);

        Whitebox.setInternalState(TransitionUtils.INSTANCE.getClass(), "mTimerView", Mockito.spy(View.class));
        animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createTimerTransAnim");
        Assert.assertNotNull(animator);
    }

    @Test
    public void should_NotNull_when_createWaveAlphaAnim() throws Exception {
        ValueAnimator animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createWaveAlphaAnim");
        Assert.assertNull(animator);

        Whitebox.setInternalState(TransitionUtils.INSTANCE.getClass(), "mWaveRecyclerView", Mockito.spy(View.class));
        animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createWaveAlphaAnim");
        Assert.assertNotNull(animator);
    }

    @Test
    public void should_NotNull_when_createWaveTransAnim() throws Exception {
        ValueAnimator animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createWaveTransAnim");
        Assert.assertNull(animator);

        Whitebox.setInternalState(TransitionUtils.INSTANCE.getClass(), "mWaveRecyclerView", Mockito.spy(View.class));
        animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createWaveTransAnim");
        Assert.assertNotNull(animator);
    }

    @Test
    public void should_NotNull_when_createToolBarAlphaAnim() throws Exception {
        ValueAnimator animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createToolBarAlphaAnim");
        Assert.assertNull(animator);

        Whitebox.setInternalState(TransitionUtils.INSTANCE.getClass(), "mToolBar", Mockito.spy(View.class));
        animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createToolBarAlphaAnim");
        Assert.assertNotNull(animator);
    }

    @Test
    public void should_NotNull_when_createToolBarTransAnim() throws Exception {
        ValueAnimator animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createToolBarTransAnim");
        Assert.assertNull(animator);

        Whitebox.setInternalState(TransitionUtils.INSTANCE.getClass(), "mToolBar", Mockito.spy(View.class));
        animator = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "createToolBarTransAnim");
        Assert.assertNotNull(animator);
    }

    @Test
    public void should_NotNull_when_putAnimView() {
        TransitionUtils.INSTANCE.release();
        View mockView = Mockito.mock(View.class);
        TransitionUtils.INSTANCE.putAnimView("mockView", mockView);
        HashMap<String, View> map = Whitebox.getInternalState(TransitionUtils.INSTANCE.getClass(), "animMap");
        Assert.assertNotNull(map);
    }

    @Test
    public void should_Null_when_convertViewToBitmap() throws Exception {
        View mockView = Mockito.mock(View.class);
        Bitmap bitmap = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "convertViewToBitmap", mockView);
        Assert.assertNull(bitmap);

        Mockito.when(mockView.getWidth()).thenReturn(1);
        Mockito.when(mockView.getHeight()).thenReturn(1);
        bitmap = Whitebox.invokeMethod(TransitionUtils.INSTANCE, "convertViewToBitmap", mockView);
        Assert.assertNotNull(bitmap);
    }

    @After
    public void tearDown() {
        mContext = null;
        TransitionUtils.INSTANCE.release();
    }
}
