package com.soundrecorder.common.sync.encryptbox;

import android.os.Environment;
import android.text.TextUtils;

import java.io.File;
import java.util.List;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.common.sync.db.RecordDataSync;

import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil;
import com.soundrecorder.common.db.RecorderDBUtil;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.ToastManager;
import com.soundrecorder.common.utils.RecordModeUtil;
import com.soundrecorder.modulerouter.cloudkit.CloudSyncAction;

public class SyncBeanProcessor {

    private static final String TAG = "SyncBeanProcessor";

    public SyncBeanProcessor() {
    }

    public void processIncomingSyncBean(SyncBean inputBean) {
        if (inputBean == null) {
            DebugUtil.e(TAG, "processIncomingSyncBean: inputBean null, return");
            return;
        }
        int syncType = inputBean.getSyncType();
        switch (syncType) {
            case EncryptBoxConstant.TYPE_ENCRYPTION:
                doProcessInputEncrypt(inputBean);
                break;
            case EncryptBoxConstant.TYPE_DECRYPTION:
                doProcessInputDecrypt(inputBean);
                break;
            case EncryptBoxConstant.TYPE_DELETE:
                doProcessInputDelete(inputBean);
                break;
            case EncryptBoxConstant.TYPE_DATA_LARGE:
                doProcessInputLargeData(inputBean);
                break;
            default:
                doOtherTypeMsg(inputBean);
                break;
        }
    }


    private void doProcessInputEncrypt(SyncBean inputSyncBean) {
        DebugUtil.i(TAG, "doProcessInputEncrypt: " + inputSyncBean);
        List<SyncBean.SyncFileInfo> syncFileInfoList = inputSyncBean.getFileInfoList();
        boolean processSuccess = false;
        if (syncFileInfoList != null && syncFileInfoList.size() > 0) {
            for (SyncBean.SyncFileInfo syncFileInfo : syncFileInfoList) {
                String displayName = syncFileInfo.getDisplayName();
                String relativePath = syncFileInfo.getRelativePath();
                String mD5 = syncFileInfo.getMd5();
                processSuccess |= CloudSyncRecorderDbUtil.processEncryptAudioFile(displayName, relativePath, mD5);
            }
            if (processSuccess) {
                CloudSyncAction.trigBackupNow(BaseApplication.getAppContext());
                DebugUtil.i(TAG, "doProcessInputEncrypt: triBackUpNow ");
            }
        }
    }

    private void doProcessInputDecrypt(SyncBean inputSyncBean) {
        DebugUtil.i(TAG, "doProcessInputDecrypt: " + inputSyncBean);
        List<SyncBean.SyncFileInfo> syncFileInfoList = inputSyncBean.getFileInfoList();
        String targetPath = inputSyncBean.getTargetPath();
        boolean processSuccess = false;
        if (TextUtils.isEmpty(targetPath) || (!RecordModeUtil.containRecordRelativePath(targetPath))) {
            DebugUtil.i(TAG, "doProcessInputDecrypt, input targetName: " + FileUtils.getDisplayNameByPath(targetPath) + ", not qualified, return");
            return;
        }
        if (syncFileInfoList != null && syncFileInfoList.size() > 0) {
            for (SyncBean.SyncFileInfo syncFileInfo : syncFileInfoList) {
                String displayName = syncFileInfo.getDisplayName();
                String relativePath = syncFileInfo.getRelativePath();
                String mD5 = syncFileInfo.getMd5();
                processSuccess |= RecorderDBUtil.getInstance(BaseApplication.getAppContext()).processDecrypAudioFile(displayName, relativePath, mD5);
            }
            if (processSuccess) {
                CloudSyncAction.trigBackupNow(BaseApplication.getAppContext());
                DebugUtil.i(TAG, "doProcessInputDecrypt: trigbackupNow");
            }
        }
    }

    private void doProcessInputDelete(SyncBean inputSyncBean) {
        DebugUtil.i(TAG, "doProcessInputDelete: " + inputSyncBean);
        List<SyncBean.SyncFileInfo> syncFileInfoList = inputSyncBean.getFileInfoList();
        boolean processSuccess = false;
        final String recorderRelativePath = Environment.DIRECTORY_MUSIC + File.separator + Constants.RECORDINGS;
        if (syncFileInfoList != null && syncFileInfoList.size() > 0) {
            for (SyncBean.SyncFileInfo syncFileInfo : syncFileInfoList) {
                String displayName = syncFileInfo.getDisplayName();
                String relativePath = syncFileInfo.getRelativePath();
                if (TextUtils.isEmpty(relativePath) || !relativePath.contains(recorderRelativePath)) {
                    continue;
                }
                String mD5 = syncFileInfo.getMd5();
                processSuccess |= RecorderDBUtil.getInstance(BaseApplication.getAppContext()).processEncrypDeleteAudioFile(displayName, relativePath, mD5);
            }
        }
        DebugUtil.i(TAG, "doProcessInputDelete: success: " + processSuccess);
    }

    private void doProcessInputLargeData(SyncBean inputSyncBean) {
        //trigLargeData, need to just this is the last broadcast
        RecordDataSync.getInstance().syncAllRecordDataFromMedia(BaseApplication.getAppContext(), false, true, CloudSyncAction.SYNC_TYPE_BACKUP);
    }


    private void doOtherTypeMsg(SyncBean inputSyncBean) {
        // show toast to notifyUsers
        final String msg = inputSyncBean.getMessage();
        if (!TextUtils.isEmpty(msg)) {
            ToastManager.showShortToast(BaseApplication.getAppContext(), msg);
        }
    }
}
