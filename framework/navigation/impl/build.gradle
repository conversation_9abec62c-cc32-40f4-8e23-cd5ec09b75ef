plugins {
    id 'com.android.library'
    id 'kotlin-kapt'
}

apply from: rootProject.file("script/compile.gradle")
apply from: rootProject.file("script/unitTest.gradle")

android {
    namespace 'com.documentsreader.navigation.impl'
}

dependencies {
    implementation libs.retrofit2.retrofit2
    implementation libs.retrofit2.converter
    implementation libs.retrofit2.adapter
    implementation libs.okhttp3.okhttp3
    implementation libs.commons.codec
    implementation libs.rxjava3.rxjava
    implementation libs.rxjava3.rxandroid
    // Koin
    implementation(libs.koin.android)
    implementation(libs.oplus.assistantscreen.plugin.mic.api)
    implementation(libs.oplus.assistantscreen.plugin.mic.interfaces)
    kapt(libs.oplus.assistantscreen.plugin.mic.processor)

    implementation project(":foundation:common")
    implementation project(":framework:navigation:interface")
    implementation(libs.coui.appcompat.core)
    implementation(libs.coui.appcompat.bottomnavigation)
    implementation(libs.coui.appcompat.responsiveui)
    implementation(libs.coui.appcompat.poplist)
}