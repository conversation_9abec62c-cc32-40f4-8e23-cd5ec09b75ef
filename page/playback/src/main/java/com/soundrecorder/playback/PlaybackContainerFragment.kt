/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlaybackContainerFragment
 * Description:
 * Version: 1.0
 * Date: 2022/11/4
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/11/4 1.0 create
 */

package com.soundrecorder.playback

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.DialogInterface
import android.content.DialogInterface.OnDismissListener
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.database.Cursor
import android.graphics.Typeface
import android.media.AudioManager
import android.net.Uri
import android.os.Bundle
import android.os.FileObserver
import android.provider.MediaStore
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.accessibility.AccessibilityEvent
import android.widget.AdapterView
import android.widget.TextView
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.children
import androidx.core.view.doOnLayout
import androidx.core.view.get
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.commit
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.searchview.COUISearchBar
import com.coui.appcompat.seekbar.COUISeekBar
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.backpressed.OnBackPressedListener
import com.soundrecorder.base.ext.currentInMsFormatTimeExclusive
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.base.ext.getValueWithDefault
import com.soundrecorder.base.ext.removeFragment
import com.soundrecorder.base.ext.replaceFragmentByTag
import com.soundrecorder.base.ext.suffix
import com.soundrecorder.base.userchange.OnFragmentUserChangeListener
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.FileUtils.core2Full
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.RecorderICUFormateUtils
import com.soundrecorder.base.utils.RecorderTextUtils
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.base.view.DeDuplicateInsetsCallback
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.buryingpoint.ConvertStaticsUtil
import com.soundrecorder.common.buryingpoint.CuttingStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.dialog.LoadingDialog
import com.soundrecorder.common.dialog.PositiveCallback
import com.soundrecorder.common.fileobserve.MultiFileObserver
import com.soundrecorder.common.fileobserve.OnFileEventListener
import com.soundrecorder.common.fileoperator.delete.DeleteFileDialog
import com.soundrecorder.common.fileoperator.delete.OnFileDeleteListener
import com.soundrecorder.common.fileoperator.recover.OnRecoverFileListener
import com.soundrecorder.common.fileoperator.recover.RecoverFileDialog
import com.soundrecorder.common.fileoperator.rename.RenameFileDialog
import com.soundrecorder.common.flexible.FollowDialogRestoreUtils
import com.soundrecorder.common.flexible.FollowRestoreCallBack
import com.soundrecorder.common.permission.PermissionDialogUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.share.IShareListener
import com.soundrecorder.common.share.OShareConvertUtil
import com.soundrecorder.common.share.ShareAction
import com.soundrecorder.common.share.ShareType
import com.soundrecorder.common.share.ShareTypeLink
import com.soundrecorder.common.share.ShareTypeNote
import com.soundrecorder.common.utils.FileDealUtil
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.utils.RecordFileChangeNotify
import com.soundrecorder.common.utils.SendSetUtil
import com.soundrecorder.common.utils.TimeSetUtils
import com.soundrecorder.common.utils.TipUtil
import com.soundrecorder.common.utils.VibrateUtils
import com.soundrecorder.common.utils.ViewUtils.setAnimatePressBackground
import com.soundrecorder.common.utils.sound.DeleteSoundEffectManager
import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.modulerouter.BrowseFileAction
import com.soundrecorder.modulerouter.EditRecordAction
import com.soundrecorder.modulerouter.SeedlingAction
import com.soundrecorder.modulerouter.cloudkit.CloudSyncAction
import com.soundrecorder.modulerouter.cloudkit.tipstatus.CloudTipManagerAction
import com.soundrecorder.modulerouter.mark.IIPictureMarkListener
import com.soundrecorder.modulerouter.mark.IPictureMarkDelegate
import com.soundrecorder.modulerouter.mark.IPictureMarkLifeOwnerProvider
import com.soundrecorder.modulerouter.mark.MarkAction
import com.soundrecorder.modulerouter.playback.PlaybackAction
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.PAGE_FROM_PLAYBACK
import com.soundrecorder.modulerouter.smartname.ISmartNameManager
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.TAB_TYPE_AUDIO
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.TAB_TYPE_CONVERT
import com.soundrecorder.playback.audio.PlaybackAudioFragment
import com.soundrecorder.playback.audio.setting.PlaySettingDialogFragment
import com.soundrecorder.playback.databinding.FragmentPlaybackContainerBinding
import com.soundrecorder.playback.databinding.IncludeActivityBottomButtonBinding
import com.soundrecorder.playback.newconvert.PlaybackConvertFragment
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_PROGRESS
import com.soundrecorder.playback.newconvert.search.ConvertSearchFragment
import com.soundrecorder.playback.newconvert.view.TransferAnimationTextView
import com.soundrecorder.playback.view.SegmentSeekBar
import com.soundrecorder.player.speaker.SpeakerModeController
import com.soundrecorder.player.speaker.SpeakerStateManager
import com.soundrecorder.player.status.PlayStatus
import com.soundrecorder.summary.RecordSummaryManager
import kotlinx.coroutines.launch
import java.util.Date
import kotlin.collections.set

@Suppress("LargeClass", "LongMethod")
class PlaybackContainerFragment : Fragment(), View.OnClickListener, OnFileEventListener,
    IIPictureMarkListener<MarkMetaData, MarkDataBean>, OnBackPressedListener,
    IPlayBackContainerListener,
    OnFragmentUserChangeListener {
    var mViewModel: PlaybackActivityViewModel? = null
    private var mBrowseFileActivityViewModel: ViewModel? = null
    private var speakerModeController: SpeakerModeController? = null

    private var mTabNames: Array<String>? = null
    private var mCurrentTabIndex: Int = TAB_TYPE_AUDIO

    private var mDeleteDialog: DeleteFileDialog? = null
    private var mDetailDialog: AlertDialog? = null
    private var mRenameDialog: RenameFileDialog? = null
    private var mSpeakerMenuItem: MenuItem? = null
    private var mDeleteMenuItem: MenuItem? = null
    private var mRecoverMenuItem: MenuItem? = null

    private var mStartEditLauncher: ActivityResultLauncher<Intent>? = null

    private var mAudioFragment: PlaybackAudioFragment? = null
    private var mConvertFragment: PlaybackConvertFragment? = null
    private var mConvertSearchFragment: ConvertSearchFragment? = null
    private var mPagerAdapter: FragmentStateAdapter? = null

    /*旧版分享txt，转文本文件超过50M的时候，显示“请稍后...”dialog
    链接分享,生成链接时，显示"正在生成…"dialog*/
    private var mLoadingDialog: LoadingDialog? = null
    private var mPageChangeCallback: ViewPager2.OnPageChangeCallback? = null

    //播放设置弹窗
    private var bottomSheetDialogFragment: COUIBottomSheetDialogFragment? = null
    private var mPlaybackConvertViewModel: PlaybackConvertViewModel? = null
    private var mPictureMarkHelper: IPictureMarkDelegate<MarkMetaData>? = null
    private lateinit var binding: FragmentPlaybackContainerBinding
    private lateinit var bottomButtonBinding: IncludeActivityBottomButtonBinding
    private var mLoadingViewControl: LoadingViewControl? = null
    private var bottomButtonHelper: ContainerBottomButtonHelper? = null
    private var typeface: Typeface? = null
    private var seekBar: SegmentSeekBar? = null

    private var searchAnimView: COUISearchBar? = null
    private var disalbeDialog: AlertDialog? = null

    private var mRecoverDialog: RecoverFileDialog? = null

    private var immersiveAnimationHelperInitial: Boolean? = null

    //切换沉浸态动画
    private val immersiveAnimationHelper: ConvertImmersiveAnimationHelper by lazy {
        immersiveAnimationHelperInitial = true
        ConvertImmersiveAnimationHelper(requireContext(), mViewModel, binding)
    }

    //系统任务栏的高度
    private var navigationHeight: Int? = null
    private var windowType: WindowType? = null

    //分享弹窗关闭监听
    private val shareDialogDismissListener by lazy {
        OnDismissListener {
            DebugUtil.i(TAG, "shareDialogDismissListener")
            mViewModel?.mNeedShowShareDialog?.value = false
        }
    }

    private var mToolbarOverflowPopupWindow: COUIPopupListWindow? = null
    private var mSubMenuCheckedPosition = 0
    private var isClickAiTitle: Boolean = false

    private var mSmartNameMangerImpl: ISmartNameManager? = null
    private var mSupportSmartName: Boolean = false
    private var mFilePermissionDialog: AlertDialog? = null

    private val shareListener = object : IShareListener {
        override fun onShowShareWaitingDialog(mediaId: Long, type: ShareType) {
            DebugUtil.i(TAG, "onShowShareWaitingDialog type:$type")
            activity?.runOnUiThread {
                showWaitingDialog(type)
            }
        }

        override fun onShareSuccess(mediaId: Long, type: ShareType) {
            DebugUtil.i(TAG, "onShareSuccess，type >> $type")
            activity?.runOnUiThread {
                dismissDialog()
            }
            unregisterShareListener()
        }

        override fun onShareFailed(mediaId: Long, type: ShareType, error: Int, message: String) {
            DebugUtil.i(TAG, "onShareFailed，type >> $type  error:$error  message:$message")
            activity?.runOnUiThread {
                dismissDialog()
            }
            unregisterShareListener()
        }
    }

    private fun unregisterShareListener() {
        ShareAction.unregisterShareListener(shareListener)
    }

    companion object {
        /*延迟showLoading时间，默认0*/
        const val ARG_KEY_SHOW_LOADING = "key_show_loading"

        const val MAX_PROGRESS = 1000
        const val THREE_SECONDS = 3000
        const val TIP_SHOW_DELAY_TIME = 300L
        const val SEARCH_INPUT_MAX_LENGTH = 50
        const val REQUEST_CODE_SYS_RENAME_AUTH = 211
        const val REQUEST_CODE_SYS_DELETE_AUTH = 212
        const val REQUEST_CODE_SYS_RECOVER_AUTH = 213

        private const val TAG = "PlaybackContainerFragment"

        private const val EKY_IS_IN_CONVERT_SEARCH = "key_is_in_convert_search"
        private const val KEY_IN_CONVERT_SEARCH_VALUE = "key_convert_search_value"
        private const val KEY_CONVERT_SEARCH_CURRENT_POS = "key_convert_search_current_pos"
        private const val KEY_CONVERT_SEARCH_LAST_POS = "key_convert_search_last_pos"
    }

    private val convertReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val mediaId = intent?.getLongExtra(PlaybackAction.KEY_NOTIFY_RECORD_ID, -1L) ?: -1L
            val isConvert = intent?.getBooleanExtra(PlaybackAction.KEY_NOTIFY_CONVERT_STATUS, false)
            DebugUtil.d(
                TAG, "ConvertReceiver, intent?.action:${intent?.action} , " +
                        "KEY_NOTIFY_CONVERT_STATUS:$isConvert ,KEY_NOTIFY_RECORD_ID:$mediaId "
            )
            when (intent?.action) {
                PlaybackAction.NOTIFY_CONVERT_STATUS_UPDATE -> {
                    if (isConvert == true && mediaId == mViewModel?.recordId) {
                        mPlaybackConvertViewModel?.updateConvertStatus(CONVERT_STATUS_PROGRESS)
                    }
                }

                PlaybackAction.NOTIFY_SMART_NAME_STATUS_UPDATE -> {
                    val resultName = intent.getStringExtra(PlaybackAction.KEY_NOTIFY_SMART_NAME_NAME_TEXT)
                    DebugUtil.d(TAG, "onReceive, smartName:$resultName")
                    if (!resultName.isNullOrBlank()) {
                        val suffix = mViewModel?.playPath?.value?.suffix()
                        mViewModel?.renameRecord(
                            core2Full(
                                resultName + suffix,
                                mViewModel?.playPath?.value
                            ), resultName + suffix
                        )
                    }
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBrowseFileActivityViewModel = BrowseFileAction.getBrowseActivityViewModel(activity as? AppCompatActivity)
        if (ScreenUtil.isSmallScreen(context) && (BrowseFileAction.getViewModelClickedToRecord(mBrowseFileActivityViewModel))) {
            /*进入了录制页面，再小屏下，退出播放详情*/
            activity?.supportFragmentManager?.commit {
                remove(this@PlaybackContainerFragment)
            }
            BrowseFileAction.clearViewModelPlayData(mBrowseFileActivityViewModel)
        }
        registerReceivers()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val rootView = inflater.inflate(R.layout.fragment_playback_container, container, false)
        binding = FragmentPlaybackContainerBinding.bind(rootView)
        bottomButtonBinding = IncludeActivityBottomButtonBinding.bind(binding.rootView)
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        DebugUtil.d(TAG, "onViewCreated, savedInstanceState is null:${savedInstanceState == null}")
        initViewModel(savedInstanceState)
        bottomButtonHelper = ContainerBottomButtonHelper()
        configToolbar()
        initStartLauncher()
        initTab(savedInstanceState != null)
        initSeekBar()
        checkShowLoading()
        initAccess()
        initBrowseActivityViewModelObserver()
        initViewModelObserver()
        initOtherViews()
        initiateWindowInsets(binding.rootView)
        initListener()
        checkDialogRestore(savedInstanceState != null)
        initSmartNameManagerImpl()
        initBottomButton()
        observe()
    }

    private fun initSmartNameManagerImpl() {
        if (mSmartNameMangerImpl == null) {
            mSmartNameMangerImpl = PlaybackAction.getSmartNameManager()
        }
    }

    private fun initBottomButton() {
        bottomButtonBinding.layoutMarkActivity.doOnLayout {
            bottomButtonHelper?.setPlaybackBottomViewPosition(
                bottomButtonBinding,
                mViewModel?.mPanelShowStatus?.value?.checkHasSummary() == true,
                mViewModel?.isSupportConvert() == true
            )
        }
        bottomButtonBinding.layoutConvertExportActivity.doOnLayout {
            bottomButtonHelper?.setConvertBottomViewPosition(
                bottomButtonBinding,
                mViewModel?.mShowSwitch?.value ?: false,
                mViewModel?.mPanelShowStatus?.value?.checkHasSummary() == true
            )
        }
    }

    private fun observe() {
        mViewModel?.showShareLinkPanel?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "observe showShareLinkPanel $it ${mViewModel?.currentLifecycleState}")
            if (it?.isNotEmpty() == true && mViewModel?.currentLifecycleState == Lifecycle.Event.ON_RESUME) {
                activity?.let { activity ->
                    ShareAction.showShareLinkPanel(activity, it, null)
                    mViewModel?.showShareLinkPanel?.value = null
                }
            }
        }

        mViewModel?.showShareToast?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "observe showShareToast $it ${mViewModel?.currentLifecycleState}")
            if (it != null && mViewModel?.currentLifecycleState == Lifecycle.Event.ON_RESUME) {
                ToastManager.showShortToast(context, it)
                mViewModel?.showShareToast?.value = null
            }
        }
    }

    private fun initViewModel(savedInstanceState: Bundle?) {
        (activity as? AppCompatActivity)?.let {
            val isFromOtherProcess = BrowseFileAction.getViewModelIsFromOther(mBrowseFileActivityViewModel)
            val supportConvertType = ConvertSupportManager.getConvertSupportType(!isFromOtherProcess)
            mPictureMarkHelper =
                MarkAction.newPictureMarkDelegate(object : IPictureMarkLifeOwnerProvider {

                    override fun provideLifeCycleOwner(): LifecycleOwner =
                        this@PlaybackContainerFragment

                    override fun provideActivity(): AppCompatActivity = it

                    override fun isFinishing(): Boolean =
                        ((activity?.isFinishing == true) || (<EMAIL>))
                }, (savedInstanceState != null), this)
            mPlaybackConvertViewModel =
                ViewModelProvider(this)[PlaybackConvertViewModel::class.java].apply {
                    if ((savedInstanceState != null) && supportConvertType != ConvertSupportManager.CONVERT_DISABLE) {
                        if (!recordFilterIsRecycle()) {
                            savedInstanceState.getBoolean(EKY_IS_IN_CONVERT_SEARCH, false)
                                .let { isConvertSearch ->
                                    DebugUtil.d(TAG, "initViewModel, isConvertSearch:$isConvertSearch")
                                    mIsInConvertSearch.value = isConvertSearch
                                }
                        }
                        mConvertSearchValue =
                            savedInstanceState.getString(KEY_IN_CONVERT_SEARCH_VALUE, "")
                        currentPos = savedInstanceState.getInt(KEY_CONVERT_SEARCH_CURRENT_POS, 0)
                        lastPos = savedInstanceState.getInt(KEY_CONVERT_SEARCH_LAST_POS, 0)
                    }
                }
            mViewModel = ViewModelProvider(this)[PlaybackActivityViewModel::class.java].apply {
                isRecycle = BrowseFileAction.getViewModelPlayData<StartPlayModel>(mBrowseFileActivityViewModel)?.value?.isRecycle ?: false
                mIsFromOtherApp = isFromOtherProcess
                convertSupportType = supportConvertType
                isAddPictureMarkingCallback = {
                    <EMAIL>?.isAddPictureMarking()
                        ?: MutableLiveData(false)
                }
                oShareConvertTextPath = activity?.intent?.getStringExtra(OShareConvertUtil.EXTRA_OSHARE_CONVERT_TEXT_PATH)
                /**
                 * 注：<1>一定要放在 isAddPictureMarkingCallback初始化之后
                 *    <2>需要mPlaybackConvertViewModel?.mIsInConvertSearch先初始化
                 */
                observeNotificationBtn(
                    savedInstanceState != null,
                    mPlaybackConvertViewModel?.mIsInConvertSearch
                )
            }
            mViewModel?.initLifecycle(this)
        }
        TimeSetUtils(viewLifecycleOwner) {
            mViewModel?.playerController?.playerState?.value = mViewModel?.playerController?.playerState?.value
        }
    }

    private fun initListener() {
        binding.buttonPanel.imgSpeed.setOnClickListener(this)
        binding.buttonPanel.imgForward.setOnClickListener(this)
        binding.buttonPanel.imgBackward.setOnClickListener(this)
        binding.buttonPanel.imgTrim.setOnClickListener(this)
        binding.buttonPanel.redCircleIcon.setOnClickListener(this)
        mViewModel?.setShareCallBack(shareListener)
    }

    private fun checkDialogRestore(isFromRestore: Boolean) {
        if (!isFromRestore) {
            DebugUtil.i(TAG, "checkDialogRestore not from restore, no need to check Dialog Show")
            return
        }
        if (mViewModel?.mNeedShowRenameDialog?.value == true) {
            DebugUtil.i(TAG, "checkDialogRestore showRenameDialog")
            showRenameDialog(mViewModel?.mRenameEditText)
        }
        if (mViewModel?.getNeedRestoreWaitingDialog() == true) {
            DebugUtil.i(TAG, "checkDialogRestore waitingDialog")
            //没有完成分享前置流程，则显示waitDialog
            showWaitingDialog(mViewModel?.getShareWaitingType())
        }
    }

    private fun findFragmentForPosition(position: Int): Fragment? {
        if (isAdded) {
            return childFragmentManager.findFragmentByTag("f" + mPagerAdapter?.getItemId(position))
        }
        return null
    }

    fun onPermissionGranted() {
        if (!PermissionUtils.hasReadAudioPermission()) {
            DebugUtil.i(TAG, "not hasReadAudioPermission")
            return
        }
        if (mViewModel?.recordId != null && mViewModel?.recordId != -1L) {
            mViewModel?.readMarkTag()
        }
    }

    fun getAudioFragment(): PlaybackAudioFragment? {
        if (mAudioFragment == null) {
            val fragment = findFragmentForPosition(0)
            if (fragment is PlaybackAudioFragment) {
                mAudioFragment = fragment
            }
        }
        return mAudioFragment
    }

    fun getConvertFragment(): PlaybackConvertFragment? {
        if (mConvertFragment == null) {
            val fragment = findFragmentForPosition(1)
            if (fragment is PlaybackConvertFragment) {
                mConvertFragment = fragment
            }
        }
        return mConvertFragment
    }

    private fun getConvertSearchFragment(): ConvertSearchFragment? {
        //判断isAdded，防止出现异常：java.lang.IllegalStateException: Fragment has not been attached yet.
        if (isAdded) {
            mConvertSearchFragment = childFragmentManager.findFragmentByTag(ConvertSearchFragment.TAG) as? ConvertSearchFragment
            if (mConvertSearchFragment == null) {
                mConvertSearchFragment = ConvertSearchFragment()
            }
            return mConvertSearchFragment
        }
        return null
    }

    private fun findConvertSearchFragment(): ConvertSearchFragment? {
        if (isAdded) {
            mConvertSearchFragment = childFragmentManager.findFragmentByTag(ConvertSearchFragment.TAG) as? ConvertSearchFragment
        }
        return mConvertSearchFragment
    }

    private fun initOtherViews() {
        adaptRtlView()
        bottomButtonBinding.layoutTransferTextActivity.setOnClickListener(this)
        bottomButtonBinding.layoutTransferTextActivity.setAnimatePressBackground()
        bottomButtonBinding.layoutMarkActivity.setOnClickListener(this)
        bottomButtonBinding.layoutMarkActivity.setAnimatePressBackground()
        bottomButtonBinding.layoutMarkPhotoActivity.setOnClickListener(this)
        bottomButtonBinding.layoutMarkPhotoActivity.setAnimatePressBackground()
        bottomButtonBinding.layoutMarkListActivity.setOnClickListener(this)
        bottomButtonBinding.layoutMarkListActivity.setAnimatePressBackground()
        bottomButtonBinding.layoutConvertRoleActivity.setOnClickListener(this)
        bottomButtonBinding.layoutConvertRoleActivity.setAnimatePressBackground()
        bottomButtonBinding.layoutConvertSearchActivity.setOnClickListener(this)
        bottomButtonBinding.layoutConvertSearchActivity.setAnimatePressBackground()
        bottomButtonBinding.layoutConvertExportActivity.setOnClickListener(this)
        bottomButtonBinding.layoutConvertExportActivity.setAnimatePressBackground()
        bottomButtonBinding.layoutSummaryActivity.setOnClickListener(this)
        bottomButtonBinding.layoutSummaryActivity.setAnimatePressBackground()
        bottomButtonBinding.layoutConvertSummaryActivity.setOnClickListener(this)
        bottomButtonBinding.layoutConvertSummaryActivity.setAnimatePressBackground()
    }

    private fun adaptRtlView() {
        if (BaseApplication.sIsRTLanguage) {
            // RTl语言下，快进快退图标也需要适配RTL
            binding.buttonPanel.imgBackward.setImageResource(R.drawable.ic_forward)
            binding.buttonPanel.imgForward.setImageResource(R.drawable.ic_backward)
        }
    }

    private fun initAccess() {
        //play button support accessibility. the view should be red_circle_icon, not the parent view middle_control
        binding.buttonPanel.redCircleIcon.accessibilityDelegate = object : View.AccessibilityDelegate() {
            override fun sendAccessibilityEvent(host: View, eventType: Int) {
                if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                    if (mViewModel?.playerController?.isWholePlaying() == true) {
                        binding.buttonPanel.redCircleIcon.contentDescription = resources.getString(
                            com.soundrecorder.common.R.string.recorder_pause
                        )
                    } else {
                        binding.buttonPanel.redCircleIcon.contentDescription = resources.getString(
                            com.soundrecorder.common.R.string.recorder_play
                        )
                    }
                }
                super.sendAccessibilityEvent(host, eventType)
            }
        }
    }

    override fun onResume() {
        DebugUtil.i(TAG, "onResume")
        super.onResume()
        checkDeleteOrRenameDialogOnResume()
    }

    override fun onPause() {
        DebugUtil.i(TAG, "onPause")
        super.onPause()
        if (speakerModeController?.mIsSpeakerOn?.value == false) {
            mViewModel?.playerController?.pausePlay()
        }
        releasePopupWindow()
    }

    private fun checkDeleteOrRenameDialogOnResume() {
        mViewModel?.let {
            if (it.isNeedSmartName && it.needSmartNameMediaList.isNotEmpty()) {
                startConvertAndSmartName(it.needSmartNameMediaList)
                it.isNeedSmartName = false
                it.needSmartNameMediaList.clear()
            }
        }
        if (mRenameDialog?.getOperating() == true) {
            DebugUtil.i(TAG, "check resume  rename")
            if (PermissionUtils.hasFilePermissionCompat()) {
                val suffix = mViewModel?.playPath?.value.suffix()
                val renameContent = mRenameDialog?.getRenameContent() ?: ""
                renameAgain(mViewModel?.playerController?.getPlayUri(), renameContent, suffix)
            }
            mRenameDialog?.resetOperating()
        }

        if (mDeleteDialog?.getOperating() == true) {
            DebugUtil.i(TAG, "check resume delete")
            if (PermissionUtils.hasFilePermissionCompat()) {
                val activity = activity ?: return
                val record = mViewModel?.getRecord() ?: return
                val isRecycle = mViewModel?.isRecycle ?: return
                val success = FileDealUtil.deleteRecord(activity, record, REQUEST_CODE_SYS_DELETE_AUTH, isRecycle)
                if (success) {
                    onDeleteRecordSuccess(isRecycle)
                    DeleteSoundEffectManager.getInstance().playDeleteSound()
                }
            }
            mDeleteDialog?.resetOperating()
        }

        if (mRecoverDialog?.getOperating() == true) {
            DebugUtil.i(TAG, "check resume ")
            if (PermissionUtils.hasFilePermissionCompat()) {
                val activity = activity ?: return
                val success = FileDealUtil.recoveryRecord(activity, getOperaRecord(), REQUEST_CODE_SYS_DELETE_AUTH)
                if (success) {
                    onRecoverRecordSuccess()
                }
            }
            mRecoverDialog?.resetOperating()
        }
    }

    private fun renameAgain(uri: Uri?, renameContent: String, suffix: String?): Boolean {
        if (FileDealUtil.renameAgain(uri, renameContent, suffix)) {
            mViewModel?.renameRecord(
                core2Full(renameContent + suffix, mViewModel?.playPath?.value),
                renameContent + suffix
            )
            notifyRefreshRecordList()
            return true
        }
        return false
    }

    private fun initStartLauncher() {
        //registerForActivityResult instead of  startActivityForResult
        mStartEditLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult? ->
            when (result?.resultCode) {
                Activity.RESULT_OK -> {

                    if (result.data?.getBooleanExtra(Constants.KEY_IS_CLIPPED_SAVE, false) == true) {
                        val newMediaId = result.data?.getLongExtra(Constants.KEY_CLIPPED_SAVE_RECORD_MEDIA_ID, -1) ?: -1
                        val isSmallWindow =
                            BrowseFileAction.getViewModelWindowType<WindowType>(mBrowseFileActivityViewModel)?.value == WindowType.SMALL
                        val playModel = if ((newMediaId > 0) && (!isSmallWindow)) {
                            /*裁切成功，若为父子级布局，则播放页面更新为裁切音频*/
                            StartPlayModel(newMediaId).apply {
                                isFromOtherApp = mViewModel?.mIsFromOtherApp ?: false
                            }
                        } else {
                            null
                        }
                        // 中大屏裁切保存新文件，右侧播放更新为新文件详情,小屏下保存成功回到列表页面
                        BrowseFileAction.setViewModelPlayData(mBrowseFileActivityViewModel, playModel)
                        notifyCutNewRecordChange()
                        if (isFromAppCard()) {
                            startToBrowseFile()
                        } else {
                            if (isSmallWindow) {
                                DebugUtil.d(TAG, "remove play fragment")
                                // 小屏从裁切回来,直接移除播放fragment，避免移除动效不执行，fragment没被移除
                                activity?.supportFragmentManager?.commit { remove(this@PlaybackContainerFragment) }
                            }
                        }
                    }
                }

                Constants.RESULT_CODE_FILEOBSERVER_FINISH -> BrowseFileAction.clearViewModelPlayData(mBrowseFileActivityViewModel)
            }
        }
    }

    private fun notifyCutNewRecordChange() {
        val intent = Intent(RecordFileChangeNotify.FILE_CUT_NEW_RECORD_ACTION)
        intent.putExtra(Constants.FRESH_FLAG, true)
        BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent)
    }

    private fun startToBrowseFile() {
        activity?.let {
            BrowseFileAction.createBrowseFileIntent(it)?.run {
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(this)
            }
            it.finish()
        }
    }

    private fun initTab(isFromRestore: Boolean) {
        val playModel = BrowseFileAction.getViewModelPlayData<StartPlayModel>(mBrowseFileActivityViewModel)?.value
        typeface = Typeface.create("sans-serif-medium", Typeface.NORMAL)
        mTabNames = if (playModel?.isRecycle == true) {
            arrayOf(getString(com.soundrecorder.common.R.string.playback_audio))
        } else {
            if (mViewModel?.isSupportConvert() == true) {
                arrayOf(
                    getString(com.soundrecorder.common.R.string.playback_audio),
                    getString(com.soundrecorder.common.R.string.convert_text)
                )
            } else {
                arrayOf(getString(com.soundrecorder.common.R.string.playback_audio))
            }
        }

        mPagerAdapter = object : FragmentStateAdapter(this@PlaybackContainerFragment) {
            override fun createFragment(position: Int): Fragment {
                //no invoke when activity re create here
                DebugUtil.i(TAG, "createFragment position $position")
                val resultFragment: Fragment = if (position == 0) {
                    PlaybackAudioFragment()
                } else {
                    PlaybackConvertFragment()
                }
                return resultFragment
            }

            override fun getItemCount(): Int {
                return mTabNames?.size ?: 0
            }
        }

        mPageChangeCallback = object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                DebugUtil.i(TAG, "onPageSelected position $position")
                super.onPageSelected(position)
                binding.tabLayout.onPageSelected(position)
                if (position == 1) {
                    checkNeedStartConvertStartAnimation()
                }
                getConvertFragment()?.onPageSelectedStateChange(position == 1)
                getAudioFragment()?.onPageSelectedStateChange(position == 0)
                BuryingPoint.addPlaySwitchTab(position)
            }

            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                binding.tabLayout.onPageScrollStateChanged(state)
            }

            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
                binding.tabLayout.onPageScrolled(position, positionOffset, positionOffsetPixels)
            }
        }

        binding.viewpager.apply {
            getChildAt(0).isNestedScrollingEnabled = false
            offscreenPageLimit = 1
            adapter = mPagerAdapter
            registerOnPageChangeCallback(mPageChangeCallback as ViewPager2.OnPageChangeCallback)
            for (i in 0 until mPagerAdapter!!.itemCount) {
                val newChild = TextView(context)
                newChild.text = mTabNames?.get(i)
                newChild.tag = if (i == 0) {
                    TAB_TYPE_AUDIO
                } else {
                    TAB_TYPE_CONVERT
                }
                binding.tabLayout.addView(newChild)
            }
        }
        binding.tabLayout.setOnSelectedSegmentChangeListener { _, p1, _ ->
            when (p1) {
                TAB_TYPE_AUDIO -> {
                    TipUtil.dismissSelf(TipUtil.TYPE_ROLE)
                    TipUtil.dismissSelf(TipUtil.TYPE_ROLE_NAME)
                }

                TAB_TYPE_CONVERT -> {
                    TipUtil.dismissSelf(TipUtil.TYPE_GUIDE)
                    dismissMenuPop()
                }
            }
            mCurrentTabIndex = p1
            mViewModel?.mCurrentTabType?.value = p1
            val textView = binding.tabLayout[p1]
            mViewModel?.mCurrentTabType?.value = textView.tag as Int
            mCurrentTabIndex = textView.tag as? Int ?: TAB_TYPE_AUDIO
            // 在viewPager不可见的时候，smooth 需要是false，否则会不生效
            binding.viewpager.setCurrentItem(p1, binding.body.isVisible)
        }

        /*deal default select-position from search*/
        if (!isFromRestore) {
            playModel?.selectPosInPlayback?.let {
                if ((it >= 0) && (it < (mTabNames?.size ?: 0))) {
                    binding.viewpager.setCurrentItem(it, false)
                }
            }
        }
    }

    private fun clickTransferTextSelectTab() {
        if (!recordFilterIsRecycle()) {
            binding.tabLayout.onPageSelected(TAB_TYPE_CONVERT)
        }
    }

    /**
     * 每次进入转文本页面，仅需要播放一次初始动效
     */
    private fun checkNeedStartConvertStartAnimation() {
        getConvertFragment()?.let {
            if ((mViewModel?.isFirstInConvertFragment == true) &&
                (it.mConvertManagerImpl?.getConvertStatus()?.value == PlaybackConvertViewModel.CONVERT_STATUS_INIT)
            ) {
                //本次进入没有执行过初始动画，则执行初始动画
                it.mConvertManagerImpl?.getConvertViewController()?.startConvertInitAnimation()
            }
            mViewModel?.isFirstInConvertFragment = false
        }
    }


    private fun initSeekBar() {
        layoutInflater.inflate(R.layout.layout_playback_seekbar, binding.buttonPanel.llSeekbarContainer, true)
        seekBar = binding.buttonPanel.llSeekbarContainer.findViewById(R.id.seek_bar)
        val seekBar = seekBar ?: return
        seekBar.max = MAX_PROGRESS
        seekBar.isHapticFeedbackEnabled = false
        seekBar.setOnSeekBarChangeListener(object : COUISeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: COUISeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    mViewModel?.let {
                        val seekToTime = progress * it.playerController.getDuration() / MAX_PROGRESS
                        it.needSyncRulerView = true
                        it.playerController.setOCurrentTimeMillis(seekToTime)
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: COUISeekBar?) {
                mViewModel?.let {
                    it.playerController.onStartTouchSeekBar()
                    if (it.playerController.isWholePlaying()) {
                        it.playerController.stopTimerNow()
                        getAudioFragment()?.waveStopMove()
                    }
                }
            }

            override fun onStopTrackingTouch(seekBar1: COUISeekBar?) {
                mViewModel?.let {
                    it.playerController.onStopTouchSeekBar()
                    seekBar.run {
                        val seekToTime = progress * it.playerController.getDuration() / MAX_PROGRESS
                        it.seekTime(seekToTime)
                    }
                    //按下之前是播放状态，松手后也应该继续播放。
                    it.playerController.onResetPlayState()
                }
            }
        })
    }

    private fun initBrowseActivityViewModelObserver() {
        BrowseFileAction.getViewModelPlayData<StartPlayModel>(mBrowseFileActivityViewModel)?.observe(viewLifecycleOwner) {
            if (it == null) {
                DebugUtil.i(TAG, "play data to be null")
                mViewModel?.cancelLoadData()
                return@observe
            }
            if (it.mediaId == mViewModel?.recordId) {
                DebugUtil.i(TAG, "play data id not change,id=${it.mediaId}")
                return@observe
            }
            DebugUtil.i(TAG, "play record data changed to $it")
            /*音频发生变化*/
            mViewModel?.startPlayModel = it
            mViewModel?.autoPlay = it.autoPlay

            /*音频发生变化时，如果当前处于转文本页面，则先隐藏转文本底部按钮*/
            if (mCurrentTabIndex == TAB_TYPE_CONVERT) {
                mViewModel?.mShowSwitch?.value = false
                mViewModel?.hasConvertContent?.value = false
            }
            onPermissionGranted()
        }
        BrowseFileAction.getViewModelWindowType<WindowType>(mBrowseFileActivityViewModel)?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "observe current windowType=$it")
            windowType = it
            if (FeatureOption.getIsFoldFeature()) {
                mViewModel?.mShowActivityControlView?.value = false
            } else {
                mViewModel?.mShowActivityControlView?.value = (it == WindowType.LARGE)
            }
            /*小屏且未处于转文本搜索页面才显示返回箭头*/
            setNavigationIcon()
            binding.buttonPanel.redCircleIcon.windowType = it
            mPlayStateChangeObserver.onChanged(mViewModel?.playerController?.playerState?.value ?: PlayStatus.PLAYER_STATE_INIT)
            correctBottomButtonWhenLayoutNotReCreate(it == WindowType.LARGE)
            correctBottomViewMarginBottom()
            /*windowType发生变化，更新下*/
            if (mViewModel?.isImmersiveState?.value == true) {
                immersiveAnimationHelper.checkUpdateWindowTypeChanged(navigationHeight ?: 0, it)
            }
        }

        mViewModel?.isImmersiveState?.observe(viewLifecycleOwner) {
            //界面重建时需要等节布局绘制完成在切换到沉浸态
            if (binding.buttonPanel.root.width == 0) {
                binding.buttonPanel.root.post {
                    switchImmersive(it)
                }
            } else {
                switchImmersive(it)
            }
        }

        mViewModel?.immersiveMoveDownDistance?.observe(viewLifecycleOwner) {
            val params = binding.buttonPanel.root.layoutParams as MarginLayoutParams
            params.bottomMargin = -it
            binding.buttonPanel.root.layoutParams = params
        }
    }

    /**
     * 矫正layout改变，应用未重建，导致加载layout同实际layout不匹配，底部操作按钮显示异常问题
     * 平板上 录音同其他应用分屏加载小中布局，其他应用切位浮窗后，录音退出分屏显示全屏，此时录音未重建，仍然加载的小中布局
     */
    private fun correctBottomButtonWhenLayoutNotReCreate(isLarge: Boolean) {
        /*回收站或折叠屏不显示横向摘要、转文本等操作按钮布局，设为0F*/
        if (recordFilterIsRecycle() || FeatureOption.getIsFoldFeature()) {
            binding.buttonPanel.toolCenterGuideLine.setGuidelinePercent(0F)
        } else {
            val guidePercent = (binding.buttonPanel.toolCenterGuideLine.layoutParams as ConstraintLayout.LayoutParams).guidePercent
            if (isLarge && guidePercent != NumberConstant.NUM_F0_5) {
                binding.buttonPanel.toolCenterGuideLine.setGuidelinePercent(NumberConstant.NUM_F0_5)
            } else if (!isLarge && guidePercent != 0F) {
                binding.buttonPanel.toolCenterGuideLine.setGuidelinePercent(0F)
            }
        }
    }

    private fun correctBottomViewMarginBottom() {
        binding.buttonPanel.middleControl.run {
            updateLayoutParams<MarginLayoutParams> {
                bottomMargin =
                    resources.getDimension(com.soundrecorder.common.R.dimen.circle_playback_button_margin_bottom).toInt()
                val newWidth = resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_diam).toInt()
                if (width != newWidth) {
                    width = newWidth
                    height = newWidth
                    binding.buttonPanel.redCircleIcon.refreshCircleRadius(
                        resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_radius)
                    )
                }
            }
        }
    }

    private fun setNavigationIcon() {
        val isConvertSearch = mPlaybackConvertViewModel?.mIsInConvertSearch?.value
        DebugUtil.d(TAG, "setNavigationIcon, isInConvertSearch:$isConvertSearch")
        if (isFromAppCard() || (BrowseFileAction.isSmallWindow(mBrowseFileActivityViewModel) && (isConvertSearch != true))) {
            binding.toolbar.setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow)
        } else {
            binding.toolbar.navigationIcon = null
        }
    }

    private fun initViewModelObserver() {
        mViewModel?.markEnable?.observe(viewLifecycleOwner) {
            refreshMarkEnable(it)
        }
        mViewModel?.playerController?.currentTimeMillis?.observe(viewLifecycleOwner) { currentTime ->
            refreshCurPlayTime(currentTime)
            if (mViewModel?.playerController?.mIsTouchSeekbar?.value == false) {
                refreshSeekBar(currentTime)
                /*若有target时间片段，未在时间片内，矫正播放时间片内数据*/
                mViewModel?.correctPlayTime(currentTime, mViewModel?.targetPlaySegment?.value) { seekTo, findCurOrNextNull ->
                    if (findCurOrNextNull && mViewModel?.playerController?.isWholePlaying() == true) {
                        mViewModel?.playerController?.doPausePlay()
                    }
                    mViewModel?.playerController?.seekTime(seekTo)
                }
            }
        }
        mViewModel?.playerController?.mDuration?.observe(viewLifecycleOwner) { duration ->
            binding.buttonPanel.tvDuration.contentDescription =
                TimeUtils.getContentDescriptionForTimeDuration(duration)
            binding.buttonPanel.tvDuration.text = duration.durationInMsFormatTimeExclusive(true)
            seekBar?.setDuration(duration)
        }
        //observe the playState forever, remember to release it
        mViewModel?.playerController?.playerState?.observeForever(mPlayStateChangeObserver)
        mViewModel?.playerController?.playSpeedIndex?.observe(viewLifecycleOwner) {
            setPlaySettingIcon()
            binding.buttonPanel.imgSpeed.contentDescription = getString(com.soundrecorder.common.R.string.play_setting_title)
            mViewModel?.playerController?.changePlayerSpeed()
        }

        mViewModel?.muteDataManager?.muteEnable?.observe(viewLifecycleOwner) {
            setPlaySettingIcon()
        }

        val loadAmpObserver = Observer<Boolean> {
            if (it) {
                seekBar?.isEnabled = true // 拿到波形数据，恢复按钮点击状态
                if (BrowseFileAction.getViewModelAnimRunning(mBrowseFileActivityViewModel)?.value != true) {
                    // 小屏动效执行完
                    PermissionUtils.checkNotificationPermission(activity)
                    hideLoading()
                }
            }
        }
        mViewModel?.mIsDecodeReady?.observe(viewLifecycleOwner, loadAmpObserver)
        mViewModel?.isPrepareAmplitudeAndMark?.observe(viewLifecycleOwner, loadAmpObserver)
        /*animEnd默认为null，作用等同于true的*/
        BrowseFileAction.getViewModelAnimRunning(mBrowseFileActivityViewModel)?.observe(viewLifecycleOwner) {
            if (!it && (mViewModel?.loadAmpSuccess() == true)) {
                PermissionUtils.checkNotificationPermission(activity)
                hideLoading()
            }
        }

        BrowseFileAction.getDeleteSummaryNoteIdLiveData(mBrowseFileActivityViewModel)?.observe(viewLifecycleOwner) {
            mViewModel?.handleClearSummaryCallId(it)
        }

        mViewModel?.isShowMarkList?.observe(viewLifecycleOwner) { isShowing ->
            bottomButtonBinding.tvMarkListActivity.setAnimateSelected(isShowing)
        }
        mPlaybackConvertViewModel?.isSpeakerRoleShowing?.observe(viewLifecycleOwner) { needShowRole ->
            bottomButtonBinding.tvConvertRoleActivity.setAnimateSelected(needShowRole)
            if (needShowRole) {
                getConvertFragment()?.stopScroll()
            } else {
                getConvertFragment()?.mConvertManagerImpl?.removeSpeakerTipTask()
                /*关闭讲话人，重置目标静音片、筛选讲话人数据*/
                mPlaybackConvertViewModel?.selectSpeakerList = null
                mViewModel?.targetPlaySegment?.value = null
            }
        }

        mViewModel?.mPanelShowStatus?.observe(viewLifecycleOwner) {
            if (recordFilterIsRecycle()) {
                bottomButtonBinding.convertLayout.isVisible = false
                bottomButtonBinding.layoutConvertRoleActivity.isVisible = false //讲话人
                bottomButtonBinding.layoutConvertSearchActivity.isVisible = false //內容搜索
                bottomButtonBinding.layoutSummaryActivity.isVisible = false //生成摘要
                bottomButtonBinding.layoutConvertSummaryActivity.isVisible = false //生成摘要
                bottomButtonBinding.layoutConvertExportActivity.isVisible = false //分享文本

                bottomButtonBinding.markLayout.isVisible = false
                bottomButtonBinding.layoutMarkPhotoActivity.isVisible = false //图片标记
                bottomButtonBinding.layoutTransferTextActivity.isVisible = false //转文本
                bottomButtonBinding.layoutMarkActivity.isVisible = false //标记
                bottomButtonBinding.layoutMarkListActivity.isVisible = false //标记列表
                /*隱藏倍速等*/
                binding.buttonPanel.imgSpeed.isVisible = false
                binding.buttonPanel.imgTrim.isVisible = false
                bottomButtonBinding.markLayout.isVisible = false
            } else {
                val outerConvertGroupShow = it.checkOuterConvertGroupShow()
                bottomButtonBinding.convertLayout.isVisible = outerConvertGroupShow
                bottomButtonBinding.layoutConvertRoleActivity.isVisible =
                    (outerConvertGroupShow && it.mConvertShowSwitch)
                bottomButtonBinding.layoutConvertSearchActivity.isVisible =
                    (outerConvertGroupShow && FunctionOption.IS_SUPPORT_CONVERT_SEARCH)

                val outMarkGroupShow = it.checkOuterMarkGroupShow()
                bottomButtonBinding.markLayout.isVisible = outMarkGroupShow
                bottomButtonBinding.layoutMarkPhotoActivity.isVisible = outMarkGroupShow
                bottomButtonBinding.layoutTransferTextActivity.isVisible =
                    (outMarkGroupShow && mViewModel?.isSupportConvert() == true)
                val summaryVisible = it.checkHasSummary()
                bottomButtonBinding.layoutSummaryActivity.isVisible =
                    outMarkGroupShow && summaryVisible
                bottomButtonBinding.layoutConvertSummaryActivity.isVisible =
                    outerConvertGroupShow && summaryVisible

                bottomButtonBinding.layoutMarkActivity.isVisible = outMarkGroupShow //标记
                bottomButtonBinding.layoutMarkListActivity.isVisible = outMarkGroupShow //标记列表
            }
        }

        mViewModel?.mControlSpeakerTip?.observe(viewLifecycleOwner) {
            if (!recordFilterIsRecycle() && it.checkOuterRoleSplitFirstTipNeedShow()) {
                addControlSpeakerTipTaskOuter()
            } else {
                removeControlSpeakerTipTaskOuter()
            }
        }
        mPlaybackConvertViewModel?.mConvertStatus?.observe(viewLifecycleOwner) {
            val canPlayAnim = bottomButtonBinding.viewTransferTextActivity.isVisible
            DebugUtil.i(TAG, "convertViewModelChange mConvertStatus changed: $it, $canPlayAnim")
            bottomButtonBinding.viewTransferTextActivity.updateTransferState(it, canPlayAnim)
        }

        mViewModel?.mMimeType?.observe(viewLifecycleOwner) {
            if (!isSupportAudioCut(it)) {
                binding.buttonPanel.imgTrim.isEnabled = false
                binding.buttonPanel.imgTrim.alpha = NumberConstant.NUM_F0_15
            } else {
                binding.buttonPanel.imgTrim.isEnabled = true
                binding.buttonPanel.imgTrim.alpha = 1f
            }
        }
        mPlaybackConvertViewModel?.mIsInConvertSearch?.observe(viewLifecycleOwner) {
            setNavigationIcon()
            inAndOutConvertSearchFragment(it)
            BrowseFileAction.onConvertSearchStateChanged(activity, it, !mPlaybackConvertViewModel?.mConvertSearchValue.isNullOrBlank()) {
                findConvertSearchFragment()?.onClickCancel()
            }
        }
        mViewModel?.mSummaryStatus?.observe(viewLifecycleOwner) {
            val context = BaseApplication.getAppContext()
            when (it) {
                RecordSummaryManager.SUMMARY_STATE_CLIENT_END -> {
                    context.resources.getString(com.soundrecorder.common.R.string.view_summary).let { text ->
                        bottomButtonBinding.tvSummaryActivity.setBottomText(text)
                        bottomButtonBinding.tvConvertSummaryActivity.setBottomText(text)
                    }
                    bottomButtonBinding.tvSummaryActivity.showDefaultImage()
                    bottomButtonBinding.tvConvertSummaryActivity.showDefaultImage()
                }

                RecordSummaryManager.SUMMARY_STATE_CLIENT_GENERATING -> {
                    context.resources.getString(com.soundrecorder.common.R.string.summary_generating).let { text ->
                        bottomButtonBinding.tvSummaryActivity.setBottomText(text)
                        bottomButtonBinding.tvConvertSummaryActivity.setBottomText(text)
                    }
                    bottomButtonBinding.tvSummaryActivity.startRunAnim()
                    bottomButtonBinding.tvConvertSummaryActivity.startRunAnim()
                }

                else -> {
                    context.resources.getString(com.soundrecorder.common.R.string.generate_summary).let { text ->
                        bottomButtonBinding.tvSummaryActivity.setBottomText(text)
                        bottomButtonBinding.tvConvertSummaryActivity.setBottomText(text)
                    }
                    bottomButtonBinding.tvSummaryActivity.showDefaultImage()
                    bottomButtonBinding.tvConvertSummaryActivity.showDefaultImage()
                }
            }
        }
        mViewModel?.targetPlaySegment?.observe(viewLifecycleOwner) { timeSegmentList ->
            /*更新seekbar 时间片高亮状态*/
            seekBar?.setSegmentTimeMillList(timeSegmentList)
            if (timeSegmentList?.isNotEmpty() == true) {
                /**设置目标静音片后,若当前暂停状态，需要矫正当前时间在时间片内*/
                mViewModel?.run {
                    resetCurrentPlaySegment()
                    if (playerController.hasPaused()) {
                        correctPlayTime(playerController.currentTimeMillis.getValueWithDefault(), timeSegmentList) { seekTo, _ ->
                            DebugUtil.i(TAG, " target changed, seek to $seekTo")
                            playerController.seekTime(seekTo)
                        }
                    }
                }
            }
        }
        initSpeakerMenuObservers()
        initDialog()
    }

    /**
     * 是否支持裁切
     * @param mimeType 音频文件mimeType
     * @return boolean true: 支持 false：不支持
     */
    private fun isSupportAudioCut(mimeType: String?): Boolean {
        var supportMime = arrayOf(
            RecordConstant.MIMETYPE_MP3,
            RecordConstant.MIMETYPE_AMR,
            RecordConstant.MIMETYPE_AMR_WB,
            RecordConstant.MIMETYPE_3GPP
        )
        // 品牌融合后，支持aac和wav
        if (FunctionOption.IS_SUPPORT_WAV_AND_AAC) {
            supportMime = supportMime
                .plus(RecordConstant.MIMETYPE_ACC)
                .plus(RecordConstant.MIMETYPE_ACC_ADTS)
                .plus(RecordConstant.MIMETYPE_WAV)
        }
        return (mimeType in supportMime)
    }

    private fun setPlaySettingIcon() {
        val isNormal = (mViewModel?.playerController?.playSpeedIndex?.value == 1 && mViewModel?.muteDataManager?.muteEnable?.value == false)
        val resId = if (isNormal) R.drawable.ic_play_setting_off else R.drawable.ic_play_setting_on
        binding.buttonPanel.imgSpeed.setImageResource(resId)
    }

    private fun refreshCurPlayTime(seekToTime: Long) {
        binding.buttonPanel.tvCurrent.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(seekToTime)
        binding.buttonPanel.tvCurrent.text = seekToTime.currentInMsFormatTimeExclusive(mViewModel?.playerController?.getDuration())
    }

    private fun refreshSeekBar(currentTime: Long) {
        val duration = mViewModel?.playerController?.getDuration() ?: 0
        if (duration > 0) {
            val seekBar = seekBar ?: return
            seekBar.progress = (currentTime * MAX_PROGRESS / (duration)).toInt()
            seekBar.contentDescription = RecorderTextUtils.getNewProgressDescription(
                BaseApplication.getAppContext(), currentTime, duration
            )
            seekBar.accessibilityDelegate = object : View.AccessibilityDelegate() {
                override fun sendAccessibilityEvent(host: View, eventType: Int) {
                    if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                        seekBar.contentDescription = RecorderTextUtils.getNewProgressDescription(
                            BaseApplication.getAppContext(), currentTime, duration
                        )
                    }
                    super.sendAccessibilityEvent(host, eventType)
                }
            }
        }
    }

    private fun refreshMarkEnable(isMarkEnable: Boolean) {
        bottomButtonBinding.tvMarkActivity.isEnabled = isMarkEnable
        bottomButtonBinding.layoutMarkPhotoActivity.isEnabled = isMarkEnable
    }

    private fun addControlSpeakerTipTaskOuter() {
        DebugUtil.i(TAG, "addControlSpeakerTipTaskOuter")
        TipUtil.checkShow(
            { if (isOnConvertWhenViewPager2IDLE()) bottomButtonBinding.layoutConvertRoleActivity else null },
            TipUtil.TYPE_ROLE,
            TIP_SHOW_DELAY_TIME,
            lifecycle,
            activity?.isInMultiWindowMode ?: false
        )
    }

    private fun removeControlSpeakerTipTaskOuter() {
        TipUtil.dismissSelf(TipUtil.TYPE_ROLE)
    }

    private fun initDialog() {
        mViewModel?.mNeedShowRecoverDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showRecoverDialog()
            }
        }
        mViewModel?.mNeedShowRecycleDeleteDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showDeleteDialog(CloudTipManagerAction.isCloudSwitchOn(), true)
            }
        }
        mViewModel?.mNeedShowDeleteDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showDeleteDialog(CloudTipManagerAction.isCloudSwitchOn(), false)
            }
        }
        mViewModel?.mNeedShowDetailDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showDetailDialog()
            }
        }
        mViewModel?.mNeedShowSpeedDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showPlaySettingDialog()
            }
        }
        mViewModel?.mNeedShowShareDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                processExport(null)
            }
        }
    }

    private fun hideLoading() {
        val showTab = if (recordFilterIsRecycle()) {
            false
        } else {
            ((mTabNames?.size ?: 0) > 1) && (mPlaybackConvertViewModel?.mIsInConvertSearch?.value != true)
        }
        mLoadingViewControl?.hideLoading(showTab)
    }

    /**
     * 检查并设置加载状态和Tab显示
     * 注意：此方法不再处理seekBar的启用状态，该逻辑已移至updateSeekBarState方法
     */
    private fun checkShowLoading() {
        if (mLoadingViewControl == null) {
            mLoadingViewControl = LoadingViewControl(lifecycle, binding.colorLoadView.colorLoadView, binding.tabLayout, binding.body)
        }
        val needShowLoading = arguments?.getBoolean(ARG_KEY_SHOW_LOADING, false) ?: false
        mLoadingViewControl?.showLoading(needShowLoading, canShowTabLayout())
        if (!needShowLoading) {
            // 默认不showLoading场景下，禁止拖动seekbar
            seekBar?.isEnabled = mViewModel?.loadAmpSuccess() ?: false
        }
    }

    private fun canShowTabLayout(): Boolean {
        return !recordFilterIsRecycle() && (mTabNames?.size ?: 0) > 1
    }

    private fun initSpeakerMenuObservers() {
        speakerModeController = BrowseFileAction.getViewModelSpeakerModeController<SpeakerModeController>(mBrowseFileActivityViewModel)
        speakerModeController?.mSpeakerUiMode?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "initSpeakerMenuObservers the mode is $it")
            changeActivityVolumeStream(
                if (it == SpeakerStateManager.SPEAKER_OFF_WITHOUT_HEADSET) {
                    AudioManager.STREAM_VOICE_CALL
                } else AudioManager.STREAM_MUSIC
            )

            when (it) {
                SpeakerStateManager.SPEAKER_OFF_WITHOUT_HEADSET -> {
                    mSpeakerMenuItem?.setIcon(com.soundrecorder.common.R.drawable.ic_small_speaker_red)
                    mSpeakerMenuItem?.contentDescription =
                        getString(com.soundrecorder.common.R.string.talk_back_handset_play)
                    mSpeakerMenuItem?.isEnabled = true
                }

                SpeakerStateManager.SPEAKER_ON_WITHIN_HEADSET -> {
                    /*don't be grey on androidS*/
                    val icon = if (BaseUtil.isAndroidSOrLater) {
                        com.soundrecorder.common.R.drawable.ic_big_speaker_black
                    } else {
                        com.soundrecorder.common.R.drawable.ic_big_speaker_gray
                    }
                    mSpeakerMenuItem?.setIcon(icon)
                    mSpeakerMenuItem?.contentDescription =
                        resources.getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
                }

                SpeakerStateManager.SPEAKER_OFF_WITHIN_HEADSET -> {
                    /*don't be grey on androidS*/
                    val icon = if (BaseUtil.isAndroidSOrLater) {
                        com.soundrecorder.common.R.drawable.ic_small_speaker_red
                    } else {
                        com.soundrecorder.common.R.drawable.ic_small_speaker_gray
                    }
                    mSpeakerMenuItem?.setIcon(icon)
                    mSpeakerMenuItem?.contentDescription =
                        resources.getString(com.soundrecorder.common.R.string.talk_back_handset_play)
                }

                SpeakerStateManager.SPEAKER_ON_WITHOUT_HEADSET -> {
                    mSpeakerMenuItem?.setIcon(com.soundrecorder.common.R.drawable.ic_big_speaker_black)
                    mSpeakerMenuItem?.contentDescription =
                        getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
                    mSpeakerMenuItem?.isEnabled = true
                }

                else -> {
                    mSpeakerMenuItem?.setIcon(com.soundrecorder.common.R.drawable.ic_big_speaker_black)
                    mSpeakerMenuItem?.contentDescription =
                        getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
                    mSpeakerMenuItem?.isEnabled = true
                }
            }
        }

        speakerModeController?.mIsSpeakerOn?.observe(viewLifecycleOwner) {
            mViewModel?.playerController?.replay()
        }
        speakerModeController?.mPlayerCommand?.observe(viewLifecycleOwner) {
            if (it == SpeakerModeController.PLAYER_COMMAND_PAUSE) {
                mViewModel?.playerController?.pausePlay()
            }
        }
    }

    private fun changeActivityVolumeStream(streamType: Int? = AudioManager.STREAM_MUSIC) {
        DebugUtil.d(TAG, "changeActivityVolumeStream  $streamType")
        activity?.volumeControlStream = streamType ?: AudioManager.STREAM_MUSIC
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.img_speed -> {
                if (mViewModel?.checkLoadAmpFinished() != true) { // 波形还未解析出来，不能使用倍速功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false")
                    return
                }
                BuryingPoint.addPlaySetting()
                mViewModel?.mNeedShowSpeedDialog?.postValue(true)
            }

            R.id.img_forward -> {
                if (mViewModel?.checkLoadAmpFinished() != true) { // 波形还未解析出来，不能使用快进功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false")
                    return
                }
                BuryingPoint.addFastForward()
                mViewModel?.forwardOrBackWard(true)
            }

            R.id.img_backward -> {
                if (mViewModel?.checkLoadAmpFinished() != true) { // 波形还未解析出来，不能使用快退功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false")
                    return
                }
                BuryingPoint.addFastBack()
                mViewModel?.forwardOrBackWard(false)
            }

            R.id.img_trim -> {
                if (ClickUtils.isFastDoubleClick()) {
                    DebugUtil.d(TAG, "onOplusOptionsMenuItemSelected() isFastDoubleClick return")
                    return
                }
                if (mViewModel?.ampList?.value.isNullOrEmpty()) {
                    ToastManager.showShortToast(
                        BaseApplication.getAppContext(),
                        com.soundrecorder.common.R.string.amplitues_not_ready
                    )
                    return
                }
                EditRecordAction.createEditRecordIntent(v.context)?.let {
                    it.putExtra("playPath", mViewModel?.playPath?.value)
                    it.putExtra("playName", mViewModel?.playName?.value)
                    it.putExtra("recordType", mViewModel?.recordType?.value)
                    it.putExtra("recordId", mViewModel?.recordId)
                    it.putExtra("isFromOtherApp", mViewModel?.mIsFromOtherApp)
                    it.putExtra("mimeType", mViewModel?.mMimeType?.value)
                    it.putParcelableArrayListExtra(
                        "mark", ArrayList(
                            mViewModel?.getMarkList()?.value
                                ?: arrayListOf()
                        )
                    )
                    mStartEditLauncher?.launch(it)
                    activity?.overridePendingTransition(
                        com.soundrecorder.common.R.anim.enter_bottom,
                        com.soundrecorder.common.R.anim.exit_top
                    )
                    mViewModel?.playerController?.pausePlay()
                    CuttingStaticsUtil.addPlayMoreTrim()
                }
            }

            R.id.red_circle_icon -> {
                if (recordFilterIsRecycle() && !PermissionUtils.hasAllFilePermission()) {
                    activity?.let {
                        PermissionDialogUtils.showPermissionAllFileAccessDialog(
                            it,
                            object : PermissionDialogUtils.PermissionDialogListener {
                                override fun onClick(alertType: Int, isOk: Boolean, permissions: ArrayList<String>?) {
                                    if (isOk) {
                                        PermissionUtils.goToAppAllFileAccessConfigurePermissions(it)
                                    }
                                }
                            })
                        return
                    }
                }
                if (mViewModel?.checkLoadAmpFinished() != true) { // 波形还未解析出来，不能使用播放功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false")
                    return
                }
                mViewModel?.recordControlClick()
            }

            R.id.layout_transfer_text_activity -> {
                if (!ClickUtils.isQuickClick()) {
                    DebugUtil.d(TAG, "onClick layout_transfer_text_activity:")
                    clickTransferTextSelectTab()
                    if (mPlaybackConvertViewModel?.mConvertStatus?.value == null) {
                        mPlaybackConvertViewModel?.mNeedTransConvert = true
                    } else {
                        mPlaybackConvertViewModel?.mConvertStatus?.value?.let {
                            if (it in intArrayOf(
                                    PlaybackConvertViewModel.CONVERT_STATUS_INIT,
                                    PlaybackConvertViewModel.CONVERT_STATUS_CANCEL,
                                    PlaybackConvertViewModel.CONVERT_STATUS_USERTIMEOUT
                                )
                            ) {
                                isClickAiTitle = false
                                getConvertFragment()?.mConvertManagerImpl?.convertStartClickHandle()
                            }
                        }
                    }
                    val transferAnimationTextView = activity?.findViewById<TransferAnimationTextView>(R.id.view_transfer_text_activity)
                    if (transferAnimationTextView?.getText() == getString(com.soundrecorder.common.R.string.transfer_text)) {
                        val duration = mViewModel?.playerController?.getDuration()
                        ConvertStaticsUtil.addConvertFileDuration(duration)
                        ConvertStaticsUtil.addConvertText()
                    } else if (transferAnimationTextView?.getText() == getString(com.soundrecorder.common.R.string.transfer_view_text)) {
                        ConvertStaticsUtil.addLookText()
                    }
                }
            }

            R.id.layout_mark_activity -> {
                if (mViewModel?.checkLoadAmpFinished() != true) {
                    // 波形还未解析出来，不能使用标记功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false")
                    return
                }
                DebugUtil.d(TAG, "onClick layout_mark_activity:")
                val markMetaData = MarkMetaData("", "", currentTimeMillis = mViewModel?.playerController?.currentTimeMillis?.value ?: 0, -1, -1)
                val result = mViewModel?.addMark(false, markMetaData) ?: -1
                if (result >= 0) {
                    getAudioFragment()?.mBinding?.waveRecyclerview?.addMarkData =
                        mViewModel?.markHelper?.getMark(result)
                }
                BuryingPoint.addClickPlayTextMark()
            }

            R.id.layout_mark_photo_activity -> {
                if (mViewModel?.checkLoadAmpFinished() != true) {
                    // 波形还未解析出来，不能使用标记功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false")
                    return
                }
                /**
                 * 存在两套布局  小屏中屏使用PlaybackAudioFragment布局
                 * 大屏使用PlaybackContainerFragment布局
                 * 点击事件同一走此方法，PlaybackAudioFragment 调用了callOnClick
                 */
                clickShowSelectPictureDialog()
                BuryingPoint.pictureMarkOnClickNumber()
            }

            R.id.layout_mark_list_activity -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }
                if (mViewModel?.checkLoadAmpFinished() != true) {
                    // 波形还未解析出来，不能使用标记列表切换功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false")
                    return
                }
                val toIsShow = !(mViewModel?.isShowMarkList?.value ?: false)
                mViewModel?.isShowMarkList?.postValue(toIsShow)
            }

            R.id.layout_convert_role_activity -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }
                mPlaybackConvertViewModel?.swithSpeakerRoleState()
            }

            R.id.layout_convert_search_activity -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }
                // 记录滚动位置
                getConvertFragment()?.mConvertManagerImpl?.getConvertViewController()?.saveScrollPosition("convert_activity")

                mPlaybackConvertViewModel?.also {
                    // 统计
                    it.clickConvertSearchCount++
                    // 进入转文本搜索
                    it.mIsInConvertSearch.value = true
                }
            }

            R.id.layout_convert_export_activity -> {
                DebugUtil.i(
                    TAG, "layout_convert_export Click mConvertFragment: ${getConvertFragment()}, " +
                            "mConvertFragment?.mConvertManagerImpl?${mConvertFragment?.mConvertManagerImpl}"
                )
                processExportText(v)
            }

            R.id.layout_summary_activity, R.id.layout_convert_summary_activity -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }
                if (mViewModel?.mSummaryStatus?.value != RecordSummaryManager.SUMMARY_STATE_CLIENT_INIT) {
                    mViewModel?.toNotesSummaryActivity(requireActivity(), Constants.RESULT_CODE_PLAYBACK_SUMMARY) { clearSummary, dialog ->
                        if (clearSummary) {
                            mViewModel?.getNoteData()
                            notifyRefreshRecordList()
                        } else {
                            <EMAIL> = dialog
                        }
                    }
                } else {
                    mViewModel?.startSummary(mPlaybackConvertViewModel?.convertContentData)
                }
            }
        }
    }

    private fun processExport(anchor: View?) {
        if (ClickUtils.isQuickClick()) {
            return
        }
        getConvertFragment()?.mConvertManagerImpl?.processExport(anchor, shareDialogDismissListener)
    }

    private fun processExportText(anchor: View?) {
        if (ClickUtils.isQuickClick()) {
            return
        }
        getConvertFragment()?.mConvertManagerImpl?.processExportText(anchor)
    }

    fun checkIsRestoreMarkPictureDialog() {
        if (mViewModel?.mNeedShowSelectPictureDialog?.value == true) {
            FollowDialogRestoreUtils.followDialogRestore(
                activity?.window?.decorView,
                true,
                object : FollowRestoreCallBack {
                    override fun restoreCallBack() {
                        mPictureMarkHelper?.postShowSelectPictureDialog(getAnchorView())
                    }
                }
            )
        }
    }

    private fun clickShowSelectPictureDialog() {
        DebugUtil.d(TAG, "onClick layout_mark_photo_activity:")
        if (ClickUtils.isFastDoubleClick()) {
            DebugUtil.d(TAG, "layout_mark_photo_activity fast double click")
            return
        }
        if (mPictureMarkHelper?.checkNeedAddMark() == true) {
            mPictureMarkHelper?.postShowSelectPictureDialog(getAnchorView())
            mViewModel?.isShowMarkList?.postValue(true)
        }
    }

    private fun getAnchorView(): View? {
        return if (ScreenUtil.isBelowMiddleScreen()) {
            getAudioFragment()?.getPictureBtnView()
        } else {
            findPictureBtnView()
        }
    }

    private fun findPictureBtnView(): View? {
        return binding.rootView.findViewById(R.id.layout_mark_photo_activity)
    }

    fun findConvertBtnView(): View? {
        return binding.rootView.findViewById(R.id.layout_convert_export_activity)
    }

    private fun showRecoverDialog() {
        if (mRecoverDialog?.isShowing() == true) {
            DebugUtil.i(TAG, "mDeleteDialog is showing")
            return
        }
        mRecoverDialog =
            activity?.let {
                RecoverFileDialog(
                    it,
                    getString(com.soundrecorder.common.R.string.recycle_tips_restore_record_title),
                    getString(com.soundrecorder.common.R.string.recycle_delete_recover)
                )
            }
        mRecoverDialog?.mOnFileRecoverListener = object : OnRecoverFileListener {
            override fun onRecoverFileResult(deleteSuccess: Boolean) {
                if (deleteSuccess) {
                    onRecoverRecordSuccess()
                }
            }

            override fun provideRecoverRequestCode(): Int {
                return REQUEST_CODE_SYS_RECOVER_AUTH
            }
        }
        mRecoverDialog?.mHideListener = OnDismissListener {
            if (mRecoverDialog != null) {
                mViewModel?.mNeedShowRecoverDialog?.postValue(false)
            }
        }
        mRecoverDialog?.showRecoverDialog(getOperaRecord())
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[RecorderUserAction.KEY_RECYCLE_RECOVER] = RecorderUserAction.VALUE_MORE_DELETE
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), RecorderUserAction.USER_ACTION_PLAY_MORE, RecorderUserAction.EVENT_PLAY_MORE,
            eventInfo, false
        )
    }

    private fun onRecoverRecordSuccess() {
        notifyRefreshRecordList()
        BrowseFileAction.clearViewModelPlayData(mBrowseFileActivityViewModel)
        //埋点
        BuryingPoint.addPlayRecycleRecoverSuccess()
    }

    private fun showDeleteDialog(isCloudOn: Boolean, isRecycle: Boolean) {
        DebugUtil.d(TAG, "showDeleteDialog, isRecycle:$isRecycle")
        if (mDeleteDialog?.isShowing() == true) {
            DebugUtil.i(TAG, "mDeleteDialog is showing")
            return
        }
        val activity = activity ?: return
        mDeleteDialog = DeleteFileDialog(
            activity, getString(com.soundrecorder.common.R.string.record_delete_title),
            getDeleteDialogMessage(isCloudOn, isRecycle), getString(com.soundrecorder.common.R.string.delete)
        )
        mDeleteDialog?.mOnFileDeleteListener = object : OnFileDeleteListener {
            override fun onDeleteFileResult(deleteSuccess: Boolean) {
                if (deleteSuccess) {
                    VibrateUtils.vibrate(activity)
                    onDeleteRecordSuccess(isRecycle)
                }
            }

            override fun provideDeleteRequestCode(): Int {
                return REQUEST_CODE_SYS_DELETE_AUTH
            }
        }
        mDeleteDialog?.mHideListener = OnDismissListener {
            if (mDeleteDialog != null) {
                if (isRecycle) {
                    mViewModel?.mNeedShowRecycleDeleteDialog?.postValue(false)
                } else {
                    mViewModel?.mNeedShowDeleteDialog?.postValue(false)
                }
            }
        }

        mDeleteDialog?.showDeleteDialog(getOperaRecord(), isRecycle)
        // 埋点
        val eventInfoDelete: MutableMap<String?, String?> = HashMap()
        if (isRecycle) {
            eventInfoDelete[RecorderUserAction.KEY_RECYCLE_DELETE] = RecorderUserAction.VALUE_MORE_DELETE
        } else {
            eventInfoDelete[RecorderUserAction.KEY_MORE_DELETE] = RecorderUserAction.VALUE_MORE_DELETE
        }
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), RecorderUserAction.USER_ACTION_PLAY_MORE, RecorderUserAction.EVENT_PLAY_MORE,
            eventInfoDelete, false
        )
    }

    private fun getDeleteDialogMessage(isCloudOn: Boolean, isRecycle: Boolean): String {
        val message: String = if (isRecycle) {
            resources.getString(com.soundrecorder.common.R.string.the_record_will_be_deleted_from_device)
        } else {
            if (isCloudOn) { //开启云同步时
                resources.getString(com.soundrecorder.common.R.string.recycle_tips_cloud_recently_delete_record)
            } else {
                resources.getString(com.soundrecorder.common.R.string.recycle_tips_recently_delete_record)
            }
        }
        return message
    }

    private fun showDetailDialog() {
        val context = context ?: return
        val inflater = LayoutInflater.from(context)
        val view = inflater.inflate(R.layout.layout_playback_detail, null)
        val name = view.findViewById<TextView>(R.id.name)
        val time = view.findViewById<TextView>(R.id.time)
        val length = view.findViewById<TextView>(R.id.length)
        val path = view.findViewById<TextView>(R.id.path)
        val pathString: String?
        val pathStart = BaseUtil.getPhoneStorageDir(BaseApplication.getAppContext())
        val sdPathStart = BaseUtil.getSDCardStorageDir(BaseApplication.getAppContext())
        pathString = if (pathStart != null && (mViewModel?.playPath?.value?.startsWith(pathStart) == true)) {
            resources.getString(
                if (FeatureOption.IS_PAD) {
                    com.soundrecorder.common.R.string.device_phone_storage
                } else {
                    com.soundrecorder.common.R.string.phone_storage
                },
                mViewModel?.playPath?.value?.replace(pathStart, "")
            )
        } else if (mViewModel?.playPath?.value?.startsWith(sdPathStart) == true) {
            resources.getString(
                com.soundrecorder.common.R.string.sd_card_storage,
                mViewModel?.playPath?.value?.replace(sdPathStart, "")
            )
        } else {
            mViewModel?.playPath?.value
        }
        name.text = mViewModel?.playName?.value
        val date = Date(
            RecorderDBUtil.getInstance(BaseApplication.getAppContext()).getCreateTimeByPath(
                mViewModel?.recordId
                    ?: -1, mViewModel?.playName?.value?.endsWith(".amr") ?: false
            )
        )
        time.text = RecorderICUFormateUtils.formatDateTime(date)
        length.text = FileUtils.getSizeDescription(
            FileUtils.getFileSize(
                MediaDBUtils.genUri(
                    mViewModel?.recordId
                        ?: -1
                )
            )
        )
        path.text = pathString

        COUIAlertDialogBuilder(context).apply {
            setView(view)
            setTitle(com.soundrecorder.common.R.string.talkback_detail)
            setNegativeButton(com.soundrecorder.base.R.string.close) { _, _ ->
                mDeleteDialog?.dismiss()
            }
            setOnDismissListener {
                if (mDetailDialog != null) {
                    mViewModel?.mNeedShowDetailDialog?.postValue(false)
                }
            }
            setBlurBackgroundDrawable(true)
            mDetailDialog = show()
            mDetailDialog?.getButton(DialogInterface.BUTTON_NEGATIVE)?.setTextColor(
                resources.getColor(
                    com.support.appcompat.R.color.coui_color_label_theme_blue,
                    null
                )
            )
        }
        val eventInfoDetail: MutableMap<String?, String?> = HashMap()
        eventInfoDetail[RecorderUserAction.KEY_PLAY_MORE_DETAIL] = RecorderUserAction.VALUE_PLAY_MORE_DETAIL
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), RecorderUserAction.USER_ACTION_PLAY_MORE, RecorderUserAction.EVENT_PLAY_MORE,
            eventInfoDetail, false
        )
    }

    private fun showRenameDialog(text: String? = null) {
        if (mRenameDialog?.isShowing() == true) {
            DebugUtil.i(TAG, "mRenameDialog is showing")
            return
        }
        val activity = activity ?: return
        val queryUri = MediaDBUtils.BASE_URI
        var cursor: Cursor? = null
        val where: String = MediaStore.Audio.Media._ID + "=?"
        var mediaRecord: Record? = null
        try {
            cursor = context?.contentResolver?.query(queryUri, null, where, arrayOf(mViewModel?.recordId.toString()), null)
            cursor?.let {
                if (it.moveToNext()) {
                    mediaRecord = Record(
                        cursor,
                        Record.TYPE_FROM_MEDIA
                    )
                }
            }
        } catch (ignored: Exception) {
            DebugUtil.e(TAG, "cursor is exception$ignored")
        } finally {
            cursor?.close()
        }

        val content = if (text == null) {
            var lastIndex = -1
            if (!TextUtils.isEmpty(mediaRecord?.displayName)
                && (mediaRecord?.displayName?.lastIndexOf(".").also { lastIndex = it ?: 0 } ?: 0) > 0
            ) {
                val content = mediaRecord?.displayName?.substring(0, lastIndex) ?: ""
                DebugUtil.i(TAG, "showRenameDialog, mRenameDialog content is $content")
                content
            } else {
                return
            }
        } else {
            text
        }

        mRenameDialog = RenameFileDialog(activity, RenameFileDialog.FROM_PLAYBACK_MORE, content, object : PositiveCallback {
            override fun callback(displayName: String?, path: String?) {
                DebugUtil.i(TAG, "rename callback displayName: $displayName, path: $path")
                var index = -1
                if (path == null || path.lastIndexOf(".").also { index = it } == -1) {
                    return
                }
                mViewModel?.renameRecord(path, displayName + path.substring(index))
                DebugUtil.i(TAG, "setValue: " + (displayName + path.substring(index)))
                mRenameDialog?.resetOperating()
                notifyRefreshRecordList()
                BuryingPoint.addActionForPlaybackRenameSuccess()
            }
        }).apply {
            this.requestCode = REQUEST_CODE_SYS_RENAME_AUTH
            this.mediaRecord = mediaRecord
        }
        mRenameDialog?.show()
        BuryingPoint.addActionForPlaybackRename()
    }

    private fun showPlaySettingDialog() {
        if (mViewModel == null) {
            return
        }
        if (bottomSheetDialogFragment?.dialog?.isShowing == true) {
            DebugUtil.i(TAG, "bottomSheetDialogFragment is showing")
            return
        }
        bottomSheetDialogFragment = COUIBottomSheetDialogFragment()
        val dialogFragment = PlaySettingDialogFragment()
        bottomSheetDialogFragment?.setMainPanelFragment(dialogFragment)
        if (isAdded) {
            bottomSheetDialogFragment?.show(childFragmentManager, PlaySettingDialogFragment.FRAGMENT_TAG)
        }
        mViewModel?.mNeedShowSpeedDialog?.value = false
    }

    private fun configToolbar() {
        binding.toolbar.isTitleCenterStyle = false
        binding.toolbar.clearMenu()
        if (recordFilterIsRecycle()) {
            showRecycleToolBar()
        } else {
            showNormalToolbar()
        }

        setNavigationIcon()
        binding.toolbar.setNavigationOnClickListener {
            if (isFromAppCard()) {
                activity?.finish()
            } else {
                BrowseFileAction.clearViewModelPlayData(mBrowseFileActivityViewModel)
            }
        }
    }

    /**
     * 从小布建议卡跳转过来
     */
    private fun isFromAppCard(): Boolean {
        val from = activity?.intent?.getStringExtra("jumpFrom") ?: return false
        return from == "AppCard"
    }

    private fun showNormalToolbar() {
        binding.toolbar.inflateMenu(R.menu.menu_play_back)

        binding.toolbar.menuView?.apply {
            this.setItemSpecialColor(
                R.id.delete,
                COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorError)
            )
        }
        mSpeakerMenuItem = binding.toolbar.menu.findItem(R.id.speaker)

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                binding.toolbar.menu.apply {
                    ensureConvertSearchAnimView(this)
                    setShowSmartNameMenuView()
                    if (FeatureOption.IS_PAD) {
                        mSpeakerMenuItem?.isVisible = false
                    }
                    this.children.forEach {
                        it.setOnMenuItemClickListener { menuItem ->
                            onOptionsItemSelected(menuItem)
                        }
                    }
                }
            }
        }
    }

    private fun setShowSmartNameMenuView() {
        mSupportSmartName = BrowseFileAction.isSupportSmartName(mBrowseFileActivityViewModel)
        DebugUtil.d(TAG, "setShowSmartNameMenuView, supportSmartName:$mSupportSmartName")
        if (mSupportSmartName) {
            binding.toolbar.menu.findItem(R.id.rename).isVisible = false
            binding.toolbar.menu.findItem(R.id.rename_new).isVisible = true
            binding.toolbar.menuView?.apply {
                setOverflowMenuListener { popup ->
                    mToolbarOverflowPopupWindow = popup
                    val mainList = popup.itemList
                    DebugUtil.d(TAG, "configToolbar mainList:${mainList.size}")
                    for (item in mainList) {
                        if (item.id == R.id.rename_new) {
                            for (subItem in item.subMenuItemList) {
                                subItem.stateIcon = ResourcesCompat.getDrawable(
                                    resources,
                                    R.drawable.menu_transparent_selector,
                                    activity?.theme
                                )
                            }
                        }
                    }
                }
            }
        } else {
            binding.toolbar.menu.findItem(R.id.rename).isVisible = true
            binding.toolbar.menu.findItem(R.id.rename_new).isVisible = false
        }
    }

    private fun showRecycleToolBar() {
        binding.toolbar.inflateMenu(R.menu.menu_play_back_recycle)

        mSpeakerMenuItem = binding.toolbar.menu.findItem(R.id.speaker)
        mRecoverMenuItem = binding.toolbar.menu.findItem(R.id.recycle_recover)
        mDeleteMenuItem = binding.toolbar.menu.findItem(R.id.recycle_delete)

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                binding.toolbar.menu.apply {
                    if (FeatureOption.IS_PAD) {
                        mSpeakerMenuItem?.isVisible = false
                    }
                    this.children.forEach {
                        it.setOnMenuItemClickListener { menuItem ->
                            onOptionsItemSelected(menuItem)
                        }
                    }
                }
            }
        }
    }

    private fun ensureConvertSearchAnimView(menu: Menu?) {
        if (!FunctionOption.IS_SUPPORT_CONVERT_SEARCH) {
            return
        }
        if (menu != null) {
            val mSearchItem = menu.findItem(R.id.search)
            searchAnimView = mSearchItem?.actionView as? COUISearchBar
            mPlaybackConvertViewModel?.let {
                searchAnimView?.apply {
                    setAtBehindToolBar(binding.toolbar, Gravity.TOP, mSearchItem)
                    searchEditText.filters =
                        arrayOf<InputFilter>(InputFilter.LengthFilter(SEARCH_INPUT_MAX_LENGTH))
                    searchEditText.hint =
                        BaseApplication.getAppContext().resources.getString(com.soundrecorder.common.R.string.search_convert_content)
                    functionalButton?.setOnClickListener {
                        getConvertSearchFragment()?.onClickCancel()
                    }
                }
            }
            searchAnimView?.searchEditText?.let {
                it.setText(mPlaybackConvertViewModel?.mConvertSearchValue ?: "")
                it.addTextChangedListener(object : android.text.TextWatcher {
                    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                    }

                    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                        getConvertSearchFragment()?.onQueryTextChange(s?.toString())
                        BrowseFileAction.onConvertSearchStateChanged(
                            activity, mPlaybackConvertViewModel?.mIsInConvertSearch?.value ?: false,
                            !s.isNullOrBlank()
                        ) {
                            findConvertSearchFragment()?.onClickCancel()
                        }
                    }

                    override fun afterTextChanged(s: Editable?) {
                    }
                })
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (mPlaybackConvertViewModel?.mIsInConvertSearch?.value == true) {
            DebugUtil.i(TAG, "onOptionsItemSelected in convert search ,ignore menu item click event")
            return super.onOptionsItemSelected(item)
        }
        when (item.itemId) {
            android.R.id.home -> BrowseFileAction.clearViewModelPlayData(mBrowseFileActivityViewModel)
            R.id.speaker -> {
                if (mViewModel?.checkLoadAmpFinished() != true) {
                    // 波形还未解析出来，不能使用听筒切换功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false return")
                    return false
                }
                BrowseFileAction.getViewModelSpeakerModeController<SpeakerModeController>(mBrowseFileActivityViewModel)
                    ?.performSpeakerMenuItemClick(SpeakerModeController.PLAYER_SPEAKER_PLAYBACK)
            }

            R.id.recycle_delete -> mViewModel?.mNeedShowRecycleDeleteDialog?.value = true
            R.id.recycle_recover -> mViewModel?.mNeedShowRecoverDialog?.value = true
            R.id.export -> {
                if (mViewModel?.hasConvertContent?.value == true) {
                    mViewModel?.mNeedShowShareDialog?.value = true
                } else {
                    if (ClickUtils.isQuickClick()) {
                        return super.onOptionsItemSelected(item)
                    }
                    val activity = activity ?: return false
                    FileDealUtil.sendRecordFile(activity, mViewModel?.recordId ?: -1, null)
                }
                BuryingPoint.addClickMoreShare()
            }

            R.id.move -> {
                BrowseFileAction.showGroupChooseFragment(
                    this@PlaybackContainerFragment,
                    mViewModel?.getRecord()
                )
            }

            R.id.rename -> {
                showRenameDialog()
            }

            R.id.rename_new -> {
                if (mSupportSmartName) {
                    showSmartNamePopupWindow()
                } else {
                    showRenameDialog()
                }
            }

            R.id.detail -> mViewModel?.mNeedShowDetailDialog?.value = true
            R.id.delete -> mViewModel?.mNeedShowDeleteDialog?.value = true
            R.id.set_ringtone -> {
                BuryingPoint.addClickSetRingtone(RecorderUserAction.VALUE_SET_RINGTONE_FROM_MORE)
                SendSetUtil.setAs(mViewModel?.recordId ?: -1, activity)
            }
        }
        return super.onOptionsItemSelected(item)
    }

    private fun showSmartNamePopupWindow() {
        mToolbarOverflowPopupWindow?.apply {
            setSubMenuClickListener(AdapterView.OnItemClickListener { _, _, position, _ ->
                val mainList = itemList
                DebugUtil.d(TAG, "onOptionsItemSelected, mainList:${mainList.size}")
                for (itemGroup in mainList) {
                    if (itemGroup.id == R.id.rename_new) {
                        mSubMenuCheckedPosition = position
                        when (mSubMenuCheckedPosition) {
                            0 -> showRenameDialog()
                            1 -> {
                                //智能命名
                                DebugUtil.d(TAG, "subMenuClick, rename ai:$position")
                                isClickAiTitle = true
                                val mediaIdList = mutableListOf<Long>()
                                mViewModel?.recordId?.let { mediaIdList.add(it) }
                                if (!PermissionUtils.hasAllFilePermission()) {
                                    showPermissionAllFileDialog(true, mediaIdList)
                                    dismiss()
                                    return@OnItemClickListener
                                }
                                startConvertAndSmartName(mediaIdList)
                            }
                        }
                        dismiss()
                    }
                }
            })
        }
    }

    private fun startConvertAndSmartName(mediaIdList: MutableList<Long>) {
        if (PermissionUtils.hasAllFilePermission()) {
            initSmartNameManagerImpl()
            mSmartNameMangerImpl?.convertStartSmartNameClickHandle(activity, mediaIdList, PAGE_FROM_PLAYBACK)
        }
    }

    private fun showPermissionAllFileDialog(needSmartName: Boolean = false, selectedMediaIdList: MutableList<Long>? = null) {
        if (ClickUtils.isFastDoubleClick()) {
            return
        }
        activity?.let {
            mFilePermissionDialog = PermissionDialogUtils.showPermissionAllFileAccessDialog(
                it,
                object : PermissionDialogUtils.PermissionDialogListener {
                    override fun onClick(alertType: Int, isOk: Boolean, permissions: ArrayList<String>?) {
                        DebugUtil.d(TAG, "showPermissionAllFileDialog, isOk:$isOk, permissions:$permissions")
                        if (isOk) {
                            if (needSmartName) {
                                mViewModel?.isNeedSmartName = true
                                mViewModel?.needSmartNameMediaList?.clear()
                                selectedMediaIdList?.let { it1 ->
                                    mViewModel?.needSmartNameMediaList?.addAll(
                                        it1
                                    )
                                }
                            }
                            PermissionUtils.goToAppAllFileAccessConfigurePermissions(it)
                        }
                    }
                })
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        DebugUtil.i(TAG, "onActivityResult: request code $requestCode result code $resultCode")
        when (requestCode) {
            REQUEST_CODE_SYS_RENAME_AUTH -> {
                if (resultCode == Activity.RESULT_OK) {
                    val suffix = mViewModel?.playPath?.value?.suffix()
                    val renameContent = mRenameDialog?.getRenameContent()
                    if (FileDealUtil.renameAgain(mViewModel?.playerController?.getPlayUri(), renameContent, suffix)) {
                        mViewModel?.renameRecord(
                            core2Full(
                                renameContent + suffix,
                                mViewModel?.playPath?.value
                            ), renameContent + suffix
                        )
                        CloudStaticsUtil.addCloudLog(TAG, "renameAgain, ${mViewModel?.playName?.value} renameTo $renameContent success")
                    }
                }
                mRenameDialog?.resetOperating()
            }

            REQUEST_CODE_SYS_DELETE_AUTH -> {
                if (resultCode == Activity.RESULT_OK) {
                    responseDeleteBatch()
                }
                mDeleteDialog?.resetOperating()
            }

            REQUEST_CODE_SYS_RECOVER_AUTH -> {
                if (resultCode == Activity.RESULT_OK) {
                    responseRecoverBatch()
                }
                mRecoverDialog?.resetOperating()
            }

            Constants.EXPORT_TO_NOTE_REQUEST_CODE -> {
                if (resultCode == Activity.RESULT_OK) {
                    ToastManager.showShortToast(
                        BaseApplication.getAppContext(),
                        com.soundrecorder.common.R.string.save_success
                    )
                }
            }

            Constants.RESULT_CODE_PLAYBACK_SUMMARY -> {
                // 通话记录ID没有摘要笔记
                if (resultCode == Constants.NOTES_RESULT_CODE_ERROR) {
                    mViewModel?.summaryNoteId?.let {
                        BrowseFileAction.getDeleteSummaryNoteIdLiveData(mBrowseFileActivityViewModel)?.value = it
                    }
                    mViewModel?.deleteNoteData()
                    ToastManager.showShortToast(
                        BaseApplication.getAppContext(),
                        com.soundrecorder.common.R.string.tip_summary_be_deleted
                    )
                }
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    private fun responseRecoverBatch() {
        val activity = activity ?: return
        val result = FileDealUtil.recoveryRecord(activity, getOperaRecord(), REQUEST_CODE_SYS_RECOVER_AUTH)
        DeleteSoundEffectManager.getInstance().playDeleteSound()
        CloudStaticsUtil.addCloudLog(TAG, "responseDeleteBatch, delete ${mViewModel?.playName?.value} result=$result")
        onRecoverRecordSuccess()
    }

    private fun getOperaRecord(): Record {
        return if (mViewModel?.getRecord() != null) {
            mViewModel?.getRecord()!!
        } else {
            Record().apply {
                id = mViewModel?.recordId ?: -1
                data = mViewModel?.playPath?.value ?: ""
                recordType = mViewModel?.recordType?.value ?: 0
                recycleFilePath = mViewModel?.mPlayPath ?: mViewModel?.playPath?.value
                displayName = mViewModel?.playName?.value ?: ""
                setIsRecycle(recordFilterIsRecycle())
            }
        }
    }

    private fun responseDeleteBatch() {
        val activity = activity ?: return
        val id = mViewModel?.recordId ?: -1
        val result = FileDealUtil.deleteRecordDBBatch(activity, mViewModel?.playPath?.value, id)
        DeleteSoundEffectManager.getInstance().playDeleteSound()
        CloudStaticsUtil.addCloudLog(TAG, "responseDeleteBatch, delete ${mViewModel?.playName?.value} result=$result")
        onDeleteRecordSuccess(recordFilterIsRecycle())
    }

    private fun onDeleteRecordSuccess(isRecycle: Boolean) {
        notifyRefreshRecordList()
        BrowseFileAction.clearViewModelPlayData(mBrowseFileActivityViewModel)
        if (isRecycle) {
            BuryingPoint.addPlayRecycleDeleteSuccess()
        } else {
            SeedlingAction.sendRecordDeleteEvent()
            BuryingPoint.addPlayMoreDeleteSuccess()
        }
        if (isFromAppCard()) {
            startToBrowseFile()
        }
    }

    private fun notifyRefreshRecordList() {
        val intent = Intent(RecordFileChangeNotify.FILE_UPDATE_ACTION)
        intent.putExtra(Constants.FRESH_FLAG, true)
        BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent)
    }

    private fun stopObserver() {
        mViewModel?.playerController?.playerState?.removeObserver(mPlayStateChangeObserver)
    }

    private fun registerReceivers() {
        MultiFileObserver.getInstance().addFileEventListener(this)
        registerConvertReceiver()
    }

    private fun unregisterReceivers() {
        MultiFileObserver.getInstance().removeFileEventListener(this)
        LocalBroadcastManager.getInstance(context ?: BaseApplication.getAppContext())
            .unregisterReceiver(convertReceiver)
    }

    override fun onFileObserver(event: Int, path: String?, allPath: String?) {
        when (event) {
            FileObserver.MOVED_FROM, FileObserver.DELETE,
            FileObserver.DELETE_SELF, FileObserver.MOVE_SELF -> {
                DebugUtil.d("$TAG-onFileObserver", "path:$path,allPath:$allPath")
                cancelAndFinish(path, allPath)
            }
        }
    }

    private fun cancelAndFinish(path: String?, allPath: String?) {
        val playPath = mViewModel?.playPath?.value
        val isTaskRunning = mViewModel?.recordId?.let { SmartNameAction.checkIsTaskRunning(it) } ?: false
        DebugUtil.d(TAG, "cancelAndFinish, isTaskRunning:$isTaskRunning")
        if (!allPath.isNullOrEmpty() && !playPath.isNullOrEmpty() && playPath.startsWith(allPath)
            && !isTaskRunning
        ) {
            /*再有的外销手机上，fileObserver会先于OnActivityResult方法，所以在此处增加删除逻辑*/
            if (mDeleteDialog?.getOperating() == true) {
                responseDeleteBatch()
                return
            }
            /*中大屏下处理列表重命名音频文件场景*/
            if (BrowseFileAction.getViewModelWindowType<WindowType>(mBrowseFileActivityViewModel)?.value != WindowType.SMALL) {
                val mediaRecord = MediaDBUtils.getRecordFromMediaByUriId(MediaDBUtils.genUri(mViewModel?.recordId ?: -1))
                DebugUtil.i(TAG, "onFileObserver,media.name=${mediaRecord?.displayName},show.name=${mViewModel?.playName?.value}")
                /**媒体库有延迟，mediaRecord有可能查询到之前的信息，所以这里增加了两边name不一致的判断*/
                if ((mediaRecord != null) && (mViewModel?.playName?.value != mediaRecord.displayName)) {
                    mViewModel?.renameRecord(
                        mediaRecord.data,
                        mediaRecord.displayName
                    )
                    return
                }
            }
            DebugUtil.d("$TAG-onFileObserver", "path:$path,allPath:$allPath,playPath:$playPath")
            mViewModel?.cancelNotification()
            BrowseFileAction.clearViewModelPlayData(mBrowseFileActivityViewModel)
            DebugUtil.d("$TAG-onFileObserver", "path:$path,allPath:$allPath,playPath:$playPath,结束")
        }
    }

    /**
     * 显示 “请稍后。。。” dialog
     * 当转文本的文件大于  Constants.SHOW_WAITING_DIALOG__THRESHOLD 的时候，保存到本地和分享需要弹窗
     * 弹窗显示不足1s的时候，需要延迟到1s之后再关闭
     */
    private fun showWaitingDialog(type: ShareType?) {
        val msgResId = when (type) {
            is ShareTypeNote -> com.soundrecorder.common.R.string.is_saving
            is ShareTypeLink -> com.soundrecorder.common.R.string.generating
            else -> com.soundrecorder.common.R.string.waiting
        }
        val activity = activity ?: return
        if (mLoadingDialog == null) {
            mLoadingDialog = LoadingDialog(activity)
        }
        if (mLoadingDialog?.isActivityNull() == true) {
            mLoadingDialog?.resetActivity(activity)
        }
        mLoadingDialog?.show(msgResId)
        mViewModel?.setNeedRestoreWaitingDialog(false)
    }

    /**
     * 关闭waitingDialog并且显示分享面板
     */
    private fun dismissDialog() {
        DebugUtil.i(TAG, "dismissDialogDoExport...")
        if (mLoadingDialog?.isShowing() == true) {
            mLoadingDialog?.dismiss()
        }
    }

    override fun onDestroyView() {
        DebugUtil.e(TAG, "onDestroyView")
        cacheDialogStatus()
        releaseDialog()
        releasePopupWindow()
        mPageChangeCallback?.let { binding.viewpager.unregisterOnPageChangeCallback(it) }
        FollowDialogRestoreUtils.releaseFollowDialogRunnable(activity?.window?.decorView)
        disalbeDialog.dismissWhenShowing()
        mFilePermissionDialog.dismissWhenShowing()
        super.onDestroyView()
        mLoadingViewControl = null
        bottomButtonHelper = null
        typeface = null
        seekBar = null
        disalbeDialog = null
        BrowseFileAction.getViewModelAnimRunning(mBrowseFileActivityViewModel)?.let {
            if (it.value == true) {
                // 将小屏动效置为false，避免动效执行过程中，重建导致该值没有被重置
                it.value = false
                DebugUtil.i(TAG, "getViewModelAnimRunning set false")
            }
        }
        mViewModel?.isImmersiveAnimationRunning = false
        if (immersiveAnimationHelperInitial == true) {
            immersiveAnimationHelper.release()
        }
    }

    private fun releasePopupWindow() {
        if (mToolbarOverflowPopupWindow?.isShowing == true) {
            mToolbarOverflowPopupWindow?.dismiss()
        }
        mToolbarOverflowPopupWindow = null
    }

    override fun onDestroy() {
        DebugUtil.d(TAG, "onDestroy: isFinishing = ${activity?.isFinishing}, isRemoving = $isRemoving")
        if ((activity?.isFinishing == true) || isRemoving) {
            checkIfNeedTrigCloudSync()
        }
        unregisterReceivers()
        stopObserver()
        mViewModel?.let {
            it.isAddPictureMarkingCallback = null
            it.setShareCallBack(null)
            it.clearLifecycle(this)
        }
        if ((activity?.isFinishing == true) || isRemoving) {
            mViewModel?.cancelNotification()
        }
        mSmartNameMangerImpl?.release()
        mSmartNameMangerImpl = null
        super.onDestroy()
        mPictureMarkHelper = null
    }


    private fun cacheDialogStatus() {
        mViewModel?.mNeedShowRenameDialog?.value = mRenameDialog?.isShowing() ?: false
        mViewModel?.setNeedRestoreWaitingDialog(mLoadingDialog?.isShowing() ?: false)
        mViewModel?.mNeedShowSelectPictureDialog?.value = mPictureMarkHelper?.isDialogShowing()
    }

    private fun releaseDialog() {
        mLoadingDialog?.dismiss()
        mLoadingDialog = null
        mDeleteDialog?.release()
        mDeleteDialog = null
        mDetailDialog?.dismiss()
        mDetailDialog = null
        mViewModel?.mRenameEditText = mRenameDialog?.getNewContent().toString()
        mRenameDialog?.release()
        mRenameDialog = null
    }

    private val mPlayStateChangeObserver = Observer<Int> {
        DebugUtil.i(TAG, "mPlayerState changed $it")
        mViewModel?.let { vm ->
            vm.markEnable.value = vm.isMarkEnabled()
        }
        when (it) {
            PlayStatus.PLAYER_STATE_PLAYING -> {
                binding.buttonPanel.redCircleIcon.switchPauseState()
                mViewModel?.showNotification()
            }

            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING ->
                binding.buttonPanel.redCircleIcon.switchPauseState()

            PlayStatus.PLAYER_STATE_PAUSE,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE,
                ->
                binding.buttonPanel.redCircleIcon.switchPlayState()

            else -> {
                binding.buttonPanel.redCircleIcon.switchPlayState()
                mViewModel?.cancelNotification()
            }
        }
    }

    override fun doPictureMark(pictureMarkMetaData: MarkMetaData) {
        mViewModel?.addMark(false, pictureMarkMetaData)
    }

    override fun doSingleOrMultiPictureMarkEnd(operateCancel: Boolean, fromSource: Int) {
        when (fromSource) {
            IPictureMarkDelegate.SOURCE_ALBUM -> {
                if (operateCancel) {
                    BuryingPoint.playingAddPictureByAlbumCancel()
                } else {
                    BuryingPoint.playingAddPictureByAlbumOk()
                }
            }

            IPictureMarkDelegate.SOURCE_CAMERA -> {
                if (operateCancel) {
                    BuryingPoint.playingAddPictureByCameraCancel()
                } else {
                    BuryingPoint.playingAddPictureByCameraNumber()
                }
            }
        }
    }

    override fun releaseMarks(isFinishing: Boolean) {
        mViewModel?.getMarkList()?.value?.forEach {
            it.release(isFinishing)
        }
    }

    override fun supportPictureMarkSource(): List<Int> = listOf(IPictureMarkDelegate.SOURCE_CAMERA, IPictureMarkDelegate.SOURCE_ALBUM)

    override fun getPlayerCurrentTimeMillis(): Long {
        return mViewModel?.getCurrentTime() ?: -1L
    }

    override fun getMarkList(): List<MarkDataBean>? = mViewModel?.getMarkList()?.value

    override fun getDuration(): Long = mViewModel?.playerController?.getDuration() ?: -1L

    override fun restorePlayerStatePlaying(cachePlayerState: Int, hasNeedSpeakOff: Boolean): Int {
        when (cachePlayerState) {
            PlayStatus.PLAYER_STATE_PLAYING, PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> {
                if (hasNeedSpeakOff && speakerModeController?.mIsSpeakerOn?.value == false) {
                    return PlayStatus.PLAYER_STATE_INIT
                }
                continuePlay()
            }
        }
        return PlayStatus.PLAYER_STATE_INIT
    }

    override fun onActivityLaunched(sourceType: Int) {
        when (sourceType) {
            IPictureMarkDelegate.SOURCE_ALBUM -> {
                activity?.overridePendingTransition(
                    com.soundrecorder.common.R.anim.enter_bottom,
                    com.soundrecorder.common.R.anim.exit_top
                )
            }

            IPictureMarkDelegate.SOURCE_CAMERA -> {
                activity?.overridePendingTransition(
                    com.support.appcompat.R.anim.coui_open_slide_enter,
                    com.support.appcompat.R.anim.coui_open_slide_exit
                )
            }
        }
    }

    private fun continuePlay() {
        if (mViewModel?.playerController?.isWholePlaying() != true) {
            mViewModel?.playerController?.seekToPlay()
        }
    }

    override fun hasPlayerStatePlaying(): Int {
        return mViewModel?.playerController?.playerState?.value ?: PlayStatus.PLAYER_STATE_INIT
    }

    override fun beforeShowSelectPictureDialog() {
        mViewModel?.playerController?.pausePlay()
    }

    private fun checkIfNeedTrigCloudSync() {
        if (mViewModel?.mNeedTrigCloud == true) {
            CloudSyncAction.trigBackupNow(BaseApplication.getAppContext())
        }
    }

    private fun inAndOutConvertSearchFragment(jumpToConvertSearch: Boolean) {
        if (jumpToConvertSearch) {
            // 保存搜索动效的高度
            getConvertFragment()?.mConvertManagerImpl?.getConvertViewController()?.saveKeyWordViewHeight()

            binding.tabLayout.visibility = View.GONE
            searchAnimView?.searchEditText?.setText(mPlaybackConvertViewModel?.mConvertSearchValue ?: "")
            binding.toolbar.post {
                searchAnimView?.showInToolBar()
            }
            // 取消右上角的更多弹窗
            binding.toolbar.postDelayed({
                binding.toolbar.dismissPopupMenus()
            }, TIP_SHOW_DELAY_TIME)

            replaceFragmentByTag(
                R.id.fl_convert_search_container,
                getConvertSearchFragment(),
                ConvertSearchFragment.TAG
            )
        } else {
            if (isAdded && childFragmentManager.findFragmentByTag(ConvertSearchFragment.TAG) != null) {
                // 退出搜索检测是否需要展示气泡
                getConvertFragment()?.mConvertManagerImpl?.getConvertViewController()?.checkOutSearchShowBackOfLocation()
                removeFragment(getConvertSearchFragment())
            }
            searchAnimView?.apply {
                searchEditText.setText("")
                hideInToolBar()
            }
            binding.tabLayout.isVisible = canShowTabLayout()
            // 退出搜索的动效
            getConvertFragment()?.mConvertManagerImpl?.getConvertViewController()?.animSearchOut()
        }
    }

    private fun initiateWindowInsets(rootView: View) {
        val callback = object : DeDuplicateInsetsCallback() {
            override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                val stableStatusBarInsets = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.systemBars())
                v.updatePadding(
                    top = stableStatusBarInsets.top, left = stableStatusBarInsets.left,
                    right = stableStatusBarInsets.right
                )
                binding.buttonPanel.root.updatePadding(bottom = stableStatusBarInsets.bottom)
                DebugUtil.i(TAG, "onApplyInsets")
                getConvertFragment()?.mConvertManagerImpl?.getConvertViewController()?.dismissRenameSpeakerDialog()
                updateImmersiveNavigationHeight(stableStatusBarInsets.bottom)
            }
        }
        ViewCompat.setOnApplyWindowInsetsListener(rootView, callback)
    }

    fun isViewPager2IDLE(): Boolean = binding.viewpager.scrollState == ViewPager2.SCROLL_STATE_IDLE

    fun isOnAudio(): Boolean = binding.viewpager.currentItem == TAB_TYPE_AUDIO

    fun isOnConvert(): Boolean {
        if (mPlaybackConvertViewModel?.mIsInConvertSearch?.value == true) { // 处于文本搜索界面
            return false
        }
        return binding.viewpager.currentItem == TAB_TYPE_CONVERT
    }

    private fun isOnConvertWhenViewPager2IDLE(): Boolean {
        val convertFragment = getConvertFragment() ?: return false
        return convertFragment.isOnConvertWhenViewPager2IDLE()
    }

    private fun dismissMenuPop() {
        getAudioFragment()?.dismissMenuPop()
    }

    fun onPrivacyPolicySuccess(type: Int) {
        when (type) {
            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT -> {
                if (isClickAiTitle) {
                    mSmartNameMangerImpl?.doClickPermissionConvertOK(activity, PrivacyPolicyConstant.PAGE_FROM_PLAYBACK)
                } else {
                    getConvertFragment()?.mConvertManagerImpl?.doClickPermissionConvertOK()
                }
                isClickAiTitle = false
            }

            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH -> getConvertFragment()?.mConvertManagerImpl?.doClickPermissionConvertSearchOK()
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        if (mViewModel?.isSupportConvert() == true) {
            outState.putBoolean(
                EKY_IS_IN_CONVERT_SEARCH,
                mPlaybackConvertViewModel?.mIsInConvertSearch?.value ?: false
            )
            outState.putString(
                KEY_IN_CONVERT_SEARCH_VALUE,
                mPlaybackConvertViewModel?.mConvertSearchValue ?: ""
            )
            outState.putInt(
                KEY_CONVERT_SEARCH_CURRENT_POS,
                mPlaybackConvertViewModel?.currentPos ?: 0
            )
            outState.putInt(
                KEY_CONVERT_SEARCH_LAST_POS,
                mPlaybackConvertViewModel?.lastPos ?: 0
            )
        }
        super.onSaveInstanceState(outState)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        mPictureMarkHelper?.onConfigurationChanged(newConfig)
    }

    override fun startActivityForResult(intent: Intent, requestCode: Int) {
        mPictureMarkHelper?.setRequestCodeX(requestCode)
        super.startActivityForResult(intent, requestCode)
    }

    override fun startActivityForResult(intent: Intent, requestCode: Int, options: Bundle?) {
        mPictureMarkHelper?.setRequestCodeX(requestCode)
        super.startActivityForResult(intent, requestCode, options)
    }

    fun getIPictureMarkDelegate(): IPictureMarkDelegate<MarkMetaData>? = mPictureMarkHelper

    override fun onBackPressed(): Boolean {
        if (mPlaybackConvertViewModel?.mIsInConvertSearch?.value == true) {
            mPlaybackConvertViewModel?.mIsInConvertSearch?.value = false
            return true
        }
        if (isFromAppCard()) {
            activity?.finish()
            return true
        } else {
            // 小屏下销毁播放页面
            if ((BrowseFileAction.getViewModelWindowType<WindowType>(mBrowseFileActivityViewModel)?.value == WindowType.SMALL)
                && (BrowseFileAction.getViewModelPlayData<StartPlayModel>(mBrowseFileActivityViewModel) != null)
            ) {
                BrowseFileAction.clearViewModelPlayData(mBrowseFileActivityViewModel)
                return true
            }
        }
        return false
    }

    override fun pausePlay(clearNotification: Boolean) {
        mViewModel?.playerController?.pausePlay()
        if (clearNotification) {
            mViewModel?.cancelNotification()
        }
    }

    private fun recordFilterIsRecycle(): Boolean {
        return mViewModel?.isRecycle == true
    }

    override fun onUserChange() {
        mViewModel?.cancelNotification()
    }

    private fun registerConvertReceiver() {
        val isFromOtherApp = BrowseFileAction.getViewModelIsFromOther(mBrowseFileActivityViewModel)
        if (ConvertSupportManager.isSupportConvert(!isFromOtherApp)) {
            DebugUtil.d(TAG, "registerConvertReceiver")
            val convertFilter = IntentFilter()
            convertFilter.addAction(PlaybackAction.NOTIFY_CONVERT_STATUS_UPDATE)
            convertFilter.addAction(PlaybackAction.NOTIFY_SMART_NAME_STATUS_UPDATE)
            LocalBroadcastManager.getInstance(context ?: BaseApplication.getAppContext())
                .registerReceiver(convertReceiver, convertFilter)
        }
    }

    /**
     * 切换沉浸态
     */
    private fun switchImmersive(isShowImmersive: Boolean) {
        if (isShowImmersive) {
            startImmersiveAnimation()
        } else {
            reverseImmersiveAnimation()
        }
    }

    /**
     * 进入沉浸态
     */
    private fun startImmersiveAnimation() {
        DebugUtil.i(TAG, "startImmersiveAnimation")
        //禁止ViewPager手动切换
        binding.viewpager.isUserInputEnabled = false
        //列表渐变遮住隐藏
        getConvertFragment()?.mConvertManagerImpl?.getConvertViewController()
            ?.startImmersiveAnimation()
        //底部面板沉浸态动画
        immersiveAnimationHelper.startImmersiveAnimation(navigationHeight ?: 0, windowType)
    }

    /**
     * 退出沉浸态
     */
    private fun reverseImmersiveAnimation() {
        DebugUtil.i(TAG, "reverseImmersiveAnimation")
        //解除ViewPager切换限制
        binding.viewpager.isUserInputEnabled = true
        getConvertFragment()?.mConvertManagerImpl?.getConvertViewController()
            ?.reverseImmersiveAnimation()
        immersiveAnimationHelper.reverseImmersiveAnimation()
    }

    /**
     * 任务栏状态变化更新沉浸态布局
     */
    private fun updateImmersiveNavigationHeight(navigationHeight: Int) {
        DebugUtil.i(
            TAG,
            "updateImmersiveNavigationHeight navigationHeight$navigationHeight this.navigationHeight:${this.navigationHeight} windowType:$windowType"
        )
        if (windowType == WindowType.SMALL || this.navigationHeight == null) {
            this.navigationHeight = navigationHeight
            return
        }
        this.navigationHeight = navigationHeight
        mViewModel?.isImmersiveState?.value?.let {
            immersiveAnimationHelper.updateImmersiveNavigationHeight(navigationHeight, it)
        }
    }
}