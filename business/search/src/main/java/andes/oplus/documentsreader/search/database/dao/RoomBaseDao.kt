/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:RoomBaseDao.kt
 * * Description:Data operation interface base class, realize public addition, deletion, and modification operations
 * * Version:1.0
 * * Date :2020/8/28
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/8/28,        v1.0,           Create
 ****************************************************************/
package andes.oplus.documentsreader.search.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy.Companion.REPLACE
import androidx.room.Update

@Dao
interface RoomBaseDao<T> {

    /**
     * Insert single item
     */
    @Insert(onConflict = REPLACE)
    fun insertItem(item: T): Long

    /**
     * Insert multiple items
     */
    @Insert(onConflict = REPLACE)
    fun insertItems(items: List<T>?): List<Long>

    /**
     * Delete single item, by match PrimaryKey
     */
    @Delete
    fun deleteItem(item: T): Int

    /**
     * Delete multiple items, by match PrimaryKey
     */
    @Delete
    fun deleteItems(item: List<T>?): Int

    /**
     * Update single item, by match PrimaryKey
     */
    @Update(onConflict = REPLACE)
    fun updateItem(item: T): Int

    /**
     * Update multiple items, by match PrimaryKey
     */
    @Update(onConflict = REPLACE)
    fun updateItems(item: List<T>?): Int
}