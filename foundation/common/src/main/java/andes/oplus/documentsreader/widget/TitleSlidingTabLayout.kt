/*********************************************************************
 ** Copyright (C), 2024-2034 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : TitleSlidingTabLayout
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/7/17 14:46
 ** Author      : 80262777
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 ** 80262777        2024/7/17       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.widget

import andes.oplus.documentsreader.core.common.R
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import android.graphics.Typeface
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.widget.FrameLayout
import android.widget.LinearLayout
import androidx.core.view.ViewCompat
import androidx.core.view.children
import com.coui.appcompat.animation.COUIMoveEaseInterpolator

open class TitleSlidingTabLayout : LinearLayout {

    private var indicatorAnimator: ValueAnimator? = null
    private var indicatorLeft = -1
    private var indicatorRight = -1
    private var lastPosition = 0
    private val tabs = java.util.ArrayList<TitleTabView>()
    private val indicatorPadding = 0
    private var selectedIndicatorPaint: Paint? = null
    private var path: Path? = null
    private var backgroundRect: RectF = RectF()
    private var radius: Float = 0f
    private val tabInitWidth by lazy {
        resources.getDimensionPixelOffset(R.dimen.dp_title_tab_item_width)
    }
    var selectedPosition = -1
    var normalTextColor = 0
    var selectedTextColor = 0
    var tabPaddingStart = 0
    var tabPaddingTop = 0
    var tabPaddingEnd = 0
    var tabPaddingBottom = 0

    var selectedTypeface: Typeface? = null
    var normalTypeface: Typeface? = null
    var isUpdateindicatorposition = false

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        val a = context.obtainStyledAttributes(
            attrs, R.styleable.TitleSlidingTabLayout
        )
        tabPaddingStart =
            a.getDimensionPixelSize(R.styleable.TitleSlidingTabLayout_tabPaddingStart, 0)
        tabPaddingTop = a.getDimensionPixelSize(R.styleable.TitleSlidingTabLayout_tabPaddingTop, 0)
        tabPaddingEnd = a.getDimensionPixelSize(R.styleable.TitleSlidingTabLayout_tabPaddingEnd, 0)
        tabPaddingBottom =
            a.getDimensionPixelSize(R.styleable.TitleSlidingTabLayout_tabPaddingBottom, 0)

        a.recycle()
        Log.d(TAG, "constructor, 2 paddingStart: $tabPaddingStart, $tabPaddingEnd $this")

        selectedTypeface = Typeface.create(MEDIUM_FONT, Typeface.NORMAL)
        normalTypeface = Typeface.create(REGULAR_FONT, Typeface.NORMAL)
        normalTextColor = resources.getColor(R.color.black_55_percent)
        selectedTextColor = resources.getColor(R.color.black_90_percent)
        selectedIndicatorPaint = Paint().apply {
            style = Paint.Style.FILL
            color = context.resources.getColor(R.color.main_title_indicator)
        }
        path = Path()
        radius = context.resources.getDimensionPixelOffset(R.dimen.dimen_16dp).toFloat()
        isForceDarkAllowed = false
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : this(
        context,
        attrs,
        defStyleAttr,
        0
    )

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int
    ) : super(context, attrs, defStyleAttr, defStyleRes) {
        val a = context.obtainStyledAttributes(
            attrs, R.styleable.TitleSlidingTabLayout,
            defStyleAttr, defStyleRes
        )
        tabPaddingStart =
            a.getDimensionPixelSize(R.styleable.TitleSlidingTabLayout_tabPaddingStart, 0)
        tabPaddingTop = a.getDimensionPixelSize(R.styleable.TitleSlidingTabLayout_tabPaddingTop, 0)
        tabPaddingEnd = a.getDimensionPixelSize(R.styleable.TitleSlidingTabLayout_tabPaddingEnd, 0)
        tabPaddingBottom =
            a.getDimensionPixelSize(R.styleable.TitleSlidingTabLayout_tabPaddingBottom, 0)

        a.recycle()
        Log.d(TAG, "constructor, 4 paddingStart: $tabPaddingStart, $tabPaddingEnd")
        selectedIndicatorPaint = Paint().apply {
            style = Paint.Style.FILL
            color = context.resources.getColor(R.color.main_title_indicator)
        }
        path = Path()
        radius = context.resources.getDimensionPixelOffset(R.dimen.dimen_16dp).toFloat()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val width = MeasureSpec.getSize(widthMeasureSpec) - paddingLeft - paddingRight
        val itemWidth = (width / childCount).coerceAtMost(tabInitWidth)
        children.forEach { view ->
            val makeMeasureSpec = MeasureSpec.makeMeasureSpec(itemWidth, MeasureSpec.EXACTLY)
            view.measure(
                makeMeasureSpec,
                MeasureSpec.makeMeasureSpec(view.measuredHeight, MeasureSpec.EXACTLY)
            )
            if (view.width != itemWidth) {
                isUpdateindicatorposition = true
            }
        }
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        super.onLayout(changed, l, t, r, b)
        if (isUpdateindicatorposition) {
            Log.d(TAG, "onLayout isUpdateindicatorposition true")
            animateIndicatorIntercept = true
            updateIndicatorPosition(updateSelectedText = true)
        }
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        Log.d(TAG, "onFinishInflate")
    }

    fun getTabAt(index: Int): TitleTabView? {
        return if (index < 0 || index >= getTabCount()) null else tabs[index]
    }

    fun getTabCount(): Int {
        return tabs.size
    }

    fun addTab(tab: TitleTabView, setSelected: Boolean) {
        addTab(tab, tabs.size, setSelected)
    }

    /**
     * Add a tab to this layout. The tab will be inserted at `position`.
     *
     * @param tab         The tab to add
     * @param position    The new position of the tab
     * @param setSelected True if the added tab should become the selected tab.
     */
    fun addTab(tab: TitleTabView, position: Int, setSelected: Boolean) {
        configureTab(tab, position)
        addTabView(tab)
        if (setSelected) {
            tab.select()
        }
    }

    fun configureTab(tab: TitleTabView, position: Int) {
        tab.position = position
        tabs.add(position, tab)
        val count = tabs.size
        for (i in position + 1 until count) {
            tabs[i].position = i
        }
    }

    fun newTab(): TitleTabView {
        val tabView = TitleTabView(context, this)
        tabView.isFocusable = true
        tabView.minimumWidth = getTabMinWidth()
        tabView.isEnabled = isEnabled
        return tabView
    }

    private fun addTabView(tabView: TitleTabView) {
        addView(
            tabView, tabView.position, LayoutParams(
                resources.getDimensionPixelOffset(R.dimen.dp_title_tab_item_width),
                FrameLayout.LayoutParams.MATCH_PARENT
            )
        )
    }

    fun updateAllTabs() {
        var i = 0
        while (i < tabs.size) {
            tabs[i].updateView()
            i++
        }
    }

    fun setPosition(position: Int) {
        val tab: TitleTabView? = getTabAt(position)
        if (position != getSelectedTabPosition() && tab != null) {
            tab.select()
        }
    }

    /**
     * Remove all tabs from the action bar and deselect the current tab.
     */
    fun removeAllTabs() {
        // Remove all the views
        for (i in childCount - 1 downTo 0) {
            removeTabViewAt(i)
        }
        val i: MutableIterator<TitleTabView> = tabs.iterator()
        while (i.hasNext()) {
            val tab = i.next()
            i.remove()
            tab.reset()
        }
        selectedTab = null
    }

    private fun removeTabViewAt(position: Int) {
        val view = getChildAt(position) as? TitleTabView
        removeViewAt(position)
        view?.reset()
        requestLayout()
    }

    override fun dispatchDraw(canvas: Canvas) {
        if (null != selectedIndicatorPaint && indicatorRight > indicatorLeft) {
            // 最小left
            val min: Int = paddingLeft - indicatorPadding
            //最大距离
            val max: Int = width - paddingRight + indicatorPadding
            var shouldDraw = true
            //indicator限制
            if (indicatorRight <= min) {
                shouldDraw = false
            } else if (indicatorLeft >= max) {
                shouldDraw = false
            }
            if (shouldDraw) {
                backgroundRect.set(
                    indicatorLeft.toFloat(), paddingTop.toFloat(),
                    indicatorRight.toFloat(), (height - paddingBottom).toFloat()
                )
                canvas.drawRoundRect(backgroundRect, radius, radius, selectedIndicatorPaint!!)
            }
        }
        super.dispatchDraw(canvas)
    }

    private fun getTabMinWidth(): Int {
        return MIN_WIDTH
    }

    private var selectedTab: TitleTabView? = null

    /**
     * @see updateIndicatorPosition
     */
    open fun selectTab(tab: TitleTabView?, updateIndicator: Boolean) {
        val currentTab = selectedTab
        if (currentTab === tab) {
            if (currentTab != null && tab != null) {
                dispatchTabReselected(tab)
            }
        } else {
            val newPosition = tab?.position ?: INVALID_POSITION
            if (updateIndicator) {
                if ((currentTab == null || currentTab.position == INVALID_POSITION || currentTab.left <= 0) && newPosition != INVALID_POSITION) {
                    // If we don't currently have a tab, just draw the indicator
                    updateIndicatorPosition(newPosition, true)
                    Log.d(TAG, "selectTab, current tab is null")
                } else {
                    Log.d(TAG, "selectTab, animate to tab, current tab : ${currentTab?.position}")
                    animateToTab(newPosition)
                }
                if (newPosition != INVALID_POSITION) {
                    setSelectedTabView(newPosition)
                }
                selectedPosition = newPosition
            }

            if (currentTab != null) {
                dispatchTabUnselected(currentTab)
            }
            selectedTab = tab
            if (tab != null) {
                dispatchTabSelected(tab)
            }
        }
    }

    private fun dispatchTabSelected(tab: TitleTabView) {
        for (i in selectedListeners.indices.reversed()) {
            selectedListeners.get(i).onTabSelected(tab)
        }
    }

    private fun dispatchTabReselected(tab: TitleTabView) {
        for (i in selectedListeners.indices.reversed()) {
            selectedListeners.get(i).onTabReselected(tab)
        }
    }

    private fun dispatchTabUnselected(tab: TitleTabView) {
        for (i in selectedListeners.indices.reversed()) {
            selectedListeners.get(i).onTabUnselected(tab)
        }
    }

    fun setIndicatorPositionFromTabPosition(position: Int) {
        if (indicatorAnimator != null && indicatorAnimator!!.isRunning) {
            indicatorAnimator!!.cancel()
        }
        selectedPosition = position
    }

    /**
     * called onLayout when first time select tab is null
     */
    private fun updateIndicatorPosition(
        position: Int = selectedPosition,
        updateSelectedText: Boolean = false
    ) {
        var left = 0
        var right = 0
        val selectedTitle = getChildAt(position) as TitleTabView
        Log.d(TAG, "initIndicatorPosition: width: ${selectedTitle.width}")
        if (selectedTitle.width > 0) {
            left = selectedTitle.left
            right = selectedTitle.right
            isUpdateindicatorposition = false
        } else {
            left = -1
            right = -1
        }
        setIndicatorPosition(left, right)
        if (updateSelectedText) {
            updateAllTabs()
        }
    }

    private fun setSelectedTabView(position: Int) {
        val tabCount: Int = childCount
        if (position < tabCount) {
            for (i in 0 until tabCount) {
                val child = getChildAt(i) as? TitleTabView
                child?.isSelected = i == position
            }
        }
    }

    private fun animateToTab(newPosition: Int) {
        if (newPosition == INVALID_POSITION) {
            return
        }
        // Now animate the indicator
        animateIndicatorToPosition(newPosition, ANIMATION_DURATION)
    }

    private val indicatorAnimTime = INVALID_WIDTH
    private var animateIndicatorIntercept = true

    open fun animateIndicatorToPosition(position: Int, duration: Int) {
        var isCancel = false
        if (indicatorAnimator != null && indicatorAnimator?.isRunning == true) {
            //可阻断，上次的还在running中，直接停止
            if (position != lastPosition) {
                indicatorAnimator?.end()
            } else {
                //cancel
                indicatorAnimator?.cancel()
                isCancel = true
            }
        }
        val targetView = getChildAt(position) ?: return
        val tabView = targetView as TitleTabView
        val curTabView = getChildAt(getSelectedTabPosition()) as TitleTabView
        if (tabView.getTextView() != null) {
            //cur距离parent的left
            val nextView = tabView.getTextView()
            val startLeft: Int = indicatorLeft
            val startRight: Int = indicatorRight
            //target距离parent的left
            val targetLeft: Int = tabView.left
            //target距离parent(x0点)的right
            val targetRight: Int = tabView.left + tabView.width
            //两个按钮文本之间的文本width diff，indicator width diff,此场景中因为item等宽，无变化，为0，不会跟着文本大小变化
            val widthDiff = targetRight - targetLeft - (startRight - startLeft)
            //left diff
            val leftDiff = targetLeft - startLeft
            val time = getIndicatorAnimTime(position, selectedPosition)
            indicatorAnimator = ValueAnimator()
            indicatorAnimator?.let {
                it.setDuration(time)
                it.interpolator = COUIMoveEaseInterpolator()
                it.setIntValues(0, 1)
                val evaluator = ArgbEvaluator()
                //anim cancel后重新anim的话，从当前颜色开始变化，正常：从normal变为selectedColor
                val nextStartColor = if (isCancel) nextView?.currentTextColor else normalTextColor
                val curStartColor = if (isCancel) curTabView.getTextView()?.currentTextColor else selectedTextColor
                var curWidth: Int
                var left: Int
                var right: Int
                // 调整日志打印位置，在 Listener 打印会多次调用
                Log.d(TAG, "startRight: $startRight, startLeft: $startLeft, widthDiff:$widthDiff")
                it.addUpdateListener { animator ->
                    if (animateIndicatorIntercept) {
                        it.cancel()
                        return@addUpdateListener
                    }
                    val offset = animator.animatedFraction
                    //文本颜色
                    nextView!!.setTextColor(evaluator.evaluate(offset, nextStartColor, selectedTextColor) as Int)
                    //文本start-end渐变效果
                    curTabView.getTextView()!!.setTextColor(evaluator.evaluate(offset, curStartColor, normalTextColor) as Int)
                    curWidth = startRight - startLeft
                    left = (startLeft + leftDiff * offset).toInt()
                    right = left + curWidth
                    setIndicatorPosition(left, right)
                }
                it.addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animator: Animator) {
                        selectedPosition = position
                    }
                })
                animateIndicatorIntercept = false
                it.start()
            }
        }
        lastPosition = getSelectedTabPosition()
    }

    private fun isLayoutRTL(): Boolean {
        return ViewCompat.getLayoutDirection(this) == ViewCompat.LAYOUT_DIRECTION_RTL
    }

    fun getSelectedTabPosition(): Int {
        return selectedTab?.position ?: -1
    }

    private fun getIndicatorAnimTime(newPosition: Int, oldPosition: Int): Long {
        return FOUR_HUNDRED
    }

    private fun setIndicatorPosition(left1: Int, right1: Int) {
        var left = left1
        var right = right1
        val center = (left + right) / 2
        val width = right - left
        left = center - width / 2
        right = center + width / 2
        if (left1 != indicatorLeft || right1 != indicatorRight) {
            // If the indicator's left/right has changed, invalidate
            indicatorLeft = left
            indicatorRight = right
            ViewCompat.postInvalidateOnAnimation(this)
        }
    }

    protected fun resetTextColorAfterAnim() {
        val tabCount: Int = getChildCount()
        for (i in 0 until tabCount) {
            val child: View = getChildAt(i)
            if (child is TitleTabView) {
                child.getTextView()
                    ?.setTextColor(if (selectedTab?.position == i) selectedTextColor else normalTextColor)
            }
        }
    }

    private var selectedListener: OnTabSelectedListener? = null
    private val selectedListeners = ArrayList<OnTabSelectedListener>()

    /**
     * registered listener
     */
    fun setOnTabSelectedListener(listener: OnTabSelectedListener?) {
        // registered listener
        selectedListener?.let { removeOnTabSelectedListener(it) }
        // time we're called
        selectedListener = listener
        listener?.let { addOnTabSelectedListener(it) }
    }

    /**
     * Add a [OnTabSelectedListener] that will be invoked when tab selection
     * changes.
     *
     * Components that add a listener should take care to remove it when finished via
     * [.removeOnTabSelectedListener].
     *
     * @param listener listener to add
     */
    fun addOnTabSelectedListener(listener: OnTabSelectedListener) {
        if (!selectedListeners.contains(listener)) {
            selectedListeners.add(listener)
        }
    }

    /**
     * Remove the given [OnTabSelectedListener] that was previously added via
     * [.addOnTabSelectedListener].
     *
     * @param listener listener to remove
     */
    fun removeOnTabSelectedListener(listener: OnTabSelectedListener) {
        selectedListeners.remove(listener)
    }

    fun clearOnTabSelectedListeners() {
        selectedListeners.clear()
    }

    /**
     * Callback interface invoked when a tab's selection state changes.
     */
    interface OnTabSelectedListener {
        /**
         * Called when a tab enters the selected state.
         *
         * @param tab The tab that was selected
         */
        fun onTabSelected(tab: TitleTabView?)

        /**
         * Called when a tab exits the selected state.
         *
         * @param tab The tab that was unselected
         */
        fun onTabUnselected(tab: TitleTabView?)

        /**
         * Called when a tab that is already selected is chosen again by the user. Some applications
         * may use this action to return to the top level of a category.
         *
         * @param tab The tab that was reselected.
         */
        fun onTabReselected(tab: TitleTabView?)
    }

    companion object {
        const val TAG = "TitleSlidingTabLayout"
        private const val ANIMATION_DURATION = 400
        const val FOUR_HUNDRED = 400L
        const val INVALID_WIDTH = -1L
        const val MIN_WIDTH = 0
        private const val MEDIUM_FONT = "sans-serif-medium"
        private const val REGULAR_FONT = "sans-serif"
        const val INVALID_POSITION = -1
    }
}