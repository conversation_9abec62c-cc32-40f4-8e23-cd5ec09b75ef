/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: VideoPreviewFragment
 * * Description: the audio view
 * * Version: 1.0
 * * Date : 2024/3/12
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2024/3/12       1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.filepreview.audio

import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.TextViewSnippet
import andes.oplus.documentsreader.core.common.utils.LaunchUtil.runOnIO
import andes.oplus.documentsreader.filepreview.R
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.MediaMetadataRetriever
import android.media.MediaPlayer
import android.text.TextUtils
import android.util.AttributeSet
import android.util.TypedValue
import android.view.ViewTreeObserver
import android.widget.ImageView

class AudioPlayerView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    BaseMediaPlayerView(context, attrs, defStyleAttr) {
    private lateinit var musicArtist: TextViewSnippet
    private lateinit var musicTitle: TextViewSnippet
    private lateinit var musicIcon: ImageView
    private var fileName: String = ""

    companion object {
        const val TITLE_SIZE_SMALL = 14f
        const val TITLE_SIZE_BIG = 18f
        const val TITLE_MAX_LINE = 2f
    }

    override fun getLayoutId(): Int {
        return R.layout.oplus_doc_audio_player_layout
    }

    override fun initView() {
        super.initView()
        musicArtist = findViewById(R.id.music_artist)
        musicTitle = findViewById(R.id.music_title)
        musicIcon = findViewById(R.id.audio_icon)
    }

    override fun onPrepared(mp: MediaPlayer?) {
        super.onPrepared(mp)
        //getInfoFromMetadata()
    }

    override fun setMediaUrl(url: String) {
        super.setMediaUrl(url)
        getInfoFromMetadata()
    }

    fun setFileName(fileName: String) {
        this.fileName = fileName
    }

    private fun setMusicInfo(title: String?, artist: String?, albumBmp: Bitmap?) {
        Log.d(TAG, "setMusicInfo title:$title  artist:$artist albumBmp not null ${albumBmp != null}")
        if (albumBmp != null) { // 没有专辑图片时默认图片缩小为64dp
            musicIcon.setPadding(0, 0, 0, 0)
            musicIcon.setImageBitmap(albumBmp)
        }
        if (!TextUtils.isEmpty(artist)) {
            musicArtist.text = artist
            musicArtist.setTextViewStyleForFixedWidth()
        }
        val realTitle = title ?: fileName
        musicTitle.text = realTitle

        musicTitle.viewTreeObserver.addOnPreDrawListener(object : ViewTreeObserver.OnPreDrawListener {
            override fun onPreDraw(): Boolean {
                val lineCount = musicTitle.lineCount
                Log.d(TAG, "setMusicInfo lineCount:$lineCount")
                if (lineCount >= TITLE_MAX_LINE) {
                    musicTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, TITLE_SIZE_SMALL)
                } else {
                    musicTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, TITLE_SIZE_BIG)
                }
                musicTitle.setTextViewStyleForFixedWidth()
                musicTitle.viewTreeObserver.removeOnPreDrawListener(this)
                return false
            }
        })
    }

    private fun getInfoFromMetadata() {
        var artist: String? = null
        var title: String? = null
        var albumBmp: Bitmap? = null
        runOnIO(context, {
            MediaMetadataRetriever().use {
                it.setDataSource(playUrl)
                artist = it.extractMetadata(MediaMetadataRetriever.METADATA_KEY_ARTIST)
                title = it.extractMetadata(MediaMetadataRetriever.METADATA_KEY_TITLE)
                Log.d(TAG, "artist = $title, title = $title")
                it.embeddedPicture?.let { pictureByte ->
                    albumBmp = BitmapFactory.decodeByteArray(pictureByte, 0, pictureByte.size)
                }
            }
        }, {
            setMusicInfo(title, artist, albumBmp)
        }) {
            Log.d(TAG, "getInfoFromMetadata error:$it")
        }
    }
}
