<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <style>
      * {
        padding: 0;
        margin: 0;
      }
      div {
        height: calc(100vh);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
      img {
        width: 75%;
      }
      p {
        font-size: 16px;
        color: rgba(0,0,0,0.302);
      }
    </style>
</head>

<body>
<div>
    <img src="no_network.svg" align="center">
    <p id="no_network" align="center"></p>
</div>

<script type="text/javascript">
    window.onload = function(){
        var str = fileJsInterface.getNoNetworkStr();
        document.getElementById("no_network").innerHTML = str;
    }
</script>
</body>
</html>