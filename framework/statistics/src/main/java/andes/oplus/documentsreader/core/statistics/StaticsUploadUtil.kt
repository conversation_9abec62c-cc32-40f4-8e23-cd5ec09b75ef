/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : StaticsUploadUtil
 ** Description : 埋点的上传Util
 ** Version     : 1.0
 ** Date        : 2023/8/7 15:22
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/7/3       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.core.statistics

import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.FileUtils
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.statistics.StaticConstants.ACTIVITY_SHOW_LAUNCH_MODE
import andes.oplus.documentsreader.core.statistics.StaticConstants.ACTIVITY_SHOW_OCCASSION
import andes.oplus.documentsreader.core.statistics.StaticConstants.CLICK_OPEN_FILE_EXT
import andes.oplus.documentsreader.core.statistics.StaticConstants.CLICK_OPEN_FILE_POSITON
import andes.oplus.documentsreader.core.statistics.StaticConstants.CLICK_OPEN_FILE_SOURCE
import andes.oplus.documentsreader.core.statistics.StaticConstants.EVENT_ACTIVITY_SHOW
import andes.oplus.documentsreader.core.statistics.StaticConstants.EVENT_CLICK_OPEN_FILE
import andes.oplus.documentsreader.core.statistics.StaticConstants.EVENT_OPEN_MY_FILE
import andes.oplus.documentsreader.core.statistics.StaticConstants.EVENT_QUICK_TOOL_CLICK
import andes.oplus.documentsreader.core.statistics.StaticConstants.EVENT_SEARCH_CLICK
import andes.oplus.documentsreader.core.statistics.StaticConstants.EVENT_TAB_CLICK
import andes.oplus.documentsreader.core.statistics.StaticConstants.EVENT_TOOL_CLICK
import andes.oplus.documentsreader.core.statistics.StaticConstants.OPEN_MY_FILE_POSITION
import andes.oplus.documentsreader.core.statistics.StaticConstants.SHOW_LAUNCH_CODE_ALLFILES
import andes.oplus.documentsreader.core.statistics.StaticConstants.SHOW_LAUNCH_COLD
import andes.oplus.documentsreader.core.statistics.StaticConstants.SHOW_LAUNCH_HOT
import andes.oplus.documentsreader.core.statistics.StaticConstants.SHOW_OCCASSION_MAIN_ACTIVITY
import andes.oplus.documentsreader.core.statistics.StaticConstants.SHOW_OCCASSION_PREVIEW_ACTIVITY
import andes.oplus.documentsreader.core.statistics.StaticConstants.TAB_CLICK_BOTTON
import andes.oplus.documentsreader.core.statistics.StaticConstants.TAB_CLICK_BOTTON_MAIN
import andes.oplus.documentsreader.core.statistics.StaticConstants.TOOL_CLICK_BUTTON

object StaticsUploadUtil {


    const val TAG = "StaticsUploadUtil"

    /**
     * 页面显示埋点
     */
    @JvmStatic
    fun activityShow(isMainActivity: Boolean, isHotLaunch: Boolean) {
        Log.i(TAG, "activityShow isMainActivity $isMainActivity, isHotLaunch $isHotLaunch")
        val eventId = EVENT_ACTIVITY_SHOW
        val occasssion = if (isMainActivity) {
            SHOW_OCCASSION_MAIN_ACTIVITY
        } else {
            SHOW_OCCASSION_PREVIEW_ACTIVITY
        }
        val launchMode = if (isHotLaunch) {
            SHOW_LAUNCH_HOT
        } else {
            SHOW_LAUNCH_COLD
        }
        val data = mapOf(
            ACTIVITY_SHOW_OCCASSION to occasssion.toString(),
            ACTIVITY_SHOW_LAUNCH_MODE to launchMode.toString()
        )
        StatisticsUtils.statistics(
            ContextGetter.context,
            eventId = eventId,
            customData = data
        )
    }


    /**
     * 点击tab埋点
     */
    @JvmStatic
    fun clickMainTab(isMain: Boolean) {
        Log.i(TAG, "clickMainTab isMain $isMain")
        val eventId = EVENT_TAB_CLICK
        val clickPosition = if (isMain) {
            TAB_CLICK_BOTTON_MAIN
        } else {
            SHOW_LAUNCH_CODE_ALLFILES
        }
        val data = mapOf(TAB_CLICK_BOTTON to clickPosition.toString())
        StatisticsUtils.statistics(
            ContextGetter.context,
            eventId = eventId,
            customData = data
        )
    }

    /**
     * 点击搜素工具埋点
     */
    @JvmStatic
    fun clickSearch() {
        Log.i(TAG, "clickSearch")
        val eventId = EVENT_SEARCH_CLICK
        StatisticsUtils.statistics(
            ContextGetter.context,
            eventId = eventId,
            customData = null
        )
    }


    /**
     * 点击工具埋点
     */
    @JvmStatic
    fun clickAllTool(toolId: Int) {
        Log.i(TAG, "clickAllTool toolId $toolId")
        val eventId = EVENT_TOOL_CLICK
        val data = mapOf(TOOL_CLICK_BUTTON to toolId.toString())
        StatisticsUtils.statistics(
            ContextGetter.context,
            eventId = eventId,
            customData = data
        )
    }

    /**
     * 点击快捷工具埋点
     */
    @JvmStatic
    fun clickQuickTool(toolId: Int) {
        Log.i(TAG, "clickQuickTool toolId $toolId")
        val eventId = EVENT_QUICK_TOOL_CLICK
        val data = mapOf(TOOL_CLICK_BUTTON to toolId.toString())
        StatisticsUtils.statistics(
            ContextGetter.context,
            eventId = eventId,
            customData = data
        )
    }


    /**
     * 点击打开文件的
     */
    @JvmStatic
    fun clickOpenFile(
        sourceType: Int,
        filePath: String?,
        isDirectory: Boolean,
        clickPosistion: Int? = null
    ) {
        val eventId = EVENT_CLICK_OPEN_FILE
        val fileExt = if (isDirectory) {
            "dir"
        } else {
            FileUtils.getExtension(filePath)
        }
        val data = mutableMapOf(
            CLICK_OPEN_FILE_SOURCE to sourceType.toString(),
            CLICK_OPEN_FILE_EXT to fileExt
        )
        if (clickPosistion != null) {
            data[CLICK_OPEN_FILE_POSITON] = clickPosistion.toString()
        }
        Log.i(
            TAG,
            "clickOpenFile sourceType $sourceType, filePath $filePath, isDirectory $isDirectory, clickPosistion $clickPosistion, ext $fileExt"
        )
        StatisticsUtils.statistics(
            ContextGetter.context,
            eventId = eventId,
            customData = data
        )
    }


    /**
     * 点击我创建的文件
     */
    @JvmStatic
    fun clickMyFile(myFileType: Int) {
        Log.i(TAG, "clickMyFile myFileType $myFileType")
        val eventId = EVENT_OPEN_MY_FILE
        val data = mapOf(OPEN_MY_FILE_POSITION to myFileType.toString())
        StatisticsUtils.statistics(
            ContextGetter.context,
            eventId = eventId,
            customData = data
        )
    }
}