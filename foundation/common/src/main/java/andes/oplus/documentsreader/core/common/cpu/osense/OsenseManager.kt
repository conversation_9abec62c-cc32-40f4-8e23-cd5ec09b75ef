/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : OsenseManager
 * * Version     : 1.0
 * * Date        : 2022/6/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.core.common.cpu.osense

import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.cpu.PerformanceInterface
import androidx.annotation.VisibleForTesting
import com.oplus.osense.OsenseResClient
import com.oplus.osense.info.OsenseSaRequest

internal object OsenseManager : PerformanceInterface {

    private const val TAG = "OsenseManager"
    private const val DEFAULT_SCENE = "OSENSE_SYSTEM_SCENE_STORAGE"
    private const val COPY_CUT_ACTION = "OSENSE_ACTION_FILE_COPY_CUT"
    private const val UN_OR_COMPRESS_ACTION = "OSENSE_ACTION_FILE_ZIP_UNZIP"
    private const val INVALID_ID = -1L

    private val _osenseClient: OsenseResClient? by lazy {
        kotlin.runCatching {
            OsenseResClient.get(OsenseManager::class.java)
        }.onFailure {
            Log.w(TAG, "osenseManager INIT error: $it")
        }.getOrNull()
    }

    @VisibleForTesting
    internal val osenseClient: OsenseResClient?
        get() = _osenseClient

    @VisibleForTesting
    internal var lastResourceId: Long = INVALID_ID

    override fun releasePerformanceManager() {
        innerRelease(lastResourceId)
        lastResourceId = INVALID_ID
    }

    override fun setSceneAction(action: String, timeout: Int) {
        Log.d(TAG, "setSceneAction: action=$action, timeout=$timeout")
        lastResourceId = kotlin.runCatching {
            osenseClient?.osenseSetSceneAction(createRequest(action, timeout))
        }.onFailure {
            Log.w(TAG, "setSceneAction error: $it")
        }.getOrNull() ?: INVALID_ID
    }

    @VisibleForTesting
    internal fun createRequest(action: String, timeout: Int): OsenseSaRequest =
        OsenseSaRequest(DEFAULT_SCENE, action, timeout)

    override fun getCopyScene(): String = COPY_CUT_ACTION

    override fun getUnzipScene(): String = UN_OR_COMPRESS_ACTION

    private fun innerRelease(resId: Long) {
        Log.d(TAG, "innerRelease: $resId")
        kotlin.runCatching {
            if (resId > INVALID_ID) {
                osenseClient?.osenseClrSceneAction(resId)
            }
        }.onFailure {
            Log.w(TAG, "innerRelease error: $it")
        }
    }
}