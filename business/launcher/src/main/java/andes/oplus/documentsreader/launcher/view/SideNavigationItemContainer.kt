/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/08/05, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package andes.oplus.documentsreader.launcher.view

import andes.oplus.documentsreader.launcher.R
import android.content.Context
import android.graphics.Color
import android.graphics.RectF
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.os.Build
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View.MeasureSpec.AT_MOST
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.ViewCompat
import com.coui.appcompat.state.COUIMaskEffectDrawable
import com.coui.appcompat.state.COUIMaskEffectDrawable.MASK_EFFECT_TYPE_CONTAINER_WIDGET
import com.coui.appcompat.state.COUIStateEffectDrawable
import com.coui.appcompat.theme.COUIThemeOverlay
import java.lang.Integer.max

class SideNavigationItemContainer : ViewGroup {

    companion object {
        private const val GAP_COUNT_WITH_WIDGET = 3
        private const val GAP_COUNT_WITHOUT_WIDGET = 2
    }

    private lateinit var dragView: ImageView
    private lateinit var optionIcon: ImageView
    private lateinit var optionTitle: TextView
    private lateinit var optionSubTitle: TextView
    private lateinit var widgetFrame: LinearLayout

    private val layoutBackgroundRect = RectF()

    private val gap: Int = context.resources.getDimensionPixelOffset(R.dimen.oplus_doc_sidebar_list_item_gap)
    private val margin: Int = context.resources.getDimensionPixelOffset(R.dimen.oplus_doc_sidebar_list_item_margin)
    private val titleMinWidth: Int = context.resources.getDimensionPixelOffset(R.dimen.oplus_doc_sidebar_list_item_title_min_width)

    private var editState = false
    private var shouldLayoutSubtitle = false
    private var shouldLayoutWidget = false
    private var shouldLayoutDrag = false

    private var maskDrawable: COUIMaskEffectDrawable? = null
    private var stateEffectDrawable: COUIStateEffectDrawable? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    override fun onFinishInflate() {
        super.onFinishInflate()
        COUIThemeOverlay.getInstance().applyThemeOverlays(context)
        initView()
    }

    fun setEditState(value: Boolean) {
        if (editState == value) {
            return
        }
        editState = value
        if (editState) {
            widgetFrame.getChildAt(0)?.apply {
                isClickable = true
                isFocusable = true
            }
        } else {
            widgetFrame.getChildAt(0)?.apply {
                isClickable = false
                isFocusable = false
            }
        }
        requestLayout()
    }

    private fun initView() {
        dragView = findViewById(R.id.drag_view)
        optionIcon = findViewById(R.id.option_icon)
        optionTitle = findViewById(R.id.option_title)
        optionSubTitle = findViewById(R.id.option_subtitle)
        widgetFrame = findViewById(R.id.option_widget)

        initBackground()
    }

    private fun initBackground() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            defaultFocusHighlightEnabled = false
        }
        maskDrawable = COUIMaskEffectDrawable(context, MASK_EFFECT_TYPE_CONTAINER_WIDGET).apply {
            val radius = context.resources.getDimension(R.dimen.oplus_doc_sidebar_list_item_wrapper_corner_radius)
            setMaskRect(layoutBackgroundRect, radius, radius)
        }
        val drawables = arrayOf(
            if (background == null) ColorDrawable(Color.TRANSPARENT) else background,
            maskDrawable
        )
        stateEffectDrawable = COUIStateEffectDrawable(drawables)
        super.setBackground(stateEffectDrawable)
    }

    override fun setBackground(background: Drawable?) {
        if (stateEffectDrawable != null) {
            if (background == null) {
                stateEffectDrawable?.setViewBackground(ColorDrawable(Color.TRANSPARENT))
            } else {
                stateEffectDrawable?.setViewBackground(background)
            }
        } else {
            super.setBackground(background)
        }
    }

    fun setIsSelected(selected: Boolean, animated: Boolean) {
        maskDrawable?.setTouchEnterStateLocked(selected, selected, animated)
    }

    fun setIsHover(selected: Boolean, animated: Boolean) {
        maskDrawable?.setHoverStateLocked(selected, selected, animated)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        var availableWidth = MeasureSpec.getSize(widthMeasureSpec)
        var maxChildHeight = 0

        checkViewState()

        // available width = total width - gap - padding
        availableWidth -= (paddingStart + paddingEnd) + margin * 2
        availableWidth -= calculateTotalGapWidth(editState)

        // measure drag image
        availableWidth = measureDragView(availableWidth, widthMeasureSpec, heightMeasureSpec)
        maxChildHeight = max(maxChildHeight, dragView.measuredHeight)

        // measure option icon
        availableWidth = measureOptionIcon(availableWidth, widthMeasureSpec, heightMeasureSpec)
        maxChildHeight = max(maxChildHeight, optionIcon.measuredHeight)

        // measure widget frame
        availableWidth = measureWidgetFrame(availableWidth, widthMeasureSpec, heightMeasureSpec)
        maxChildHeight = max(maxChildHeight, widgetFrame.measuredHeight)

        // measure main title and subtitle with available space
        measureText(availableWidth, widthMeasureSpec, heightMeasureSpec)
        maxChildHeight = max(maxChildHeight, optionTitle.measuredHeight)
        maxChildHeight = max(maxChildHeight, optionSubTitle.measuredHeight)

        setMeasuredDimension(MeasureSpec.getSize(widthMeasureSpec), maxChildHeight + paddingTop + paddingBottom)
    }

    private fun checkViewState() {
        shouldLayoutWidget = widgetFrame.childCount != 0 && widgetFrame.visibility != GONE
        shouldLayoutSubtitle = !TextUtils.isEmpty(optionSubTitle.text) && optionSubTitle.visibility != GONE
        shouldLayoutDrag = dragView.visibility != GONE
    }

    private fun calculateTotalGapWidth(edit: Boolean): Int {
        val res = if (edit) {
            if (shouldLayoutWidget) GAP_COUNT_WITH_WIDGET else GAP_COUNT_WITHOUT_WIDGET
        } else {
            if (shouldLayoutSubtitle) GAP_COUNT_WITH_WIDGET else GAP_COUNT_WITH_WIDGET - 1
        }
        return res * gap
    }

    private fun measureDragView(availableWidth: Int, widthMeasureSpec: Int, heightMeasureSpec: Int): Int {
        if (!shouldLayoutDrag) {
            return availableWidth
        }
        val lp = dragView.layoutParams

        val childWidthMeasureSpec = getChildMeasureSpec(widthMeasureSpec, 0, lp.width)
        val childHeightMeasureSpec = getChildMeasureSpec(heightMeasureSpec, 0, lp.height)
        dragView.measure(childWidthMeasureSpec, childHeightMeasureSpec)
        return if (editState) (availableWidth - dragView.measuredWidth) else availableWidth
    }

    private fun measureOptionIcon(availableWidth: Int, widthMeasureSpec: Int, heightMeasureSpec: Int): Int {
        val lp = optionIcon.layoutParams

        val childWidthMeasureSpec = getChildMeasureSpec(widthMeasureSpec, 0, lp.width)
        val childHeightMeasureSpec = getChildMeasureSpec(heightMeasureSpec, 0, lp.height)
        optionIcon.measure(childWidthMeasureSpec, childHeightMeasureSpec)
        return availableWidth - optionIcon.measuredWidth
    }

    private fun measureText(availableWidth: Int, widthMeasureSpec: Int, heightMeasureSpec: Int) {
        preMeasureTitle(widthMeasureSpec, heightMeasureSpec)

        if (optionTitle.measuredWidth > titleMinWidth) {
            val lp1 = optionSubTitle.layoutParams
            val childHeightMeasureSpec1 = getChildMeasureSpec(heightMeasureSpec, 0, lp1.height)
            val subtitleWidth = if (editState) {
                var res = MeasureSpec.getSize(widthMeasureSpec) - calculateTotalGapWidth(false) - ((paddingStart + paddingEnd) + margin * 2)
                res -= optionIcon.measuredWidth
                res -= titleMinWidth
                res
            } else {
                availableWidth - titleMinWidth
            }
            optionSubTitle.measure(MeasureSpec.makeMeasureSpec(subtitleWidth, AT_MOST), childHeightMeasureSpec1)

            val lp2 = optionTitle.layoutParams
            val childHeightMeasureSpec2 = getChildMeasureSpec(heightMeasureSpec, 0, lp2.height)

            val remainingWidth = if (editState) availableWidth else availableWidth - optionSubTitle.measuredWidth
            optionTitle.measure(MeasureSpec.makeMeasureSpec(max(titleMinWidth, remainingWidth), AT_MOST), childHeightMeasureSpec2)
        } else {
            val lp = optionSubTitle.layoutParams
            val childHeightMeasureSpec = getChildMeasureSpec(heightMeasureSpec, 0, lp.height)
            val subtitleWidth = if (editState) {
                var res = MeasureSpec.getSize(widthMeasureSpec) - calculateTotalGapWidth(false) - ((paddingStart + paddingEnd) + margin * 2)
                res -= optionIcon.measuredWidth
                res -= optionTitle.measuredWidth
                res
            } else {
                availableWidth - optionTitle.measuredWidth
            }
            optionSubTitle.measure(MeasureSpec.makeMeasureSpec(subtitleWidth, AT_MOST), childHeightMeasureSpec)
        }
    }

    private fun preMeasureTitle(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val lp = optionTitle.layoutParams
        val childWidthMeasureSpec = getChildMeasureSpec(widthMeasureSpec, 0, lp.width)
        val childHeightMeasureSpec = getChildMeasureSpec(heightMeasureSpec, 0, lp.height)
        optionTitle.measure(childWidthMeasureSpec, childHeightMeasureSpec)
    }

    private fun measureWidgetFrame(availableWidth: Int, widthMeasureSpec: Int, heightMeasureSpec: Int): Int {
        if (!shouldLayoutWidget) {
            return availableWidth
        }
        val maxWidth = MeasureSpec.getSize(widthMeasureSpec)
        widgetFrame.measure(MeasureSpec.makeMeasureSpec(maxWidth, AT_MOST), heightMeasureSpec)
        return if (editState) (availableWidth - widgetFrame.measuredWidth) else availableWidth
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        layoutFromStart()
        layoutFromEnd()
    }

    private fun layoutFromStart() {
        val isRtl = isRtl()
        var currentX = paddingStart + margin
        if (editState) {
            if (shouldLayoutDrag) {
                val dragLeft = if (isRtl) measuredWidth - currentX - dragView.measuredWidth else currentX
                val dragTop = (measuredHeight - dragView.measuredHeight) / 2
                dragView.layout(dragLeft, dragTop, dragLeft + dragView.measuredWidth, dragTop + dragView.measuredHeight)
                currentX += dragView.measuredWidth + gap
            }

            val iconLeft = if (isRtl) measuredWidth - currentX - optionIcon.measuredWidth else currentX
            val iconTop = (measuredHeight - optionIcon.measuredHeight) / 2
            optionIcon.layout(iconLeft, iconTop, iconLeft + optionIcon.measuredWidth, iconTop + optionIcon.measuredHeight)
            currentX += optionIcon.measuredWidth + gap

            val titleLeft = if (isRtl) measuredWidth - currentX - optionTitle.measuredWidth else currentX
            val titleTop = (measuredHeight - optionTitle.measuredHeight) / 2
            optionTitle.layout(titleLeft, titleTop, titleLeft + optionTitle.measuredWidth, titleTop + optionTitle.measuredHeight)
        } else {
            val iconLeft = if (isRtl) measuredWidth - currentX - optionIcon.measuredWidth else currentX
            val iconTop = (measuredHeight - optionIcon.measuredHeight) / 2
            optionIcon.layout(iconLeft, iconTop, iconLeft + optionIcon.measuredWidth, iconTop + optionIcon.measuredHeight)
            currentX += optionIcon.measuredWidth + gap

            if (shouldLayoutDrag) {
                val dragLeft = if (isRtl) optionIcon.right + gap else optionIcon.left - gap - dragView.measuredWidth
                val dragTop = (measuredHeight - dragView.measuredHeight) / 2
                dragView.layout(dragLeft, dragTop, dragLeft + dragView.measuredWidth, dragTop + dragView.measuredHeight)
            }

            val titleLeft = if (isRtl) measuredWidth - currentX - optionTitle.measuredWidth else currentX
            val titleTop = (measuredHeight - optionTitle.measuredHeight) / 2
            optionTitle.layout(titleLeft, titleTop, titleLeft + optionTitle.measuredWidth, titleTop + optionTitle.measuredHeight)
        }
    }

    private fun layoutFromEnd() {
        val isRtl = isRtl()
        if (shouldLayoutWidget) {
            if (editState) {
                val widgetLeft = if (isRtl) paddingEnd + margin else measuredWidth - paddingEnd - margin - widgetFrame.measuredWidth
                val widgetTop = (measuredHeight - widgetFrame.measuredHeight) / 2
                widgetFrame.layout(widgetLeft, widgetTop, widgetLeft + widgetFrame.measuredWidth, widgetTop + widgetFrame.measuredHeight)
            } else {
                val widgetLeft = if (isRtl) paddingEnd else measuredWidth - paddingEnd - widgetFrame.measuredWidth
                val widgetTop = (measuredHeight - widgetFrame.measuredHeight) / 2
                widgetFrame.layout(widgetLeft, widgetTop, widgetLeft + widgetFrame.measuredWidth, widgetTop + widgetFrame.measuredHeight)
            }
        }
        if (shouldLayoutSubtitle) {
            val subtitleLeft = if (isRtl) paddingEnd + margin else measuredWidth - paddingEnd - margin - optionSubTitle.measuredWidth
            val subtitleTop = (measuredHeight - optionSubTitle.measuredHeight) / 2
            optionSubTitle.layout(subtitleLeft, subtitleTop, subtitleLeft + optionSubTitle.measuredWidth, subtitleTop + optionSubTitle.measuredHeight)
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        val paddingHorizontal = context.resources.getDimension(R.dimen.oplus_doc_sidebar_list_item_wrapper_padding_horizontal)
        layoutBackgroundRect.set(paddingHorizontal, 0F, w - paddingHorizontal, h.toFloat())
    }

    private fun isRtl(): Boolean {
        return ViewCompat.getLayoutDirection(this) == ViewCompat.LAYOUT_DIRECTION_RTL
    }
}