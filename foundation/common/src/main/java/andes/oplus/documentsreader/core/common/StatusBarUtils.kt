/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : StatusBarUtil
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/6/26 21:54
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/6/26       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.core.common

import andes.oplus.documentsreader.core.common.compat.FeatureCompat
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.os.Build
import android.provider.Settings
import android.view.View
import android.view.WindowInsets
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.FragmentActivity
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.statusbar.COUIStatusbarTintUtil
import com.coui.appcompat.version.COUIVersionUtil

object StatusBarUtils {

    private const val TAG = "StatusBarUtil"

    @JvmStatic
    fun getStatusBarHeight(context: Context): Int = COUIPanelMultiWindowUtils.getStatusBarHeight(context)

    /**
     * 隐藏导航栏
     */
    private const val HIDE_NAV_ENABLE_KEY = "hide_navigationbar_enable"
    private const val NAV_STATE_VIRTUAL_KEY = 0 //导航栏状态-虚拟按键
    private const val NAV_STATE_SWIPE_UP_GESTURE = 2 //导航栏状态-全屏手势 针对Oppo 老机型的一种手势导航
    private const val NAV_STATE_SWIPE_SIDE_GESTURE = 3    //导航栏状态-全屏手势

    @SuppressLint("ObsoleteSdkInt")
    @JvmStatic
    fun setStatusBarTransparentAndBlackFont(activity: Activity) {
        val window = activity.window
        val decorView = activity.window.decorView
        if (SdkUtils.getSDKVersion() >= Build.VERSION_CODES.LOLLIPOP) {
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            window.statusBarColor = Color.TRANSPARENT
        }
        var flag = decorView.systemUiVisibility
        val versionCode = COUIVersionUtil.getOSVersionCode()
        val white = activity.resources.getBoolean(R.bool.is_status_white)
        if (versionCode >= COUIVersionUtil.COUI_3_0 || versionCode == 0) {
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            if (COUIDarkModeUtil.isNightMode(activity)) {
                flag = flag and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv()
                flag = flag and View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR.inv()
            } else {
                flag = if (SdkUtils.getSDKVersion() >= Build.VERSION_CODES.M) {
                    if (!white) {
                        flag or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
                    } else {
                        flag or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    }
                } else {
                    flag or COUIStatusbarTintUtil.SYSTEM_UI_FLAG_OP_STATUS_BAR_TINT
                }
            }
            decorView.systemUiVisibility = flag
        }
    }

    @SuppressLint("NewApi")
    @JvmStatic
    fun setNavigationBarColor(activity: Activity, navigationBarColorRes: Int = -1) {
        if (SdkUtils.getSDKVersion() >= Build.VERSION_CODES.LOLLIPOP) {
            if (!COUIDarkModeUtil.isNightMode(activity)) {
                activity.window.navigationBarColor = if (navigationBarColorRes != -1) {
                    Log.i(TAG, "navicolor setNavigationBarColor navigationBarColorRes !=-1")
                    activity.resources.getColor(navigationBarColorRes, activity.theme)
                } else {
                    Log.i(TAG, "navicolor setNavigationBarColor navigationBarColorRes -1")
                    COUIContextUtil.getAttrColor(activity, R.attr.couiColorBackground)
                }
            } else {
                //bug4154313 分屏resize时会导致闪白。解决方案是：应用暗色模式时，设置导航栏背景色为透明，就不会绘制白色
                activity.window.isNavigationBarContrastEnforced = false
                activity.window.navigationBarColor = Color.TRANSPARENT
            }
        }
        adaptNavigationBarForOS12(activity)
    }

    /**
     * 为适配OS12底部导航栏，当是手势导航时，并且需要适配的页面（通常为列表页，可滚动页面）
     * 将底部导航条设为透明，且隐藏导航栏空间，即可出现底部沉浸式效果
     */
    @JvmStatic
    fun adaptNavigationBarForOS12(activity: Activity) {
        if (SdkUtils.isAtLeastS()) {
            val isAdaptNavigationBar = checkIsAdaptNavigationBar(activity)
            //适配12底部导航栏
            val isGestureNavMode = checkIsGestureNavMode(activity)
            Log.d(TAG, "adaptNavigationBarForOS12 gestureMode = $isGestureNavMode isAdapt = $isAdaptNavigationBar")
            if (isAdaptNavigationBar && isGestureNavMode) {
                activity.window.setDecorFitsSystemWindows(false)
                activity.window.isNavigationBarContrastEnforced = false
                activity.window.navigationBarColor = Color.TRANSPARENT
            }
        }
    }

    @JvmStatic
    fun navigationBarTransparent(activity: Activity) {
        activity.window.setDecorFitsSystemWindows(false)
        activity.window.isNavigationBarContrastEnforced = false
        activity.window.navigationBarColor = Color.TRANSPARENT
    }

    @JvmStatic
    private fun checkIsAdaptNavigationBar(activity: Activity): Boolean {
        if (activity is BaseVMActivity) {
            return activity.isAdaptNavigationBar()
        }
        return false
    }

    /**
     * 为适配OS12底部导航栏
     * 当弹起底部文件操作栏时，需要重置底部导航栏为不透明，
     */
    @JvmStatic
    fun resetAdaptingNavigationBarForOS12(activity: Activity, ignoreIsAdaptNavigationBar: Boolean = false) {
        if (SdkUtils.isAtLeastS()) {
            //适配12底部导航栏
            val isGestureNavMode = checkIsGestureNavMode(activity)
            Log.d(TAG, "resetAdaptingNavigationBarForOS12 isGestureNavMode :$isGestureNavMode isAdaptNavigationBar:$ignoreIsAdaptNavigationBar")
            if (ignoreIsAdaptNavigationBar && isGestureNavMode) {
                activity.window.setDecorFitsSystemWindows(true)
                activity.window.isNavigationBarContrastEnforced = true
                activity.window.navigationBarColor =
                    activity.resources.getColor(R.color.oplusdoc_navigation_bar_color, activity.theme)
            }
        }
    }

    /**
     * 检测是否是手势导航，是返回True
     */
    @JvmStatic
    fun checkIsGestureNavMode(activity: Activity): Boolean {
        val secureIndex = Settings.Secure.getInt(
            activity.contentResolver, HIDE_NAV_ENABLE_KEY, NAV_STATE_VIRTUAL_KEY)

        return (secureIndex == NAV_STATE_SWIPE_UP_GESTURE
                || secureIndex == NAV_STATE_SWIPE_SIDE_GESTURE)
    }

    /**
     * 适配Taskbar，不是所有页面都需要使用
     * 当有被taskbar遮挡的页面，调用此方法适配，增加底部高度进行适配
     * 使用此方法需要在页面销毁时注销监听
     * @param view 页面容器
     */
    @JvmStatic
    fun adaptTaskbar(activity: AppCompatActivity, view: View) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            view.setOnApplyWindowInsetsListener(object : View.OnApplyWindowInsetsListener {

                override fun onApplyWindowInsets(view: View, insets: WindowInsets): WindowInsets {
                    if (!checkIsGestureNavMode(activity)) {
                        return insets
                    }
                    if (FeatureCompat.isSupportTaskbar) {
                        //此高度由适配文档SDK提供，30DP
                        val height = activity.resources.getDimensionPixelSize(R.dimen.navigation_gesture_taskbar_height)
                        view.setPadding(view.paddingLeft, view.paddingTop, view.paddingRight, height)
                        Log.d(TAG, "adaptTaskbar onApplyWindowInsets height:$height")
                    }
                    return insets
                }
            })
        }
    }

    /**
     * 检测当前窗口是否需要兼容taskbar的高度
     * 返回true为不需要
     */
    @JvmStatic
    fun checkNotDisplayTaskbarHeight(activity: FragmentActivity): Boolean {
        return !checkIsGestureNavMode(activity) || activity.isInMultiWindowMode
    }

    /**
     * 是否显示taskbar ,可在设置-》大屏应用-》任务栏-》仅显示在桌面 中隐藏taskbar
     * 支持taskbar并且 导航栏bottom高于30dp
     */
    @JvmStatic
    fun isDisplayTaskbar(rootView: View?): Boolean {
        rootView ?: return false
        val navigationBarInsets = ViewCompat.getRootWindowInsets(rootView)?.getInsets(WindowInsetsCompat.Type.navigationBars())
        val taskbarHeight = rootView.resources.getDimensionPixelSize(R.dimen.navigation_gesture_taskbar_height)
        val navigationBarHeight = navigationBarInsets?.bottom ?: 0
        Log.d(TAG, "isDisplayTaskbar navigationBarHeight ${navigationBarInsets?.bottom}  taskbarHeight:$taskbarHeight")
        return navigationBarHeight > taskbarHeight
    }

    /**
     * 防止内存泄漏，在页面销毁的时候取消监听
     */
    @JvmStatic
    fun adaptTaskbarByDestroy(view: View) {
        view.setOnApplyWindowInsetsListener(null)
    }

    @SuppressLint("ObsoleteSdkInt")
    @JvmStatic
    fun setStatusBarReversal(activity: Activity, keepNightMode: Boolean = true) {
        val window = activity.window
        val decorView = activity.window.decorView
        if (SdkUtils.getSDKVersion() >= Build.VERSION_CODES.LOLLIPOP) {
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            window.statusBarColor = Color.TRANSPARENT
        }
        val versionCode = COUIVersionUtil.getOSVersionCode()
        if (versionCode >= COUIVersionUtil.COUI_3_0 || versionCode == 0) {
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            val insetsController = WindowCompat.getInsetsController(window, decorView)
            val isNightMode = COUIDarkModeUtil.isNightMode(activity)
            Log.d(TAG, "keepNightMode: $keepNightMode, isNightMode = $isNightMode")
            if (keepNightMode) {
                insetsController.isAppearanceLightStatusBars = false
            } else {
                insetsController.isAppearanceLightStatusBars = !isNightMode
            }
        }
    }
}