/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/08/12, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package andes.oplus.documentsreader.launcher.utils

import andes.oplus.documentsreader.core.common.CategoryHelper
import andes.oplus.documentsreader.core.common.CategoryHelper.CATEGORY_BROWSER_MY_DOCUMENTS
import andes.oplus.documentsreader.core.common.CategoryHelper.CATEGORY_DOC_TOOL
import andes.oplus.documentsreader.core.common.CategoryHelper.CATEGORY_MAIN
import andes.oplus.documentsreader.core.common.CategoryHelper.CATEGORY_MY_DOCUMENTS
import andes.oplus.documentsreader.core.common.CategoryHelper.CATEGORY_RECENT
import andes.oplus.documentsreader.core.common.CategoryHelper.CATEGORY_RECYCLE_BIN
import andes.oplus.documentsreader.core.common.CategoryHelper.CATEGORY_SELECT_DOC
import andes.oplus.documentsreader.core.common.CategoryHelper.CATEGORY_SOURCE_GROUP
import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.parentchild.IParentChildApi
import andes.oplus.documentsreader.core.common.uiconfig.type.IUIConfig
import andes.oplus.documentsreader.interfaces.IDocToolsManager
import andes.oplus.documentsreader.launcher.MainViewApi
import andes.oplus.documentsreader.launcher.recent.RecentDocApi
import andes.oplus.documentsreader.mydocument.MyDocumentApi
import andes.oplus.documentsreader.recycle.IRecycleBinViewApi
import andes.oplus.documentsreader.selectdoc.SelectDocApi
import andes.oplus.documentsreader.superapp.ISuperApp
import android.app.Activity
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.fragment.app.Fragment
import com.coui.appcompat.sidenavigation.COUISideNavigationBar

object RouterUtil {

    private val categoryInterface = HashMap<Int, IParentChildApi?>()

    init {
        initData()
    }

    @JvmStatic
    private fun initData() {
        categoryInterface[CATEGORY_MAIN] = MainViewApi
        categoryInterface[CATEGORY_RECENT] = RecentDocApi
        categoryInterface[CATEGORY_MY_DOCUMENTS] = MyDocumentApi
        categoryInterface[CATEGORY_BROWSER_MY_DOCUMENTS] = MyDocumentApi
        categoryInterface[CATEGORY_DOC_TOOL] = Injector.injectFactory<IDocToolsManager>()
        categoryInterface[CATEGORY_RECYCLE_BIN] = Injector.injectFactory<IRecycleBinViewApi>()
        categoryInterface[CATEGORY_SELECT_DOC] = SelectDocApi
        categoryInterface[CategoryHelper.CATEGORY_SEARCH_DOC] = SelectDocApi
        categoryInterface[CATEGORY_SOURCE_GROUP] = Injector.injectFactory<ISuperApp>()
    }

    @JvmStatic
    fun getFragment(activity: Activity, category: Int): Fragment? {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.let {
            return it.getFragment(activity, category) ?: Fragment()
        }
        return null
    }

    @JvmStatic
    fun onResumeLoadData(fragment: Fragment, category: Int) {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.onResumeLoadData(fragment)
    }

    @JvmStatic
    fun onCreateOptionsMenu(fragment: Fragment, menu: Menu, inflater: MenuInflater, category: Int) {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.onCreateOptionsMenu(fragment, menu, inflater)
    }

    @JvmStatic
    fun onOptionItemSelected(fragment: Fragment, item: MenuItem, category: Int): Boolean {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.let {
            return it.onOptionsMenuItemSelected(fragment, item)
        }
        return false
    }

    @JvmStatic
    fun pressBack(fragment: Fragment, category: Int): Boolean {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.let {
            return it.pressBack(fragment)
        }
        return false
    }

    @JvmStatic
    fun fromSelectPathResult(fragment: Fragment, requestCode: Int, path: String?, category: Int) {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.fromSelectPathResult(fragment, requestCode, path)
    }

    @JvmStatic
    fun backToTop(fragment: Fragment, category: Int) {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.backToTop(fragment)
    }

    /**
     * 设置当前fragment是否处于半屏显示
     * 在小屏上，父子级的父级隐藏，子级全屏显示时，子级fragment处于全屏显示
     * 在大屏上，显示父子级时，子级fragment处于半屏显示
     * @param fragment
     * @param category
     * @param isHalfScreen 是否处于半屏
     */
    @JvmStatic
    fun setIsHalfScreen(fragment: Fragment, category: Int, isHalfScreen: Boolean) {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.setIsHalfScreen(fragment, category, isHalfScreen)
    }

    @JvmStatic
    fun onUIConfigChanged(fragment: Fragment, category: Int, configList: MutableCollection<IUIConfig>) {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.onUIConfigChanged(fragment, configList)
    }

    @JvmStatic
    private fun getRealCategoryType(category: Int): Int {
        if (CategoryHelper.isSuperAppType(category)) {
            return CATEGORY_SOURCE_GROUP
        }
        return category
    }

    @JvmStatic
    fun onWindowInsetsCallback(
        fragment: Fragment,
        category: Int,
        showNavigationBar: Boolean,
        systemBarInsetsBottom: Int
    ) {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.onWindowInsetsCallback(fragment, showNavigationBar, systemBarInsetsBottom)
    }

    @JvmStatic
    fun openDrawer(fragment: Fragment, category: Int, fragmentWidth: Int) {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.openDrawer(fragment, fragmentWidth)
    }

    @JvmStatic
    fun closeDrawer(fragment: Fragment, category: Int, fragmentWidth: Int) {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.closeDrawer(fragment, fragmentWidth)
    }

    @JvmStatic
    fun notifyDrawerRight(fragment: Fragment, category: Int, right: Int) {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.notifyDrawerRight(fragment, right)
    }

    @JvmStatic
    fun onSideNavigationClicked(
        fragment: Fragment,
        sideNavigationContainer: COUISideNavigationBar?,
        category: Int,
        isOpen: Boolean
    ): Boolean {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.let {
            return it.onSideNavigationClicked(fragment, sideNavigationContainer, isOpen)
        }
        return false
    }

    @JvmStatic
    fun updateFragmentData(fragment: Fragment, category: Int, bundle: Bundle?) {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.updateFragmentData(fragment, bundle)
    }

    @JvmStatic
    fun exitSelectionMode(fragment: Fragment, category: Int) {
        val type = getRealCategoryType(category)
        categoryInterface[type]?.exitSelectionMode(fragment)
    }
}