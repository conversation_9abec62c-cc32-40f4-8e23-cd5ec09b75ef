/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: UserStatementManager
 * * Description: UserStatementManager
 * * Version: 1.0
 * * Date : 2024/03/01
 * * Author:ztf
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *        ztf           2024/03/01       1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.impl

import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.PreferencesUtils
import andes.oplus.documentsreader.core.common.compat.FeatureCompat
import andes.oplus.documentsreader.impl.helper.UserStatementDialog
import andes.oplus.documentsreader.impl.ui.PrivacyPolicyActivity
import andes.oplus.documentsreader.impl.ui.SharedListActivity
import andes.oplus.documentsreader.impl.ui.UserProtocolActivity
import andes.oplus.documentsreader.impl.ui.personal.PersonalListActivity
import andes.oplus.documentsreader.impl.utils.StatementUtil
import andes.oplus.documentsreader.impl.utils.UserAuthorizePointUtil
import andes.oplus.documentsreader.impl.utils.UserStatementUtils
import andes.oplus.documentsreader.impl.utils.UserStatementUtils.SP_KEY_SHOW_STATEMENT
import andes.oplus.documentsreader.interfaces.IUserStatementManager
import andes.oplus.documentsreader.userprivacy.impl.R
import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import androidx.activity.ComponentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner

class UserStatementManager : IUserStatementManager {

    companion object {
        private const val TAG = "UserStatementManager"
    }

    private val lifecycleObservers = HashMap<Int, LifeCycleObserver>()
    private var userStatementDialog: UserStatementDialog? = null

    override fun launchToPersonalListActivity(activity: Activity) {
        PersonalListActivity.start(activity)
    }

    override fun launchToPrivacyPolicyActivity(activity: Activity) {
        PrivacyPolicyActivity.start(activity)
    }

    override fun launchToUserProtocolActivity(activity: Activity) {
        UserProtocolActivity.start(activity)
    }

    override fun launchToSharedListActivity(activity: Activity) {
        SharedListActivity.start(activity)
    }

    override fun buryAuthorizeNotification(value: Boolean) {
        UserAuthorizePointUtil.buryAuthorizeNotification(value)
    }

    override fun checkShowStatement(packageContext: Context): Boolean {
        /*
         * 1，外销全量是不会走到这里的，但是由于轻量不区分内外销，轻量又必须打入这个module，所以会调用到这里
         * 2，内销是不需要用户须知弹框的，所以外销默认授权
         */
        val showStatement =
            (UserStatementUtils.getShowStatementValue() && UserStatementUtils.checkNeedUpdateConfig().not())
        Log.d(TAG, "checkShowStatement showStatement = $showStatement")
        return showStatement
    }

    override fun getBasicStatementValue(): Boolean {
        return UserStatementUtils.getBasicStatementValue()
    }

    override fun showWithdrawDialog(context: Context, withdrawInvoke: (() -> Unit)?) {
        UserStatementUtils.showWithdrawDialog(context, withdrawInvoke)
    }

    override fun checkStatementResultFor(
        context: Context,
        statementType: Int,
        resultInvoke: ((onResult: Boolean) -> Unit)?
    ) {
        StatementUtil.checkStatementResultFor(context, statementType, resultInvoke)
    }

    override suspend fun changeBasicValue(value: Boolean, callback: (() -> Unit)?) {
        UserStatementUtils.changeBasicValue(value, callback)
    }

    override fun checkNeedUpdateConfig(): Boolean {
        return UserStatementUtils.checkNeedUpdateConfig()
    }

    override fun needShowUserProtocol(context: Context): Boolean {
        return true
    }

    override fun isEnableWithDraw(context: Context): Boolean {
        return UserStatementUtils.getBasicStatementValue()
    }

    override fun needShowDataList(context: Context): Boolean {
        return true
    }

    override fun needShowSharedList(context: Context): Boolean {
        return true
    }

    override fun needShowAboutDescribe(context: Context): Boolean {
        return FeatureCompat.sIsLightVersion.not()
    }

    override fun getPrivacyTitle(context: Context): String {
        return context.getString(R.string.personal_information_protection_policy_title)
    }

    override fun getWithDrawTitle(context: Context): String {
        return context.getString(R.string.withdraw_personal_information_protection_policy)
    }

    override fun releaseDialog() {
        UserStatementUtils.releaseDialog()
    }

    override fun showStatementDialog(activity: Activity, goInvoke: (code: Int) -> Unit) {
        Log.i(TAG, "showStatementDialog activity = $activity, ${goInvoke.hashCode()}")
        val innerGoInvoke: (code: Int) -> Unit = { code ->
            Log.d(TAG, "goInvoke = ${goInvoke.hashCode()}, activity = $activity")
            goInvoke.invoke(code)
            disAttachActivity(activity)
        }
        userStatementDialog = UserStatementDialog(activity, innerGoInvoke)
        userStatementDialog?.let {
            it.showRightDialog()
            attachActivity(activity, it)
        }
    }

    override fun updateLayoutWhileConfigChange(newConfig: Configuration) {
        userStatementDialog?.updateLayoutWhileConfigChange(newConfig)
    }

    private fun attachActivity(
        activity: Activity,
        userStatementDialog: UserStatementDialog
    ) {
        val lifecycleObserver = LifeCycleObserver(userStatementDialog)
        lifecycleObservers[activity.hashCode()] = lifecycleObserver
        (activity as? ComponentActivity)?.lifecycle?.addObserver(lifecycleObserver)
    }

    private fun disAttachActivity(activity: Activity) {
        val hash = activity.hashCode()
        val lifecycleObserver = lifecycleObservers[hash]
        Log.i(TAG, "disAttachActivity ${lifecycleObserver != null}")
        lifecycleObserver?.let {
            (activity as? ComponentActivity)?.lifecycle?.removeObserver(it)
            it.userStatementDialog.onDestroy()
            lifecycleObservers.remove(hash)
        }
        userStatementDialog = null
    }

    override suspend fun checkCanUseNetwork(context: Context): Boolean {
        //外销默认授权网络
        val canUseNetwork = UserStatementUtils.getAgreeNetworkState()
        Log.i(TAG, "checkCanUseNetwork = $canUseNetwork")
        /*
        * UserStatementUtils.getShowStatementValue() && UserStatementUtils.getBasicStatementValue()这个判断是为了以前状态的兼容
        * 这两个都为true的时候就是全部同意了
         */
        return canUseNetwork ||
                (PreferencesUtils.getBoolean(key = SP_KEY_SHOW_STATEMENT) && UserStatementUtils.queryBasicValue())
    }

    override fun hasAgreeType(statementType: Int): Boolean {
        return UserStatementUtils.hasAgreeType(statementType)
    }

    inner class LifeCycleObserver(
        val userStatementDialog: UserStatementDialog,
    ) : DefaultLifecycleObserver {

        override fun onCreate(owner: LifecycleOwner) {
            Log.d(TAG, "LifeCycleObserver onCreate")
            super.onCreate(owner)
        }

        override fun onDestroy(owner: LifecycleOwner) {
            Log.d(TAG, "LifeCycleObserver onDestroy")
            super.onDestroy(owner)
            userStatementDialog.onDestroy()
            disAttachActivity(userStatementDialog.activity)
        }

        override fun onPause(owner: LifecycleOwner) {
            Log.d(TAG, "LifeCycleObserver onPause")
            super.onPause(owner)
            userStatementDialog.onPause()
        }

        override fun onResume(owner: LifecycleOwner) {
            Log.d(TAG, "LifeCycleObserver onResume")
            super.onResume(owner)
            userStatementDialog.onResume()
        }
    }
}
