/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CheckOperatorWithPermissionTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/11/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package oplus.multimedia.soundrecorder.permission

import android.app.Activity
import android.content.Context
import android.net.Uri
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.fileoperator.CheckOperate
import com.soundrecorder.common.fileoperator.CheckOperatorWithPermission
import com.soundrecorder.common.fileoperator.rename.NameFileDialogUtil
import oplus.multimedia.soundrecorder.shadows.ShadowAppFeatureUtil
import oplus.multimedia.soundrecorder.shadows.ShadowCOUIMaxHeightScrollView
import oplus.multimedia.soundrecorder.shadows.ShadowCOUIVersionUtil
import oplus.multimedia.soundrecorder.shadows.ShadowFeatureOption
import oplus.multimedia.soundrecorder.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config
import java.util.concurrent.Future

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class,
        ShadowAppFeatureUtil::class,
        ShadowCOUIMaxHeightScrollView::class,
        ShadowCOUIVersionUtil::class]
)
class CheckOperatorWithPermissionTest {
    private var mContext: Context? = null
    private var mActivityController: ActivityController<Activity>? = null
    private var mActivity: Activity? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mActivityController = Robolectric.buildActivity(Activity::class.java)
        mActivity = mActivityController?.create()?.resume()?.get()
    }

    @After
    fun tearDown() {
        mActivityController = null
        mActivity = null
        mContext = null
    }

    @Test
    fun not_null_when_initOperate() {
        val checkOperatorWithPermission = CheckOperatorWithPermission(mActivity)
        var operate = Whitebox.getInternalState<CheckOperate>(checkOperatorWithPermission, "mOperate")
        Assert.assertNull(operate)

        Whitebox.invokeMethod<Void>(checkOperatorWithPermission, "initOperate")
        operate = Whitebox.getInternalState(checkOperatorWithPermission, "mOperate")
        Assert.assertNotNull(operate)
    }

    @Test
    fun assert_not_null_when_renameUri() {
        val checkOperatorWithPermission = CheckOperatorWithPermission(mActivity)
        checkOperatorWithPermission.renameUri(Uri.EMPTY, "123", ".mp3", null, NameFileDialogUtil(Constants.REQUEST_CODE_RENAME))
        val operate = Whitebox.getInternalState<CheckOperate>(checkOperatorWithPermission, "mOperate")
        Assert.assertEquals(CheckOperate.OPERATE_RENAME, operate.getOperateStatus())
    }

    @Test
    fun assert_equals_when_deleteRecords() {
        val checkOperatorWithPermission = CheckOperatorWithPermission(mActivity)
        checkOperatorWithPermission.deleteRecords(null, null, true) {}
        val records = arrayListOf<Record>(
            Record().also { it.id = 1 }
        )
        checkOperatorWithPermission.deleteRecords(null, records, true) {}
        val deleteTask = Whitebox.getInternalState<Future<*>>(checkOperatorWithPermission, "mDeleteTask")
        Assert.assertNotNull(deleteTask)
    }

    @Test
    fun assert_equals_when_deleteUris() {
        val checkOperatorWithPermission = CheckOperatorWithPermission(mActivity)
        checkOperatorWithPermission.deleteRecords(null, null, true) {}
        val records = mutableListOf(1L)
        Whitebox.invokeMethod<Void>(checkOperatorWithPermission, "deleteUris", null as? Int?, records, true, { })
        val operate = Whitebox.getInternalState<CheckOperate>(checkOperatorWithPermission, "mOperate")
        Assert.assertNull(operate)
    }

    @Test
    fun assert_equals_when_getUriListFromIds() {
        val ids = mutableListOf(1L, 2L, 3L, 4L)
        val checkOperatorWithPermission = CheckOperatorWithPermission(mActivity)
        val uris = Whitebox.invokeMethod<MutableList<Uri>>(
            checkOperatorWithPermission,
            "getUriListFromIds",
            ids
        )
        Assert.assertEquals(ids.size, uris.size)
    }

    @Test
    fun assert_equals_when_needContinueOperator() {
        val checkOperatorWithPermission = CheckOperatorWithPermission(mActivity)
        var result = checkOperatorWithPermission.needContinueOperator()
        Assert.assertEquals(-1, result)

        val operater = CheckOperate().also {
            it.mOperating = true
            it.setOperateStatus(CheckOperate.OPERATE_RENAME)
        }
        Whitebox.setInternalState(checkOperatorWithPermission, "mOperate", operater)
        result = checkOperatorWithPermission.needContinueOperator()
        Assert.assertEquals(CheckOperate.OPERATE_RENAME, result)
    }

    @Test
    fun assert_equals_when_resetContinueOperator() {
        val checkOperatorWithPermission = CheckOperatorWithPermission(mActivity)
        checkOperatorWithPermission.resetContinueOperator()
        val operate = Whitebox.getInternalState<CheckOperate>(checkOperatorWithPermission, "mOperate")
        Assert.assertNotEquals(true, operate?.mOperating)
    }

    @Test
    fun assert_equals_when_getRenameContent() {
        val checkOperatorWithPermission = CheckOperatorWithPermission(mActivity)
        var result = checkOperatorWithPermission.getRenameContent()
        Assert.assertEquals("", result)

        val operater = CheckOperate().also {
            it.setRenameUri(Uri.EMPTY, "123", "mp3", null, NameFileDialogUtil(Constants.REQUEST_CODE_RENAME))
        }
        Whitebox.setInternalState(checkOperatorWithPermission, "mOperate", operater)
        result = checkOperatorWithPermission.getRenameContent()
        Assert.assertEquals("123", result)
    }

    @Test
    fun assert_equals_when_getOperating() {
        val checkOperatorWithPermission = CheckOperatorWithPermission(mActivity)
        var result = checkOperatorWithPermission.getOperating()
        Assert.assertFalse(result)

        val operater = CheckOperate().also {
            it.mOperating = true
            it.setOperateStatus(CheckOperate.OPERATE_RENAME)
        }
        Whitebox.setInternalState(checkOperatorWithPermission, "mOperate", operater)
        result = checkOperatorWithPermission.getOperating()
        Assert.assertTrue(result)
    }

    @Test
    fun assert_equals_when_release() {
        val checkOperatorWithPermission = CheckOperatorWithPermission(mActivity)
        checkOperatorWithPermission.release()
        val operate = Whitebox.getInternalState<CheckOperate>(checkOperatorWithPermission, "mOperate")
        Assert.assertNull(operate)
    }
}