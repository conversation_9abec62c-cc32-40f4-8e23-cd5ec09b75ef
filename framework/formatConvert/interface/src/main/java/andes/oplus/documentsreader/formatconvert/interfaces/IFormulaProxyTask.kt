/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/06/06, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package andes.oplus.documentsreader.formatconvert.interfaces

import android.content.Context
import android.graphics.Bitmap
import java.lang.ref.WeakReference

interface IFormulaProxyTask : IFormulaTask {

    var weakContext: WeakReference<Context>?
    var image: Bitmap?
    var formulaListener: FormulaListener?
    var netConfig: INetConfig
}