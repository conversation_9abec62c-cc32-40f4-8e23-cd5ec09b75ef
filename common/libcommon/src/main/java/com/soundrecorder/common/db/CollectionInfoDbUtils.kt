/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CollectionInfoDbUtils.kt
 * * Description : 个人信息收集明示清单 db 工具类
 * * Version     : 1.0
 * * Date        : 2024/9/25
 * * Author      : W9035969
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.db

import android.content.ContentProviderOperation
import android.content.ContentValues
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.icu.text.SimpleDateFormat
import android.os.RemoteException
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.databean.CollectionInfo
import java.util.ArrayList
import java.util.Date

object CollectionInfoDbUtils {

    const val TAG = "CollectionInfoDbUtils"

    const val TABLE_NAME_COLLECTION_INFO = "collection_info"

    /**
     * 主键id
     */
    const val COLUMN_NAME_ID = "_id"
    /**
     * 类型
     */
    const val COLUMN_NAME_TYPE = "type"
    /**
     * 信息内容
     */
    const val COLUMN_NAME_CONTENT = "content"
    /**
     * 创建时间
     */
    const val COLUMN_NAME_DATECREATED = "dateCreated"

    /**
     * 创建时间
     */
    const val COLUMN_NAME_COUNT = "count"

    /**
     * 数据库版本
     */
    const val DATABASE_VERSION_COLLECTION_INFO = 16

    const val  PROVIDER_COLLECTION_INFO_TYPE = "vnd.android.cursor.dir/collection_info"

    private val collectionInfoUri = DatabaseConstant.getContentUri(TABLE_NAME_COLLECTION_INFO)

    /**
     * 7天、一个月、3个月、一年
     */
    const val  TIME_MILLIS_DAY7 = 7L * 24 * 60 * 60 * 1000
    const val  TIME_MILLIS_MONTH1 = 30L * 24 * 60 * 60 * 1000
    const val  TIME_MILLIS_MONTH3 = 90L * 24 * 60 * 60 * 1000
    const val  TIME_MILLIS_YEAR = 365L * 24 * 60 * 60 * 1000

    /**
     * 创建 collection_info 表
     */
    @JvmStatic
    fun createCollectionInfoTable(db: SQLiteDatabase) {
        db.execSQL(
            "CREATE TABLE IF NOT EXISTS " + TABLE_NAME_COLLECTION_INFO + " ("
                    + COLUMN_NAME_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
                    + COLUMN_NAME_TYPE + " TEXT, "
                    + COLUMN_NAME_CONTENT + " TEXT,"
                    + COLUMN_NAME_DATECREATED + " BIGINT DEFAULT 0,"
                    + COLUMN_NAME_COUNT + " INTEGER DEFAULT 0"
                    + ");"
        )
    }

    /**
     * 升级 collection_info表
     */
    @JvmStatic
    fun upgradeCollectionInfoTable(db: SQLiteDatabase, fromVersion: Int, toVersion: Int) {
        DebugUtil.e(TAG, "upgradeCollectionInfoTable from:$fromVersion to:$toVersion")
        if (fromVersion >= toVersion) { // 升级时，from < to ,如果大于等于，则说明传入的值错误
            DebugUtil.e(TAG, "upgradeCollectionInfoTable version error")
            return
        }
        for (version in (fromVersion + 1..toVersion)) {
            when (version) { //只处理 version >= 16 的情况
                DATABASE_VERSION_COLLECTION_INFO -> createCollectionInfoTable(db)
            }
        }
    }

    /**
     * 降级数据库表 collection_info
     * @param db
     * @param fromVersion
     * @param toVersion
     */
    @JvmStatic
    fun downgradeCollectionInfoTable(db: SQLiteDatabase, fromVersion: Int, toVersion: Int) {
        DebugUtil.e(TAG, "downgradeCollectionInfoTable from:$fromVersion to:$toVersion")
        if (fromVersion <= toVersion) { // 降级时，from > to，如果小于等于，则说明版本错误
            DebugUtil.e(TAG, "downgradeCollectionInfoTable version error")
            return
        }

        for (version in (fromVersion downTo toVersion + 1)) {
            when (version) { //只处理 version >= 16 的情况
                DATABASE_VERSION_COLLECTION_INFO -> dropCollectionInfoTable(db)
            }
        }
    }

    /**
     * 删除 collection_info表
     */
    @JvmStatic
    fun dropCollectionInfoTable(db: SQLiteDatabase) {
        DebugUtil.e(TAG, "dropCollectionTable")
        db.execSQL("DROP TABLE IF EXISTS $TABLE_NAME_COLLECTION_INFO;")
    }

    @JvmStatic
    private fun getContentResolver() = BaseApplication.getAppContext().contentResolver

    @JvmStatic
    fun getProjections(): Array<String> {
        return arrayOf(
            COLUMN_NAME_TYPE,
            COLUMN_NAME_CONTENT,
            COLUMN_NAME_DATECREATED,
            COLUMN_NAME_COUNT
        )
    }

    @JvmStatic
    fun getContentValues(collectionInfo: CollectionInfo): ContentValues {
        val values = ContentValues()
        values.put(COLUMN_NAME_TYPE, collectionInfo.type)
        values.put(COLUMN_NAME_CONTENT, collectionInfo.content)
        values.put(COLUMN_NAME_DATECREATED, collectionInfo.dateCreated)
        values.put(COLUMN_NAME_COUNT, collectionInfo.count)
        return values
    }

    /**
     * 添加转文本成功收集信息
     */
    @JvmStatic
    fun addConvertCollectionInfo(type: String = CollectionInfo.COLLECTION_TYPE_RECORD_AUDIO, content: String?): Boolean {
        val collectionInfo = CollectionInfo(type = type, content = content, dateCreated = System.currentTimeMillis())
        return addCollectionInfo(collectionInfo)
    }

    /**
     * 添加收集信息
     * @param collectionInfo 收集信息
     * @return true 添加成功； false 失败
     */
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun addCollectionInfo(collectionInfo: CollectionInfo): Boolean {
        kotlin.runCatching {
            val contentValues = getContentValues(collectionInfo)
            val contentResolver = getContentResolver()
            contentResolver.insert(collectionInfoUri, contentValues)
            DebugUtil.d(TAG, "addCollectionInfo collectionInfo:$collectionInfo")
            return true
        }.onFailure {
            DebugUtil.e(TAG, "addCollectionInfo error", it)
        }
        return false
    }

    @JvmStatic
    fun addCollectionInfoList(collectionInfos: List<CollectionInfo>) {
        kotlin.runCatching {
            val operations = ArrayList<ContentProviderOperation>()
            collectionInfos.forEach { collectionInfo ->
                val contentValues = getContentValues(collectionInfo)

                operations.add(
                    ContentProviderOperation.newInsert(collectionInfoUri)
                        .withValues(contentValues)
                        .build()
                )
            }

            val contentResolver = getContentResolver()
            contentResolver.applyBatch(DatabaseConstant.AUTHORITY, operations)
            DebugUtil.d(TAG, "addCollectionInfos size:${collectionInfos.size}")
        }.onFailure {
            DebugUtil.e(TAG, "addCollectionInfos error", it)
        }
    }

    @JvmStatic
    fun isTodayTime(oldTime: Long): Boolean {
        val sdf = SimpleDateFormat("yyyy-MM-dd")
        val d1 = sdf.format(Date(oldTime))
        val nowDate = Date()
        val d2 = sdf.format(nowDate)
        if (d1.equals(d2, true)) {
            return true
        }
        return false
    }

    @JvmStatic
    private fun isSameCollectionInfo(dbInfo: CollectionInfo, collectionInfo: CollectionInfo): Boolean {
        return dbInfo.type == collectionInfo.type && dbInfo.content == collectionInfo.content
    }

    @JvmStatic
    fun addCollectionInfoRemoveDuplicates(collectionInfo: CollectionInfo): Boolean {
        val list = queryCollectionInfosForType(collectionInfo.type)
        if (list.isNotEmpty()) {
            val idList = arrayListOf<String>()
            list.forEach { dbInfo ->
                val subTime = System.currentTimeMillis() - dbInfo.dateCreated
                if (subTime > TIME_MILLIS_YEAR) {
                    idList.add(dbInfo.id.toString())
                    //deleteCollectionInfo(dbInfo)
                } else {
                    if (isSameCollectionInfo(dbInfo, collectionInfo) && !collectionInfo.isContact()) {
                        //不包含当天数据，并且间隔时间大于7天，小于7天则直接更新数量
                        if (!isTodayTime(dbInfo.dateCreated) && subTime > TIME_MILLIS_DAY7) {
                            DebugUtil.d(TAG, "updateCollectionInfo collectionInfo:$collectionInfo, isToday")
                            return addCollectionInfo(collectionInfo)
                        } else {
                            collectionInfo.dateCreated = System.currentTimeMillis()
                            collectionInfo.count = dbInfo.count + 1
                            val count = updateCollectionInfo(collectionInfo)
                            DebugUtil.d(TAG, "updateCollectionInfo collectionInfo:$collectionInfo, isToday,false")
                            return count > 0
                        }
                    } else {
                        return addCollectionInfo(collectionInfo)
                    }
                }
            }
            if (idList.isNotEmpty()) {
                deleteCollectionInfosById(idList)
            }
        } else {
            return addCollectionInfo(collectionInfo)
        }
        return false
    }

    /**
     * 添加收集信息列表
     */
    @JvmStatic
    fun addCollectionInfos(collectionInfos: List<CollectionInfo>) {
        kotlin.runCatching {
            collectionInfos.forEach { collectionInfo ->
                addCollectionInfoRemoveDuplicates(collectionInfo)
            }
            DebugUtil.d(TAG, "addCollectionInfos size:${collectionInfos.size}")
        }.onFailure {
            DebugUtil.e(TAG, "addCollectionInfos error", it)
        }
    }

    /**
     * 更新收集信息
     * @param collectionInfo 新的收集信息
     * @return 成功更新数据的个数
     */
    @JvmStatic
    fun updateCollectionInfo(collectionInfo: CollectionInfo): Int {
        val where = " $COLUMN_NAME_TYPE = ? "
        val args = arrayOf(collectionInfo.type)

        kotlin.runCatching {
            val contentResolver = getContentResolver()
            val contentValues = getContentValues(collectionInfo)
            val count = contentResolver.update(collectionInfoUri, contentValues, where, args)
            DebugUtil.d(TAG, "updateCollectionInfo collectionInfo:$collectionInfo, updateCount:$count")
            return count
        }.onFailure {
            DebugUtil.e(TAG, "updateCollectionInfo collectionInfo:$collectionInfo, $it")
        }
        return 0
    }

    /**
     * 根据type删除对应的collectionInfo表
     * @param type 类型
     */
    @JvmStatic
    fun deleteCollectionByType(type: String): Int {
        return deleteCollectionInfo(COLUMN_NAME_TYPE, type)
    }

    @JvmStatic
    private fun deleteCollectionInfo(column: String, columnValue: String): Int {
        val where = " $column = ? "
        val args = arrayOf(columnValue)
        kotlin.runCatching {
            val contentResolver = getContentResolver()
            val count = contentResolver.delete(collectionInfoUri, where, args)
            DebugUtil.d(TAG, "deleteCollectionInfo column:$column columnValue:$columnValue deleteCount:$count")
            return count
        }.onFailure {
            DebugUtil.e(TAG, "deleteCollectionInfo error column:$column columnValue:$columnValue", it)
        }
        return 0
    }

    /**
     * 根据主键id删除对应的收集信息列表
     * @param id 主键id
     */
    @JvmStatic
    fun deleteCollectionInfo(id: Int): Int {
        val where = " $COLUMN_NAME_ID = ? "
        val args = arrayOf(id.toString())

        kotlin.runCatching {
            val contentResolver = getContentResolver()
            val count = contentResolver.delete(collectionInfoUri, where, args)
            DebugUtil.d(TAG, "deleteCollectionInfo id:$id deleteCount:$count")
            return count
        }.onFailure {
            DebugUtil.e(TAG, "deleteCollectionInfo error id:$id", it)
        }
        return 0
    }
    /**
     * 根据collectionId批量删除对应的收集信息
     * @param IdList 收集信息collectionId列表
     */
    @JvmStatic
    fun deleteCollectionInfosById(collectionIdList: List<String>): Int {
        if (collectionIdList.isEmpty()) {
            return 0
        }
        return deleteCollectionInfos(COLUMN_NAME_ID, collectionIdList)
    }

    @JvmStatic
    private fun deleteCollectionInfos(column: String, columnValues: List<String>): Int {
        val where = MediaDBUtils.getWhereForInKeyword(columnValues.size, column).toString()
        kotlin.runCatching {
            val contentResolver = getContentResolver()
            val count = contentResolver.delete(collectionInfoUri, where, columnValues.toTypedArray())
            DebugUtil.d(TAG, "deleteCollectionInfos column:$column size:${columnValues.size} deleteCount:$count")
            return count
        }.onFailure {
            DebugUtil.e(TAG, "deleteCollectionInfos error column:$column size:${columnValues.size}", it)
        }
        return 0
    }

    /**
     * 根据收集信息 type，查询对应的收集信息列表,不包含今天数据
     * @param type 收集信息 type
     */
    @JvmStatic
    fun queryCollectionInfos(type: String): MutableList<CollectionInfo> {
        val where = " $COLUMN_NAME_TYPE = ? "
//        val where = " $COLUMN_NAME_TYPE COLLATE NOCASE = ? AND DATE_SUB(${System.currentTimeMillis()}, INTERVAL 7 DAY)"
        val args = arrayOf(type)
        val projections = arrayOf(
            COLUMN_NAME_ID,
            COLUMN_NAME_CONTENT,
            COLUMN_NAME_DATECREATED,
            COLUMN_NAME_COUNT,
        )
        val order = " $COLUMN_NAME_DATECREATED DESC " // 降序排序
        var cursor: Cursor? = null
        try {
            val contentResolver = getContentResolver()
            cursor = contentResolver.query(collectionInfoUri, projections, where, args, order)
            cursor?.let {
                if (it.moveToFirst()) {
                    val idIndex = it.getColumnIndexOrThrow(COLUMN_NAME_ID)
                    val contentIndex = it.getColumnIndexOrThrow(COLUMN_NAME_CONTENT)
                    val dateCreateIndex = it.getColumnIndexOrThrow(COLUMN_NAME_DATECREATED)
                    val countIndex = it.getColumnIndexOrThrow(COLUMN_NAME_COUNT)

                    val list = mutableListOf<CollectionInfo>()
                    do {
                        val id = it.getInt(idIndex)
                        val content = it.getString(contentIndex)
                        val dateCreate = it.getLong(dateCreateIndex)
                        val count = it.getInt(countIndex)
                        if (!isTodayTime(dateCreate)) {  //不包含今天
                            list.add(CollectionInfo(id, type, content, dateCreate, count))
                        }
                    } while (it.moveToNext())
                    DebugUtil.d(TAG, "queryCollectionInfos type:$type  list:${list.size}")
                    return list
                }
            }
        } catch (e: RemoteException) {
            DebugUtil.e(TAG, "queryCollectionInfos error type:$type", e)
        } finally {
            cursor?.close()
        }
        return mutableListOf()
    }

    /**
     * 根据收集信息 type，查询对应的收集信息列表,包含今天数据
     * @param type 收集信息 type
     */
    @JvmStatic
    fun queryCollectionInfosForType(type: String): MutableList<CollectionInfo> {
        val where = " $COLUMN_NAME_TYPE = ? "
        val args = arrayOf(type)
        val projections = arrayOf(
            COLUMN_NAME_ID,
            COLUMN_NAME_CONTENT,
            COLUMN_NAME_DATECREATED,
            COLUMN_NAME_COUNT,
        )
        val order = " $COLUMN_NAME_DATECREATED DESC " // 降序排序
        var cursor: Cursor? = null
        try {
            val contentResolver = getContentResolver()
            cursor = contentResolver.query(collectionInfoUri, projections, where, args, order)
            cursor?.let {
                if (it.moveToFirst()) {
                    val idIndex = it.getColumnIndexOrThrow(COLUMN_NAME_ID)
                    val contentIndex = it.getColumnIndexOrThrow(COLUMN_NAME_CONTENT)
                    val dateCreateIndex = it.getColumnIndexOrThrow(COLUMN_NAME_DATECREATED)
                    val countIndex = it.getColumnIndexOrThrow(COLUMN_NAME_COUNT)

                    val list = mutableListOf<CollectionInfo>()
                    do {
                        val id = it.getInt(idIndex)
                        val content = it.getString(contentIndex)
                        val dateCreate = it.getLong(dateCreateIndex)
                        val count = it.getInt(countIndex)
                        list.add(CollectionInfo(id, type, content, dateCreate, count))
                    } while (it.moveToNext())
                    DebugUtil.d(TAG, "queryCollectionInfos type:$type  list:${list.size}")
                    return list
                }
            }
        } catch (e: RemoteException) {
            DebugUtil.e(TAG, "queryCollectionInfos error type:$type", e)
        } finally {
            cursor?.close()
        }
        return mutableListOf()
    }

    /**
     * 查询所有的关键词
     * @return 关键词列表
     */
    @JvmStatic
    fun queryAllCollectionInfos(): MutableList<CollectionInfo> {
        var cursor: Cursor? = null
        try {
            val contentResolver = getContentResolver()
            cursor = contentResolver.query(collectionInfoUri, null, null, null, null)
            cursor?.let {
                if (it.moveToFirst()) {
                    val idIndex = it.getColumnIndexOrThrow(COLUMN_NAME_ID)
                    val typeIndex = it.getColumnIndexOrThrow(COLUMN_NAME_TYPE)
                    val contentIndex = it.getColumnIndexOrThrow(COLUMN_NAME_CONTENT)
                    val dateCreateIndex = it.getColumnIndexOrThrow(COLUMN_NAME_DATECREATED)
                    val countIndex = it.getColumnIndexOrThrow(COLUMN_NAME_COUNT)

                    val list = mutableListOf<CollectionInfo>()
                    do {
                        val id = it.getInt(idIndex)
                        val type = it.getString(typeIndex)
                        val content = it.getString(contentIndex)
                        val dateCreate = it.getLong(dateCreateIndex)
                        val count = it.getInt(countIndex)

                        list.add(CollectionInfo(id, type, content, dateCreate, count))
                    } while (it.moveToNext())
                    DebugUtil.d(TAG, "queryAllCollectionInfos  list:${list.size}")
                    return list
                }
            }
        } catch (e: RemoteException) {
            DebugUtil.e(TAG, "queryAllCollectionInfos error ", e)
        } finally {
            cursor?.close()
        }
        return mutableListOf()
    }

    @JvmStatic
    fun clearAllCollectionInfo() {
        val where = " $COLUMN_NAME_TYPE not null"
        kotlin.runCatching {
            val contentResolver = getContentResolver()
            val count = contentResolver.delete(collectionInfoUri, where, null)
            DebugUtil.d(TAG, "clearAllCollectionInfo deleteCount:$count")
        }.onFailure {
            DebugUtil.e(TAG, "clearAllCollectionInfo error ", it)
        }
    }
}