/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : KtAppUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/6/25 17:52
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/6/25       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.core.common

import andes.oplus.documentsreader.core.common.compat.FeatureCompat
import andes.oplus.documentsreader.core.common.utils.BrandUtil
import android.annotation.SuppressLint
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.OplusPackageManager.STATE_OPLUS_FREEZE_FREEZED
import android.content.pm.OplusPackageManager.getOplusPackageManager
import android.net.Uri
import android.os.Bundle
import androidx.annotation.Keep
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.oplus.compat.content.pm.ApplicationInfoNative.getOplusFreezeState
import com.oplus.compat.content.pm.PackageManagerNative.OPLUS_STATE_FREEZE_FREEZED
import com.oplus.os.OplusVersion
import com.oplus.wrapper.os.UserHandle

@Keep
object KtAppUtils {
    private const val TAG = "KtAppUtils"
    private const val SETTING_DETAIL_ACTION = "android.settings.APPLICATION_DETAILS_SETTINGS"
    private const val ACTION_OWORK = "oplus.intent.action.owork.OPEN_OWORK"
    const val OWOKR_PACKAGE = "com.oplus.owork"
    private const val ENTRANCE_FLAG = "entrance_flag"

    const val ENTRANCE_FILE_MANAGER_WORK_SPACE = "entrance_file_manager_work_space"

    enum class ConnectionResult {
        SUCCESS, DISABLED, MISSED
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun getAppName(packageName: String): String {
        return try {
            ContextGetter.context.packageManager.let {
                it.getApplicationLabel(
                    it.getApplicationInfo(
                        packageName,
                        0
                    )
                ).toString()
            }
        } catch (e: Exception) {
            Log.w(TAG, "getAppName: exception : ${e.message}")
            ""
        }
    }

    /**
     * check app enabled with dialog to setting
     */
    @JvmStatic
    fun checkAppEnabledWithDialog(context: Context, pkgName: String, msgId: Int): Boolean {
        return when (FileHelper.safeCheck({ checkAppForceEnabled(pkgName) }, ConnectionResult.MISSED)) {
            ConnectionResult.DISABLED -> {
                showAppEnabledWithDialog(context, pkgName, msgId)
                false
            }

            ConnectionResult.SUCCESS -> true
            ConnectionResult.MISSED -> false
        }
    }

    /**
     * show app enabled with dialog to setting
     */
    @JvmStatic
    fun showAppEnabledWithDialog(context: Context, pkgName: String, msgId: Int): AlertDialog? {
        var dialog: AlertDialog? = null
        val appName = getAppName(pkgName)
        (context as? Activity)?.runOnUiThread {
            dialog = COUIAlertDialogBuilder(context)
                .setBlurBackgroundDrawable(true)
                .setTitle(
                    String.format(
                        ContextGetter.context.getString(R.string.enable_request_title),
                        appName
                    )
                )
                .setMessage(
                    String.format(
                        ContextGetter.context.getString(msgId),
                        appName
                    )
                )
                .setPositiveButton(R.string.app_enable_button) { _, _ ->
                    startAppDetailWithSetting(context, pkgName)
                }
                .setNegativeButton(R.string.dialog_cancel) { _, _ -> }
                .create().also {
                    it.show()
                }
        } ?: Log.d(TAG, " checkAppEnabledWithDialog: context is not activity")

        return dialog
    }


    /**
     * start target app setting detail activity
     */
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    private fun startAppDetailWithSetting(context: Context, packageName: String) {
        Intent(SETTING_DETAIL_ACTION)
            .setData(Uri.fromParts("package", packageName, null)).let {
                try {
                    if (context !is Activity) {
                        it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    context.startActivity(it)
                } catch (e: Exception) {
                    Log.w(TAG, "startAppDetailWithSetting: exception: ${e.message}")
                }
            }
    }

    /**
     * @attention check target app is enable to be called
     */
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun checkAppForceEnabled(packageName: String): ConnectionResult {
        if (packageName.isEmpty()) return ConnectionResult.MISSED
        return try {
            val applicationInfo =
                ContextGetter.context.packageManager.getApplicationInfo(packageName, 0)
            val enable = applicationInfo.enabled || isAppFrozen(applicationInfo)
            if (enable) {
                ConnectionResult.SUCCESS
            } else {
                ConnectionResult.DISABLED
            }
        } catch (e: Exception) {
            Log.w(TAG, "checkAppForceEnabled error: ${e.message}")
            ConnectionResult.MISSED
        }
    }

    /**
     * @attention when app is frozen, the applicationInfo params $enabled will be false either.
     */
    @SuppressLint("NewApi")
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    private fun isAppFrozen(applicationInfo: ApplicationInfo): Boolean {
        runCatching {
            return if (OplusVersion.isAtLeast(OplusVersion.OS_15_0_0)) { // 使用 下沉的接口
                val pm = getOplusPackageManager(ContextGetter.context)
                val state = pm.getOplusFreezePackageState(applicationInfo.packageName, UserHandle.myUserId())
                Log.d(TAG, "isAppFrozen by osdk interface packageState:$state")
                state == STATE_OPLUS_FREEZE_FREEZED
            } else { // 使用历史接口
                val state = getOplusFreezeState(applicationInfo)
                Log.d(TAG, "isAppFrozen by old interface packageState:$state")
                state == OPLUS_STATE_FREEZE_FREEZED
            }
        }.onFailure {
            Log.w(TAG, "isAppFrozen error: ${it.message}")
        }
        return false
    }

    @JvmStatic
    fun startPhoneManager(context: Context, bundle: Bundle? = null) {
        try {
            FeatureCompat.sPhoneManagerStartInfo?.apply {
                if (!checkAppEnabledWithDialog(context, first, R.string.phone_manager_disable_message)) {
                    return
                }

                val intent = Intent(second)
                intent.setPackage(first)
                intent.putExtra("enter_from", context.packageName)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                if (context !is Activity) {
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                bundle?.let {
                    intent.putExtras(bundle)
                }
                context.startActivity(intent)
            }
        } catch (e: ActivityNotFoundException) {
            Log.e(TAG, "startPhoneManager failed: ${e.message}")
        }
    }

    val isOnePlusOverSea by lazy {
        BrandUtil.isOnePlus() && (FeatureCompat.sIsExpRom)
    }

    @JvmStatic
    fun getFileManagerAppId(): String {
        val applicationId = if (isOnePlusOverSea) {
            "com.oneplus.filemanager"
        } else {
            "com.coloros.filemanager"
        }
        Log.d(TAG, "getFileManagerAppId: applicationId=$applicationId")
        return applicationId
    }

    @JvmStatic
    fun startOWork(context: Context, entranceFlag: String) {
        try {
            val intent = Intent()
            intent.setPackage(OWOKR_PACKAGE)
            intent.action = ACTION_OWORK
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
            intent.putExtra(ENTRANCE_FLAG, entranceFlag)
            context.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            Log.e(TAG, "startOWork failed: $e")
        }
    }
}