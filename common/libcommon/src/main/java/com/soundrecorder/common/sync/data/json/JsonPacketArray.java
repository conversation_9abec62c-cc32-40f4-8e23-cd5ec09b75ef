/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: JsonPacketArray
 ** Description:
 ** Version: 1.0
 ** Date : 2019-07-11
 ** Author: huangyuanwang
 **
 ** v1.0, 2019-3-12, huangyuanwang, create
 ****************************************************************/

package com.soundrecorder.common.sync.data.json;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;

import com.soundrecorder.common.sync.data.Packet;
import com.soundrecorder.common.sync.data.PacketArray;

public class JsonPacketArray implements PacketArray<JsonElement> {

    private JsonArray mJsonArray = new JsonArray();

    @Override
    public void add(Packet kv) {
        if (kv instanceof JsonPacketObject) {
            mJsonArray.add(((JsonPacketObject) kv).mJsonObject);
        }
    }

    @Override
    public void addAll(PacketArray<?> pkArray) {
        if ((null != pkArray) && (pkArray instanceof JsonPacketArray)) {
            JsonArray jsonArray = (JsonArray) pkArray.toT();
            mJsonArray.addAll(jsonArray);
        }

    }

    @Override
    public boolean remove(Packet kv) {
        if (kv instanceof JsonPacketObject) {
            return mJsonArray.remove(((JsonPacketObject) kv).mJsonObject);
        }
        return false;
    }

    @Override
    public Packet get(int index) {
        final JsonElement item = mJsonArray.get(index);
        final JsonPacketObject jsonKv = new JsonPacketObject();
        if (item.isJsonObject()) {
            jsonKv.mJsonObject = item.getAsJsonObject();
        }
        return jsonKv;
    }

    @Override
    public int size() {
        return mJsonArray.size();
    }

    @Override
    public JsonArray toT() {
        return mJsonArray;
    }

    @Override
    public PacketArray<JsonElement> parse(JsonElement jsonArray) {
        this.mJsonArray = (JsonArray) jsonArray;
        return this;
    }

}
