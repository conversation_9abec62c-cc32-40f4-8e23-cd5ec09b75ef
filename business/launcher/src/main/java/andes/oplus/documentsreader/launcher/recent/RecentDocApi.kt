/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/08/12, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package andes.oplus.documentsreader.launcher.recent

import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.interfaces.IBaseVMFragment
import andes.oplus.documentsreader.core.common.parentchild.IParentChildApi
import andes.oplus.documentsreader.core.common.uiconfig.UIConfigMonitor
import andes.oplus.documentsreader.core.common.uiconfig.type.IUIConfig
import andes.oplus.documentsreader.sidepreview.IPreviewListFragment
import andes.oplus.documentsreader.sidepreview.IPreviewListFragmentCreator
import andes.oplus.documentsreader.sidepreview.IPreviewOperate
import android.app.Activity
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.fragment.app.Fragment
import com.coui.appcompat.sidenavigation.COUISideNavigationBar

object RecentDocApi : IParentChildApi {

    override fun getFragment(activity: Activity, category: Int): Fragment {
        val previewOperate = Injector.injectFactory<IPreviewOperate>() ?: return RecentDocFragment()
        return if (previewOperate.isSupportPreview()) {
            previewOperate.setPreviewListFragmentCreator(object : IPreviewListFragmentCreator {
                override fun create(): IPreviewListFragment {
                    return RecentDocFragment()
                }
            })
            previewOperate.getFragment()
        } else {
            RecentDocFragment()
        }
    }

    override fun onResumeLoadData(fragment: Fragment) {
        (fragment as? IBaseVMFragment)?.onResumeLoadData()
    }

    override fun onCreateOptionsMenu(fragment: Fragment, menu: Menu, inflater: MenuInflater) {
        fragment.onCreateOptionsMenu(menu, inflater)
    }

    override fun onOptionsMenuItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        return fragment.onOptionsItemSelected(item)
    }

    override fun pressBack(fragment: Fragment): Boolean {
        return (fragment as? IBaseVMFragment)?.pressBack() ?: true
    }

    override fun fromSelectPathResult(fragment: Fragment, requestCode: Int, path: String?) {
        (fragment as? IBaseVMFragment)?.fromSelectPathResult(requestCode, path)
    }

    override fun backToTop(fragment: Fragment) {
        (fragment as? IBaseVMFragment)?.backToTop()
    }

    override fun setIsHalfScreen(fragment: Fragment, category: Int, isHalfScreen: Boolean) {
        (fragment as? IBaseVMFragment)?.setIsHalfScreen(isHalfScreen)
    }

    override fun onUIConfigChanged(fragment: Fragment, configList: MutableCollection<IUIConfig>) {
        (fragment as? UIConfigMonitor.OnUIConfigChangeListener)?.onUIConfigChanged(configList)
    }

    override fun onWindowInsetsCallback(
        fragment: Fragment,
        showNavigationBar: Boolean,
        systemBarInsetsBottom: Int
    ) {
        (fragment as? IBaseVMFragment)?.onWindowInsetsCallback(
            showNavigationBar,
            systemBarInsetsBottom
        )
    }

    override fun openDrawer(fragment: Fragment, fragmentWidth: Int) {
        (fragment as? IBaseVMFragment)?.openDrawer(fragmentWidth)
    }

    override fun closeDrawer(fragment: Fragment, fragmentWidth: Int) {
        (fragment as? IBaseVMFragment)?.closeDrawer(fragmentWidth)
    }

    override fun notifyDrawerRight(fragment: Fragment, right: Int) {
        (fragment as? IBaseVMFragment)?.notifyDrawerRight(right)
    }

    override fun onSideNavigationClicked(
        fragment: Fragment,
        sideNavigationContainer: COUISideNavigationBar?,
        isOpen: Boolean
    ): Boolean {
        return (fragment as? IBaseVMFragment)?.onSideNavigationClicked(
            sideNavigationContainer,
            isOpen
        ) ?: false
    }

    override fun updateFragmentData(fragment: Fragment, bundle: Bundle?) {
        (fragment as? IBaseVMFragment)?.updateFragmentData(bundle)
    }

    override fun exitSelectionMode(fragment: Fragment) {
        (fragment as? IBaseVMFragment)?.exitSelectionMode()
    }
}