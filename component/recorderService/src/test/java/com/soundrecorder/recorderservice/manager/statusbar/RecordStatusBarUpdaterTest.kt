/*
 Copyright (C), 2008-2023 OPLUS Mobile Comm Corp., Ltd.
 File: RecordStatusBarUpdaterTest
 Description:
 Version: 1.0
 Date: 2023/3/22
 Author: ********(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2023/3/22 1.0 create
 */

package com.soundrecorder.recorderservice.manager.statusbar

import android.content.Context
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.modulerouter.SeedlingAction
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.recorderservice.manager.RecordStatusManager
import com.soundrecorder.recorderservice.manager.RecorderViewModel
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption
import com.soundrecorder.recorderservice.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.recorderservice.shadows.ShadowOplusCompactUtil
import org.json.JSONObject
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyBoolean
import org.mockito.ArgumentMatchers.anyObject
import org.mockito.ArgumentMatchers.anyString
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowOplusCompactUtil::class]
)
class RecordStatusBarUpdaterTest {
    private var ctx: Context? = null
    private var seedlingActionStatic1: MockedStatic<SeedlingAction>? = null
    private var recorderViewModelStatic: MockedStatic<RecorderViewModel>? = null
    private var seedlingStatusBar: MockedStatic<SeedlingStatusBar>? = null

    @Before
    fun setUp() {
//        seedlingActionStatic1 = Mockito.mockStatic(SeedlingAction::class.java)
        recorderViewModelStatic = Mockito.mockStatic(RecorderViewModel::class.java)
        seedlingStatusBar = Mockito.mockStatic(SeedlingStatusBar::class.java)
    }

    @After
    fun reset() {
        ctx = null
        /*seedlingActionStatic1?.close()
        seedlingActionStatic1 = null*/
        recorderViewModelStatic?.close()
        recorderViewModelStatic = null
        seedlingStatusBar?.close()
        seedlingStatusBar = null
    }

    @Test
    @Ignore
    fun onReadyServiceTest() {
        RecordStatusBarUpdater.onReadyService()
        seedlingStatusBar?.verify({
            SeedlingStatusBar.dismiss(any(Context::class.java), anyBoolean(),  anyString())
        }, Mockito.times(1))
    }

    private fun <T> any(type: Class<T>): T = Mockito.any<T>(type)

    @Test
    @Ignore
    fun onCloseServiceTest() {
        RecordStatusBarUpdater.onCloseService()
        seedlingStatusBar?.verify({
            SeedlingStatusBar.release()
        }, Mockito.times(1))
    }

    @Test
    @Ignore
    fun setHasForegroundTest() {
        RecordStatusBarUpdater.setHasForeground(false)
        Assert.assertFalse(
            Whitebox.getInternalState(
                RecordStatusBarUpdater::class.java,
                "hasForeground"
            )
        )
        RecordStatusBarUpdater.setHasForeground(true)
        Assert.assertTrue(
            Whitebox.getInternalState(
                RecordStatusBarUpdater::class.java,
                "hasForeground"
            )
        )
    }

    @Test
    @Ignore
    fun onWaveStateChangeTest() {
        Whitebox.setInternalState(RecordStatusBarUpdater::class.java, "isScreenOff", false)
        Whitebox.setInternalState(RecordStatusBarUpdater::class.java, "hasForeground", false)
        Whitebox.setInternalState(SeedlingStatusBar::class.java, "isShowing", true)
        RecordStatusBarUpdater.onWaveStateChange(0)
        seedlingActionStatic1?.verify({
            SeedlingAction.refreshSeedlingData(any(JSONObject::class.java))
        }, Mockito.times(1))
    }

    @Test
    @Ignore
    fun onConfigurationChangedTest() {
        Whitebox.setInternalState(RecordStatusBarUpdater::class.java, "isScreenOff", false)
        Whitebox.setInternalState(RecordStatusBarUpdater::class.java, "hasForeground", false)
        Whitebox.setInternalState(SeedlingStatusBar::class.java, "isShowing", true)
        RecordStatusBarUpdater.onConfigurationChanged()
        seedlingActionStatic1?.verify({
            SeedlingAction.refreshSeedlingData(any(JSONObject::class.java))
        }, Mockito.times(1))
    }

    @Test
    @Ignore
    fun should_invoke_when_onMarkDataChange() {
        Whitebox.setInternalState(RecordStatusBarUpdater::class.java, "isScreenOff", false)
        Whitebox.setInternalState(RecordStatusBarUpdater::class.java, "hasForeground", false)
        Whitebox.setInternalState(SeedlingStatusBar::class.java, "isShowing", true)
        RecordStatusBarUpdater.onMarkDataChange(RecorderViewModelAction.MarkAction.SINGLE_ADD, 1)
        seedlingActionStatic1?.verify({
            SeedlingAction.refreshSeedlingData(any(JSONObject::class.java))
        }, Mockito.times(1))
    }

    @Test
    @Ignore
    fun should_invoke_when_onRecordCallConnected() {
        Whitebox.setInternalState(RecordStatusBarUpdater::class.java, "isScreenOff", false)
        Whitebox.setInternalState(RecordStatusBarUpdater::class.java, "hasForeground", false)
        Whitebox.setInternalState(SeedlingStatusBar::class.java, "isShowing", true)
        RecordStatusBarUpdater.onRecordCallConnected()
        seedlingStatusBar?.verify({
            SeedlingAction.refreshSeedlingData(any(JSONObject::class.java))
        }, Mockito.times(1))
    }

    @Test
    @Ignore
    fun onRecordStatusChangeTest() {
        Mockito.`when`(SeedlingAction.isSupportFluidCloud(any(Context::class.java))).thenReturn(true)
        Mockito.`when`(SeedlingAction.isSupportSystemSendIntent(any(Context::class.java))).thenReturn(true)
        RecordStatusManager.setCurrentStatus(RecordStatusManager.RECORDING)
        Whitebox.setInternalState(RecordStatusBarUpdater::class.java, "isScreenOff", false)
        Whitebox.setInternalState(RecordStatusBarUpdater::class.java, "hasForeground", false)
        Whitebox.setInternalState(SeedlingStatusBar::class.java, "isShowing", false)
        RecordStatusBarUpdater.onRecordStatusChange(RecordStatusManager.RECORDING)
        seedlingStatusBar?.verify({
            SeedlingAction.showSeedlingStatusBar(any(JSONObject::class.java), anyObject())
        }, Mockito.times(1))
    }

    @Test
    @Ignore
    fun showOrHideTest() {
        Mockito.`when`(SeedlingAction.isSupportFluidCloud(any(Context::class.java))).thenReturn(false)
        RecordStatusManager.setCurrentStatus(RecordStatusManager.RECORDING)
        RecordStatusBarUpdater.setHasForeground(true)
        RecordStatusBarUpdater.setHasForeground(false)
        RecordStatusManager.setCurrentStatus(RecordStatusManager.PAUSED)
        RecordStatusBarUpdater.setHasForeground(true)
        RecordStatusBarUpdater.setHasForeground(false)
        RecordStatusManager.setCurrentStatus(RecordStatusManager.INIT)
        RecordStatusBarUpdater.setHasForeground(true)
        RecordStatusBarUpdater.setHasForeground(false)
        RecordStatusManager.setCurrentStatus(RecordStatusManager.HALT_ON)
        RecordStatusBarUpdater.setHasForeground(true)
        RecordStatusBarUpdater.setHasForeground(false)
        Mockito.`when`(SeedlingAction.isSupportFluidCloud(any(Context::class.java))).thenReturn(true)
        RecordStatusManager.setCurrentStatus(RecordStatusManager.RECORDING)
        RecordStatusBarUpdater.setHasForeground(true)
        RecordStatusBarUpdater.setHasForeground(false)
        RecordStatusManager.setCurrentStatus(RecordStatusManager.PAUSED)
        RecordStatusBarUpdater.setHasForeground(true)
        RecordStatusBarUpdater.setHasForeground(false)
        RecordStatusManager.setCurrentStatus(RecordStatusManager.INIT)
        RecordStatusBarUpdater.setHasForeground(true)
        RecordStatusBarUpdater.setHasForeground(false)
        RecordStatusManager.setCurrentStatus(RecordStatusManager.HALT_ON)
        RecordStatusBarUpdater.setHasForeground(true)
        RecordStatusBarUpdater.setHasForeground(false)
    }

    @Test
    @Ignore
    fun should_invoke_when_forceDismiss() {
        Mockito.`when`(SeedlingAction.isSupportFluidCloud(any(Context::class.java))).thenReturn(true)
        Whitebox.setInternalState(SeedlingStatusBar::class.java, "isShowing", true)
        RecordStatusBarUpdater.forceDismiss("from")
        seedlingStatusBar?.verify({
            SeedlingStatusBar.dismiss(any(Context::class.java), anyBoolean(), anyString())
        }, Mockito.times(1))
    }
}