/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: OpenAnyTool
 * * Description: OpenAnyTool
 * * Version: 1.0
 * * Date : 2024/07/03
 * * Author:ztf
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *        ztf           2024/07/03       1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.impl.tool

import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.KtConstants.FROM_MAIN_PAGE
import andes.oplus.documentsreader.core.common.Log
import android.content.Context
import android.content.Intent
import android.net.Uri

object OpenAnyTool {

    private const val TAG = "OpenAnyTool"
    private const val START_CONVERT_ACTION = "oplus.intent.action.IWORK_CONVERT_PDF"

    @JvmStatic
    fun startToConvert(context: Context, convertType: Int, mimeType: String, selectUri: Uri) {
        Log.d(TAG, "startToConvert -> convertType = $convertType,   mimeType = $mimeType")
        val intent = Intent()
        intent.action = START_CONVERT_ACTION
        val packageName = context.packageName
        intent.`package` = packageName
        intent.putExtra(KtConstants.FILE_CONVERT_TYPE, convertType)
        intent.putExtra(FROM_MAIN_PAGE, true)
        intent.setDataAndType(selectUri, mimeType)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        intent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
        runCatching {
            context.startActivity(intent)
        }.onFailure {
            Log.e(TAG, "startToConvert fail = ${it.message}")
        }
    }
}