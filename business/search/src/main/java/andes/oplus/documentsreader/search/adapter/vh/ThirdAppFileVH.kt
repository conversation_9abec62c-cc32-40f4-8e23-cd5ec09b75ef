/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ThirdAppFileVH
 ** Description : 三方应用文件的结果item的ViewHolder
 ** Version     : 1.0
 ** Date        : 2024/05/22 10:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/06       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.search.adapter.vh

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.BaseSelectionRecycleAdapter
import andes.oplus.documentsreader.core.common.Constants
import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.FileImageLoader
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.MimeTypeHelper
import andes.oplus.documentsreader.core.common.TextViewSnippet
import andes.oplus.documentsreader.core.common.ThirdAppConstants.DINGTALK_PACKAGE
import andes.oplus.documentsreader.core.common.ThirdAppConstants.FEISHU_PACKAGE
import andes.oplus.documentsreader.core.common.ThirdAppConstants.QQ_PACKAGE
import andes.oplus.documentsreader.core.common.ThirdAppConstants.WECHAT_PACKAGE
import andes.oplus.documentsreader.core.common.ThirdAppConstants.WEWORK_PACKAGE
import andes.oplus.documentsreader.core.common.Utils
import andes.oplus.documentsreader.core.common.view.FileThumbView
import andes.oplus.documentsreader.core.common.viewholder.BaseSelectionViewHolder
import andes.oplus.documentsreader.core.common.wrapper.ThirdAppFileWrapper
import andes.oplus.documentsreader.search.R
import andes.oplus.documentsreader.search.adapter.GlobalSearchAdapter
import andes.oplus.documentsreader.search.ui.CommonUtil
import android.content.Context
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.annotation.LayoutRes
import androidx.cardview.widget.CardView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.setPadding
import com.coui.appcompat.contextutil.COUIContextUtil

class ThirdAppFileVH(val adapter: GlobalSearchAdapter, convertView: View) : BaseSelectionViewHolder(convertView, false) {

    companion object {

        private const val TAG = "ThirdAppFileVH"

        @LayoutRes
        fun layoutId(): Int {
            return R.layout.oplusdoc_search_thrid_app_file_item_new
        }

        const val CHOICE_MODE_ALPHA_0_2 = 0.2f
        const val CHOICE_MODE_ALPHA_0_3 = 0.3f
        const val CHOICE_MODE_ALPHA_1 = 1f
    }

    private var rootView: ConstraintLayout? = null
    private var iconImg: FileThumbView
    private var titleTv: TextViewSnippet
    private var sourceCardLayout: CardView
    private var sourceImg: ImageView
    private var sourceTv: TextViewSnippet
    private var sourcePackageTv: TextView
    private var divider1: View

    init {
        rootView = convertView.findViewById(R.id.search_item_root)
        iconImg = convertView.findViewById(R.id.file_list_item_icon)
        titleTv = convertView.findViewById(R.id.file_list_item_title)
        sourceCardLayout = convertView.findViewById(R.id.card_view)
        sourceImg = convertView.findViewById(R.id.file_list_item_source_img)
        sourceTv = convertView.findViewById(R.id.file_list_item_source)
        divider1 = convertView.findViewById(R.id.file_list_item_divider_line_1)
        sourcePackageTv = convertView.findViewById(R.id.file_list_item_source_package)
    }

    override fun isInDragRegionImpl(event: MotionEvent): Boolean = false


    fun bindData(
        data: ThirdAppFileWrapper,
        keyword: String?,
        choiceMode: Boolean,
        adapter: BaseSelectionRecycleAdapter<*, *>
    ) {
        Log.i(TAG, "bindData data $data, keyword: $keyword")
        itemCount = adapter.getRealFileItemCount()
        val fileName = data.mDisplayName
        val type = data.mLocalType
        if (fileName == null) {
            Log.d(TAG, "bindData path is null")
            return
        }
        showFileIcon(iconImg, type, data, choiceMode)
        showTitle(titleTv, data, keyword, choiceMode)
        showSourceIcon(sourceCardLayout, sourceImg, data.sourcePackageName, choiceMode)
        showSourcePackageTv(sourcePackageTv, data, choiceMode)
        showDivider(divider1, choiceMode)
    }


    private fun showDivider(view: View, choiceMode: Boolean) {
        if (choiceMode) {
            view.setBackgroundColor(
                COUIContextUtil.getAttrColor(
                    view.context,
                    com.support.appcompat.R.attr.couiColorLabelQuaternary
                )
            )
        } else {
            view.setBackgroundColor(
                COUIContextUtil.getAttrColor(
                    view.context,
                    com.support.appcompat.R.attr.couiColorLabelSecondary
                )
            )
        }
    }


    private fun showFileIcon(
        imageView: FileThumbView,
        type: Int,
        data: BaseFileBean,
        choiceMode: Boolean
    ) {
        val padding = when (type) {
            MimeTypeHelper.IMAGE_TYPE, MimeTypeHelper.VIDEO_TYPE -> {
                ContextGetter.application.resources.getDimension(R.dimen.file_list_image_padding)
                    .toInt()
            }

            else -> 0
        }
        if (choiceMode) {
            imageView.alpha = CHOICE_MODE_ALPHA_0_3
        } else {
            imageView.alpha = CHOICE_MODE_ALPHA_1
        }
        imageView.setPadding(padding)
        FileImageLoader.displayDefault(data, imageView)
    }

    private fun showTitle(
        titleTv: TextViewSnippet,
        data: ThirdAppFileWrapper,
        keyword: String?,
        choiceMode: Boolean
    ) {
        val displayName = data.mDisplayName
        val hightKeyWords = data.titleHighLight.getKeyWordList()
        // 设置高亮和文字的颜色
        val context = titleTv.context
        titleTv.setHighLightColor(getHighLightColor(context, choiceMode))
        titleTv.setTextColor(getTitleTextColor(context, choiceMode))

        if (keyword.isNullOrEmpty()) {
            titleTv.text = displayName
        } else {
            if (Utils.isNeededTargetLanguage(Constants.SNIPPET_LANGUAGE)) {
                titleTv.text = displayName
            } else {
                if (hightKeyWords.isEmpty()) {
                    titleTv.setDMPTitleList(null)
                    titleTv.setTextWithPost(displayName, keyword)
                } else {
                    titleTv.setDMPTitleList(hightKeyWords)
                    titleTv.setTextWithPost(displayName, hightKeyWords[0])
                }
            }
        }

        titleTv.post {
            val title = displayName ?: return@post
            val moreOne = titleTv.isMoreThanOneLine(title)
            rootView?.let {
                setIconConstraintSet(it, moreOne)
            }
        }
    }

    /**
     * 获取sourceName的高亮的颜色，
     */
    @ColorInt
    private fun getSourceNameTextColor(context: Context, choiceMode: Boolean): Int {
        return if (choiceMode) {
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelQuaternary)
        } else {
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelSecondary)
        }
    }

    /**
     * 获取高亮的颜色
     */
    @ColorInt
    private fun getHighLightColor(context: Context, choiceMode: Boolean): Int {
        return if (choiceMode) {
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorContainerThemeHalftone)
        } else {
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelTheme)
        }
    }

    /**
     * 设置文字颜色
     */
    @ColorInt
    private fun getTitleTextColor(context: Context, choiceMode: Boolean): Int {
        return if (choiceMode) {
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelQuaternary)
        } else {
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimaryNeutral)
        }
    }

    private fun showSourceIcon(cardLayout: CardView, imageView: ImageView, source: String, choiceMode: Boolean) {
        if (source.contentEquals(FEISHU_PACKAGE, true)) {
            //这个地方需要UX给截图，替换资源
            imageView.setImageResource(R.drawable.oplusdoc_feishu_source_icon)
        } else if (source.contentEquals(WECHAT_PACKAGE, true)) {
            //这个地方需要UX给截图，替换资源
            imageView.setImageResource(R.drawable.oplusdoc_wechat_source_icon)
        } else if (source.contentEquals(QQ_PACKAGE, true)) {
            //这个地方需要UX给截图，替换资源
            imageView.setImageResource(R.drawable.oplusdoc_qq_source_icon)
        } else if (source.contentEquals(WEWORK_PACKAGE, true)) {
            //这个地方需要UX给截图，替换资源
            imageView.setImageResource(R.drawable.oplusdoc_wework_source_icon)
        } else if (source.contentEquals(DINGTALK_PACKAGE, true)) {
            //这个地方需要UX给截图，替换资源
            imageView.setImageResource(R.drawable.oplusdoc_dingtalk_source_icon)
        } else {
            val appIcon = CommonUtil.getApplicationIconForPackage(imageView.context, source)
            if (appIcon != null) {
                imageView.setImageDrawable(appIcon)
            } else {
                //这个地方需要UX单独给图
                imageView.setImageResource(R.drawable.oplusdoc_wechat_source_icon)
            }
        }
        if (choiceMode) {
            cardLayout.alpha = CHOICE_MODE_ALPHA_0_2
        } else {
            cardLayout.alpha = CHOICE_MODE_ALPHA_1
        }
    }


    private fun showSourcePackageTv(textView: TextView, data: ThirdAppFileWrapper, choiceMode: Boolean) {
        val context = textView.context
        val packageString = data.sourcePackageName
        val dateAndTime = CommonUtil.getThirdAppNameForPackage(context, packageString)
        textView.text = dateAndTime
        if (choiceMode) {
            textView.setTextColor(
                COUIContextUtil.getAttrColor(
                    textView.context,
                    com.support.appcompat.R.attr.couiColorLabelQuaternary
                )
            )
        } else {
            textView.setTextColor(
                COUIContextUtil.getAttrColor(
                    textView.context,
                    com.support.appcompat.R.attr.couiColorLabelSecondary
                )
            )
        }
    }

    /**
     * 当标题显示一行时，左边的icon在整个item中上下居中
     * 当标题显示两行时，左边的icon顶部和标题顶部对齐
     */
    private fun setIconConstraintSet(rootView: ConstraintLayout, isMoreThanOneLine: Boolean) {
        val constraintSet = ConstraintSet()
        constraintSet.apply {
            clone(rootView)
            clear(R.id.file_list_item_icon, ConstraintSet.TOP)
            clear(R.id.file_list_item_icon, ConstraintSet.BOTTOM)
            if (isMoreThanOneLine) {
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.TOP,
                    R.id.rl_item_title,
                    ConstraintSet.TOP
                )
            } else {
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP
                )
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )
            }
            applyTo(rootView)
        }
    }

    override fun drawDivider(): Boolean {
        return adapter.isNextItemSame(bindingAdapterPosition)
    }

    override fun getDividerStartAlignView(): View {
        return titleTv
    }

    override fun getDividerEndInset(): Int {
        return ContextGetter.application.resources.getDimensionPixelSize(R.dimen.dimen_24dp)
    }
}