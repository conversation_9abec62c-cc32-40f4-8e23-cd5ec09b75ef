<vector
    android:autoMirrored="true"
    android:height="20dp"
    android:viewportHeight="20"
    android:viewportWidth="20"
    android:width="20dp"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <path
        android:fillAlpha="0.3"
        android:fillColor="#ffffff"
        android:pathData="M18.072,3.458C17.814,3.374 17.539,3.333 17.247,3.333H2.747C2.455,3.333 2.18,3.374 1.922,3.458C1.98,2.599 2.705,1.916 3.58,1.916H16.414C17.289,1.916 18.014,2.599 18.072,3.458Z"/>
    <path
        android:fillColor="#00000000"
        android:pathData="M2.75,5L17.25,5A1.083,1.083 0,0 1,18.333 6.083L18.333,16.417A1.083,1.083 0,0 1,17.25 17.5L2.75,17.5A1.083,1.083 0,0 1,1.667 16.417L1.667,6.083A1.083,1.083 0,0 1,2.75 5z"
        android:strokeAlpha="0.3"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:strokeWidth="1.16667"/>
    <path
        android:fillAlpha="0.3"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"
        android:pathData="M6.667,9.917C5.953,9.917 5.375,10.495 5.375,11.208C5.375,11.922 5.953,12.5 6.667,12.5C7.38,12.5 7.958,11.922 7.958,11.208C7.958,10.495 7.38,9.917 6.667,9.917ZM8.759,9.917C8.991,10.292 9.125,10.734 9.125,11.208C9.125,12.566 8.025,13.667 6.667,13.667C5.309,13.667 4.208,12.566 4.208,11.208C4.208,9.851 5.309,8.75 6.667,8.75H13.333C14.691,8.75 15.792,9.851 15.792,11.208C15.792,12.566 14.691,13.667 13.333,13.667C11.976,13.667 10.875,12.566 10.875,11.208C10.875,10.734 11.009,10.292 11.241,9.917H8.759ZM13.333,9.917C12.62,9.917 12.042,10.495 12.042,11.208C12.042,11.922 12.62,12.5 13.333,12.5C14.047,12.5 14.625,11.922 14.625,11.208C14.625,10.495 14.047,9.917 13.333,9.917Z"/>
</vector>
