apply from: "../../common_build.gradle"

android {
    sourceSets {
        main {
            res.srcDirs += ['res']
            res.srcDirs += ['../../res-strings']
        }
    }
}
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$prop_kotlinVersion"
    implementation "androidx.appcompat:appcompat:${prop_appcompatVersion}"
    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycleVersion"

    // base包为必须引用的包，prop_versionName需保持一致
    implementation ("com.oplus.appcompat:core:${prop_versionName}") {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    implementation "com.oplus.appcompat:poplist:${prop_versionName}"
    implementation "com.oplus.appcompat:recyclerview:${prop_versionName}"
    implementation "com.oplus.appcompat:dialog:${prop_versionName}"
    implementation "com.oplus.appcompat:panel:${prop_versionName}"

    //依赖Ostich
    implementation "com.inno.ostitch:stitch:${stitchVersion}"
    implementation "com.inno.ostitch:stitch-annotation:${stitchAnnotationVersion}"
    kapt "com.inno.ostitch:stitch-compile:${stitchCompileVersion}"

    implementation project(':common:libbase')
    implementation project(':common:libimageload')
    implementation project(':common:libcommon')
    implementation project(':common:modulerouter')
}
