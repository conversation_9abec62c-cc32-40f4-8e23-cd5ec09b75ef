package com.oplus.recorderlogx

import android.content.Context
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.modulerouter.xlog.RecordLogXConfig
import com.soundrecorder.modulerouter.xlog.XLogAction

@Component(XLogAction.COMPONENT_NAME)
object XLogApi {

    private var recordXLog: RecorderXLog = RecorderXLog()

    @Action(XLogAction.INIT_LOG)
    @JvmStatic
    fun initLog(context: Context) {
        recordXLog.initLog(context)
    }


    @Action(XLogAction.PROCESS_MANUAL_REPORT_LOG)
    @JvmStatic
    fun flushLog(isSync: Boolean) {
        recordXLog.flushLog(isSync)
    }


    @Action(XLogAction.PROCESS_PUSH_LOG)
    @JvmStatic
    fun processPushLog(context: Context?, cloudLogConfigMsg: RecordLogXConfig) {
        recordXLog.processPushLog(context, cloudLogConfigMsg)
    }


    @Action(XLogAction.PROCESS_MANUAL_REPORT_LOG)
    @JvmStatic
    fun processManualReportLog() {
        recordXLog.processManualReportLog()
    }


    @Action(XLogAction.PROCESS_PRINT_DB)
    @JvmStatic
    fun processDBPrint(context: Context?) {
        recordXLog.processDBPrint(context)
    }


    @Action(XLogAction.LOG_V)
    @JvmStatic
    fun v(tag: String?, message: String?) {
        recordXLog.v(tag, message)
    }

    @Action(XLogAction.LOG_D)
    @JvmStatic
    fun d(tag: String?, message: String?) {
        recordXLog.d(tag, message)
    }

    @Action(XLogAction.LOG_I)
    @JvmStatic
    fun i(tag: String?, message: String?) {
        recordXLog.i(tag, message)
    }

    @Action(XLogAction.LOG_W)
    @JvmStatic
    fun w(tag: String?, message: String?) {
        recordXLog.w(tag, message)
    }

    @Action(XLogAction.LOG_E)
    @JvmStatic
    fun e(tag: String?, message: String?) {
        recordXLog.e(tag, message)
    }

    @Action(XLogAction.LOG_E_THROWABLE)
    @JvmStatic
    fun e(tag: String?, message: String?, e: Throwable?) {
        recordXLog.e(tag, message, e)
    }
}