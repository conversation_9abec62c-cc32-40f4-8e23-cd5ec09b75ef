<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/icp_content"
        style="@style/couiTextAppearanceDescription"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:maxLines="1"
        android:ellipsize="end"
        android:paddingStart="@dimen/dimen_8dp"
        android:paddingEnd="@dimen/dimen_4dp"
        android:text="@string/icp_registration_number_content"
        android:textColor="?attr/couiColorHintNeutral"
        android:background="@drawable/bg_icp_registration_view"
        app:drawableEndCompat="@drawable/icon_icp_arrow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>