/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : FileEncryptController.kt
 * * Description : com.coloros.filemanager.filerefactor.controller
 * * Version     : 1.0
 * * Date        : 2020/3/18
 * * Author      : W9001165
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.encrypt

import andes.oplus.documentsreader.core.common.BaseLifeController
import andes.oplus.documentsreader.core.common.BaseVMActivity
import android.os.IBinder

abstract class FileEncryptController(activity: BaseVMActivity) : BaseLifeController {
    companion object {
        const val TAG = "FileEncryptController"
    }

    protected val mActivity: BaseVMActivity

    init {
        mActivity = activity
        activity.lifecycle.addObserver(this)
    }

    /**
     * run encrypt task
     */
    abstract fun runEncryptTask(callback: ServiceConnectedCallback)

    /**
     * encrypt task ondestroy
     */
    protected abstract fun ondestroy()

    /**
     * bind EncryptService
     */
    protected abstract fun bindEncryptService()

    override fun onDestroy() {
        ondestroy()
    }

    interface ServiceConnectedCallback {
        fun onServiceConnected(service: FileManagerEncryptionInterface)
    }

    interface FileManagerEncryptionInterface {
        fun encryptionTasks(sources: List<String?>?, imageTypes: IntArray?, scanState: Boolean, listener: FileManagerIEncryptProgressListener?): Int
        fun setStopEncryption(state: Boolean)
    }

    interface FileManagerIEncryptProgressListener {
        fun onStarted() {}
        fun onProgress(progress: Int) {}
        fun asBinder(): IBinder? = null
        fun onFinished(result: Int, failedCount: Int) {}
    }
}