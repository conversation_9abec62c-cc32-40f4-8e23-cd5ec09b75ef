/*********************************************************************************
 * Copyright (C), 2008-2024, Oplus, All rights reserved.
 *
 * File: - IDocThumbServLogger.kt
 * Description:
 *     The the interface of logger of DocThumbnailService.
 *
 * Version: 1.0
 * Date: 2024-12-18
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-12-18   1.0    Create this module
 *********************************************************************************/
package com.oplus.documentsreader.thumbnail

interface IDocThumbServLogger {

    /**
     * Output normal logs
     *
     * @param tag log tag
     * @param msg log message
     */
    fun printLog(tag: String, msg: String)

    /**
     * Output error logs
     *
     * @param tag log tag
     * @param msg log message
     * @param tr the exception or error to output, can be null
     */
    fun printError(tag: String, msg: String, tr: Throwable?)
}