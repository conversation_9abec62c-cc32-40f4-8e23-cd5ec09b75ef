package andes.oplus.documentsreader.selectdir.impl.ui.path

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.BaseSelectionRecycleAdapter
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.OnRecyclerItemClickListener
import andes.oplus.documentsreader.core.common.isActivityAndInvalid
import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.LifecycleObserver
import java.util.Locale

class SelectDirPathAdapter(context: Context) : BaseSelectionRecycleAdapter<SelectPathViewHolder, BaseFileBean>(context),
    LifecycleObserver {

    companion object {
        private const val TAG = "SelectDirPathAdapter"
        const val TEST_FILENAME = ".test"  // this name is set for hidden file alpha
    }

    private var mOnRecyclerItemClickListener: OnRecyclerItemClickListener? = null


    fun setOnRecyclerItemClickListener(onRecyclerItemClickListener: OnRecyclerItemClickListener) {
        mOnRecyclerItemClickListener = onRecyclerItemClickListener
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: ArrayList<BaseFileBean>) {
        mFiles = data
        checkComputingAndExecute {
            notifyDataSetChanged()
        }
    }

    override fun getItemViewType(position: Int): Int {
        return mFiles[position].mFileWrapperViewType ?: 0
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SelectPathViewHolder {
        val v = LayoutInflater.from(mContext).inflate(SelectPathViewHolder.getLayoutId(), parent, false)
        return SelectPathViewHolder(v)
    }


    override fun onBindViewHolder(holder: SelectPathViewHolder, position: Int) {
        if (mContext.isActivityAndInvalid()) {
            Log.e(TAG, "onBindViewHolder: Activity is Destroyed!")
            return
        }
        if ((position < 0) || (position >= mFiles.size)) {
            Log.e(TAG, "onBindViewHolder position:$position is out range")
            return
        }
        val file = mFiles[position]
        if (mOnRecyclerItemClickListener != null) {
            holder.itemView.setOnClickListener {
                mOnRecyclerItemClickListener?.onItemClick(holder.itemView, position)
            }
        }
        holder.loadData(getItemKey(file, position), file, mChoiceMode, this)
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemKey(item: BaseFileBean, position: Int): Int? {
        val path = item.mData
        if (path.isNullOrEmpty()) {
            return null
        }
        return path.lowercase(Locale.getDefault()).hashCode()
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        setChoiceModeAnimFlag(flag)
    }
}