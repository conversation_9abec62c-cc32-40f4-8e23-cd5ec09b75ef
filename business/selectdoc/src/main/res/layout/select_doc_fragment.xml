<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackgroundWithCard"
    android:clipChildren="false"
    android:splitMotionEvents="false">

    <andes.oplus.documentsreader.core.common.view.viewpager.RTLViewPager
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="horizontal"/>

    <include layout="@layout/oplus_doc_appbar_with_tab_layout_secondary" />

    <include layout="@layout/bottom_select_btn_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <FrameLayout
        android:id="@+id/mask_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:clipChildren="false"
        android:clipToPadding="false">

        <View
            android:id="@+id/background_mask"
            style="@style/BackgroundMaskStyle" />
    </FrameLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>