/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2025/01/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9017070 2025/01/20      1.0     create file
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.group.util

import android.app.Activity
import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.home.view.group.entity.RecordingGroupData.pureCover2GroupColorInDb
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.db.GroupInfoManager
import com.soundrecorder.common.fileoperator.delete.OnFileDeleteListener
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

internal class RecordGroupEditViewModel(application: Application) :
    AndroidViewModel(application) {
    companion object {
        private const val TAG = "RecordGroupEditViewModel"
    }

    private val groupInfoManager = GroupInfoManager.getInstance(application)

    fun updateByDrag(modifiedGroupInfos: List<GroupInfo>, callback: (() -> Unit)? = null) {
        if (modifiedGroupInfos.isNotEmpty()) {
            viewModelScope.launch {
                val bSuccess = groupInfoManager.updateGroupSort(modifiedGroupInfos.toMutableList())
                if (bSuccess) {
                    callback?.invoke()
                }
            }
        }
    }

    /**
     * 编辑弹窗界面新建或修改组信息
     *
     * @return Pair<修改后的组信息,编辑信息代号>
     *
     */
    suspend fun insertOrUpdateFromEdit(
        groupInfo: GroupInfo?,
        name: String,
        cover: String
    ): Pair<GroupInfo?, Int> = withContext(Dispatchers.IO) {
        var pair = Pair<GroupInfo?, Int>(null, 0)
        if (groupInfo == null) {
            //新建组
            val createResult = groupInfoManager.createNewGroup(
                GroupInfo(
                    mGroupName = name,
                    mGroupColor = pureCover2GroupColorInDb(cover)
                )
            )
            when (createResult) {
                GroupInfoManager.RESULT_CODE_SUCCESS -> pair = Pair(null, GroupManageUtils.NUM_2_INSERT_SUCCESS)
                GroupInfoManager.RESULT_CODE_EXISTS -> pair = Pair(null, GroupManageUtils.NUM_1_UPDATE_OR_INSERT_EXIST)
                GroupInfoManager.RESULT_CODE_FAILED -> DebugUtil.e(TAG, "insertOrUpdateFromEdit() createNewGroup failed!")
            }
        } else {
            //更新已有组
            val updateResult = groupInfoManager.updateGroupNameAndColor(
                groupInfo,
                name,
                pureCover2GroupColorInDb(cover)
            )
            when (updateResult) {
                GroupInfoManager.RESULT_CODE_SUCCESS -> {
                    groupInfo.apply {
                        mGroupName = name
                        mGroupColor = pureCover2GroupColorInDb(cover)
                    }
                    pair = Pair(groupInfo, GroupManageUtils.NUM_3_UPDATE_SUCCESS)
                }
                GroupInfoManager.RESULT_CODE_EXISTS -> pair = Pair(null, GroupManageUtils.NUM_1_UPDATE_OR_INSERT_EXIST)
                GroupInfoManager.RESULT_CODE_FAILED -> DebugUtil.e(TAG, "insertOrUpdateFromEdit() createNewGroup failed!")
            }
        }
        return@withContext pair
    }

    fun deleteGroupInfos(activity: Activity?, groupInfos: MutableList<GroupInfo>, recordsDeleteListener: OnFileDeleteListener?) {
        viewModelScope.launch {
            activity?.let {
                GroupInfoManager.getInstance(it)
                    .deleteGroups(groupInfos, object : GroupInfoManager.DeleteGroupListener {
                        override fun onPreDeleteGroup() {
                        }

                        override fun onPostDeleteGroup(deletedGroupInfoList: List<GroupInfo>) {
                            GroupInfoManager.getInstance(it).handleRecordsAfterGroupsDeleted(
                                it,
                                deletedGroupInfoList,
                                recordsDeleteListener
                            )
                        }
                    })
            }
        }
    }
}