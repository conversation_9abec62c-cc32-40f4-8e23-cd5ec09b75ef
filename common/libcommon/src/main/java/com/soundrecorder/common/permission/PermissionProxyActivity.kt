/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PermissionProxyActivity.java
 Description:
 Version: 1.0
 Date: 2009-12-15
 Author: zhanghr
 -----------Revision History-----------
 <author> <date> <version> <desc>
 zhanghr 2009-12-15 create
 */

package com.soundrecorder.common.permission

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.result.contract.ActivityResultContracts.RequestMultiplePermissions
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.common.R
import com.soundrecorder.common.task.ActivityTaskUtils.isEmptyExcludeSelf

class PermissionProxyActivity : BaseActivity(), PermissionDialogUtils.PermissionDialogListener {
    companion object {
        const val EXTRA_PERMISSIONS = "EXTRA_PERMISSIONS"

        class RequestPermissionProxy : ActivityResultContract<Array<String>, Int>() {
            override fun createIntent(context: Context, input: Array<String>): Intent {
                return Intent(context, PermissionProxyActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                    putExtra(EXTRA_PERMISSIONS, input)
                }
            }

            override fun parseResult(resultCode: Int, intent: Intent?): Int {
                return resultCode
            }
        }
    }

    private val permissions: Array<String> by lazy {
        intent.getStringArrayExtra(EXTRA_PERMISSIONS) ?: arrayOf()
    }
    private var permissionDialog: Dialog? = null

    private val requestMultiplePermissions = registerForActivityResult(
        RequestMultiplePermissions()
    ) {
        PermissionUtils.putRequestPermissions(permissions)
        //it {android.permission.READ_MEDIA_IMAGES=true}
        //遍历所有，如果全部都是true，返回true，如果其中一个false，返回false
        if (it.all { kv -> kv.value }) {
            setResult(RESULT_OK)
        } else if (PermissionUtils.hasOnlyReadVisualUserSelectedPermission()) {
            setResult(RESULT_FIRST_USER)
        } else {
            setResult(RESULT_CANCELED)
        }
        finish()
    }

    private var hasClickOnOk = false
    override fun navigationBarColor(): Int {
        return R.color.navigation_bar_transparent_color
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        overridePendingTransition(0, 0)
        if (savedInstanceState == null) {
            if (!PermissionUtils.cannotRequestPermissions(this, permissions)) {
                requestMultiplePermissions.launch(permissions)
            }
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putBoolean("hasClickOnOk", hasClickOnOk)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        hasClickOnOk = savedInstanceState.getBoolean("hasClickOnOk", false)
    }

    override fun onResume() {
        super.onResume()
        if (!isFinishing) {
            if (PermissionUtils.hasReadImagesPermission()) {
                setResult(RESULT_OK)
                finish()
            } else {
                if (hasClickOnOk) {
                    setResult(RESULT_CANCELED)
                    finish()
                } else {
                    permissionDialog?.dismiss()
                    if (PermissionUtils.cannotRequestPermissions(this, permissions)) {
                        permissionDialog = PermissionDialogUtils.showPermissionsDialog(this, this, permissions)
                    }
                }
            }
        }
    }

    override fun onClick(alertType: Int, isOk: Boolean, permissions: ArrayList<String>?) {
        hasClickOnOk = isOk
        if (isOk) {
            PermissionUtils.goToAppSettingConfigurePermissions(this, permissions)
        } else {
            setResult(RESULT_CANCELED)
            finish()
        }
    }

    override fun onBackPress(alertType: Int) {
        setResult(RESULT_CANCELED)
        finish()
    }

    override fun finish() {
        overridePendingTransition(0, 0)
        if (isEmptyExcludeSelf()) {
            finishAndRemoveTask()
        } else {
            super.finish()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        permissionDialog?.dismiss()
    }
}