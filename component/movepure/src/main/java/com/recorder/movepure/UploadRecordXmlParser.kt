package com.recorder.movepure

import com.soundrecorder.common.databean.UploadRecord
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.base.utils.DebugUtil
import org.xmlpull.v1.XmlPullParser

import org.xmlpull.v1.XmlPullParserFactory

import java.io.StringReader
import java.lang.Exception

class UploadRecordXmlParser {

    private val TAG = "UploadRecordXmlParser"

    fun parse(recordString: String?): HashSet<UploadRecord>? {
        var record: UploadRecord? = null
        val list = HashSet<UploadRecord>()
        try {
            val factory = XmlPullParserFactory.newInstance()
            val parser = factory.newPullParser()
            parser.setInput(StringReader(recordString))
            var eventType = parser.eventType
            var tagName = ""
            while (eventType != XmlPullParser.END_DOCUMENT) {
                when (eventType) {
                    XmlPullParser.START_DOCUMENT -> {
                    }
                    XmlPullParser.START_TAG -> {
                        record = UploadRecord()
                        tagName = parser.name
                        if (tagName == DatabaseConstant.ROOT) {
                            val attrNum = parser.attributeCount
                            var i = 0
                            while (i < attrNum) {
                                val name = parser.getAttributeName(i)
                                val value = parser.getAttributeValue(i)
                                when (name) {
                                    DatabaseConstant.UploadColumn.ONLY_ID -> record.mOnlyId = value
                                    DatabaseConstant.UploadColumn.UPLOAD_URL -> record.mUrl = value
                                    DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_START -> record.mFileStartRange =
                                        value.toLong()
                                    DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_END -> record.mFileEndRange =
                                        value.toLong()
                                    DatabaseConstant.UploadColumn.UPLOAD_ETAG -> record.mETag = value
                                    DatabaseConstant.UploadColumn.UPLOAD_SEQ_NUM -> record.mSeqNumber = value.toInt()
                                    else -> {
                                    }
                                }
                                ++i
                            }
                        }
                    }
                    XmlPullParser.END_TAG -> if (parser.name == DatabaseConstant.ROOT && record != null) {
                        list.add(record)
                        DebugUtil.i(TAG, "parsed uploadRecord: " + record)
                    }
                    else -> {
                    }
                }
                eventType = parser.next()
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "UploadRecordXmlParser parse error", e)
        }
        return list
    }
}