<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginBottom="@dimen/dimen_4dp"
    android:animateLayoutChanges="true"
    android:gravity="center"
    android:orientation="vertical">

    <com.coui.appcompat.imageview.COUIRoundImageView
        android:id="@+id/about_app_icon"
        android:layout_width="@dimen/dimen_60dp"
        android:layout_height="@dimen/dimen_60dp"
        android:layout_marginTop="@dimen/open_setting_about_app_info_icon_margin_top"
        android:src="@drawable/ic_doc_viewer"
        app:couiBorderRadius="16dp"
        app:couiType="round"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/about_app_name"
        style="@style/COUIPreferenceTitleStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/open_setting_about_app_info_icon_inner_margin"
        android:alpha="0.85"
        android:text="@string/documents_app_name"
        android:textColor="@color/icon_black"
        android:textSize="@dimen/open_setting_about_app_info_name_text_size"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/about_app_icon" />

    <TextView
        android:id="@+id/about_app_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:clickable="true"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:paddingTop="@dimen/open_setting_about_app_info_icon_inner_half_margin"
        android:paddingBottom="@dimen/open_setting_about_app_info_icon_inner_half_margin"
        android:textColor="@color/coui_color_secondary_neutral"
        android:textSize="@dimen/open_setting_about_app_info_version_code_text_size"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/about_app_name" />

</androidx.constraintlayout.widget.ConstraintLayout>
