/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SampleDocumentManager
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/6/13 14:46
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  <EMAIL>        2023/6/26       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.core.sampledoc

import andes.oplus.documentsreader.aidepend.interfaces.IAIInterface
import andes.oplus.documentsreader.core.cloudconfig.interfaze.ICloudConfigManager
import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.PermissionUtils
import andes.oplus.documentsreader.core.common.PreferencesUtils
import andes.oplus.documentsreader.core.common.Utils
import andes.oplus.documentsreader.core.common.compat.FeatureCompat
import andes.oplus.documentsreader.core.common.compat.MediaScannerCompat
import andes.oplus.documentsreader.core.common.isInternetAvailable
import android.content.Context
import android.os.Environment
import android.provider.Settings
import androidx.annotation.VisibleForTesting
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.nio.charset.Charset
import java.util.zip.ZipInputStream

object SampleDocManager {

    private const val TAG = "SampleDocManager"
    private const val DOC_DIR_NAME = "My Documents"
    private const val DOC_DOWNLOAD_INFO = "sample_doc_download_info"
    private const val TAG_ALREADY_DOWNLOAD = "already_download"
    private const val ALREADY_DOWNLOAD_FILE_NAME = "oplus_doc_already_download_file_name"
    private const val ALREADY_DOWNLOAD_CONFIG_CODE = "oplus_doc_already_download_config_code"
    private const val CONFIG_NAME = "sample_doc"
    private const val VERSION_DIVISOR = 1000000
    private const val CHARSET_GBK = "GBK"
    private const val WRITE_BUFFER_SIZE = 1024
    private const val LANGUAGE_ZH = "zh"
    private const val LANGUAGE_EN = "en"
    private const val PARAM_KEY_VERSION = "C_doc_version"
    private const val PARAM_KEY_LANGUAGE = "C_doc_language"
    private const val PARAM_KEY_AI = "C_doc_ai"
    private const val LAST_REQUEST_TIME_STR = "#LastTime"
    private const val REQUEST_INTERVAL = 12 * 60 * 60 * 1000 // 两次请求间隔
    private const val DECOMPRESS_RETRY_COUNT = 2 // 解压缩重试次数
    private const val SPLIT = 2

    private const val HAS_AI = "ai"
    private const val HAS_NONE_AI = "ai_non"

    private val cloudConfigManager by lazy {
        Injector.injectFactory<ICloudConfigManager>()
    }

    private val aiDependency by lazy {
        Injector.injectFactory<IAIInterface>()
    }

    private val docVersion: Int by lazy {
        (ContextGetter.versionCode / VERSION_DIVISOR).toInt() // 获取版本号开始两位
    }

    @JvmStatic
    private fun getSupportAiTag(): String {
        return if (aiDependency?.checkAbilitySupport(
                ContextGetter.context,
                IAIInterface.SUMMARY_DETECT_NAME
            ) == true
        ) {
            HAS_AI
        } else {
            HAS_NONE_AI
        }
    }

    @JvmStatic
    fun updateSampleDoc(context: Context) {
        if (!PermissionUtils.hasStoragePermission()) {
            Log.w(TAG, failedMessage("", "no storage permission."))
            return
        }
        if (!isInternetAvailable(context)) {
            Log.w(TAG, failedMessage("", "network not available."))
            return
        }
        if (!checkSdCard()) { // 写权限
            Log.w(TAG, failedMessage("", "check write  permission."))
            return
        }
        val configCode = createConfigCode()
        if (!checkTime(configCode)) { // 未开始下载/下载失败时，再次进入下载流程最小间隔
            Log.w(TAG, failedMessage(configCode, "unable to retry for a short time."))
            return
        }
        if (checkAlreadyDownload(configCode)) {
            Log.w(TAG, failedMessage(configCode, "already download!"))
            return
        }
        if (checkExistFile(configCode)) {
            Log.w(TAG, failedMessage(configCode, "file already exist!"))
            return
        }
        requestDownloadSampleDoc(configCode)
        setLastRequestTime(configCode)
    }

    @VisibleForTesting
    @JvmStatic
    fun requestDownloadSampleDoc(configCode: String) {
        cloudConfigManager?.checkSampleDocConfig(configCode, getCustomParams()) { code, file ->
            onDownloadSuccess(code, file)
        }
    }

    @JvmStatic
    private fun failedMessage(code: String?, msg: String): String {
        return "getSampleDoc $code failed:$msg"
    }

    @JvmStatic
    private fun infoMessage(code: String?, msg: String): String {
        return "getSampleDoc $code :$msg"
    }

    @JvmStatic
    private fun getCustomParams(): HashMap<String, String> {
        val map = HashMap<String, String>()
        map[PARAM_KEY_VERSION] = "$docVersion"
        map[PARAM_KEY_LANGUAGE] = getDocLanguage()
        map[PARAM_KEY_AI] = getSupportAiTag()
        return map
    }

    @VisibleForTesting
    @JvmStatic
    fun checkTime(configCode: String): Boolean {
        val last = PreferencesUtils.getLong(DOC_DOWNLOAD_INFO, "$configCode$LAST_REQUEST_TIME_STR")
        return System.currentTimeMillis() - last >= REQUEST_INTERVAL
    }

    @VisibleForTesting
    @JvmStatic
    fun checkAlreadyDownload(configCode: String): Boolean {
        val alreadyDownloadConfig = PreferencesUtils.getString(DOC_DOWNLOAD_INFO, TAG_ALREADY_DOWNLOAD, "", getMultiProcess = true)
        Log.d(TAG, "checkAlreadyDownload alreadyDownloadConfig = $alreadyDownloadConfig, configCode = $configCode")
        return configCode == alreadyDownloadConfig
    }

    @VisibleForTesting
    @JvmStatic
    fun checkExistFile(configCode: String): Boolean {
        val lastConfigCode = Settings.Secure.getString(ContextGetter.context.contentResolver, ALREADY_DOWNLOAD_CONFIG_CODE)
        if (configCode != lastConfigCode) {
            Log.w(TAG, "checkExistFile configCode is different, just return false")
            return false
        }
        val filePath = Settings.Secure.getString(ContextGetter.context.contentResolver, ALREADY_DOWNLOAD_FILE_NAME)
        return filePath?.let { File(it).exists() } ?: false
    }

    @VisibleForTesting
    @JvmStatic
    fun setLastRequestTime(configCode: String) { // 记录上次请求数据时间
        PreferencesUtils.put(DOC_DOWNLOAD_INFO, "$configCode$LAST_REQUEST_TIME_STR", System.currentTimeMillis(), true)
    }

    @VisibleForTesting
    @JvmStatic
    fun onDownloadSuccess(configCode: String, downloadFile: File): Boolean {
        if (downloadFile.exists()) { // 匹配成功解压文件到/my documents
            val charset = getCharSet(configCode) // 解决中文乱码问题
            val destDir = getDocBaseDir()
            saveToSdCard(configCode, charset, destDir, downloadFile, DECOMPRESS_RETRY_COUNT)
            markAlreadyDownload(configCode)
            return true
        } else {
            Log.e(TAG, infoMessage(configCode, "get data is empty"))
            return false
        }
    }

    @JvmStatic
    private fun markAlreadyDownload(configCode: String) {
        PreferencesUtils.put(DOC_DOWNLOAD_INFO, key = TAG_ALREADY_DOWNLOAD, value = configCode, true)
    }

    @VisibleForTesting
    @JvmStatic
    fun createConfigCode(): String {
        return "${CONFIG_NAME}_${getDocLanguage()}_${docVersion}_${getSupportAiTag()}"
    }

    @VisibleForTesting
    @JvmStatic
    fun getDocLanguage(): String {
        Log.d(TAG, "getDocLanguage ${FeatureCompat.sIsExpRom}")
        return if (FeatureCompat.sIsExpRom) {
            LANGUAGE_EN
        } else {
            LANGUAGE_ZH
        }
    }

    @VisibleForTesting
    @JvmStatic
    fun checkSdCard(): Boolean {
        getDocBaseDir().let {
            if (!it.exists() || !it.canWrite()) {
                return false
            }
        }
        return true
    }

    @VisibleForTesting
    @JvmStatic
    fun getCharSet(code: String): Charset {
        return if (code.contains(LANGUAGE_ZH)) {
            Charset.forName(CHARSET_GBK)
        } else {
            Charsets.UTF_8
        } // 解决中文乱码问题
    }

    /**
     * 文档根目录
     */
    @JvmStatic
    private fun getDocBaseDir(): File {
        File("${Environment.getExternalStorageDirectory()}/$DOC_DIR_NAME/").let {
            if (!it.exists()) {
                it.mkdir()
            }
            return it
        }
    }

    @VisibleForTesting
    @JvmStatic
    fun saveToSdCard(
        configCode: String,
        charset: Charset,
        dest: File,
        downloadFile: File,
        reTryCount: Int
    ) {
        runCatching {
            Log.d(TAG, infoMessage(configCode, "saveToSdCard, ${dest.path}, ${downloadFile.path}"))
            ZipInputStream(FileInputStream(downloadFile), charset).use { zipFiles ->
                val zipEntry = zipFiles.nextEntry
                if (zipEntry == null) { // 不是zip压缩文件
                    Log.e(TAG, failedMessage(configCode, "only support zip file"))
                    return
                }
                val name = zipFiles.nextEntry?.name
                Log.d(TAG, "saveToSdCard name = $name")
                if (name.isNullOrEmpty()) {
                    Log.e(TAG, failedMessage(configCode, "file is null"))
                    return
                }
                val fileNames = name.split("/")
                if (fileNames.size < SPLIT) {
                    Log.e(TAG, failedMessage(configCode, "split error"))
                    return
                }
                val fileName = fileNames[1]
                Log.i(TAG, "fileName = $fileName")
                unCompressFile(configCode, dest.path, fileName, zipFiles)
                MediaScannerCompat.sendMediaScanner(dest.path, Utils.MEDIA_SCAN_DOWNLOAD)
                //记录当前下载文件的值
                markDownloadFile(dest.path, fileName, configCode)
            }
        }.onFailure {
            Log.e(TAG, failedMessage(configCode, "decompress error ${it.message} reTryCount:$reTryCount"))
            if (reTryCount > 0) {
                saveToSdCard(configCode, charset, getDocBaseDir(), downloadFile, reTryCount - 1)
            }
        }
    }

    private fun markDownloadFile(filePath: String, fileName: String, configCode: String) {
        val absolutePath = File(filePath, fileName).absolutePath
        Settings.Secure.putString(ContextGetter.context.contentResolver, ALREADY_DOWNLOAD_FILE_NAME, absolutePath)
        Settings.Secure.putString(ContextGetter.context.contentResolver, ALREADY_DOWNLOAD_CONFIG_CODE, configCode)
    }

    @JvmStatic
    private fun unCompressFile(
        configCode: String,
        saveDir: String,
        name: String,
        zipFiles: ZipInputStream
    ) {
        val buffer = ByteArray(WRITE_BUFFER_SIZE)
        var length: Int
        val destFile = File(saveDir, name)
        if (destFile.exists()) {
            destFile.delete()
        }
        FileOutputStream(destFile).use { stream ->
            while (zipFiles.read(buffer).also { length = it } > 0) {
                stream.write(buffer, 0, length)
            }
        }
        Log.d(TAG, infoMessage(configCode, "success path:$destFile file length:${destFile.length()}"))
    }
}