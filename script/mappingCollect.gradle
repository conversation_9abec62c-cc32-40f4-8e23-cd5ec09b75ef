final hasOBuildPlugin = project.plugins.hasPlugin("obuildplugin")
if (!hasOBuildPlugin) {
    final errMsg = "${project.path}: mappingCollect.gradle is depended on obuildplugin, " +
            "please apply obuildplugin and configure OBuildConfig first"
    System.err.println(errMsg)
    throw new GradleException(errMsg)
}

@SuppressWarnings("unused")
class MappingCollectExtensions {
    String publishTaskName = "publishAllPublicationsToReleaseRepository"
    String buildType = "release"
    String mappingRecordTag = "mapping"
    String mappingRecordFormat = "zip"
}

class MappingCollectPlugin implements Plugin<Project> {
    private static final EXTENSION_NAME = "mappingCollect"
    private static final COLLECT_MAPPING_TASK_NAME = "publishCollectMapping"
    private static final SNAPSHOT_SUFFIX = "-SNAPSHOT"

    @Override
    void apply(Project target) {
        final mappingExt = target.extensions.create(EXTENSION_NAME, MappingCollectExtensions)
        target.afterEvaluate {
            createCollectTask(target, mappingExt)
        }
    }

    private static void createCollectTask(Project target, MappingCollectExtensions mappingExt) {
        final srcPath = "outputs/mapping/${mappingExt.buildType}".replace("/", File.separator)
        final srcDir = new File(target.buildDir, srcPath)
        final dstPath = "libs/${target.name}".replace("/", File.separator)
        final dstDir = new File(target.rootProject.buildDir, dstPath)
        final dstZipName = getMappingZipFileName(mappingExt, target.OBuildConfig.sdkArtifactId, target.version)
        target.tasks.register(COLLECT_MAPPING_TASK_NAME, Zip) {
            it.dependsOn(mappingExt.publishTaskName)
            it.group = "publishing"
            it.setArchiveFileName(dstZipName)
            it.setDestinationDirectory(dstDir)
            it.from(srcDir.absolutePath)
        }
        target.tasks.named(mappingExt.publishTaskName).configure {
            it.finalizedBy(COLLECT_MAPPING_TASK_NAME)
        }
    }

    private static String getMappingZipFileName(
            MappingCollectExtensions mappingExt,
            String artifactId,
            String version
    ) {
        final mappingTag = mappingExt.mappingRecordTag
        final fileFormat = mappingExt.mappingRecordFormat
        def versionStr = version
        if (version.endsWith(SNAPSHOT_SUFFIX)) {
            versionStr = version.substring(0, version.length() - SNAPSHOT_SUFFIX.length())
        }
        return "${artifactId}-${versionStr}.${mappingTag}.${fileFormat}"
    }
}

project.plugins.apply(MappingCollectPlugin)