/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - BundleVariantUtilsTest.kt
 * Description:
 * The test cases for BundleVariantUtils
 *
 * Version: 1.0
 * Date: 2024-07-09
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-07-09   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.variant

import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class BundleVariantUtilsTest : Assert() {

    @Before
    fun setUp() {
        mockkStatic(BundleVariantInfo::class)
    }

    @After
    fun tearDown() {
        unmockkStatic(BundleVariantInfo::class)
    }

    @Test
    fun `should true when isBrandAvailable if current is ball`() {
        // Given
        every { BundleVariantInfo.brandType } returns BundleVariantUtils.BRAND_ALL

        // When
        val result = BundleVariantUtils.isBrandAvailable(BundleVariantUtils.BRAND_ONEPLUS)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should true when isBrandAvailable if same as current`() {
        // Given
        every { BundleVariantInfo.brandType } returns BundleVariantUtils.BRAND_REALME

        // When
        val result = BundleVariantUtils.isBrandAvailable(BundleVariantUtils.BRAND_REALME)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should false when isBrandAvailable if different with current`() {
        // Given
        every { BundleVariantInfo.brandType } returns BundleVariantUtils.BRAND_REALME

        // When
        val result = BundleVariantUtils.isBrandAvailable(BundleVariantUtils.BRAND_OPPO)

        // Then
        assertFalse(result)
    }

    @Test
    fun `should true when isProductAvailable if current is pall`() {
        // Given
        every { BundleVariantInfo.productType } returns BundleVariantUtils.PRODUCT_ALL

        // When
        val result = BundleVariantUtils.isProductAvailable(BundleVariantUtils.PRODUCT_FULL)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should true when isProductAvailable if compat tablet and is full`() {
        // Given
        every { BundleVariantInfo.productType } returns BundleVariantUtils.PRODUCT_FULL
        every { BundleVariantInfo.isTabletVariantSplit } returns false

        // When
        val result = BundleVariantUtils.isProductAvailable(BundleVariantUtils.PRODUCT_TABLET)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should false when isProductAvailable if spilt tablet but is full`() {
        // Given
        every { BundleVariantInfo.productType } returns BundleVariantUtils.PRODUCT_FULL
        every { BundleVariantInfo.isTabletVariantSplit } returns true

        // When
        val result = BundleVariantUtils.isProductAvailable(BundleVariantUtils.PRODUCT_TABLET)

        // Then
        assertFalse(result)
    }

    @Test
    fun `should true when isProductAvailable if same as current`() {
        // Given
        every { BundleVariantInfo.productType } returns BundleVariantUtils.PRODUCT_LIGHT
        every { BundleVariantInfo.isTabletVariantSplit } returns false

        // When
        val result = BundleVariantUtils.isProductAvailable(BundleVariantUtils.PRODUCT_LIGHT)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should false when isProductAvailable if different with current`() {
        // Given
        every { BundleVariantInfo.productType } returns BundleVariantUtils.PRODUCT_FULL
        every { BundleVariantInfo.isTabletVariantSplit } returns false

        // When
        val result = BundleVariantUtils.isProductAvailable(BundleVariantUtils.PRODUCT_LIGHT)

        // Then
        assertFalse(result)
    }

    @Test
    fun `should true when isRegionAvailable if current is regionall`() {
        // Given
        every { BundleVariantInfo.regionType } returns BundleVariantUtils.REGION_ALL

        // When
        val result = BundleVariantUtils.isRegionAvailable(BundleVariantUtils.REGION_ALL)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should true when isRegionAvailable if compat gdpr and is export`() {
        // Given
        every { BundleVariantInfo.regionType } returns BundleVariantUtils.REGION_EXPORT
        every { BundleVariantInfo.isGdprVariantSplit } returns false

        // When
        val result = BundleVariantUtils.isRegionAvailable(BundleVariantUtils.REGION_GDPR)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should false when isRegionAvailable if spilt gdpr but is export`() {
        // Given
        every { BundleVariantInfo.regionType } returns BundleVariantUtils.REGION_EXPORT
        every { BundleVariantInfo.isGdprVariantSplit } returns true

        // When
        val result = BundleVariantUtils.isRegionAvailable(BundleVariantUtils.REGION_GDPR)

        // Then
        assertFalse(result)
    }

    @Test
    fun `should true when isRegionAvailable if same as current`() {
        // Given
        every { BundleVariantInfo.regionType } returns BundleVariantUtils.REGION_DOMESTIC
        every { BundleVariantInfo.isGdprVariantSplit } returns false

        // When
        val result = BundleVariantUtils.isRegionAvailable(BundleVariantUtils.REGION_DOMESTIC)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should false when isRegionAvailable if different with current`() {
        // Given
        every { BundleVariantInfo.regionType } returns BundleVariantUtils.REGION_DOMESTIC
        every { BundleVariantInfo.isGdprVariantSplit } returns false

        // When
        val result = BundleVariantUtils.isRegionAvailable(BundleVariantUtils.REGION_EXPORT)

        // Then
        assertFalse(result)
    }

    @Test
    fun `should true when isRegionAvailable if check international and is export`() {
        // Given
        every { BundleVariantInfo.regionType } returns BundleVariantUtils.REGION_EXPORT
        every { BundleVariantInfo.isGdprVariantSplit } returns true

        // When
        val result = BundleVariantUtils.isRegionAvailable(BundleVariantUtils.REGION_INTERNATIONAL)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should true when isRegionAvailable if check international and is gdpr`() {
        // Given
        every { BundleVariantInfo.regionType } returns BundleVariantUtils.REGION_GDPR
        every { BundleVariantInfo.isGdprVariantSplit } returns true

        // When
        val result = BundleVariantUtils.isRegionAvailable(BundleVariantUtils.REGION_INTERNATIONAL)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should true when isRegionAvailable if check international but is domestic`() {
        // Given
        every { BundleVariantInfo.regionType } returns BundleVariantUtils.REGION_DOMESTIC
        every { BundleVariantInfo.isGdprVariantSplit } returns true

        // When
        val result = BundleVariantUtils.isRegionAvailable(BundleVariantUtils.REGION_INTERNATIONAL)

        // Then
        assertFalse(result)
    }
}