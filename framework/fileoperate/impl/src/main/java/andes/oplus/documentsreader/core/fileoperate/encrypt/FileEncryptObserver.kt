/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FilePrivacyObserver.kt
 ** Description: File upload to cloud
 ** Version: 1.0
 ** Date: 2020/3/5
 ** Author: LiHao(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package andes.oplus.documentsreader.core.fileoperate.encrypt

import andes.oplus.documentsreader.core.common.CustomToast
import andes.oplus.documentsreader.core.common.R
import andes.oplus.documentsreader.core.fileoperate.base.ACTION_DONE
import andes.oplus.documentsreader.core.fileoperate.base.ACTION_FAILED
import andes.oplus.documentsreader.core.fileoperate.base.BaseFileActionObserver
import android.content.Context
import android.view.ContextThemeWrapper

const val ERROR_ENCRYPT_PERMISSION = -2

open class FileEncryptObserver(context: ContextThemeWrapper) : BaseFileActionObserver(context) {

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        when (result.first) {
            ERROR_ENCRYPT_PERMISSION -> {
                dismissProgressDialog()
                CustomToast.showLong(R.string.unable_to_encrypted)
                onActionDone(false)
                return true
            }
            ACTION_DONE -> CustomToast.showLong(R.string.string_privacy_success_toast1)
            ACTION_FAILED -> {
                dismissProgressDialog()
                val failedCount = if (result.second is Int) result.second as Int else -1
                if (failedCount > 0) {
                    CustomToast.showLong(context.resources.getQuantityString(
                        R.plurals.encrypt_failed_hint_mix, failedCount, failedCount))
                }
            }
        }
        return false
    }
}