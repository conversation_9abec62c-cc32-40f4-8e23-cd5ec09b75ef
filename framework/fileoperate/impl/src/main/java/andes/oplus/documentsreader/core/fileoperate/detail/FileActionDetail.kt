/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File: FileActionDetail.kt
 ** Description: File/Directory detail function
 ** Version: 1.0
 ** Date: 2020/3/2
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package andes.oplus.documentsreader.core.fileoperate.detail

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.FileUtils.isNeedShowHiddenFile
import andes.oplus.documentsreader.core.common.JavaFileHelper
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.fileoperate.base.ACTION_CANCELLED
import andes.oplus.documentsreader.core.fileoperate.base.BaseFileAction
import andes.oplus.documentsreader.core.fileoperate.detail.FileDetailObserver.Companion.FILE_NOT_EXISTS
import andes.oplus.documentsreader.core.fileoperate.detail.FileDetailObserver.Companion.SHOW_DETAIL_DIALOG
import andes.oplus.documentsreader.core.fileoperate.detail.FileDetailObserver.FileDetailBean.CREATOR.TYPE_DIRECTORY
import andes.oplus.documentsreader.core.fileoperate.detail.FileDetailObserver.FileDetailBean.CREATOR.TYPE_FILE
import andes.oplus.documentsreader.core.fileoperate.detail.FileDetailObserver.FileDetailBean.CREATOR.TYPE_MULTI_FILES
import android.content.DialogInterface
import androidx.lifecycle.LifecycleOwner
import java.io.File

class FileActionDetail(
    lifecycle: LifecycleOwner,
    originFiles: List<BaseFileBean>,
    isRecycleBin: Boolean = false
) : BaseFileAction<FileDetailObserver>(lifecycle) {
    companion object {
        private const val TAG = "FileActionDetail"
    }

    private var mOperateFiles: ArrayList<BaseFileBean> = ArrayList(originFiles)
    private val mFileDetailBean = FileDetailObserver.FileDetailBean()

    init {
        mFileDetailBean.mDismissListener = DialogInterface.OnDismissListener {
            if (mFileDetailBean.mCalculating) {
                cancel()
            }
            mFileDetailBean.mDismissListener = null
        }
    }

    init {
        mFileDetailBean.mIsRecycleBin = isRecycleBin
    }

    override fun run(): Boolean {
        if (mOperateFiles.isEmpty()) {
            Log.d(TAG, "source file/directory is null or empty")
            return false
        }
        val count = mOperateFiles.size
        if (count == 1) {
            if (!JavaFileHelper.exists(mOperateFiles[0])) {
                notifyObserver(FILE_NOT_EXISTS)
                return false
            } else {
                mFileDetailBean.setSingleFile(mOperateFiles[0])
            }
        }
        notifyObserver(SHOW_DETAIL_DIALOG, mFileDetailBean)

        mFileDetailBean.type = if (count == 1) {
            if (mOperateFiles[0].mIsDirectory) TYPE_DIRECTORY else TYPE_FILE
        } else {
            TYPE_MULTI_FILES
        }
        for (file in mOperateFiles) {
            if (isCancelled()) {
                return false
            }
            if (file.mIsDirectory) {
                if (file.mData.isNullOrEmpty()) continue
                getDirectoryAndFileInfo(file.mData?.let { File(it) })
            } else {
                mFileDetailBean.mFileCount++
                mFileDetailBean.mTotalFileSize += file.mSize
            }
        }
        return true
    }

    private fun getDirectoryAndFileInfo(file: File?) {
        file ?: return
        if (!file.exists()) {
            return
        }
        //count folder size
        mFileDetailBean.mTotalFileSize += file.length()
        mFileDetailBean.mDirCount++
        val list = JavaFileHelper.listJavaFiles(file, !(isNeedShowHiddenFile() || mFileDetailBean.mIsRecycleBin)) ?: return
        list.forEach {
            if (isCancelled()) {
                return
            }
            if (it.isDirectory) {
                getDirectoryAndFileInfo(it)
            } else {
                mFileDetailBean.mFileCount++
                mFileDetailBean.mTotalFileSize += it.length()
            }
        }
    }

    override fun onCancelled() {
        mOperateFiles.clear()
        super.onCancelled()
    }

    override fun afterRun(result: Boolean) {
        mOperateFiles.clear()
        mFileDetailBean.mCalculating = false
        mFileDetailBean.mDismissListener = null
        if (result) {
            notifyObserver(SHOW_DETAIL_DIALOG, mFileDetailBean)
        } else {
            notifyObserver(ACTION_CANCELLED)
        }
    }

    override fun recycle() {
        mFileDetailBean.recycle()
        super.recycle()
    }
}