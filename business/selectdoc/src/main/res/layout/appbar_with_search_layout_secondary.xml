<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.COUIDividerAppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/appbar_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="?attr/couiColorBackgroundWithCard"
    app:elevation="0dp"
    app:hasDivider="false">

    <com.coui.appcompat.toolbar.COUIToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/oplus_doc_toolbar_height"
        app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

    <com.coui.appcompat.tablayout.COUITabLayout
        android:id="@+id/tab_layout"
        style="@style/COUISmallTabLayoutStyle"
        android:background="?attr/couiColorBackgroundWithCard"
        app:couiTabMinDivider="@dimen/tablayout_min_margin"
        app:couiTabMinMargin="@dimen/tablayout_small_min_divider" />

</com.google.android.material.appbar.COUIDividerAppBarLayout>