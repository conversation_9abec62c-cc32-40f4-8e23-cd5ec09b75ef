/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : OpenWebView.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/8/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/8/16       1      create
 ***********************************************************************/
package andes.oplus.documentsreader.preview.ui.open.webview

import andes.oplus.documentsreader.openfile.R
import andes.oplus.documentsreader.openfile.databinding.WebviewLayoutBinding
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.webkit.WebView
import androidx.constraintlayout.widget.ConstraintLayout

class OpenWebView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    ConstraintLayout(context, attrs, defStyleAttr) {

    private var binding: WebviewLayoutBinding? = null
    val webView: WebView? by lazy {
        binding?.webView
    }

    init {
        val root = View.inflate(context, R.layout.webview_layout, this)
        binding = WebviewLayoutBinding.bind(root)
    }

    fun showFooter(isShow: Boolean) {
        val visible = if (isShow) View.VISIBLE else View.GONE
        binding?.openFooter?.xmindFooterRoot?.visibility = visible
    }

    fun setText() {
        binding?.openFooter?.tvDes?.setText(R.string.doc_viewer_support_from_xmind)
    }
}