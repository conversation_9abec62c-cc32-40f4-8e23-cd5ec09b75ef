/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - AbstractSelectionAdapter.kt
 ** Description: AbstractSelectionAdapter
 ** Version: 1.0
 ** Date : 2020/06/04
 ** Author: Jiafei.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/06/04    1.0     create
 ****************************************************************/

package andes.oplus.documentsreader.core.common

import androidx.recyclerview.widget.RecyclerView

abstract class AbstractSelectionAdapter<V : RecyclerView.ViewHolder, K, T> : RecyclerView.Adapter<V>() {

    var mSelectionArray: MutableList<K> = mutableListOf()

    /**
     * 获取itemKey
     */
    abstract fun getItemKey(item: T, position: Int): Int?

    /**
     * 返回position对应itemkey
     */
    abstract fun getItemKeyByPosition(position: Int): Int?
}