/***********************************************************
 * * Copyright (C), 2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:FileActionInterface.kt
 * * Description:the interface for models to achieve
 * * Version:1.0
 * * Date :2020.1.9
 * * Author:liuzeming
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * * liuzeming    20200109   1.0       the interface for models to achieve
 ****************************************************************/
package andes.oplus.documentsreader.core.fileoperate.base

import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.interfaces.fileoprate.IFileOperateAction
import andes.oplus.documentsreader.core.common.thread.FileRunnable
import andes.oplus.documentsreader.core.common.thread.ThreadManager
import andes.oplus.documentsreader.core.common.thread.ThreadPriority
import andes.oplus.documentsreader.core.common.thread.ThreadType
import andes.oplus.documentsreader.core.common.Log
import android.annotation.SuppressLint
import android.content.Context
import android.os.Handler
import android.os.Looper
import androidx.annotation.MainThread
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

abstract class BaseFileAction<T : BaseFileActionObserver>(lifecycle: LifecycleOwner) : IFileOperateAction<T> {

    companion object {
        private const val TAG = "BaseFileAction"
    }

    private var mUIObserver: BaseFileActionObserver? = null
    private var mAsyncKey: String? = null
    private var mIsCancelled: AtomicBoolean? = AtomicBoolean(false)
    @Volatile
    private var mHandler: Handler? = object : Handler(Looper.getMainLooper()) {}
    private val mTempPairSecond = Any()
    private var mLifecycleOwner: WeakReference<LifecycleOwner>?
    private var mIsRunSuccess = true
    private var mDelayNotifyMap: ConcurrentHashMap<Any, Runnable>? = ConcurrentHashMap()
    protected var mContext: Context = ContextGetter.context
    private var mLifecycleEventObserver: LifecycleEventObserver? = null

    init {
        mLifecycleOwner = WeakReference(lifecycle)
    }

    /**
     * Default execute this action asynchronous, child class can override it and
     * invoke executeSync() to execute this action synchronous
     */
    override fun execute(uiObserver: T): BaseFileAction<T> {
        executeAsync(uiObserver)
        return this
    }

    /**
     * Run the task in a new thread
     */
    private fun executeAsync(uiObserver: T) {
        bindObserver(uiObserver)
        mHandler?.post {
            beforeRun()
            mAsyncKey = ThreadManager.sThreadManager.execute(FileRunnable({
                mIsRunSuccess = <EMAIL>()
                afterRun()
            }, TAG + <EMAIL>), ThreadType.NORMAL_THREAD, ThreadPriority.HIGH)
        }
    }

    /**
     * Do some init work before start task, run in UI thread
     */
    @MainThread
    open fun beforeRun() {}

    /**
     * Task running entry
     */
    abstract fun run(): Boolean

    open fun afterRun() {
        mHandler?.post { internalAfterRun() }
    }

    private fun bindObserver(uiObserver: T) {
        mUIObserver = uiObserver
        uiObserver.setFileActionObserverLifecycle(object : BaseFileActionObserver.FileActionObserverLifecycle {
            override fun onProgressDialogCancel() {
                cancel()
            }
        })
        if (mLifecycleEventObserver == null) {
            mLifecycleEventObserver = LifecycleEventObserver { _, event ->
                if (event == Lifecycle.Event.ON_DESTROY) {
                    mUIObserver?.recycle()
                    mUIObserver = null
                    cancel(true)
                }
            }
            mLifecycleOwner?.get()?.lifecycle?.addObserver(mLifecycleEventObserver!!)
        }
    }

    /**
     * Cancel the action, maybe invoke in different thread
     *
     * @param immediately Recycle the resource immediately, generally used when the activity destroyed
     */
    fun cancel(immediately: Boolean = false) {
        Log.d(TAG, "cancel, immediately=$immediately")
        if ((mIsCancelled != null) && !isCancelled()) {
            Log.d(TAG, "cancel, set state to true")
            mIsCancelled!!.set(true)
            mHandler?.removeCallbacksAndMessages(null)
            onCancelled()
            if (immediately) {
                internalRecycle(true)
            }
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    open fun onCancelled() {
        notifyObserver(ACTION_CANCELLED)
    }

    fun isCancelled(): Boolean = (mIsCancelled?.get() == true) || Thread.currentThread().isInterrupted

    fun notifyObserver(what: Any, obj: Any? = null, delayInMill: Long = 0) {
        // First cancel the same 'what' notification where in the delayed map
        cancelNotifyObserver(what)
        if ((mHandler == null) || (isCancelled() && (what != ACTION_CANCELLED))) {
            return
        }
        if (delayInMill > 0) {
            val newRunnable = Runnable {
                toNotifyObserver(what, obj)
            }
            mDelayNotifyMap?.set(what, newRunnable)
            mHandler?.postDelayed(newRunnable, delayInMill)
        } else {
            // If invoked in main thread, run it directly
            if (Thread.currentThread() == Looper.getMainLooper().thread) {
                toNotifyObserver(what, obj)
            } else {
                mHandler?.post {
                    toNotifyObserver(what, obj)
                }
            }
        }
    }

    private fun toNotifyObserver(what: Any, obj: Any?) {
        mDelayNotifyMap?.remove(what)
        if ((isCancelled() && (what != ACTION_CANCELLED))) {
            mHandler?.removeCallbacksAndMessages(null)
            return
        }
        mUIObserver?.getObserver()?.onChanged(Pair(what, obj ?: mTempPairSecond))
    }

    @SuppressLint("NewApi")
    fun cancelNotifyObserver(what: Any) {
        mDelayNotifyMap?.remove(what)?.let {
            Log.d(TAG, "cancelNotifyObserver: removeCallBacks it = $it")
            // Must check the runnable exist first, or else all pending runnable will be removed
            if (mHandler?.hasCallbacks(it) == true) {
                mHandler?.removeCallbacks(it)
            }
        }
    }

    @MainThread
    private fun internalAfterRun() {
        if (mIsCancelled?.get() == false) {
            mIsCancelled = null
            afterRun(mIsRunSuccess)
        }
        internalRecycle()
    }

    private fun internalRecycle(immediately: Boolean = false) {
        fun realRecycle() {
            Log.d(TAG, "internalRecycle")
            recycle()
            mDelayNotifyMap?.clear()
            mDelayNotifyMap = null
            mUIObserver?.recycle()
            mUIObserver = null
            mHandler?.removeCallbacksAndMessages(null)
            mHandler = null
            mLifecycleEventObserver?.let {
                mLifecycleOwner?.get()?.lifecycle?.removeObserver(it)
            }
            mLifecycleEventObserver = null
            mLifecycleOwner?.clear()
            mLifecycleOwner = null
        }

        if (immediately) {
            realRecycle()
        } else {
            // Post recycle task to the end of the message queue
            mHandler?.post { realRecycle() }
        }
    }

    protected open fun recycle() {}

    /**
     * Do some finishing work before start task, run in UI thread
     */
    @MainThread
    open fun afterRun(result: Boolean) {}

    /**
     * 运行在子线程中
     */
    open fun runOnAsyncThread(run: Runnable) {
        if (Thread.currentThread() == Looper.getMainLooper().thread) {
            ThreadManager.sThreadManager.execute(FileRunnable(run, TAG))
        } else {
            run.run()
        }
    }

    /**
     * re-show dialog when ui configuration changed
     */
    fun reShowDialog() {
        notifyObserver(ACTION_SHOW_RENAME_DIALOG_RE)
    }

    override fun hideDialog() {
        Log.d(TAG, "hideDialog")
    }
}