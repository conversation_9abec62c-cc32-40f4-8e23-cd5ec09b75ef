/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDIForFileService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.clouddrive.di

import andes.oplus.documentsreader.clouddrive.CloudDriveApi
import andes.oplus.documentsreader.clouddrive.interfaces.ICloudDrive
import com.oplus.assistantscreen.mic.interfaces.KoinModuleMarker
import com.oplus.assistantscreen.mic.interfaces.MarkField
import org.koin.dsl.module

@KoinModuleMarker
class AutoDIForCloudDrive {

    @MarkField
    val cloudDriveModule = module {
        single<ICloudDrive>(createdAtStart = true) {
            CloudDriveApi()
        }
    }
}