/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - RecycleBinFragment.kt
 * Description:
 *     The fragment for recycle bin page
 *
 * Version: 1.0
 * Date: 2024-07-12
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-07-12   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.recycle.ui

import andes.oplus.documentsreader.core.common.CategoryHelper
import andes.oplus.documentsreader.core.common.FileManagerDefaultItemAnimator
import andes.oplus.documentsreader.core.common.FileUtils.hasDrmFile
import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.IntentUtils
import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.LoadingController
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.Utils
import andes.oplus.documentsreader.core.common.controller.SortPopupController
import andes.oplus.documentsreader.core.common.interfaces.ICategoryGlobalSearchApi
import andes.oplus.documentsreader.core.common.interfaces.OnBackPressed
import andes.oplus.documentsreader.core.common.loader.LoaderViewModel
import andes.oplus.documentsreader.core.common.selection.BaseUiModel
import andes.oplus.documentsreader.core.common.selection.RecyclerSelectionVMFragment
import andes.oplus.documentsreader.core.common.sort.SelectItemListener
import andes.oplus.documentsreader.core.common.sort.SortEntryView
import andes.oplus.documentsreader.core.common.sort.SortRecordModeFactory
import andes.oplus.documentsreader.core.common.uiconfig.type.IUIConfig
import andes.oplus.documentsreader.core.common.utils.ToolbarUtil
import andes.oplus.documentsreader.core.common.view.NavigationView
import andes.oplus.documentsreader.core.common.view.NormalDecoration
import andes.oplus.documentsreader.recycle.navigation.RecycleNormalNavigation
import andes.oplus.documentsreader.recycle.navigation.RecycleSelectNavigation
import andes.oplus.documentsreader.recycle.operation.RecycleFileOperatorController
import andes.oplus.documentsreader.recycle.utils.RecycleBinFeatures
import andes.oplus.documentsreader.recycle.utils.RecycleBinUtils
import andes.oplus.documentsreader.recycle.viewmodel.BaseRecycleFileBean
import andes.oplus.documentsreader.recycle.viewmodel.RecycleBinViewModel
import andes.oplus.documentsreader.setting.ISetting
import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.view.menu.ActionMenuItem
import androidx.core.view.doOnLayout
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.documentsreader.emptyfile.IFileEmptyController
import com.documentsreader.navigation.INavigationController
import com.documentsreader.navigation.NavigationInterface
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.dropdrag.recycleview.ItemDetailsLookup

internal class RecycleBinFragment : RecyclerSelectionVMFragment<RecycleBinViewModel>(),
    OnBackPressed, NavigationBarView.OnItemSelectedListener, NavigationInterface {

    private val sortPopupController by lazy { SortPopupController(lifecycle) }
    private val fileEmptyController by lazy {
        Injector.injectFactory<IFileEmptyController>()?.apply {
            setLifecycle(lifecycle)
        }
    }
    private val setting by lazy {
        Injector.injectFactory<ISetting>()
    }

    var pageTitle: String? = null
    private var topOverdueTips: View? = null
    private var sortEntryView: SortEntryView? = null
    private var pageAdapter: RecycleBinAdapter? = null
    private var layoutManager: LinearLayoutManager? = null
    private var fileOperateController: RecycleFileOperatorController? = null
    private var loadingController: LoadingController? = null
    private var tempSortType = -1
    private var isChildDisplay = false
    private var needLoadData = false
    private var isShowNaviTool = false
    private var lastIsEmpty: Boolean? = null
    private var previousItemId: Int? = -1

    private val selectNavController by lazy {
        Injector.injectFactory<INavigationController>(RecycleSelectNavigation())
    }
    private val normalNavController by lazy {
        Injector.injectFactory<INavigationController>(RecycleNormalNavigation())
    }
    private var normalNavigationView: NavigationView? = null
    private var selectNavigationView: NavigationView? = null

    override fun createViewModel(): RecycleBinViewModel =
        ViewModelProvider(this)[TAG, RecycleBinViewModel::class.java].also {
            fileOperateController = RecycleFileOperatorController(lifecycle, it)
        }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        previousItemId = savedInstanceState?.getInt(RecycleBinUtils.PREVIOUS_ITEM_ID)
        if (previousItemId != null && previousItemId != -1) {
            activity?.let { fileOperateController?.reCreateDialog(it) }
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        previousItemId?.let { outState.putInt(RecycleBinUtils.PREVIOUS_ITEM_ID, it) }
    }

    override fun getLayoutResId(): Int =
        andes.oplus.documentsreader.recycle.R.layout.oplus_doc_fragment_recycle_bin

    override fun onAttach(context: Context) {
        super.onAttach(context)
        withBaseVMActivity {
            pageAdapter = RecycleBinAdapter(it, <EMAIL>).apply {
                setHasStableIds(true)
            }
        }
        val bundle = arguments ?: return
        isChildDisplay = bundle.getBoolean(KtConstants.IS_HALF_SCREEN, false)
        needLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
        pageTitle = IntentUtils.getTitleFromBundle(context, bundle)
        showNavigationBar = arguments?.getBoolean(KtConstants.SHOW_SYS_NAVIGATION_BAR) ?: false
        systemBarInsetsBottom = arguments?.getInt(KtConstants.SYSTEM_BAR_INSERT_BOTTOM) ?: 0
    }

    @SuppressLint("RestrictedApi")
    override fun initView(view: View) {
        rootView = view.findViewById(andes.oplus.documentsreader.recycle.R.id.coordinator_layout)
        appBarLayout = view.findViewById(andes.oplus.documentsreader.recycle.R.id.appBarLayout)
        toolbar = view.findViewById(andes.oplus.documentsreader.recycle.R.id.toolbar)
        fragmentRecyclerView =
            view.findViewById(andes.oplus.documentsreader.recycle.R.id.recycler_view)
        fragmentFastScroller =
            view.findViewById(andes.oplus.documentsreader.recycle.R.id.fastScroller)
        initToolbar()
        sortEntryView = view.findViewById(andes.oplus.documentsreader.recycle.R.id.sort_entry_view)
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getRecycleBinKey())
        sortEntryView?.setClickSortListener {
            val menu = ActionMenuItem(
                view.context,
                0,
                andes.oplus.documentsreader.recycle.R.id.actionbar_sort,
                0,
                0,
                ""
            )
            onMenuItemSelected(menu)
        }
        topOverdueTips =
            view.findViewById(andes.oplus.documentsreader.recycle.R.id.toolbar_tips_count_view)
        val normalNavigation =
            view.findViewById<NavigationView>(andes.oplus.documentsreader.recycle.R.id.navigation_def)
        normalNavigationView = normalNavigation
        normalNavController?.run {
            initNavigationView(normalNavigation, this@RecycleBinFragment)
            baseVMActivity?.apply { hideNavigationDirectly(this) }
        }
        val selectNavigation =
            view.findViewById<NavigationView>(andes.oplus.documentsreader.recycle.R.id.navigation_tool)
        selectNavigationView = selectNavigation
        selectNavController?.run {
            initNavigationView(selectNavigation, this@RecycleBinFragment)
            baseVMActivity?.apply { hideNavigationDirectly(this) }
        }
        onWindowInsetsCallback(showNavigationBar, systemBarInsetsBottom)
    }

    private fun showTopOverdueTips(toShow: Boolean) {
        if (!RecycleBinFeatures.SHOW_OVERDUE_TIPS_IN_TOOLBAR) {
            topOverdueTips?.visibility = View.GONE
            return
        }
        topOverdueTips?.visibility = if (toShow) View.VISIBLE else View.GONE
    }

    private fun initToolbar() {
        toolbar?.apply {
            title = pageTitle
            titleMarginStart = 0
            isTitleCenterStyle = false
            inflateMenu(andes.oplus.documentsreader.recycle.R.menu.oplus_doc_recycle_bin_file_list_option)
            setToolbarMenuVisible(this, !isChildDisplay)
        }
        runOnBaseVMActivity {
            setSupportActionBar(toolbar)
            supportActionBar?.let {
                it.setDisplayHomeAsUpEnabled(!isChildDisplay)
                it.setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
            this.updateNavigationBarColor(this, window, showNavigationBar, true)
        }
        rootView?.doOnLayout {
            updateChildPadding()
        }
        rootView?.apply {
            setPadding(
                paddingLeft,
                COUIPanelMultiWindowUtils.getStatusBarHeight(context), paddingRight, paddingBottom
            )
        }
    }

    private fun setToolbarMenuVisible(toolbar: COUIToolbar, visible: Boolean) {
        toolbar.menu.findItem(andes.oplus.documentsreader.recycle.R.id.actionbar_search)?.isVisible =
            visible
        toolbar.menu.findItem(andes.oplus.documentsreader.recycle.R.id.action_setting)?.isVisible =
            visible
        toolbar.menu.findItem(andes.oplus.documentsreader.recycle.R.id.actionbar_edit)?.let {
            it.isVisible = getViewModel()?.hasRealFile() ?: false
            if (visible) {
                it.setIcon(null)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            } else {
                it.setIcon(andes.oplus.documentsreader.core.common.R.drawable.color_tool_menu_ic_edit)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
            }
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(
            andes.oplus.documentsreader.recycle.R.menu.oplus_doc_recycle_bin_file_list_option,
            menu
        )
        toolbar?.let {
            setToolbarMenuVisible(it, !isChildDisplay)
        }
    }

    @Suppress("DEPRECATION")
    fun onMenuItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return when (item.itemId) {
            android.R.id.home -> {
                activity?.onBackPressed()
                true
            }
            andes.oplus.documentsreader.recycle.R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(activity, CategoryHelper.CATEGORY_RECYCLE_BIN)
                hideNavigationWithoutAnim()
                true
            }
            andes.oplus.documentsreader.recycle.R.id.actionbar_edit -> {
                getViewModel()?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                true
            }
            andes.oplus.documentsreader.recycle.R.id.actionbar_sort -> {
                onActionBarSortSelected()
                true
            }
            andes.oplus.documentsreader.recycle.R.id.action_setting -> {
                setting?.startSettingActivity(requireActivity())
                true
            }
            andes.oplus.documentsreader.recycle.R.id.action_select_all -> {
                getViewModel()?.onClickSelectAllOrDeSelectAll(pageAdapter)
                true
            }
            andes.oplus.documentsreader.recycle.R.id.action_select_cancel -> {
                getViewModel().takeIf {
                    it?.modeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE
                }?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                true
            }
            else -> false
        }
    }

    private fun hideNavigationWithoutAnim() {
        baseVMActivity?.apply {
            selectNavController?.hideNavigationDirectly(this)
            normalNavController?.hideNavigationDirectly(this)
        }
    }

    private fun onActionBarSortSelected() = withBaseVMActivity {
        sortPopupController.showSortPopUp(
            it,
            tempSortType,
            andes.oplus.documentsreader.recycle.R.id.sort_entry_anchor,
            SortRecordModeFactory.getRecycleBinKey(),
            object : SelectItemListener {
                override fun onDismiss() {
                    sortEntryView?.rotateArrow()
                }

                override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                    if (flag) {
                        sortEntryView?.setSortOrder(sortMode, isDesc)
                        getViewModel()?.sortReload()
                    }
                }
            })
    }

    override fun initData(savedInstanceState: Bundle?) {
        withFragmentRecyclerView {
            layoutManager = LinearLayoutManager(context)
            it.isNestedScrollingEnabled = true
            it.layoutManager = layoutManager
            val animator = FileManagerDefaultItemAnimator()
            it.itemAnimator = animator
            animator.supportsChangeAnimations = false
            pageAdapter?.let { adapter ->
                it.adapter = adapter
            }
            it.isForceDarkAllowed = false
            it.addItemDecoration(NormalDecoration(context))
        }
        appBarLayout?.post(Runnable {
            if (!isAdded) {
                return@Runnable
            }
            withFragmentRecyclerView {
                it.clipToPadding = false
                val paddingBottom = if (it.paddingBottom == 0) {
                    requireContext().resources
                        .getDimensionPixelSize(andes.oplus.documentsreader.recycle.R.dimen.ftp_text_margin_bottom)
                } else {
                    it.paddingBottom
                }
                it.updatePadding(bottom = paddingBottom)
            }
        })
        if (needLoadData) {
            onResumeLoadData()
        }
        if (actionCheckPermission().not()) {
            sortEntryView?.setFileCount(0)
        }
    }

    override fun updateChildPadding(left: Int, right: Int, offset: Int) {
        super.updateChildPadding(left, right, offset)
        sortEntryView?.updatePadding(left = left, right = right + offset)
        topOverdueTips?.updatePadding(left = left)
        pageAdapter?.updatePaddingHorizontal(left, right)
    }

    override fun startObserve(): Unit = withBaseVMActivity {
        runOnFragmentRecyclerView {
            post {
                setListModelChangeObserve()
                setUIDataChangeObserve()
                startObserveLoadState()
            }
        }
    }

    private fun setListModelChangeObserve() {
        getViewModel()?.modeState?.listModel?.observe(this) { value ->
            onListModelChange(value)
        }
    }

    private fun onListModelChange(value: Int) {
        if (getViewModel()?.modeState?.initState == false) {
            toolbar?.setTag(andes.oplus.documentsreader.recycle.R.id.toolbar_animation_id, true)
            return
        }
        Log.d(TAG, "onListModelChange: listModel=$value")
        if (value == KtConstants.LIST_SELECTED_MODE) {
            pageAdapter?.let {
                it.setSelectEnabled(true)
                it.setChoiceModeAnimFlag(true)
            }
            toolbar?.let {
                changeActionModeAnim(it, {
                    initToolbarWithEditMode(it)
                    refreshSelectToolbar(it)
                })
                it.setTag(andes.oplus.documentsreader.recycle.R.id.toolbar_animation_id, true)
            }
            updateNavigationTool()
            getViewModel()?.setNavigateItemAble(this)
        } else {
            isShowNaviTool = !isShowNaviTool
            pageAdapter?.let {
                it.setSelectEnabled(false)
                it.setChoiceModeAnimFlag(false)
            }
            toolbar?.let {
                changeActionModeAnim(
                    it,
                    { initToolbarNormalMode(it, true) },
                    (it.getTag(andes.oplus.documentsreader.recycle.R.id.toolbar_animation_id) == true)
                )
                it.setTag(andes.oplus.documentsreader.recycle.R.id.toolbar_animation_id, true)
            }
            updateNavigationTool()
            getViewModel()?.loadData()
        }
    }

    private fun initToolbarWithEditMode(toolbar: COUIToolbar) {
        runOnBaseVMActivity {
            supportActionBar?.setDisplayHomeAsUpEnabled(false)
        }
        toolbar.apply {
            menu.clear()
            isTitleCenterStyle = true
            inflateMenu(andes.oplus.documentsreader.recycle.R.menu.oplus_doc_menu_edit_mode)
        }
    }

    private fun setUIDataChangeObserve() {
        getViewModel()?.uiState?.observe(this) { uiModel ->
            onUiModelChanged(uiModel)
        }
    }

    private fun onUiModelChanged(uiModel: BaseUiModel<BaseRecycleFileBean>) {
        val listMode = uiModel.stateModel.listModel.value
        val listSize = uiModel.fileList.size
        val selectedSize = uiModel.selectedList.size
        val keyword = uiModel.keyWord
        val stateMsg = "listMode=$listMode, listSize=$listSize, selectedSize=$selectedSize, " +
                "keyword=$keyword, isShowNaviTool=$isShowNaviTool, lastIsEmpty=$lastIsEmpty"
        Log.d(TAG, "onUiModelChanged: $stateMsg")
        sortEntryView?.setFileCount(getViewModel()?.getRealFileSize() ?: 0)
        if (listMode == KtConstants.LIST_SELECTED_MODE) {
            toolbar?.let {
                refreshSelectToolbar(it)
            }
            pageAdapter?.setData(uiModel.fileList, uiModel.selectedList)
        } else {
            val currentIsEmpty = uiModel.fileList.isEmpty()
            val emptyChanged = (currentIsEmpty != lastIsEmpty)
            lastIsEmpty = currentIsEmpty
            if (currentIsEmpty) {
                sortEntryView?.visibility = View.GONE
            } else {
                sortEntryView?.visibility = View.VISIBLE
            }
            if (currentIsEmpty && (!isShowNaviTool || emptyChanged)) {
                showEmptyView()
            } else if (uiModel.fileList.isNotEmpty() && (isShowNaviTool || emptyChanged)) {
                fileEmptyController?.hideFileEmptyView()
                showTopOverdueTips(true)
            }
            if (!isShowNaviTool || emptyChanged) {
                withBaseVMActivity {
                    it.updateNavigationBarColor(it, it.window, it.showNavigationBar, true)
                }
                updateNavigationTool()
                if (currentIsEmpty) {
                    isShowNaviTool = true
                }
            }
            toolbar?.let {
                initToolbarNormalMode(it, false)
            }
            pageAdapter?.run {
                keyWord = uiModel.keyWord
                setData(uiModel.fileList, uiModel.selectedList)
            }
        }
    }

    private fun initToolbarNormalMode(toolbar: COUIToolbar, needToInit: Boolean) {
        withBaseVMActivity {
            it.supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(!isChildDisplay)
                setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }
        if (needToInit) {
            toolbar.menu.clear()
            toolbar.isTitleCenterStyle = false
            toolbar.title = pageTitle
            toolbar.inflateMenu(andes.oplus.documentsreader.recycle.R.menu.oplus_doc_recycle_bin_file_list_option)
        }
        updateEditAndSortMenu(toolbar)
        setToolbarMenuVisible(toolbar, !isChildDisplay)
    }

    private fun updateEditAndSortMenu(toolbar: COUIToolbar) {
        val edit = toolbar.menu.findItem(andes.oplus.documentsreader.recycle.R.id.actionbar_edit)
        edit?.isVisible = (getViewModel()?.uiState?.value?.fileList?.isNotEmpty() == true)
    }

    private fun updateNavigationTool() {
        isShowNaviTool =
            if (getViewModel()?.modeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                showNavigation(KtConstants.LIST_SELECTED_MODE)
                true
            } else {
                if (getViewModel()?.uiState?.value?.fileList.isNullOrEmpty()) {
                    hideNavigationWithoutAnim()
                    false
                } else {
                    showNavigation(KtConstants.LIST_NORMAL_MODE)
                    true
                }
            }
        updateRecycleListPadding(isShowNaviTool)
    }

    private fun showNavigation(uiMode: Int) {
        Log.d(TAG, "showNavigation: uiMode=$uiMode")
        if (uiMode == KtConstants.LIST_NORMAL_MODE) {
            baseVMActivity?.apply { selectNavController?.hideNavigation(this) }
            normalNavController?.run {
                normalNavigationView?.let { initNavigationView(it, this@RecycleBinFragment) }
                baseVMActivity?.apply { showNavigation(this) }
                setNavigateItemAble(isEnable = true, mHasDrm = false)
            }
        } else {
            baseVMActivity?.apply { normalNavController?.hideNavigation(this) }
            selectNavController?.run {
                selectNavigationView?.let { initNavigationView(it, this@RecycleBinFragment) }
                baseVMActivity?.apply { showNavigation(this) }
            }
        }
    }

    override fun showNavigation() {}

    override fun hideNavigation() {
        baseVMActivity?.apply {
            selectNavController?.hideNavigation(this)
            normalNavController?.hideNavigation(this)
        }
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean) {
        Log.d(TAG, "setNavigateItemEnable isEnable = $isEnable mHasDrm = $mHasDrm")
        selectNavController?.setNavigateItemAble(isEnable, mHasDrm)
    }

    private fun updateRecycleListPadding(isShowNaviTool: Boolean = false) {
        val paddingBottomRes = if (isShowNaviTool) {
            activity?.resources?.getDimensionPixelSize(andes.oplus.documentsreader.recycle.R.dimen.oplus_doc_recycle_bin_list_bottom_padding)
        } else {
            activity?.resources?.getDimensionPixelSize(andes.oplus.documentsreader.recycle.R.dimen.ftp_text_margin_bottom)
        }
        runOnFragmentRecyclerView {
            val newPaddingBottom = paddingBottomRes ?: paddingBottom
            setPadding(paddingLeft, paddingTop, paddingRight, newPaddingBottom)
            runOnFragmentFastScroller {
                trackMarginBottom = newPaddingBottom
            }
        }
    }

    private fun startObserveLoadState() {
        val act = activity ?: return
        loadingController = LoadingController(act, this).apply {
            observe(getViewModel()?.dataLoadState, rootView) {
                (getViewModel()?.getRealFileSize() ?: 0) > 0
            }
        }
    }

    private fun refreshSelectToolbar(toolbar: COUIToolbar) {
        val uiState = getViewModel()?.uiState?.value
        val checkedCount = uiState?.selectedList?.size ?: 0
        val isSelectAll = (getViewModel()?.getRealFileSize() == uiState?.selectedList?.size)
        ToolbarUtil.updateToolbarTitle(toolbar, checkedCount, isSelectAll)
        setNavigateItemAble(
            uiState?.selectedList?.isNotEmpty() ?: false,
            hasDrmFile(getViewModel()?.getSelectedRecycleFiles())
        )
    }

    override fun onResume() {
        super.onResume()
        val isEmpty = getViewModel()?.uiState?.value?.fileList != null
                && getViewModel()?.uiState?.value?.fileList?.size == 0
        Log.d(TAG, "onResume: isEmpty=$isEmpty")
        lastIsEmpty = isEmpty
        if (isEmpty) {
            loadingController?.dismissLoading(true)
            showEmptyView()
        } else {
            showTopOverdueTips(true)
        }
        fileOperateController?.triggerClearObsolete(requireContext())
    }

    override fun onResumeLoadData() {
        if (!isAdded) {
            return
        }
        checkShowPermissionEmpty()
        getViewModel()?.initLoader(LoaderViewModel.getLoaderController(this))
        val bundle = arguments ?: return
        if (bundle.getBoolean(KtConstants.P_RESET_TOOLBAR, false)) {
            withBaseVMActivity {
                it.setSupportActionBar(toolbar)
                it.supportActionBar?.apply {
                    setDisplayHomeAsUpEnabled(!isChildDisplay)
                    setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
                }
            }
            bundle.putBoolean(KtConstants.P_RESET_TOOLBAR, false)
        }
    }

    override fun onItemClick(item: ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        item.takeIf {
            it.canLongPressOrClick()
        }?.selectionKey?.let {
            getViewModel()?.uiState?.value?.keyMap?.get(it)
        }?.let { file ->
            withBaseVMActivity { fileOperateController?.onFileClick(it, file, e) }
        }
        Log.w(TAG, "onItemClick: ${item.selectionKey}")
        return true
    }

    override fun pressBack(): Boolean {
        return getViewModel()?.pressBack() ?: false
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        activity?.let {
            previousItemId = item.itemId
            fileOperateController?.onNavigationItemSelected(it, item)
        }
        return false
    }

    override fun onDragStart(e: MotionEvent): Boolean {
        return false
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        withBaseVMActivity {
            fileEmptyController?.changeEmptyFileIcon()
            if (getViewModel()?.uiState?.value?.fileList?.isEmpty() == true) {
                showEmptyView()
            } else {
                showTopOverdueTips(true)
            }
        }
        sortPopupController.hideSortPopUp()
        fileOperateController?.onConfigurationChanged(newConfig)
    }

    private fun showEmptyView() {
        Log.d(TAG, "showEmptyView")
        showTopOverdueTips(false)
        withBaseVMActivity {
            val fragmentRootView = rootView ?: return@withBaseVMActivity
            fileEmptyController?.showFileEmptyView(
                it,
                fragmentRootView,
                null,
                andes.oplus.documentsreader.recycle.R.string.empty_file
            )
            fileEmptyController?.setFileEmptyTitle(andes.oplus.documentsreader.recycle.R.string.empty_file)
            val summaryTitle = requireContext()
                .getString(andes.oplus.documentsreader.recycle.R.string.recycle_file_retain_time_tips)
            fileEmptyController?.setEmptySummaryVisibilityAndContent(View.VISIBLE, summaryTitle)
            it.updateNavigationBarColor(it, it.window, it.showNavigationBar, false)
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        getViewModel()?.run {
            if (modeState.listModel.value == KtConstants.LIST_SELECTED_MODE) {
                changeListMode(KtConstants.LIST_SELECTED_MODE)
            }
        }
        selectNavController?.invalidType()
    }

   override fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        arguments?.putBoolean(KtConstants.IS_HALF_SCREEN, isChildDisplay)
        toolbar?.let {
            setToolbarMenuVisible(it, !isChildDisplay)
        }
        withBaseVMActivity {
            it.setSupportActionBar(toolbar)
            it.supportActionBar?.apply {
                if (getViewModel()?.isInSelectMode() == true) {
                    setDisplayHomeAsUpEnabled(false)
                    setHomeAsUpIndicator(com.support.snackbar.R.drawable.coui_menu_ic_cancel)
                } else {
                    setDisplayHomeAsUpEnabled(!isChildDisplay)
                }
            }
        }
    }

    override fun backToTop() {
        runOnFragmentRecyclerView { fastSmoothScrollToTop() }
    }

    override fun onWindowInsetsCallback(showNavigationBar: Boolean, systemBarInsetsBottom: Int) {
        super.onWindowInsetsCallback(showNavigationBar, systemBarInsetsBottom)
        baseVMActivity?.setNavigationHeight(normalNavigationView, systemBarInsetsBottom)
        baseVMActivity?.setNavigationHeight(selectNavigationView, systemBarInsetsBottom)
    }

    override fun getDropCategoryType(): Int {
        return CategoryHelper.CATEGORY_RECYCLE_BIN
    }

    private companion object {
        private const val TAG = "RecycleBinFragment"
    }
}