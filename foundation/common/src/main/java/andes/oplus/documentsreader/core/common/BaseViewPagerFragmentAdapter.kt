/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.base.BaseViewPagerFragmentAdapter
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/5/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.core.common

import androidx.annotation.NonNull
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.viewpager.adapter.COUIFragmentStateAdapter

abstract class BaseViewPagerFragmentAdapter : COUIFragmentStateAdapter {

    constructor(@NonNull fragmentActivity: FragmentActivity) : super(fragmentActivity)
    constructor(@NonNull fragment: Fragment) : super(fragment)

    private var mRecycleView: RecyclerView? = null

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        mRecycleView = recyclerView
    }

    fun scrollToPosition(position: Int) {
        if (position in 0 until itemCount) {
            mRecycleView?.scrollToPosition(position)
        }
    }
}