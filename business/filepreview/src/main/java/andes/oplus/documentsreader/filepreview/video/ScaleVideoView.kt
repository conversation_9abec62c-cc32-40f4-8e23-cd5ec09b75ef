/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: VideoPreviewFragment
 * * Description: the  video view can move and scale
 * * Version: 1.0
 * * Date : 2024/3/12
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2024/3/12       1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.filepreview.video

import andes.oplus.documentsreader.core.common.uiconfig.UIConfigMonitor
import andes.oplus.documentsreader.core.common.uiconfig.type.IUIConfig
import andes.oplus.documentsreader.core.common.uiconfig.type.ScreenOrientationConfig
import andes.oplus.documentsreader.filepreview.bean.PointF
import andes.oplus.documentsreader.filepreview.image.ScaleLayout
import andes.oplus.documentsreader.filepreview.image.IScaleListener
import android.content.Context
import android.media.MediaPlayer
import android.util.AttributeSet

class ScaleVideoView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    VideoPlayerView(context, attrs, defStyleAttr), UIConfigMonitor.OnUIConfigChangeListener,
    IScaleListener.OnClickListener, IScaleListener.OnScaleToListener {
    private var scaleLayout: ScaleLayout? = null
    override fun onSingleClick() {
        chgProgressVisible()
    }

    /**
     * player 准备完成，是否重新播放
     */
    override fun onPrepared(mp: MediaPlayer?) {
        super.onPrepared(mp)
        mp?.let {
            initScaleLayout()
        }
    }

    override fun scaleTo(targetScale: Float, position: PointF) {
        chgProgressVisible(false) // 缩放时，隐藏进度栏
    }

    private fun initScaleLayout() {
        scaleLayout = parent as? ScaleLayout
        updateMediaSize()
        scaleLayout?.initManager()
        scaleLayout?.setOnSingleClickListener(this)
        scaleLayout?.setOnScaleToListener(this)
        scaleLayout?.forceFullScreenDisable()
        scaleLayout?.getScaleManager()?.scaleByScreenCenter() // 设置缩放时按屏幕中心缩放
        scaleLayout?.getScaleManager()?.scaleFillParent() // 双击放大时，充满屏幕
    }

    private fun updateMediaSize(orientation: Int? = null) {
        mediaPlayer.apply {
            scaleLayout?.initMediaSize(videoWidth, videoHeight, orientation)
        }
    }

    override fun onPause() {
        super.onPause()
        scaleLayout?.onPause()
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        var alreadyLayout = false
        configList.forEach {
            if (it is ScreenOrientationConfig) {
                updateMediaSize(it.orientation)
                alreadyLayout = true
            }
        }
        if (alreadyLayout.not()) {
            updateMediaSize()
        }
        scaleLayout?.onUIConfigChanged()
    }
}