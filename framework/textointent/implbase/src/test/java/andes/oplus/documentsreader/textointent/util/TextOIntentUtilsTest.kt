/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: VideoPreviewFragment
 * * Description: the TextOIntent util test
 * * Version: 1.0
 * * Date : 2024/3/20
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2024/3/20      1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.textointent.util

import andes.oplus.documentsreader.textointent.implbase.R
import andes.oplus.documentsreader.textointent.impl.utils.TextOIntentUtils
import android.content.Context
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.json.JSONObject
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.koin.core.context.GlobalContext
import org.koin.dsl.koinApplication
import org.koin.dsl.module

class TextOIntentUtilsTest {
    private val koinApp = koinApplication {
        modules(module {
        })
    }

    @Before
    fun setUp() {
        mockkStatic(TextOIntentUtils::class)
        GlobalContext.startKoin(koinApp)
    }

    @After
    fun tearDown() {
        unmockkStatic(TextOIntentUtils::class)
        GlobalContext.stopKoin()
    }

    @Test
    fun `should return true when call isSupportType if check code is success`() {
        val supportTypes = arrayOf(
            "PHONE",
            "EMAIL",
            "WEB_URL",
            "BOOK",
            "ADDRESS",
            "FIGHT_NUM",
            "TRAIN_NUM",
            "EXPRESS_NUM",
            "SCHEDULE_COMPAT"
        )
        val unSupportTypes = arrayOf(
            "UNKNOWN",
            "UNIT_WEIGHT",
            "UNIT_LENGTH",
            "UNIT_AREA",
            "UNIT_VOLUME",
            "UNIT_TEMPERATURE",
            "UNIT_POWER",
            "UNIT_SPEED",
            "UNIT_PRESSURE",
            "CURRENCY_RATE",
            "SCHEDULE",
            "NER",
            "ID_NUM",
            "BANK_NUM",
            "TRANSLATE",
            "TOKEN"
        )
        supportTypes.forEach {
            Assert.assertEquals(true, TextOIntentUtils.isSupportType(it))
        }
        unSupportTypes.forEach {
            Assert.assertEquals(false, TextOIntentUtils.isSupportType(it))
        }
    }

    @Test
    fun `should return true when call getSupportMenuItemNames if check code is success`() {
        Assert.assertEquals(
            R.array.text_preview_phone_list,
            TextOIntentUtils.getSupportMenuItemNames("PHONE")
        )
        Assert.assertEquals(
            R.array.text_preview_email_list,
            TextOIntentUtils.getSupportMenuItemNames("EMAIL")
        )
        Assert.assertEquals(
            R.array.text_preview_web_url_list,
            TextOIntentUtils.getSupportMenuItemNames("WEB_URL")
        )
        Assert.assertEquals(-1, TextOIntentUtils.getSupportMenuItemNames("UNKNOWN"))
    }

    @Test
    fun `should return true when call startByIntelligentScene if check code is success`() {
        val context = mockk<Context>()
        mockkConstructor(JSONObject::class)
        every { context.packageName } returns "andes.oplus.documentsreader"
        justRun { context.startActivity(any()) }
        val unKnown = andes.oplus.documentsreader.textointent.interfaces.TextIntentLinkSpan(
            49,
            59,
            "UNKNOWN",
            ""
        )
        Assert.assertEquals(false, TextOIntentUtils.startByIntelligentScene(context, unKnown))
        val phone = andes.oplus.documentsreader.textointent.interfaces.TextIntentLinkSpan(
            49,
            59,
            "PHONE",
            ""
        )
        val email = andes.oplus.documentsreader.textointent.interfaces.TextIntentLinkSpan(
            49,
            59,
            "EMAIL",
            ""
        )
        val web = andes.oplus.documentsreader.textointent.interfaces.TextIntentLinkSpan(
            49,
            59,
            "WEB_URL",
            ""
        )
        val express = andes.oplus.documentsreader.textointent.interfaces.TextIntentLinkSpan(
            49,
            59,
            "EXPRESS_NUM",
            ""
        )
        val schedule = andes.oplus.documentsreader.textointent.interfaces.TextIntentLinkSpan(
            49,
            59,
            "SCHEDULE_COMPAT",
            ""
        )
        val beans = arrayOf(phone, email, web, express, schedule)
        beans.forEach {
            Assert.assertEquals(true, TextOIntentUtils.startByIntelligentScene(context, it))
        }
    }

    @Test
    fun `should return true when call saveOIntentText if check code is success`() {
        val context = mockk<Context>()
        every { context.packageName } returns "andes.oplus.documentsreader"
        justRun { context.startActivity(any()) }
        val phone = andes.oplus.documentsreader.textointent.interfaces.TextIntentLinkSpan(
            49,
            59,
            "PHONE",
            ""
        )
        val email = andes.oplus.documentsreader.textointent.interfaces.TextIntentLinkSpan(
            49,
            59,
            "EMAIL",
            ""
        )
        val web = andes.oplus.documentsreader.textointent.interfaces.TextIntentLinkSpan(
            49,
            59,
            "WEB_URL",
            ""
        )
        val express = andes.oplus.documentsreader.textointent.interfaces.TextIntentLinkSpan(
            49,
            59,
            "EXPRESS_NUM",
            ""
        )
        Assert.assertEquals(false, TextOIntentUtils.saveOIntentText(context, express))
        val beans = arrayOf(phone, email, web)
        beans.forEach {
            Assert.assertEquals(true, TextOIntentUtils.saveOIntentText(context, it))
        }
    }

    @Test
    fun `should return true when call TextIntentLinkSpan if check code is success`() {
        val intentJson = mockk<JSONObject>()
        every { intentJson.optString(any()) } returns "TextIntentLinkSpan"
        every { intentJson.optInt(any()) } returns 1
        every { intentJson.optLong(any()) } returns 1
        val express =
            andes.oplus.documentsreader.textointent.interfaces.TextIntentLinkSpan(intentJson)
        Assert.assertEquals("TextIntentLinkSpan", express.sourceText)
    }

    @Test
    fun `should return true when call sendSms if check code is success`() {
        val context = mockk<Context>()
        justRun { context.startActivity(any()) }
        val phoneNumber = "12382259365"
        Assert.assertEquals(true, TextOIntentUtils.sendSms(context, ""))
        Assert.assertEquals(true, TextOIntentUtils.sendSms(context, phoneNumber))
    }

    @Test
    fun `should return false when call startActivity error if check code is success`() {
        val context = mockk<Context>()
        every { context.startActivity(any()) } throws NullPointerException()
        Assert.assertEquals(false, TextOIntentUtils.sendSms(context, ""))
    }
}