<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="BaseTheme" parent="@style/Theme.COUI.Main">
        <item name="android:textAlignment">gravity</item>
        <item name="android:textDirection">locale</item>
        <item name="viewInflaterClass">com.coui.appcompat.theme.COUIComponentsViewInflater</item>
        <item name="android:forceDarkAllowed">true</item>
        <item name="android:isLightTheme">true</item>
        <item name="enableFollowSystemForceDarkRank">true</item>
        <item name="android:fastScrollThumbDrawable">@drawable/icon_fast_scroll</item>
        <item name="android:fastScrollTrackDrawable">@null</item>
        <item name="android:windowBackground">?attr/couiColorBackgroundWithCard</item>
    </style>

    <style name="AppBaseTheme" parent="BaseTheme">
        <!--
            Theme customizations available in newer API levels can go in
            res/values-vXX/styles.xml, while customizations related to
            backward-compatibility can go here.
        -->
    </style>

    <!-- Application theme. -->
    <style name="AppTheme" parent="AppBaseTheme">
        <item name="preferenceTheme">@style/PreferenceThemeOverlay.COUITheme.WithToolBar</item>
    </style>

    <!-- Application no title theme. -->
    <style name="AppNoTitleTheme" parent="AppTheme">
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
    </style>

    <!-- 支持卡片样式的Preference列表theme, 非卡片式列表，请勿使用 -->
    <style name="AppNoTitleTheme.PreferenceFragment">
        <!-- 必须设置 -->
        <item name="android:windowBackground">?attr/couiColorBackgroundWithCard</item>
        <item name="android:navigationBarColor">?attr/couiColorBackgroundWithCard</item>
        <!-- 必须设置 -->
    </style>

    <style name="AppNoTitleTheme.ActionMode" parent="AppNoTitleTheme">
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowActionModeOverlay">true</item>
        <item name="android:actionModeStyle">@style/actionModeStyle</item>
    </style>

    <style name="actionModeStyle">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:height">@dimen/action_mode_style_height</item>
    </style>

    <style name="AppNoTitleThemeTranslucent" parent="AppNoTitleTheme">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="windowPreviewType">0</item>
        <item name="android:windowDisablePreview">true</item>
    </style>

    <style name="SettingAppBarStyle" parent="Widget.COUI.Toolbar">
        <item name="android:background">?attr/couiColorBackgroundWithCard</item>
    </style>

    <style name="FileOpenActivityThemeWithDwg" parent="AppTheme">
        <item name="android:isLightTheme">false</item>
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="enableFollowSystemForceDarkRank">false</item>
        <item name="android:windowBackground">@color/coui_color_background_dark</item>
        <item name="couiColorBackgroundWithCard">@color/coui_color_background_dark</item>
        <item name="couiColorBackground">@color/coui_color_background_dark</item>
        <!-- COUINavigationView -->
        <item name="couiNavigationViewStyle">@style/Widget.COUI.COUINavigationView.Dark</item>
        <item name="couiNavigationToolColorNormal">@color/coui_color_primary_neutral_dark</item>
    </style>

    <style name="LegalTextStyle">
        <item name="android:paddingTop">24dp</item>
        <item name="android:layout_marginStart">24dp</item>
        <item name="android:layout_marginEnd">24dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">#FF737373</item>
        <item name="android:lineSpacingMultiplier">1.4</item>
    </style>

    <style name="CommonAppBarStyle" parent="Widget.COUI.Toolbar">
        <item name="android:background">?attr/couiColorBackgroundWithCard</item>
    </style>
</resources>