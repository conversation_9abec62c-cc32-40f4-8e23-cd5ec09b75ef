/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : LoadUtils.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/6/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/6/20       1      create
 ***********************************************************************/
package andes.oplus.documentsreader.preview.utils

import andes.oplus.documentsreader.openfile.R
import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.CustomToast
import andes.oplus.documentsreader.core.common.FileUtils
import andes.oplus.documentsreader.core.common.KtConstants.YOZO_MAIN_PAGE_CLASS_NAME
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.MimeType
import andes.oplus.documentsreader.core.common.MimeTypeHelper
import android.content.ActivityNotFoundException
import android.content.ClipData
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.Intent.FLAG_ACTIVITY_CLEAR_TASK
import android.content.Intent.FLAG_ACTIVITY_NEW_TASK
import android.net.Uri
import android.os.RemoteException
import androidx.core.content.FileProvider
import java.io.File

object ShareUtils {

    private const val TAG = "ShareUtils"

    private const val SEND_ENTRANCE = "send_entrance"

    val FILE_TYPE_MAP  = mapOf(
        ".pdf" to "PDF",
        ".key" to "Key",
        ".pptx" to "PowerPoint",
        ".ppt" to "PowerPoint",
        ".png" to "PNG",
        ".xmind" to "Xmind",
        ".docx" to "Word",
        ".doc" to "Word",
        ".pages" to "Pages",
        ".numbers" to "Numbers",
        ".xlsx" to "Excel",
        ".vsdx" to "Visio"
    )

    @JvmStatic
    fun shareExistFile(context: Context, file: File): Uri {
        val mimeType = MimeTypeHelper.getMimeTypeFromPath(file.path)
        val fileUri = getUriForFile(file)
        share(context, fileUri, mimeType)
        return fileUri
    }

    @JvmStatic
    fun shareOriginalFile(context: Context, filePath: String?): Uri? {
        filePath?.let { path ->
            val file = File(path)
            val exist = file.exists()
            Log.d(TAG, "shareOriginalFile, file is exist:$exist file:${FileUtils.getName(path)}")
            if (exist) {
                val mimeType = MimeTypeHelper.getMimeTypeFromPath(file.path)
                val fileUri = getUriForFile(file)
                share(context, fileUri, mimeType)
                return fileUri
            }
        }
        return null
    }

    @JvmStatic
    private fun getUriForFile(file: File): Uri {
        return FileProvider.getUriForFile(ContextGetter.context, ContextGetter.context.packageName, file)
    }

    @JvmStatic
    private fun share(context: Context, uri: Uri, mimeType: String?) {
        val intent = Intent()
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        intent.action = Intent.ACTION_SEND
        intent.putExtra(Intent.EXTRA_STREAM, uri)
        if (mimeType == MimeType.MIMETYPE_IMAGE) {
            intent.clipData = ClipData.newUri(context.contentResolver, null, uri)
        }
        intent.putExtra(SEND_ENTRANCE, ContextGetter.context.packageName)
        intent.type = mimeType
        try {
            intent.addFlags(FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(FLAG_ACTIVITY_CLEAR_TASK)
            val chooseIntent = Intent.createChooser(
                intent,
                context.getText(andes.oplus.documentsreader.filepreviewbase.R.string.doc_viewer_share_menu_title)
            ).apply {
                val targets = ArrayList<ComponentName>()
                targets.add(ComponentName(context.packageName, YOZO_MAIN_PAGE_CLASS_NAME))
                putExtra(Intent.EXTRA_EXCLUDE_COMPONENTS, targets.toTypedArray())
            }
            context.startActivity(chooseIntent)
            Log.d(TAG, "share startActivity")
        } catch (ex: ActivityNotFoundException) {
            Log.e(TAG, "share exception", ex)
        } catch (exception: RemoteException) {
            CustomToast.showShort(R.string.toast_share_failture)
        }
    }

    @JvmStatic
    fun genSharePath(filePath: String, ext: String, fillName: String? = null): String {
        val fullPath = FileUtils.getFullPath(filePath)
        val baseName = FileUtils.getBaseName(filePath)
        val fileName = if (fillName.isNullOrEmpty()) {
            baseName
        } else {
            "$baseName$fillName"
        }
        val newPath = "$fullPath$fileName$ext"
        Log.d(TAG, "genSharePath baseName:$baseName fullPath:$fullPath newPath:$newPath")
        return newPath
    }
}