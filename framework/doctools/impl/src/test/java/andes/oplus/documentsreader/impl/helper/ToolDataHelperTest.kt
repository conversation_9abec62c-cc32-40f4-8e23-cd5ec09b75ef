/***********************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File:   ToolDataHelperTest
 * * Description: unit test
 * * Version:1.0
 * * Date :2025/02/26
 * * Author:renjiahao
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * *   renjiahao                    1.0           create
 ****************************************************************/
package andes.oplus.documentsreader.impl.helper

import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.stringResource
import andes.oplus.documentsreader.doctools.impl.R
import andes.oplus.documentsreader.impl.bean.ToolConfigBean
import andes.oplus.documentsreader.impl.bean.ToolContentBean
import andes.oplus.documentsreader.impl.bean.ToolTitleBean
import andes.oplus.documentsreader.impl.enumer.EnumId
import andes.oplus.documentsreader.impl.enumer.EnumLayout
import andes.oplus.documentsreader.impl.enumer.EnumPriority
import andes.oplus.documentsreader.impl.enumer.EnumWay
import android.content.Context
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.mockito.junit.MockitoJUnitRunner
import kotlin.test.assertEquals

@RunWith(MockitoJUnitRunner::class)
class ToolDataHelperTest {

    private lateinit var layoutEnum: EnumLayout

    @Before
    fun setUp() {
        mockkObject(ContextGetter)
        val context: Context = mockk(relaxed = true)
        every { ContextGetter.context }.returns(context)
        layoutEnum = EnumLayout.LAYOUT_CONTENT_LARGE
    }

    @Test
    fun should_return_tool_bean_when_get_doc_tool_title() {
        val bean = ToolDataHelper.dataDocToolTitle(true)
        assertEquals(
            ToolTitleBean(
                EnumPriority.PRIORITY_DOC_TOOL,
                EnumLayout.LAYOUT_TITLE,
                EnumId.ID_DOC_TOOL,
                0,
                stringResource(R.string.document_tool),
                true
            ),
            bean
        )
    }

    @Test
    fun should_return_tool_bean_when_get_panel_tool_title() {
        val bean = ToolDataHelper.dataPanelToolTitle()
        assertEquals(
            ToolTitleBean(
                EnumPriority.PRIORITY_FAST_CREATE,
                EnumLayout.LAYOUT_TITLE_PANEL,
                EnumId.ID_FAST_CREATE,
                0,
                stringResource(R.string.fast_create_title),
                false
            ),
            bean
        )
    }

    @Test
    fun should_return_tool_bean_when_get_ai_tool_title() {
        val bean = ToolDataHelper.dataAiToolTitle()
        assertEquals(
            ToolTitleBean(
                EnumPriority.PRIORITY_AI_TOOL,
                EnumLayout.LAYOUT_TITLE,
                EnumId.ID_AI_TOOL,
                R.drawable.icon_ai_tool,
                stringResource(R.string.ai_tool),
                false
            ),
            bean
        )
    }

    @Test
    fun should_return_tool_bean_when_get_doc_deal_title() {
        val bean = ToolDataHelper.dataDocDealTitle()
        assertEquals(
            ToolTitleBean(
                EnumPriority.PRIORITY_DOC_DEAL,
                EnumLayout.LAYOUT_TITLE,
                EnumId.ID_DOC_DEAL,
                0,
                stringResource(R.string.document_deal),
                false
            ),
            bean
        )
    }

    @Test
    fun should_return_tool_bean_when_get_format_convert_title() {
        val bean = ToolDataHelper.dataFormatConvertTitle()
        assertEquals(
            ToolTitleBean(
                EnumPriority.PRIORITY_FORMAT_CONVERT,
                EnumLayout.LAYOUT_TITLE,
                EnumId.ID_FORMAT_CONVERT,
                0,
                stringResource(R.string.format_convert),
                false
            ),
            bean
        )
    }

    @Test
    fun should_return_tool_bean_when_get_doc_summary() {
        val bean = ToolDataHelper.dataDocSummary(EnumLayout.LAYOUT_CONTENT_LARGE)
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_DOC_SUMMARY,
                layoutEnum,
                EnumId.ID_DOC_SUMMARY,
                R.drawable.icon_doc_summary,
                stringResource(R.string.document_summary),
                stringResource(R.string.document_summary_sub),
                R.color.color_document_summary
            ),
            bean
        )
    }

    @Test
    fun should_return_tool_bean_when_get_doc_translation() {
        val bean = ToolDataHelper.dataDocTranslation(EnumLayout.LAYOUT_CONTENT_LARGE)
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_DOC_TRANSLATION,
                layoutEnum,
                EnumId.ID_DOC_TRANSLATION,
                R.drawable.icon_doc_translation,
                stringResource(R.string.document_translation),
                stringResource(R.string.document_translation_sub),
                R.color.color_document_translation
            ),
            bean
        )
    }

    @Test
    fun should_return_tool_bean_when_get_extract_chart() {
        val bean = ToolDataHelper.dataExtractChart(EnumWay.WAY_HOME)
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_EXTRACT_CHART_PRIMARY,
                layoutEnum,
                EnumId.ID_EXTRACT_CHART,
                R.drawable.icon_extract_chart,
                stringResource(R.string.extract_chart),
                stringResource(R.string.extract_chart_sub),
                R.color.color_extract_chart
            ),
            bean
        )
    }

    @Test
    fun should_return_tool_bean_when_get_extract_formula() {
        val bean = ToolDataHelper.dataExtractFormula(EnumWay.WAY_HOME)
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_EXTRACT_FORMULA_PRIMARY,
                layoutEnum,
                EnumId.ID_EXTRACT_FORMULA,
                R.drawable.icon_extract_formula,
                stringResource(R.string.extract_formula),
                stringResource(R.string.extract_formula_sub),
                R.color.color_extract_formula
            ),
            bean
        )
    }

    @Test
    fun should_return_tool_bean_when_get_scan_doc() {
        val bean = ToolDataHelper.dataScanDoc()
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_SCAN_DOC,
                layoutEnum,
                EnumId.ID_SCAN_DOC,
                R.drawable.icon_scan_document,
                stringResource(R.string.scan_document),
                stringResource(R.string.scan_document_sub),
                R.color.color_scan_document
            ),
            bean
        )
    }

    @Test
    fun should_return_tool_bean_when_get_test_paper() {
        val bean = ToolDataHelper.dataTestPaper()
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_TEST_PAPER,
                layoutEnum,
                EnumId.ID_TEST_PAPER,
                R.drawable.icon_test_paper,
                stringResource(R.string.restore_test_paper),
                stringResource(R.string.restore_test_paper_sub),
                R.color.color_restore_test_paper
            ),
            bean
        )
    }

    @Test
    fun should_return_pdf_tool_bean_when_hasOfficeToPdf_and_hasIworkToPdf() {
        val bean = ToolDataHelper.dataConvertToPdf(
            toolConfigBean = ToolConfigBean(
                hasOfficeToPdf = true,
                hasIworkToPdf = true
            )
        )
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_CONVERT_TO_PDF,
                layoutEnum,
                EnumId.ID_CONVERT_TO_PDF,
                R.drawable.icon_convert_to_pdf,
                stringResource(R.string.convert_to_pdf2),
                stringResource(R.string.convert_to_pdf2_sub),
                R.color.color_convert_to_pdf
            ),
            bean
        )
    }

    @Test
    fun should_return_pdf_tool_bean_when_hasOfficeToPdf() {
        val bean = ToolDataHelper.dataConvertToPdf(
            toolConfigBean = ToolConfigBean(
                hasOfficeToPdf = true,
            )
        )
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_CONVERT_TO_PDF,
                layoutEnum,
                EnumId.ID_CONVERT_TO_PDF,
                R.drawable.icon_convert_to_pdf,
                stringResource(R.string.convert_to_pdf2),
                stringResource(R.string.convert_to_pdf2_sub_export),
                R.color.color_convert_to_pdf
            ),
            bean
        )
    }

    @Test
    fun should_return_pdf_tool_bean_when_all_false() {
        val bean = ToolDataHelper.dataConvertToPdf(
            toolConfigBean = ToolConfigBean()
        )
        val subTitle = stringResource(R.string.convert_to_ppt_sub).replace(
            stringResource(R.string.type_keynote),
            stringResource(R.string.type_iWork)
        )
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_CONVERT_TO_PDF,
                layoutEnum,
                EnumId.ID_CONVERT_TO_PDF,
                R.drawable.icon_convert_to_pdf,
                stringResource(R.string.convert_to_pdf2),
                subTitle,
                R.color.color_convert_to_pdf
            ),
            bean
        )
    }

    @Test
    fun should_return_word_tool_bean_when_hasPagesToWord_and_hasPdfToWord() {
        val bean = ToolDataHelper.dataConvertToWord(
            toolConfigBean = ToolConfigBean(
                hasPagesToWord = true,
                hasPdfToWord = true
            )
        )
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_CONVERT_TO_WORD,
                layoutEnum,
                EnumId.ID_CONVERT_TO_WORD,
                R.drawable.icon_convert_to_word,
                stringResource(R.string.convert_to_word),
                stringResource(R.string.convert_to_word_sub),
                R.color.color_convert_to_word
            ),
            bean
        )
    }

    @Test
    fun should_return_word_tool_bean_when_hasPagesToWord() {
        val bean = ToolDataHelper.dataConvertToWord(
            toolConfigBean = ToolConfigBean(
                hasPagesToWord = true,
            )
        )
        val subTitle = stringResource(R.string.convert_to_ppt_sub).replace(
            stringResource(R.string.type_keynote),
            stringResource(R.string.pdf)
        )
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_CONVERT_TO_WORD,
                layoutEnum,
                EnumId.ID_CONVERT_TO_WORD,
                R.drawable.icon_convert_to_word,
                stringResource(R.string.convert_to_word),
                subTitle,
                R.color.color_convert_to_word
            ),
            bean
        )
    }

    @Test
    fun should_return_word_tool_bean_when_all_false() {
        val bean = ToolDataHelper.dataConvertToWord(
            toolConfigBean = ToolConfigBean()
        )
        val subTitle = stringResource(R.string.convert_to_ppt_sub).replace(
            stringResource(R.string.type_keynote),
            stringResource(R.string.type_pages)
        )
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_CONVERT_TO_WORD,
                layoutEnum,
                EnumId.ID_CONVERT_TO_WORD,
                R.drawable.icon_convert_to_word,
                stringResource(R.string.convert_to_word),
                subTitle,
                R.color.color_convert_to_word
            ),
            bean
        )
    }

    @Test
    fun should_return_excel_tool_bean_when_hasNumbersToExcel_and_hasPdfToExcel() {
        val bean = ToolDataHelper.dataConvertToExcel(
            toolConfigBean = ToolConfigBean(
                hasNumbersToExcel = true,
                hasPdfToExcel = true,
            )
        )
        val subTitle = stringResource(R.string.convert_to_excel_sub)
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_CONVERT_TO_EXCEL,
                layoutEnum,
                EnumId.ID_CONVERT_TO_EXCEL,
                R.drawable.icon_convert_to_excel,
                stringResource(R.string.convert_to_excel),
                subTitle,
                R.color.color_convert_to_excel
            ),
            bean
        )
    }

    @Test
    fun should_return_excel_tool_bean_when_hasPdfToExcel() {
        val bean = ToolDataHelper.dataConvertToExcel(
            toolConfigBean = ToolConfigBean(
                hasPdfToExcel = true,
            )
        )
        val subTitle = stringResource(R.string.convert_to_ppt_sub).replace(
            stringResource(R.string.type_keynote),
            stringResource(R.string.pdf)
        )
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_CONVERT_TO_EXCEL,
                layoutEnum,
                EnumId.ID_CONVERT_TO_EXCEL,
                R.drawable.icon_convert_to_excel,
                stringResource(R.string.convert_to_excel),
                subTitle,
                R.color.color_convert_to_excel
            ),
            bean
        )
    }

    @Test
    fun should_return_excel_tool_bean_when_all_false() {
        val bean = ToolDataHelper.dataConvertToExcel(
            toolConfigBean = ToolConfigBean()
        )
        val subTitle = stringResource(R.string.convert_to_ppt_sub).replace(
            stringResource(R.string.type_keynote),
            stringResource(R.string.type_number)
        )
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_CONVERT_TO_EXCEL,
                layoutEnum,
                EnumId.ID_CONVERT_TO_EXCEL,
                R.drawable.icon_convert_to_excel,
                stringResource(R.string.convert_to_excel),
                subTitle,
                R.color.color_convert_to_excel
            ),
            bean
        )
    }

    @Test
    fun should_return_ppt_tool_bean_when_hasPdfToPPT_and_hasKeyToPPT() {
        val bean = ToolDataHelper.dataConvertToPpt(
            toolConfigBean = ToolConfigBean(
                hasPdfToPPT = true,
                hasKeyToPPT = true,
            )
        )
        val subTitle = stringResource(
            R.string.convert_to_sub_for_two_category,
            stringResource(R.string.pdf),
            stringResource(R.string.type_keynote),
        )
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_CONVERT_TO_PPT,
                layoutEnum,
                EnumId.ID_CONVERT_TO_PPT,
                R.drawable.icon_convert_to_ppt,
                stringResource(R.string.convert_to_ppt),
                subTitle,
                R.color.color_convert_to_ppt
            ),
            bean
        )
    }

    @Test
    fun should_return_ppt_tool_bean_when_hasPdfToPPT() {
        val bean = ToolDataHelper.dataConvertToPpt(
            toolConfigBean = ToolConfigBean(
                hasPdfToPPT = true,
            )
        )
        val subTitle = stringResource(R.string.convert_to_ppt_sub).replace(
            stringResource(R.string.type_keynote),
            stringResource(R.string.pdf)
        )
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_CONVERT_TO_PPT,
                layoutEnum,
                EnumId.ID_CONVERT_TO_PPT,
                R.drawable.icon_convert_to_ppt,
                stringResource(R.string.convert_to_ppt),
                subTitle,
                R.color.color_convert_to_ppt
            ),
            bean
        )
    }

    @Test
    fun should_return_ppt_tool_bean_when_all_false() {
        val bean = ToolDataHelper.dataConvertToPpt(
            toolConfigBean = ToolConfigBean()
        )
        val subTitle = stringResource(R.string.convert_to_ppt_sub)
        assertEquals(
            ToolContentBean(
                EnumPriority.PRIORITY_CONVERT_TO_PPT,
                layoutEnum,
                EnumId.ID_CONVERT_TO_PPT,
                R.drawable.icon_convert_to_ppt,
                stringResource(R.string.convert_to_ppt),
                subTitle,
                R.color.color_convert_to_ppt
            ),
            bean
        )
    }

    @Test
    fun should_return_tool_data_list_when_load_tool_data() {
        Mockito.mockStatic(ToolDataHelper::class.java, Mockito.CALLS_REAL_METHODS).use {
            it.`when`<ToolConfigBean> {
                ToolDataHelper.checkToolAbility(ContextGetter.context)
            }.thenReturn(
                ToolConfigBean(
                    hasAiToolTitle = true,
                    hasDocSummary = true,
                    hasDocTranslation = true,
                    hasExtractChart = true,
                )
            )
            val result = ToolDataHelper.loadToolData(ContextGetter.context, EnumWay.WAY_TOOL)
            val expectData = mutableListOf(
                ToolDataHelper.dataAiToolTitle(),
                ToolDataHelper.dataDocSummary(),
                ToolDataHelper.dataDocTranslation(),
                ToolDataHelper.dataExtractChart(EnumWay.WAY_TOOL),
            )
            assertEquals(expectData, result)
        }
    }
}