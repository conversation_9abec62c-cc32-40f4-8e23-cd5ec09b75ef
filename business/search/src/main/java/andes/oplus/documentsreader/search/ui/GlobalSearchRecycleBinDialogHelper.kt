/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - GlobalSearchRecycleBinDialogHelper.kt
 * Description:
 *     The helper to create dialog for recycle bin operations.
 *
 * Version: 1.0
 * Date: 2024-07-20
 * Author: 80241271
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * 8024121    2024-07-14   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.search.ui

import andes.oplus.documentsreader.core.common.utils.getBottomAlertDialogWindowAnimStyle
import andes.oplus.documentsreader.search.R
import andes.oplus.documentsreader.search.bean.SearchRecycleWrapper
import andes.oplus.documentsreader.search.utils.GlobalSearchRecycleBinUtils
import android.content.Context
import android.view.Gravity
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder

internal class GlobalSearchRecycleBinDialogHelper(private val context: Context) {

    fun confirmDelete(
        selectedFiles: Collection<SearchRecycleWrapper>,
        isSelectedAll: Boolean,
        onApproved: (Context, Collection<SearchRecycleWrapper>, Boolean) -> Unit
    ): COUIAlertDialogBuilder =
        COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setBlurBackgroundDrawable(true)
            .setWindowGravity(Gravity.BOTTOM)
            .setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(context))
            .setTitle(
                GlobalSearchRecycleBinUtils.getDeleteTitle(
                    context,
                    selectedFiles,
                    isSelectedAll
                )
            )
            .setMessage(
                GlobalSearchRecycleBinUtils.getDeleteMessage(
                    context,
                    selectedFiles,
                    isSelectedAll
                )
            )
            .setNeutralButton(
                GlobalSearchRecycleBinUtils.getDeleteButtonText(
                    context,
                    isSelectedAll
                )
            ) { _, _ ->
                onApproved(context, selectedFiles, isSelectedAll)
                GlobalSearchRecycleBinUtils.playDeleteEffect()
            }
            .setNegativeButton(R.string.dialog_cancel, null)

    fun confirmRestore(
        selectedFiles: Collection<SearchRecycleWrapper>,
        isSelectedAll: Boolean,
        onApproved: (Context, Collection<SearchRecycleWrapper>, Boolean) -> Unit
    ): COUIAlertDialogBuilder =
        COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setBlurBackgroundDrawable(true)
            .setWindowGravity(Gravity.BOTTOM)
            .setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(context))
            .setTitle(
                GlobalSearchRecycleBinUtils.getRestoreTitle(
                    context,
                    selectedFiles,
                    isSelectedAll
                )
            )
            .setNeutralButton(
                GlobalSearchRecycleBinUtils.getRestoreButtonText(
                    context,
                    isSelectedAll
                )
            ) { _, _ ->
                onApproved(context, selectedFiles, isSelectedAll)
            }
            .setNegativeButton(R.string.dialog_cancel, null)

    fun deleteProgress(): AlertDialog =
        progressDialog(R.string.dialog_deleting)

    fun restoreProgress(): AlertDialog =
        progressDialog(R.string.send_file_message)

    private fun progressDialog(@StringRes titleId: Int): AlertDialog =
        COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Progress)
            .setBlurBackgroundDrawable(true)
            .setTitle(titleId)
            .setCancelable(false)
            .create().apply {
                setCanceledOnTouchOutside(false)
            }
}