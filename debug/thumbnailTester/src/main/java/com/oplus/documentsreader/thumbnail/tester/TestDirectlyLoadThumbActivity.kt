/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - TestDirectlyLoadThumbActivity.kt
 * Description:
 *     The test case for load thumbnail with setting onlyCache as false.
 *
 * Version: 1.0
 * Date: 2025-01-02
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-01-02   1.0    Create this module
 *********************************************************************************/
package com.oplus.documentsreader.thumbnail.tester


internal class TestDirectlyLoadThumbActivity : AbsTestThumbnailActivity() {

    override val logTag: String = ThumbnailTestConstants.logTag("TestDirectlyLoadThumbActivity")

    override val onlyCacheMode: Boolean = false
}