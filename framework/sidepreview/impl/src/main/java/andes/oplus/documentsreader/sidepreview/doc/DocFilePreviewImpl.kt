/*********************************************************************
 * * Copyright (C), 2024, OPlus. All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/10/4
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.sidepreview.doc

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.KtThumbnailHelper
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.sidepreview.core.AbsPreviewLoadingScheduler
import andes.oplus.documentsreader.sidepreview.core.FilePreviewViewModel
import andes.oplus.documentsreader.sidepreview.impl.R
import andes.oplus.documentsreader.sidepreview.utils.BorderedRoundedCornersTrans
import andes.oplus.documentsreader.sidepreview.utils.GlideExtend
import andes.oplus.documentsreader.sidepreview.utils.glideSignatureKey
import andes.oplus.documentsreader.sidepreview.utils.isBoardDoc
import android.graphics.Bitmap
import androidx.lifecycle.Observer
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.doc.DocThumbnail

internal class DocFilePreviewImpl(
    private val fragment: DocPreviewFragment,
    private val previewModel: FilePreviewViewModel
) : IDocFilePreview {
    private companion object {
        private const val TAG = "DocFilePreviewImpl"
    }

    private val docPaperWidth: Int
    private val docPaperHeight: Int
    private val docBoardWidth: Int
    private val docBoardHeight: Int
    private val loadingScheduler = PreviewLoadingScheduler()
    private val onDocFileLoaded = Observer<BaseFileBean?> {
        it?.apply {
            displayOnContainer(this)
        }
    }

    private var isObserved = false
    private var containerManager: IDocPreviewContainerManager? = null

    init {
        val res = fragment.requireContext().resources
        docPaperWidth = res.getDimensionPixelSize(R.dimen.oplusdoc_preview_doc_paper_max_width)
        docPaperHeight = res.getDimensionPixelSize(R.dimen.oplusdoc_preview_doc_paper_max_height)
        docBoardWidth = res.getDimensionPixelSize(R.dimen.oplusdoc_preview_doc_board_max_width)
        docBoardHeight = res.getDimensionPixelSize(R.dimen.oplusdoc_preview_doc_board_max_height)
    }

    override fun release() {
        previewModel.releaseLoader(onDocFileLoaded)
        loadingScheduler.resetLoadingScheduler()
        containerManager?.thumbnailView?.let {
            GlideExtend.with(it.context)?.clear(it)
        }
        isObserved = false
    }

    override fun attachToContainer(containerManager: IDocPreviewContainerManager) {
        this.containerManager = containerManager
        loadingScheduler.requireToShowLoading()
        if (isObserved) {
            return
        }
        isObserved = true
        containerManager.thumbnailView.setOnClickListener { fragment.actionOpen() }
        previewModel.loadPreviewFile(fragment.viewLifecycleOwner, onDocFileLoaded)
    }

    private fun showDefaultContainer(baseFileBean: BaseFileBean) {
        loadingScheduler.dismissLoading(true)
        val containerMgr = containerManager ?: return
        val defaultContainer = containerMgr.defaultContainer
        val drawable =
            KtThumbnailHelper.getClassifyDrawable(defaultContainer.context, baseFileBean.mLocalType)
        defaultContainer.fileIconView.setOnClickListener { fragment.actionOpen() }
        defaultContainer.setFileIcon(drawable)
    }

    private fun showSuccessContainer() {
        loadingScheduler.dismissLoading(false)
    }

    private fun displayOnContainer(baseFileBean: BaseFileBean) {
        Log.d(TAG, "displayOnContainer")
        val containerMgr = containerManager ?: return
        val previewImageView = containerMgr.thumbnailView
        if (ThumbnailManager.isDocThumbnailSupported(previewImageView.context)) {
            val roundCorners = BorderedRoundedCornersTrans(previewImageView.context)
            var options = RequestOptions().diskCacheStrategy(DiskCacheStrategy.NONE)
                .skipMemoryCache(false)
                .signature(baseFileBean.glideSignatureKey())
            options = options.transform(roundCorners)
            val docThumbnail = DocThumbnail(
                baseFileBean.mData ?: "",
                baseFileBean.mDateModified, baseFileBean.mSize
            )
            docThumbnail.extraSerializeKey = <EMAIL>()
            Log.d(TAG, "displayOnContainer docThumbnail=$docThumbnail")
            val width: Int
            val height: Int
            if (baseFileBean.isBoardDoc()) {
                width = docBoardWidth
                height = docBoardHeight
            } else {
                width = docPaperWidth
                height = docPaperHeight
            }
            Glide.with(previewImageView.context)
                .asBitmap()
                .load(docThumbnail)
                .apply(options)
                .override(width, height)
                .addListener(object : RequestListener<Bitmap> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Bitmap>,
                        isFirstResource: Boolean
                    ): Boolean {
                        showDefaultContainer(baseFileBean)
                        Log.d(TAG, "displayOnContainer: onLoadFailed: ${baseFileBean.hashCode()}")
                        return false
                    }

                    override fun onResourceReady(
                        resource: Bitmap,
                        model: Any,
                        target: Target<Bitmap>?,
                        dataSource: DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.d(TAG, "displayOnContainer: onResourceReady: ${baseFileBean.hashCode()}")
                        showSuccessContainer()
                        return false
                    }
                }).into(previewImageView)
        } else {
            Log.d(TAG, "not support")
            showDefaultContainer(baseFileBean)
        }
        showFileInfo(baseFileBean)
    }

    private fun showFileInfo(baseFileBean: BaseFileBean) {
        val containerMgr = containerManager ?: return
        containerMgr.defaultContainer.setFileName(baseFileBean.mDisplayName)
        containerMgr.nameView.text = baseFileBean.mDisplayName
        containerMgr.fileInfoRecyclerView.bindFileBean(baseFileBean)
    }

    override fun refresh(baseFileBean: BaseFileBean) {
        showFileInfo(baseFileBean)
    }

    private inner class PreviewLoadingScheduler : AbsPreviewLoadingScheduler() {
        override val subTag: String = TAG

        override fun onShowLoading() {
            containerManager?.showAsLoading()
        }

        override fun onGetStartLoadingTime(): Long? = containerManager?.startLoadingTime

        override fun onDismissLoading(extraObj: Any?) {
            val isDefault = extraObj as? Boolean ?: return
            containerManager?.showAsFileContainer(isDefault)
        }
    }
}