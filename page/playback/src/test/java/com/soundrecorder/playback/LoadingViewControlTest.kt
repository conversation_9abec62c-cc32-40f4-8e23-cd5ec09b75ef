/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: LoadingViewControlTest
 * Description:
 * Version: 1.0
 * Date: 2023/6/26
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/6/26 1.0 create
 */

package com.soundrecorder.playback

import android.content.Context
import android.os.Build
import android.view.View
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class LoadingViewControlTest {

    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @After
    fun release() {
        mContext = null
    }

    @Test
    fun should_when_showLoading() {
        val loadingView = View(mContext)
        val tabView = View(mContext)
        val contentView = View(mContext)
        val lifecycle = Mockito.mock(Lifecycle::class.java)
        val loadingViewControl = LoadingViewControl(lifecycle, loadingView, tabView, contentView)
        loadingViewControl.showLoading(showLoading = true, showTabView = true)
        Assert.assertTrue(loadingView.isVisible)
        Assert.assertFalse(contentView.isVisible)
        Assert.assertTrue(tabView.isVisible)

        loadingViewControl.showLoading(showLoading = true, showTabView = false)
        Assert.assertFalse(tabView.isVisible)

        loadingViewControl.showLoading(showLoading = false, showTabView = true)
        Assert.assertTrue(tabView.isVisible)
        Assert.assertTrue(contentView.isVisible)
        Assert.assertFalse(loadingView.isVisible)
    }

    @Test
    fun should_when_hideLoading() {
        val lifecycle = Mockito.mock(Lifecycle::class.java)
        val loadingView = View(mContext)
        val tabView = View(mContext)
        val contentView = View(mContext)
        val loadingViewControl = LoadingViewControl(lifecycle, loadingView, tabView, contentView)
        loadingViewControl.hideLoading(true)
        Assert.assertFalse(loadingView.isVisible)
        Assert.assertTrue(contentView.isVisible)
        Assert.assertTrue(tabView.isVisible)

        loadingViewControl.hideLoading(false)
        Assert.assertFalse(tabView.isVisible)
    }

    @Test
    fun should_when_getAnimationView() {
        val lifecycle = Mockito.mock(Lifecycle::class.java)
        val loadingView = Mockito.mock(View::class.java)
        val tabView = View(mContext)
        val contentView = View(mContext)
        val loadingViewControl = LoadingViewControl(lifecycle, loadingView, tabView, contentView)
        var animView = Whitebox.invokeMethod<EffectiveAnimationView>(loadingViewControl, "getAnimationView")
        Assert.assertNull(animView)

        val animationView = EffectiveAnimationView(mContext)
        Mockito.`when`(loadingView.findViewById<EffectiveAnimationView>(anyInt())).thenReturn(animationView)
        animView = Whitebox.invokeMethod(loadingViewControl, "getAnimationView")
        Assert.assertNotNull(animView)
    }
}