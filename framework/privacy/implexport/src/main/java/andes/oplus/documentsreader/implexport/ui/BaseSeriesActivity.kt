/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: BaseSeriesActivity
 * * Description: BaseSeriesActivity
 * * Version: 1.0
 * * Date : 2024/03/01
 * * Author:ztf
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *        ztf           2024/03/01      1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.implexport.ui

import andes.oplus.documentsreader.core.common.BaseVMActivity
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.StatusBarUtils
import andes.oplus.documentsreader.core.common.WindowUtils
import andes.oplus.documentsreader.core.common.compat.FeatureCompat
import andes.oplus.documentsreader.core.common.uiconfig.type.IUIConfig
import andes.oplus.documentsreader.implexport.utils.StatementUtil
import andes.oplus.documentsreader.userprivacy.implexport.R
import android.view.ViewTreeObserver
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.core.widget.NestedScrollView
import com.coui.appcompat.toolbar.COUIToolbar
import com.google.android.material.appbar.AppBarLayout

abstract class BaseSeriesActivity : BaseVMActivity() {

    companion object {
        private const val TAG = "BaseSeriesActivity"
        const val UPDATE_TIME_MILLS = 1714492800000L
        const val UPDATE_TIME_MILLS_915 = 1726329600000L

        const val TWO = 2
        const val THREE = 3
        const val SIX = 6
        const val SEVEN = 7
        const val EIGHT = 8
    }

    var statementState = StatementUtil.STATEMENT_NONE

    override fun initView() {
        findViewById<COUIToolbar>(R.id.toolbar)?.apply {
            title = ""
            setSupportActionBar(this)
            supportActionBar?.setDisplayHomeAsUpEnabled(true)
        }
        initLayoutPadding()
        findViewById<NestedScrollView>(R.id.scrollview)?.apply {
            val listener = object : ViewTreeObserver.OnPreDrawListener {
                override fun onPreDraw(): Boolean {
                    viewTreeObserver.removeOnPreDrawListener(this)
                    val appBarHeight =
                        <EMAIL><AppBarLayout>(R.id.appbar_layout)?.measuredHeight
                            ?: 0
                    setPadding(
                        0,
                        appBarHeight + resources.getDimensionPixelOffset(R.dimen.dimen_12dp),
                        0,
                        0
                    )
                    clipToPadding = false
                    return true
                }
            }
            viewTreeObserver.addOnPreDrawListener(listener)
            ViewCompat.setNestedScrollingEnabled(this, true)
        }
    }

    private fun initLayoutPadding() {
        var toolbarTopPadding = StatusBarUtils.getStatusBarHeight(this)
        if (FeatureCompat.isApplicableForFlexibleWindow &&
            WindowUtils.isMiddleAndLargeScreen(this) &&
            !WindowUtils.isInMultiWindowMode(this)
        ) {
            toolbarTopPadding = resources.getDimensionPixelOffset(R.dimen.dimen_4dp)
        }
        findViewById<CoordinatorLayout>(R.id.coordinator)?.apply {
            setPadding(paddingLeft, toolbarTopPadding, paddingRight, paddingBottom)
        }
    }

    override fun startObserve() {
        // do nothing
    }

    override fun initData() {
        statementState = StatementUtil.getStatementResult(this)
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        Log.e(TAG, "onUIConfigChanged")
        initLayoutPadding()
    }

    override fun onWindowInsetsCallback(showNavigationBar: Boolean, systemBarInsetsBottom: Int) {
        super.onWindowInsetsCallback(showNavigationBar, systemBarInsetsBottom)
        if (showNavigationBar) {
            findViewById<CoordinatorLayout>(R.id.coordinator)?.updatePadding(bottom = systemBarInsetsBottom)
        } else {
            findViewById<CoordinatorLayout>(R.id.coordinator)?.updatePadding(bottom = 0)
        }
    }
}