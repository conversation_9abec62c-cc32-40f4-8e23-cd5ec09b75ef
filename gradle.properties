# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx8192m -Dfile.encoding=UTF-8 -XX:+UseParallelGC
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
android.enableJetifier=true
android.injected.testOnly=false
# Non-transitive R class and R8 full mode are default enabled from AGP 8.0
# Manually disable them until completely adapt them in codes.
# https://developer.android.com/build/releases/past-releases/agp-8-0-0-release-notes#default-changes
android.nonTransitiveRClass=false
android.enableR8.fullMode=false
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
org.gradle.workers.max=16
org.gradle.parallel.threads=16
org.gradle.configureondemand=false
org.gradle.caching=true
org.gradle.daemon=true
org.gradle.parallel=true

sonatypeUsername = swdp
sonatypePassword = swdp

prop_oppoMavenUrlRelease=http://maven.scm.adc.com:8081/nexus/content/groups/stable-public/
prop_oppoMavenUrlSnapshot=http://maven.scm.adc.com:8081/nexus/content/groups/snapshots/
prop_oppoNexusMavenUrl=http://nexus.os.adc.com/nexus/content/groups/public
prop_sdkMavenUrlRelease=http://mirror-maven.myoas.com/repository/ars-sdk-release/
prop_oppoBrowserMavenUrl=http://maven.scm.adc.com:8081/nexus/content/groups/oppo-browser-public/
prop_oplusMavenUrl=http://maven.scm.adc.com:8081/nexus/content/groups/public/
#sophon-sdk-snapshot
prop_cacheMavenUrl=http://mirror-maven.myoas.com/repository/ars-sdk-snapshot/
prop_adcMavenUrlRelease=http://nexus.os.adc.com/nexus/content/repositories/releases/
prop_adcMavenUrlSnapshot=http://nexus.os.adc.com/nexus/content/repositories/snapshots/

#Configure global properties
#Define APK name
prop_archivesBaseName=OplusDocumentsReader
#copy apk to vm android-29
prop_appPrebuildSdkVersion=android-33,android-U,android-34
prop_appTargetPlatformVersion=android-33,android-U,android-34
prop_compileSdkVersion=android-34
# Require Java 17 from AGP 8.0
# https://developer.android.com/build/releases/past-releases/agp-8-0-0-release-notes#jdk-17-agp
prop_targetCompatibility=17
prop_buildToolsVersion=34.0.0
prop_minSdkVersion=31
prop_targetSdkVersion=34
prop_disableSubPackage=false
#Language resource settings, the configuration is valid locally, the server will modify this configuration to package all resources
prop_exp_resConfig=en_US,es_MX,es_ES,pt_BR,da_DK,de_DE,el_GR,fr_FR,in_ID,it_IT,ja_JP,nl_NL,nb_NO,\
  pt_PT,ru_RU,sv_SE,th_TH,tr_TR,vi_VN,zh_CN,zh_HK,zh_TW,my_MM,my_ZG,hi_IN,ms_MY,tl_PH,cs_CZ,en_GB,\
  hu_HU,pl_PL,ro_RO,uk_UA,fa_IR,ur_PK,ar_EG,bn_BD,ko_KR,mr_IN,ta_IN,gu_IN,lo_LA,km_KH,sw_KE,si_LK,\
  pa_IN,te_IN,kn_IN,ml_IN,or_IN,as_IN,kk_KZ,iw_IL,ne_NP,bo_CN,en_AU,en_NZ,ca_ES,eu_ES,bg_BG,fi_FI,\
  sk_SK,hr_HR,lt_LT,sl_SI,lv_LV,et_EE,gl_ES,fr_CH,it_CH,de_CH,uz_UA,uz_UZ,sr_Latn_RS,kea_CV,ar_AR,\
  fil_PH,id_ID,sr_RS,sq_AL,mk_MK
prop_resConfigs=zh_CN,en_US,zh_TW,hi_IN,in_ID,ms_MY,my_MM,tl_PH,ru_RU,th_TH,vi_VN,ja_JP,fr_FR,\
  bn_BD,ko_KR,lo_LA,ne_NP,bo_CN,ug_CN,km_KH,ar_EG,zh_HK,kk_CN,kea_CV,ar_AR,fil_PH,id_ID,it_IT,\
  nl_NL,de_DE,es_MX,pt_BR
prop_versionCommit=12345678
prop_versionDate=230811
mainVersionCode=15011017
mainVersionName=15.11.17
#need_updateVersion is used to determine whether the conditions for displaying the "User Instructions Update" panel have been configured.
need_updateVersion=4

# Yozosoft AAR GroupId & ArtifactId
prop_archivesGroupName=andes.oplus.documentsreader
prop_artifactId_full_domestic=merged-to-yozosoft
prop_artifactId_full_export=merged-to-yozosoft-full-export
prop_artifactId_light_regionall=merged-to-yozosoft-light-regionall
# Exported libs GroupId & ArtifactId
prop_exported_archivesGroupName=com.oplus.documentsreader
prop_exported_artifactId_thumbnail_wrapper=thumbnail-wrapper
prop_exported_artifactId_thumbnail_exporter=thumbnail-exporter
prop_exported_artifactId_file_exporter=file-exporter
#AAR suffix when build with pipeline
versionSuffix=-alpha01

useOBuildPlugin=true
# The gradle task to execute unit test on pipeline
oppo_unit_test=unitTestTask
# The modules which are not be tested when execute unit test
prop_unitTestBlackProjects=app,:aarToYozosoft:ballFullDomestic,:aarToYozosoft:ballFullExport,\
  :aarToYozosoft:ballLightRegionall,repoToYozosoft,:appLib:koin:interface,\
  :debug:recent,:debug:recycle,:debug:thumbnailTester
# The modules which are not applied olint
prop_olintBlackProjects=:aarToYozosoft:ballFullDomestic,:aarToYozosoft:ballFullExport,\
  :aarToYozosoft:ballLightRegionall,repoToYozosoft,:debug:recent,:debug:recycle,:debug:thumbnailTester
# The modules which can not be integrated by business modules
prop_businessBlackProjects=:aarToYozosoft:ballFullDomestic,:aarToYozosoft:ballFullExport,\
  :aarToYozosoft:ballLightRegionall,repoToYozosoft,:debug:thumbnailTester
# The modules which can not be integrated by :app
prop_integrateBlackProjects=repoToYozosoft
# The module which can only use compileOnly to depend it
prop_compileOnlyProjects=:appLib:koin:interface
# For test integrate Yozosoft aar to :app module
prop_testIntegrateYozosoftAarToApp=false
# The dir name when locally publish Yozosoft aar under rootProject.buildDir
prop_localPublishAarRepo=repo
# Enable OAPM configs
prop_enableOapm=true
# Enable OCoverage configs
prop_enableObuildCoverage=true
# Debug YoZo doc thumbnail with release doc app and install doc aar as another debug apk.
# And then we can invoke YoZo doc thumbnail service with our local codes to debug.
prop_localThumbnailDebug=false