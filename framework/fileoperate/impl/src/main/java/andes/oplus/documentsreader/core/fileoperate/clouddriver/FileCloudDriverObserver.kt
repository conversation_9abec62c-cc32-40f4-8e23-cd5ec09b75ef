/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileCloudDriverObserver.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package andes.oplus.documentsreader.core.fileoperate.clouddriver

import andes.oplus.documentsreader.core.common.CustomToast
import andes.oplus.documentsreader.core.common.R
import andes.oplus.documentsreader.core.fileoperate.base.ACTION_FAILED
import andes.oplus.documentsreader.core.fileoperate.share.EMPTY_FOLDER_STATE
import andes.oplus.documentsreader.core.fileoperate.share.FileShareObserver
import andes.oplus.documentsreader.core.fileoperate.share.TOO_MANY_FILES_STATE
import andes.oplus.documentsreader.core.common.Utils.MAX_SEND_COUNT
import android.content.Context
import android.view.ContextThemeWrapper

open class FileCloudDriverObserver(context: ContextThemeWrapper) : FileShareObserver(context) {

    companion object {
        const val TAG = "FileCloudDriverObserver"
    }

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        return when (result.first) {
            ACTION_FAILED -> {
                when (result.second) {
                    TOO_MANY_FILES_STATE -> CustomToast.showShort(context.getString(R.string.toast_upload_beyond_count_new, MAX_SEND_COUNT))
                    EMPTY_FOLDER_STATE -> CustomToast.showShort(R.string.toast_upload_file_error)
                }
                false
            }
            else -> false
        }
    }
}