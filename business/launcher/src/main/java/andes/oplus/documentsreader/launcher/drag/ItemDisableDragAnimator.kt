/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/11/01, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package andes.oplus.documentsreader.launcher.drag

import andes.oplus.documentsreader.core.common.Log
import android.animation.ValueAnimator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator

class ItemDisableDragAnimator {

    private var alphaChangedCallback: (alpha: Float, enterDrag: Boolean) -> Unit = { _, _ -> }

    companion object {
        const val TAG = "ItemDisableDragAnimator"
        private const val DEFAULT_ENTER_EDIT_ANIMATION_DURATION = 350L
        private val DEFAULT_ANIMATOR_INTERPOLATOR = COUIMoveEaseInterpolator()
        const val DISABLE_VIEW_ALPHA = 0.26f
    }

    private val startDragAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
        addUpdateListener {
            val fraction = it.animatedFraction
            val alpha = DISABLE_VIEW_ALPHA + (1 - fraction) * (1 - DISABLE_VIEW_ALPHA)
            alphaChangedCallback.invoke(alpha, true)
        }
        duration = DEFAULT_ENTER_EDIT_ANIMATION_DURATION
        interpolator = DEFAULT_ANIMATOR_INTERPOLATOR
    }

    private val endDragAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
        addUpdateListener {
            val fraction = it.animatedFraction
            val alpha = DISABLE_VIEW_ALPHA + fraction * (1 - DISABLE_VIEW_ALPHA)
            alphaChangedCallback.invoke(alpha, false)
        }
        duration = DEFAULT_ENTER_EDIT_ANIMATION_DURATION
        interpolator = COUIMoveEaseInterpolator()
    }

    fun onDragStart(alphaChangedCallback: (alpha: Float, enterDrag: Boolean) -> Unit) {
        Log.d(TAG, "onDragStart")
        this.alphaChangedCallback = alphaChangedCallback
        if (endDragAnimator.isRunning) {
            endDragAnimator.cancel()
        }
        startDragAnimator.start()
    }

    fun onDragEnd(alphaChangedCallback: (alpha: Float, enterDrag: Boolean) -> Unit) {
        Log.d(TAG, "onDragEnd")
        this.alphaChangedCallback = alphaChangedCallback
        if (startDragAnimator.isRunning) {
            startDragAnimator.cancel()
        }
        endDragAnimator.start()
    }
}