/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AbsPreviewFragment.kt
 * Description:
 *     The common implementations for each file preview fragment.
 *
 * Version: 1.0
 * Date: 2024-09-19
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-09-19   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.sidepreview.core

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.interfaces.fileoprate.IFileOperate
import andes.oplus.documentsreader.core.common.uiconfig.UIConfigMonitor
import andes.oplus.documentsreader.core.common.uiconfig.type.IUIConfig
import andes.oplus.documentsreader.sidepreview.doc.ISidePreviewFragment
import andes.oplus.documentsreader.sidepreview.doc.ISidePreviewFragment.OnClickFilePathListener
import andes.oplus.documentsreader.sidepreview.impl.R
import andes.oplus.documentsreader.sidepreview.widget.PreviewFilePathItem
import andes.oplus.documentsreader.sidepreview.widget.PreviewOperationsBar
import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.annotation.CallSuper
import androidx.annotation.LayoutRes
import androidx.annotation.MenuRes
import androidx.annotation.StringRes
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider

internal abstract class AbsPreviewFragment<VM : FilePreviewViewModel>(
    @LayoutRes fragmentLayoutId: Int
) : Fragment(fragmentLayoutId), ISidePreviewFragment, IUiConfigChangeManager {

    /**
     * The log tag and view model tag for preview fragment
     */
    protected abstract val logTag: String

    /**
     * The view model class to obtain view model from [ViewModelProvider]
     */
    protected abstract val viewModelClass: Class<VM>

    /**
     * The operations bar widget id in fragment layout
     */
    protected abstract val operationsBarId: Int

    /**
     * The text res id of open button
     */
    @StringRes
    protected open val openButtonTextId: Int = R.string.open

    /**
     * The menu res id of more menu
     */
    @MenuRes
    protected open val moreMenuResId: Int = R.menu.oplusdoc_menu_preview_file_more_common

    private val uiConfigChangeObservers = mutableSetOf<IUiConfigChangeObserver>()

    protected var previewModel: VM? = null
        private set(value) {
            field = value
            if (value != null) {
                lastPreviewModel = value
            }
        }

    protected var operationsBar: PreviewOperationsBar? = null
        private set

    protected var operationsController: PreviewOperationsController? = null
        private set

    protected var filePathItem: PreviewFilePathItem? = null
        private set

    protected var toPreviewFileBean: BaseFileBean? = null
        private set

    private var clickFilePathListener: OnClickFilePathListener? = null
    private var operatorResultListener: IFileOperate.OperateResultListener? = null
    private var operateInterceptor: IFileOperate? = null
    private var lastPreviewModel: VM? = null

    final override fun setPreviewFile(context: Context, fileBean: BaseFileBean): Boolean {
        if (!isPreviewFileApproved(context, fileBean)) {
            return false
        }
        val viewModel = previewModel
        if (viewModel == null) {
            toPreviewFileBean = fileBean
        } else {
            putPreviewFileToViewModel(viewModel, fileBean)
            toPreviewFileBean = null
        }
        return true
    }

    override fun refreshPreviewFile(context: Context, fileBean: BaseFileBean) {
        Log.d("AbsPreviewFragment", "fileBean = $fileBean")
        applyFilePathIntoItem(fileBean)
    }

    final override fun setOperatorResultListener(listener: IFileOperate.OperateResultListener) {
        operatorResultListener = listener
        operationsController?.setOperatorResultListener(listener)
    }

    final override fun setOperateInterceptor(interceptor: IFileOperate) {
        operateInterceptor = interceptor
        operationsController?.setOperateInterceptor(interceptor)
    }

    /**
     * Override the put implement if necessary.
     */
    protected open fun putPreviewFileToViewModel(viewModel: VM, fileBean: BaseFileBean) {
        viewModel.putPreviewFile(fileBean)
    }

    /**
     * Check while preview file is approved to be previewed by this fragment.
     */
    protected abstract fun isPreviewFileApproved(context: Context, fileBean: BaseFileBean): Boolean

    @CallSuper
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val viewModel = ViewModelProvider(this)[logTag, viewModelClass]
        toPreviewFileBean?.let {
            putPreviewFileToViewModel(viewModel, it)
            toPreviewFileBean = null
        }
        previewModel = viewModel
        operationsBar = prepareOperationsBar(view, viewModel)
        filePathItem = onCreateFilePathItem(view)
        viewModel.loadPreviewFile(viewLifecycleOwner, ::applyFilePathIntoItem)
        onViewModelCreated(view, viewModel)
    }

    /**
     * Do some init actions after created view model.
     */
    protected abstract fun onViewModelCreated(view: View, viewModel: VM)

    /**
     * Find [PreviewFilePathItem] from fragment layout if has it.
     */
    protected open fun onCreateFilePathItem(view: View): PreviewFilePathItem? = null

    override fun onStart() {
        super.onStart()
        if ((filePathItem != null) && (clickFilePathListener == null)) {
            Log.d(logTag, "onStart: create default click file path listener")
            activity?.also {
                setOnClickFilePathListener(PreviewOnClickFilePathImpl(it))
            } ?: run {
                Log.e(logTag, "onStart: ERROR! Activity is null")
            }
        }
    }

    @CallSuper
    override fun onDestroyView() {
        super.onDestroyView()
        operationsBar?.release()
        operationsBar = null
        operationsController = null
        filePathItem = null
        previewModel = null
        uiConfigChangeObservers.clear()
    }

    private fun prepareOperationsBar(
        view: View,
        viewModel: FilePreviewViewModel
    ): PreviewOperationsBar {
        val bar = view.findViewById<PreviewOperationsBar>(operationsBarId)
        val controller = onCreatePreviewOperationsController(viewModel).apply {
            operatorResultListener?.let(::setOperatorResultListener)
            operateInterceptor?.let(::setOperateInterceptor)
        }
        controller.attachToOperationsBar(bar, openButtonTextId, moreMenuResId)
        operationsController = controller
        return bar
    }

    /**
     * Use default implementation of [PreviewOperationsController]
     * And override to replace with customized implementation if necessary.
     */
    protected open fun onCreatePreviewOperationsController(
        viewModel: FilePreviewViewModel
    ): PreviewOperationsController = PreviewOperationsController(this, viewModel)

    @CallSuper
    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (!UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            return
        }
        operationsController?.onConfigurationChanged(context?.resources?.configuration)
        onUpdateUIWhenConfigChange(configList)
        uiConfigChangeObservers.forEach {
            it.onUpdateUIWhenConfigChange(configList)
        }
    }

    /**
     * Update UI layout and so on when UI config has been changed if necessary.
     */
    protected open fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>) {
        // Default do nothing
    }

    override fun addUIConfigChangeObserver(observer: IUiConfigChangeObserver) {
        uiConfigChangeObservers.add(observer)
    }

    override fun removeUIConfigChangeObserver(observer: IUiConfigChangeObserver) {
        uiConfigChangeObservers.remove(observer)
    }

    @CallSuper
    override fun fromSelectPathResult(requestCode: Int, path: String?) {
        operationsController?.onSelectPathReturn(requestCode, path)
    }

    override fun setOnClickFilePathListener(listener: OnClickFilePathListener?) {
        clickFilePathListener = listener
        applyFilePathIntoItem(previewModel?.pickPreviewFile())
    }

    open fun applyFilePathIntoItem(previewFile: BaseFileBean?) {
        filePathItem?.setFileBean(previewFile)
    }

    override fun pressBack(): Boolean {
        // Default do nothing
        return false
    }

    fun actionOpen() {
        operationsController?.actionOpen()
    }

    override fun onDetach() {
        super.onDetach()
        releasePreview()
    }
}