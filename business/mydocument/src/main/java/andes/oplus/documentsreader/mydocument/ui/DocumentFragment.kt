/***********************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File:   DocumentFragment
 * * Description: DocumentFragment
 * * Version:1.0
 * * Date :2024/6/20
 * * Author:yangqichang
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 ****************************************************************/
package andes.oplus.documentsreader.mydocument.ui

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.CategoryHelper
import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.LoadingController
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.Utils
import andes.oplus.documentsreader.core.common.animation.FolderTransformAnimator
import andes.oplus.documentsreader.core.common.animation.FolderTransformAnimator.Companion.FILE_BROWSER_FOLDER_ANIM_TIME
import andes.oplus.documentsreader.core.common.controller.SortPopupController
import andes.oplus.documentsreader.core.common.interfaces.ICategoryGlobalSearchApi
import andes.oplus.documentsreader.core.common.interfaces.OnBackPressed
import andes.oplus.documentsreader.core.common.interfaces.TabActivityListener
import andes.oplus.documentsreader.core.common.loader.LoaderViewModel
import andes.oplus.documentsreader.core.common.selection.BaseUiModel
import andes.oplus.documentsreader.core.common.selection.RecyclerSelectionVMFragment
import andes.oplus.documentsreader.core.common.sort.SelectItemListener
import andes.oplus.documentsreader.core.common.sort.SortEntryView
import andes.oplus.documentsreader.core.common.sort.SortHelper
import andes.oplus.documentsreader.core.common.sort.SortModeUtils
import andes.oplus.documentsreader.core.common.sort.SortRecordModeFactory
import andes.oplus.documentsreader.core.common.utils.KtViewUtils
import andes.oplus.documentsreader.core.fileoperate.interfaces.INormalFileOperateController
import andes.oplus.documentsreader.mydocument.R
import andes.oplus.documentsreader.mydocument.adapter.DocumentListAdapter
import andes.oplus.documentsreader.setting.ISetting
import android.app.Activity
import android.os.Bundle
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import androidx.core.view.updatePadding
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.toolbar.COUIToolbar
import com.documentsreader.emptyfile.IFileEmptyController
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.dropdrag.recycleview.ItemDetailsLookup
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

open class DocumentFragment : RecyclerSelectionVMFragment<DocumentViewModel>(),
    NavigationBarView.OnItemSelectedListener,
    OnBackPressed {

    companion object {
        private const val TAG = "DocumentFragment"
        private const val DELAY_REFRESH = 50L
    }

    var fileOperateController: INormalFileOperateController? = null

    private var sortEntryView: SortEntryView? = null
    private val fileEmptyController = Injector.injectFactory<IFileEmptyController>()
    private val sortPopupController by lazy { SortPopupController(lifecycle) }
    private var adapter: DocumentListAdapter? = null
    private var layoutManager: GridLayoutManager? = null
    private var loadingController: LoadingController? = null
    private var tabActivityListener: TabActivityListener<BaseFileBean>? = null
    private var tabPosition = KtConstants.TAB_ALL
    private var extension: String? = null
    private var path: String? = null
    private var hasShowEmpty: Boolean = false
    private val mFolderTransformAnimator by lazy { FolderTransformAnimator() }
    private var sortMode: Int = 0
    private var isDesc: Boolean = true
    private var canSmoothScrollToTop = false

    override fun getLayoutResId(): Int {
        return R.layout.oplus_doc_document_fragment
    }

    private fun initArguments() {
        val bundle = arguments ?: return
        tabPosition = bundle.getInt(KtConstants.P_TAB_POSITION)
        extension = bundle.getString(KtConstants.P_EXTENSION)
        path = bundle.getString(KtConstants.P_PATH)
    }

    override fun initView(view: View) {
        initArguments()
        rootView = view.findViewById(R.id.root_view)
        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        sortEntryView = getMyDocumentFragment()?.sortEntryView
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC))
    }

    override fun initData(savedInstanceState: Bundle?) {
        sortMode = SortModeUtils.getSharedSortMode(ContextGetter.context, SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC))
        isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC))
        initRecyclerView()
        view?.post {
            if (isAdded.not()) return@post
            if (isCurrentFragment()) {
                Log.d(TAG, "position=$tabPosition")
                onResumeLoadData()
            }
        }
    }

    private fun initRecyclerView() {
        val activity = baseVMActivity ?: return
        fragmentRecyclerView?.let {
            adapter = DocumentListAdapter(activity, this, <EMAIL>)
            adapter?.setHasStableIds(true)
            it.adapter = adapter

            it.isNestedScrollingEnabled = true
            it.clipToPadding = false
            layoutManager = GridLayoutManager(baseVMActivity, 1).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        return 1
                    }
                }
            }
            it.layoutManager = layoutManager
            it.itemAnimator = mFolderTransformAnimator
            it.itemAnimator?.apply {
                changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
            }

            toolbar?.post {
                if (!isAdded) {
                   return@post
                }
                val paddingBottom = if (it.paddingBottom == 0) {
                    getMyDocumentFragment()?.getRecyclerViewPaddingBottom() ?: 0
                } else {
                    it.paddingBottom
                }
                val appbar = getMyDocumentFragment()?.appBarLayout
                val paddingTop = KtViewUtils.getRecyclerViewTopPadding(appbar, 0)
                it.updatePadding(top = paddingTop, bottom = paddingBottom)
            }
        }
    }

    override fun createViewModel(): DocumentViewModel? {
        val vm = ViewModelProvider(this)[tabPosition.toString(), DocumentViewModel::class.java]
        fileOperateController = Injector.injectFactory<INormalFileOperateController>(lifecycle, vm)
        vm.fileOperateController = fileOperateController
        return vm
    }

    override fun startObserve() {
        fragmentRecyclerView?.post {
            if (isAdded) {
                startListSelectModeObserver()
                startUIDataStateObserver()
                startObserveLoadState()
                startObserverClickPreviewFile()
            }
        }
    }

    private fun startObserveLoadState() {
        activity?.let {
            loadingController = LoadingController(it, this).apply {
                observe(fragmentViewModel?.dataLoadState, rootView) {
                    (fragmentViewModel?.getRealFileSize() ?: 0) > 0
                }
            }
        }
    }

    private fun startListSelectModeObserver() {
        val viewModule = fragmentViewModel ?: return
        viewModule.modeState.listModel.observe(this, object : Observer<Int> {
            override fun onChanged(value: Int) {
                if (!viewModule.modeState.initState) {
                    toolbar?.setTag(R.id.toolbar_animation_id, true)
                    return
                }
                val myDocumentFragment = getMyDocumentFragment() ?: run {
                    Log.d(TAG, "startListSelectModeObserver: parent is null")
                    return
                }
                Log.d(TAG, "startListSelectModeObserver: listModel=$value")
                val selectModel = (value == KtConstants.LIST_SELECTED_MODE)
                adapter?.setSelectEnabled(selectModel)
                if (selectModel) {
                    myDocumentFragment.disableViewPager()
                }
                if (selectModel) {
                    myDocumentFragment.showNavigation()
                    myDocumentFragment.setNavigateItemAble((viewModule.uiState.value?.selectedList?.size ?: 0) > 0)
                } else {
                    myDocumentFragment.hideNavigation()
                }
                myDocumentFragment.previewOperateInstance?.onListSelectModeChange(value)
                fragmentRecyclerView?.let {
                    val navigationView: View? = myDocumentFragment.navigationView
                    val bottom = if (selectModel) {
                        KtViewUtils.getSelectModelPaddingBottom(it, navigationView)
                    } else {
                        myDocumentFragment.getRecyclerViewPaddingBottom()
                    }
                    it.updatePadding(bottom = bottom)
                    fragmentFastScroller?.apply { trackMarginBottom = bottom }
                }
                toolbar?.let {
                    val isShowAnimation = (it.getTag(R.id.toolbar_animation_id) == true) && myDocumentFragment.canShowToolBarAnimation()
                    myDocumentFragment.changeActionModeAnim(it, {
                        if (selectModel) {
                            tabActivityListener?.initToolbarSelectedMode(
                                true,
                                viewModule.getRealFileSize(),
                                viewModule.uiState.value?.selectedList?.size ?: 0,
                                viewModule.getSelectItems())
                        } else {
                            val empty = (viewModule.uiState.value?.fileList?.isNotEmpty() != true)
                            tabActivityListener?.initToolbarNormalMode(true, empty)
                        }
                        myDocumentFragment.previewOperateInstance?.onToolbarMenuUpdated()
                    }, isShowAnimation = isShowAnimation)
                    it.setTag(R.id.toolbar_animation_id, true)
                }
                //刷新下
                val groupFileList = viewModule.uiState.value?.fileList as? ArrayList<BaseFileBean> ?: ArrayList()
                val selectList = viewModule.uiState.value?.selectedList ?: ArrayList()
                adapter?.setData(groupFileList, selectList, viewModule.isLastOpenTimeOrder, isForceFullRefresh())
            }
        })
    }

    private fun startUIDataStateObserver() {
        val viewModule = fragmentViewModel ?: return
        viewModule.uiState.observe(this) { uiModel ->
            Log.d(TAG, "fragmentViewModel mUiState: total=${uiModel.fileList.size},"
                        + "select=${uiModel.selectedList.size}, keyword=${uiModel.keyWord}")
            val fileSize = viewModule.getRealFileSize()
            getMyDocumentFragment()?.updateSortEntryView(fileSize, tabPosition)
            val empty = (viewModule.uiState.value?.fileList?.isNotEmpty() != true)
            if (empty) {
                showEmptyView()
            } else {
                hasShowEmpty = false
                fileEmptyController?.hideFileEmptyView()
            }
            updateToolbar(uiModel)
            getMyDocumentFragment()?.previewOperateInstance?.onListDataChange(
                uiModel.stateModel.listModel.value ?: KtConstants.LIST_NORMAL_MODE
            )
            if (uiModel.fileList is ArrayList<BaseFileBean>) {
                mFolderTransformAnimator.mIsFolderInAnimation = viewModule.isFolderBrowser
                adapter?.setData(
                    uiModel.fileList as ArrayList<BaseFileBean>,
                    uiModel.selectedList,
                    viewModule.isLastOpenTimeOrder,
                    isForceFullRefresh()
                )
            }
        }
    }

    private fun isForceFullRefresh(): Boolean {
        val tmpSortMode = SortModeUtils.getSharedSortMode(ContextGetter.context, SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC))
        val tmpIsDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC))
        if ((sortMode == tmpSortMode && isDesc == tmpIsDesc)) {
            return false
        } else {
            sortMode = tmpSortMode
            isDesc = tmpIsDesc
            return true
        }
    }

    private fun updateToolbar(uiModel: BaseUiModel<BaseFileBean>) {
        val viewModule = fragmentViewModel ?: return
        val fileSize = viewModule.getRealFileSize()
        if (uiModel.stateModel.listModel.value == KtConstants.LIST_SELECTED_MODE) {
            tabActivityListener?.initToolbarSelectedMode(false, fileSize, uiModel.selectedList.size, viewModule.getSelectItems())
            getMyDocumentFragment()?.setNavigateItemAble(uiModel.selectedList.isNotEmpty())
        } else {
            val empty = (viewModule.uiState.value?.fileList?.isNotEmpty() != true)
            tabActivityListener?.initToolbarNormalMode(false, empty)
        }
    }

    private fun startObserverClickPreviewFile() {
        getMyDocumentFragment()?.previewOperateInstance?.observerClickPreviewFile { file ->
            adapter?.setPreviewClickedFile(file)
        }
    }

    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    override fun onItemClick(item: ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        return fragmentViewModel?.onItemClick(getMyDocumentFragment(), item, e) ?: true
    }

    private fun showEmptyView() {
        if (hasShowEmpty) return
        if ((baseVMActivity != null) && (rootView != null)) {
            fileEmptyController?.showFileEmptyView(baseVMActivity!!, rootView!!, null, null)
            hasShowEmpty = true
        }
        fileEmptyController?.setFileEmptyTitle(andes.oplus.documentsreader.core.common.R.string.empty_file)
        Log.d(TAG, "showEmptyView")
    }

    override fun onResumeLoadData() {
        Log.d(TAG, "onResumeLoadData tabPosition = $tabPosition")
        if (!isAdded) {
            Log.e(TAG, "onResumeLoadData fragment don't add")
            return
        }
        fragmentViewModel?.initLoader(
            LoaderViewModel.getLoaderController(parentFragment ?: this),
            tabPosition,
            extension,
            path
        )
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        if (hasShowEmpty) return
        if (fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == true) {
            showEmptyView()
        }
    }

    fun onMenuItemSelected(item: MenuItem?): Boolean {
        if ((null == item) || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }

        return when (item.itemId) {
            android.R.id.home -> {
                if (getMyDocumentFragment()?.pressBack() != true) {
                    getMyDocumentFragment()?.activity?.onBackPressed()
                }
                true
            }
            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(context as Activity)
                true
            }
            R.id.actionbar_edit -> {
                fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                true
            }
            R.id.navigation_sort -> {
                showSortPopUp()
                true
            }
            R.id.actionbar_create_folder -> {
                activity?.let { ac -> fragmentViewModel?.currentPath?.let {
                    fileOperateController?.onCreateFolder(ac,
                        it
                    )
                } }
                true
            }
            R.id.action_setting -> {
                Injector.injectFactory<ISetting>()?.startSettingActivity(requireActivity())
                true
            }
            R.id.action_select_all -> {
                fragmentViewModel?.clickToolbarSelectAll()
                true
            }
            R.id.action_select_cancel -> {
                if (fragmentViewModel?.modeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                    fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                }
                true
            }
            else -> false
        }
    }

    private fun showSortPopUp() {
        baseVMActivity?.let {
            val tempSort = if (fragmentViewModel?.isLastOpenTimeOrder == true) {
                SortHelper.FILE_LAST_OPEN_TIME_ORDER
            } else {
                -1
            }
            sortPopupController.showSortPopUp(it, tempSort, R.id.sort_entry_anchor,
                SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC), object : SelectItemListener {
                    override fun onDismiss() {
                        sortEntryView?.rotateArrow()
                    }

                    override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                        if (flag) {
                            fragmentViewModel?.isLastOpenTimeOrder = false
                            sortEntryView?.setSortOrder(sortMode, isDesc)
                            fragmentViewModel?.sortReload()
                        }
                    }
                })
        }
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        if (pause) {
            Log.d(TAG, "already pause, return")
            return false
        }
        return activity?.let {
            fileOperateController?.onNavigationItemSelected(it, item)
        } ?: false
    }

    fun setToolbarNew(toolbarParam: COUIToolbar?) {
        toolbar = toolbarParam
    }

    fun setTabActivityListener(tabListener: TabActivityListener<BaseFileBean>) {
        tabActivityListener = tabListener
    }

    private fun isCurrentFragment(): Boolean {
        val fragment = getMyDocumentFragment() ?: return false
        return fragment.getCurrentTabPosition() == tabPosition
    }

    private fun getMyDocumentFragment(): MyDocumentFragment? {
        return parentFragment as? MyDocumentFragment
    }

    fun isInSelectMode(): Boolean {
        return fragmentViewModel?.isInSelectMode() == true
    }

    override fun pressBack(): Boolean {
        return fragmentViewModel?.pressBack() ?: false
    }

    override fun fromSelectPathResult(requestCode: Int, path: String?) {
        activity?.let { fileOperateController?.onSelectPathReturn(it, requestCode, path) }
        fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
    }

    fun getAdapter(): DocumentListAdapter? {
        return adapter
    }

    private fun getFileRealSize(): Int {
        val viewModule = fragmentViewModel ?: return 0
        return viewModule.getRealFileSize()
    }

    fun hasRealFile(): Boolean {
        return getFileRealSize() > 0
    }

    override fun getDropCategoryType(): Int? {
        return CategoryHelper.CATEGORY_MY_DOCUMENTS
    }

    fun refreshRecyclerView() {
        if (tabPosition != 0) {
            return
        }
        lifecycleScope.launch {
            Log.d(TAG, "refreshRecyclerView start")
            if (isAdded.not()) return@launch
            val myDocumentFragment = getMyDocumentFragment() ?: return@launch
            if (myDocumentFragment.isAdded.not()) return@launch
            val recyclerView = getRecyclerView() ?: return@launch
            delay(DELAY_REFRESH)
            val top = KtViewUtils.getRecyclerViewTopPadding(myDocumentFragment.appBarLayout, 0)
            val paddingBottom = if (recyclerView.paddingBottom == 0) {
                myDocumentFragment.getRecyclerViewPaddingBottom()
            } else {
                recyclerView.paddingBottom
            }
            recyclerView.updatePadding(top = top, bottom = paddingBottom)
            Log.d(TAG, "refreshRecyclerView = $top, bottom = $paddingBottom, canSmoothScrollToTop = $canSmoothScrollToTop")
            if (canSmoothScrollToTop) {
                recyclerView.fastSmoothScrollToTop()
            }
            disableSmoothScrollToTop()
        }
    }

    private fun disableSmoothScrollToTop() {
        canSmoothScrollToTop = false
    }

    fun enableSmoothScrollToTop() {
        canSmoothScrollToTop = true
    }
}