/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/06/06, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package andes.oplus.documentsreader.formatconvert.di

import andes.oplus.documentsreader.formatconvert.FormatApi
import andes.oplus.documentsreader.formatconvert.interfaces.IConvertProxyTask
import andes.oplus.documentsreader.formatconvert.interfaces.IFormatInterface
import andes.oplus.documentsreader.formatconvert.interfaces.IFormulaProxyTask
import andes.oplus.documentsreader.formatconvert.interfaces.INetConfig
import andes.oplus.documentsreader.formatconvert.task.ConvertProxyTask
import andes.oplus.documentsreader.formatconvert.task.FormulaProxyTask
import andes.oplus.documentsreader.formatconvert.util.NetConfig
import com.oplus.assistantscreen.mic.interfaces.KoinModuleMarker
import com.oplus.assistantscreen.mic.interfaces.MarkField
import org.koin.dsl.module

@KoinModuleMarker
class AutoDIForConvert {

    @MarkField
    val convertTask = module {
        factory<IConvertProxyTask> {
            ConvertProxyTask()
        }
    }

    @MarkField
    val formulaTask = module {
        factory<IFormulaProxyTask> {
            FormulaProxyTask()
        }
    }

    @MarkField
    val netConfig = module {
        factory<INetConfig> {
            NetConfig()
        }
    }

    @MarkField
    val formatModule = module {
        single<IFormatInterface> {
            FormatApi()
        }
    }
}