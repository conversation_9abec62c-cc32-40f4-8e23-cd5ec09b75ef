/***********************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File:   SearchAnimController
 * * Description: SearchAnim Controller
 * * Version:1.0
 * * Date :2024/8/16
 * * Author:chao.xue
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 ****************************************************************/
package andes.oplus.documentsreader.selectdoc.controller

import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.selectdoc.R
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.os.Build
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.KeyEvent
import android.view.MenuItem
import android.view.View
import android.view.animation.Interpolator
import android.view.animation.PathInterpolator
import android.view.inputmethod.EditorInfo
import android.widget.ImageView
import android.widget.TextView
import android.widget.TextView.OnEditorActionListener
import com.coui.appcompat.animation.COUILinearInterpolator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.searchview.COUISearchBar
import com.coui.appcompat.searchview.COUISearchBar.TYPE_INSTANT_SEARCH
import com.coui.appcompat.toolbar.COUIToolbar

class SearchAnimController(private var activity: Activity?, private var backgroundMask: View?, private var searchView: COUISearchBar?) {

    companion object {
        private const val TAG = "SearchAnimController"
        private const val CONTROL_X_0_3 = 0.3f
        private const val CONTROL_X_0_9 = 0.9f
        private const val TRANSLATE_UP_DURATION = 250L
        private const val FADE_DURATION = 150
        private const val SEARCHBAR_DISMISS_DURATION = 200L
        private const val PROPERTY_ALPHA = "alpha"
    }

    private var queryTextCleared = false
    private var inSearch = false

    private var maskAlphaEnterAnimator: ObjectAnimator? = null
    private var maskAlphaExitAnimator: ObjectAnimator? = null
    private var cubicBezierEnterInterpolator: Interpolator? = null
    private var cubicBezierExitInterpolator: Interpolator? = null

    private var searchListener: SearchListener? = null
    private val onQueryTextListener = object : TextWatcher, OnEditorActionListener {

        override fun onEditorAction(textView: TextView?, actionId: Int, event: KeyEvent?): Boolean {
            Log.d(TAG, "onEditorAction actionId:$actionId event:$event")
            if (EditorInfo.IME_ACTION_SEND == actionId || EditorInfo.IME_ACTION_SEARCH == actionId) {
                searchListener?.onSearchSubmit(textView?.text.toString())
                return true
            }
            return false
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(newText: CharSequence?, start: Int, before: Int, count: Int) {
            newText?.let { searchListener?.onSearch(it.toString()) }
        }

        override fun afterTextChanged(s: Editable?) {
            searchView?.quickDeleteButton?.contentDescription =
                ContextGetter.context.getString(R.string.oplus_talkback_des_clear_btn)
        }
    }

    init {
        initSearchAnim()
    }

    @SuppressLint("ObjectAnimatorBinding")
    private fun initSearchAnim() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            cubicBezierEnterInterpolator = COUIMoveEaseInterpolator()
            cubicBezierExitInterpolator = PathInterpolator(CONTROL_X_0_3, 0.0f, CONTROL_X_0_9, 1.0f)
        } else {
            cubicBezierEnterInterpolator = COUILinearInterpolator()
            cubicBezierExitInterpolator = COUILinearInterpolator()
        }
        maskAlphaEnterAnimator = ObjectAnimator.ofFloat(backgroundMask, PROPERTY_ALPHA, 0f, 1f)
            ?.apply {
                duration = FADE_DURATION.toLong()
                interpolator = cubicBezierEnterInterpolator
                addUpdateListener {
                    backgroundMask?.visibility = View.VISIBLE
                }
            }
        maskAlphaExitAnimator = ObjectAnimator.ofFloat(backgroundMask, PROPERTY_ALPHA, 1f, 0f)
            ?.apply {
                duration = FADE_DURATION.toLong()
                interpolator = cubicBezierExitInterpolator
                addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        backgroundMask?.visibility = View.GONE
                    }
                })
            }
    }


    fun initSearchBar(toolbar: COUIToolbar?, item: MenuItem, searchListener: SearchListener) {
        Log.d(TAG, "initSearchBar toolbar:$toolbar item:$item")
        this.searchListener = searchListener
        searchView?.apply {
            setSearchAnimateType(TYPE_INSTANT_SEARCH)
            functionalButton.setOnClickListener {
                searchListener.onExitSearch()
            }
            searchEditText.setHint(R.string.search_hide_text)
            findViewById<ImageView>(com.support.toolbar.R.id.animated_search_icon)?.contentDescription =
                activity?.getString(R.string.search_hide_text)
            setAtBehindToolBar(toolbar, Gravity.TOP, item)

            searchEditText?.apply {
                addTextChangedListener(onQueryTextListener)
                setOnEditorActionListener(onQueryTextListener)
            }
        }
    }

    fun getQueryText(): CharSequence? {
        return searchView?.searchEditText?.text
    }

    fun setQueryText(queryText: String) {
        searchView?.searchEditText?.setText(queryText)
        setSelectionPosition()
    }

    private fun setSelectionPosition() {
        searchView?.searchEditText?.setSelection(getQueryText()?.length ?: 0)
    }

    fun animateSearch() {
        Log.d(TAG, "animateSearch")
        if (maskAlphaEnterAnimator?.isRunning == true || maskAlphaExitAnimator?.isRunning == true) {
            Log.e(TAG, "animateSearch anim is running")
            return
        }
        inSearch = true
        searchView?.showInToolBar()
        queryTextCleared = false
        maskAlphaEnterAnimator?.start()
        searchView?.postDelayed({
            activity?.window?.navigationBarColor = COUIContextUtil.getColor(activity, com.support.appcompat.R.color.coui_transparence)
        }, TRANSLATE_UP_DURATION)
    }


    fun animateBack() {
        Log.d(TAG, "animateBack")
        activity?.window?.navigationBarColor = COUIContextUtil.getAttrColor(activity, com.support.appcompat.R.attr.couiColorBackground)
        if (maskAlphaEnterAnimator?.isRunning == true || maskAlphaExitAnimator?.isRunning == true || !inSearch) {
            Log.e(TAG, "animateBack anim is running")
            return
        }
        inSearch = false
        searchView?.hideInToolBar()
        queryTextCleared = true
        /**
         * since searchView becomes View.GONE after 150ms animation in hideInToolBar(), we need to
         * make sure setQuery() run after the searchView became GONE or SearchView will call the keyboard to show
         */
        searchView?.apply {
            postDelayed({
                searchEditText?.text = null
            }, SEARCHBAR_DISMISS_DURATION)
        }
        maskAlphaExitAnimator?.start()
    }

    /**
     * 是否进入了搜索模式
     */
    fun isInSearchMode(): Boolean = inSearch

    fun release() {
        maskAlphaEnterAnimator?.cancel()
        maskAlphaExitAnimator?.cancel()
    }

    fun hideKeyboard() {
        searchView?.apply {
            clearFocus()
            openSoftInput(false)
        }
    }
}