/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FilePathHelper
 ** Description : 文件的路径接口
 ** Version     : 1.0
 ** Date        : 2023/12/14 15:50
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2023/12/14       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.core.common.path

import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.R
import andes.oplus.documentsreader.core.common.utils.PathUtils
import android.text.TextUtils
import java.io.File
import java.util.LinkedList

abstract class FilePathHelper(currentPath: String) {
    companion object {
        const val TAG = "FilePathHelper"
        const val PREVIEW_ROOT_PATH = ""
    }

    private var mRootPath: String? = null
    private var mRootPathInfo: PathInfo? = null
    private val mPathStack = LinkedList<PathInfo>()

    init {
        updateRootPath(currentPath)
        Log.d(TAG, "initRootPath mRootPathInfo=$mRootPathInfo")
    }

    fun updateRootPath(currentPath: String) {
        mPathStack.clear()
        val rootInternalPath = getInternalPath()
        val rootExternalPath = getExternalPath()
        val rootOtgPath = getOtgPath()
        val rootDfmPath = getDfmRootPath()
        var isSetRoot = false
        if ((rootInternalPath != null) && (currentPath.startsWith(rootInternalPath))) {
            mRootPathInfo = PathInfo(rootInternalPath)
            mRootPath = rootInternalPath
            isSetRoot = true
        } else if ((rootExternalPath != null) && (currentPath.startsWith(rootExternalPath))) {
            mRootPathInfo = PathInfo(rootExternalPath)
            mRootPath = rootExternalPath
            isSetRoot = true
        } else if (PathUtils.checkIsMultiAppPath(currentPath)) {
            mRootPathInfo = PathInfo(KtConstants.LOCAL_VOLUME_MULTI_APP_PATH)
            mRootPath = KtConstants.LOCAL_VOLUME_MULTI_APP_PATH
            isSetRoot = true
        } else if (rootDfmPath != null && PathUtils.checkIsDfmPath(currentPath)) {
            mRootPathInfo = PathInfo(rootDfmPath)
            mRootPath = rootDfmPath
            isSetRoot = true
        } else {
            rootOtgPath?.let {
                it.forEach {
                    if (currentPath.startsWith(it)) {
                        mRootPathInfo = PathInfo(it)
                        mRootPath = it
                        isSetRoot = true
                    }
                }
            }
        }
        if (!isSetRoot) {
            mRootPathInfo = PathInfo(PREVIEW_ROOT_PATH)
            mRootPath = currentPath
        }
        Log.d(TAG, "updateRootPath mRootPathInfo=$mRootPathInfo mRootPath=$mRootPath")
        mPathStack.push(mRootPathInfo)
    }

    /**
     * 判断是否用于本地文件
     */
    open fun isLocalFile(): Boolean = true

    /**
     * 获取外部存储sd卡的路径
     */
    abstract fun getExternalPath(): String?

    /**
     * 获取内部存储sd卡的路径
     */
    abstract fun getInternalPath(): String?

    /**
     * 获取otg的路径
     */
    abstract fun getOtgPath(): List<String>?

    /**
     * 获取分布式文管DFM的路径
     */
    abstract fun getDfmRootPath(): String?

    /**
     * 获取分布式文管DFM的设备名称
     */
    abstract fun getDfmDeviceName(): String?

    /**
     * 判断是否是外部存储sd卡的根路径
     */
    abstract fun isRootExternalPath(path: String): Boolean

    /**
     * 判断是否是内部存储sd卡的根路径
     */
    abstract fun isRootInternalPath(path: String): Boolean

    /**
     * 判断是否是otg的根路径
     */
    abstract fun isRootOtgPath(path: String): Boolean

    /**
     * 判断是否是DFM的根路径
     */
    abstract fun isRootDfmPath(path: String): Boolean

    fun getRootPath(): String? {
        return mRootPath
    }

    fun getCurrentDirectoryName(currentPath: String): String? {
        var currentPathName = currentPath
        if (TextUtils.isEmpty(currentPathName)) {
            return null
        }
        val res = ContextGetter.context.getResources()

        if ((currentPath == getInternalPath())) {
            currentPathName = res.getString(R.string.device_storage)
        } else if ((currentPath == KtConstants.LOCAL_VOLUME_MULTI_APP_PATH)) {
            currentPathName = res.getString(R.string.device_storage)
        } else if (currentPath.equals(getExternalPath())) {
            currentPathName = res.getString(R.string.storage_external)
        } else if (isRootOtgPath(currentPath)) {
            currentPathName = res.getString(R.string.storage_otg)
        } else if (isRootDfmPath(currentPath) && getDfmDeviceName() != null) {
            currentPathName = getDfmDeviceName()!!
        } else {
            val index = currentPathName.lastIndexOf(File.separator)
            if (index != -1) {
                currentPathName = currentPathName.substring(index)
            }
            currentPathName = currentPathName.replaceFirst(File.separator.toRegex(), "")
        }
        return currentPathName
    }

    fun pop(): PathInfo? {
        if (hasUp()) {
            return mPathStack.pop() ?: return mRootPathInfo
        } else {
            return null
        }
    }

    /**
     * Push a path info to stack, and the specified path info's position will be
     * set to the last path's position.
     *
     * @param path
     * @return how many path info in stack.
     */
    fun push(path: PathInfo?): Int {
        if (path != null) {
            // Updating the location of the last directory.
            val tempPath = mPathStack.peek()
            if (null != tempPath) {
                tempPath.position = path.position
                tempPath.y = path.y
            }
            if (!mPathStack.contains(path)) {
                mPathStack.push(path)
            }
        }
        return mPathStack.size
    }

    /**
     * 获取上一级的路径
     */
    abstract fun getParentPath(path: String): String

    fun pushTo(path: String, extra: String = "") {
        if ((mRootPath != null) && !isRootPath(path) && path.startsWith(mRootPath!!)) {
            mPathStack.clear()
            mPathStack.addLast(PathInfo(path, extra))
            if (isRootExternalPath(path) || isRootInternalPath(path) || isRootMultiAppPath(path)) {
                mPathStack.addLast(PathInfo(mRootPath!!))
                return
            }
            var parent = getParentPath(path)

            var flag = false
            while (!isRootPath(parent) && parent.isNotEmpty()) {
                mPathStack.addLast(PathInfo(parent))
                if (isRootExternalPath(parent) || isRootInternalPath(parent) || isRootMultiAppPath(parent)) {
                    if (isRootPath(parent)) {
                        break
                    } else {
                        parent = getParentPath(mRootPath!!)
                    }
                } else {
                    parent = getParentPath(parent)
                }
                if (parent.isEmpty()) {
                    flag = true
                    break
                }
            }
            if (!flag && isRootPath(parent)) {
                mPathStack.addLast(PathInfo(parent))
            }
        } else if (isRootPath(path)) {
            mPathStack.clear()
            mPathStack.addLast(PathInfo(path, extra))
        }
    }

    /**
     * Search the path info of the specified index, and remove all path info
     * before it.
     *
     * @param index
     * @return The path info of set, or null if the path it's not found in the
     * path stack.
     */
    fun setTopPath(index: Int): PathInfo? {
        var index = index
        val size = mPathStack.size
        if (index != -1 && index < size) {
            while (index-- > 0) {
                mPathStack.removeAt(0)
            }
            return mPathStack.first
        }
        return null
    }

    /**
     * Search the path info of the specified index, and remove all path info
     * after it.
     *
     * @param index
     * @return The path info of set, or null if the path it's not found in the
     * path stack.
     */
    fun setCurrentPathIndex(index: Int) {
        val size = mPathStack.size
        var decreaseCount = size - (index + 1)
        if (decreaseCount > 0 && decreaseCount < size) {
            while (decreaseCount-- > 0) {
                mPathStack.removeAt(0)
            }
        }
    }

    /**
     * Fetch the specified index path info.
     *
     * @param index
     * @return the specified index path info, or null if the index great than or
     * equals the stack size.
     */
    fun getPath(index: Int): PathInfo? {
        val size = mPathStack.size
        return if (index >= 0 && index < size) {
            mPathStack[index]
        } else {
            null
        }
    }


    fun getCount(): Int {
        return mPathStack.size
    }

    fun getPathLeft(): Int {
        return mPathStack.size
    }

    /**
     * Retrieves, but does not remove, the head (first path info) of this list.
     *
     * @return the top path info of the path stack, if no path in stack ,return
     * the root path.
     */
    fun getTopPathInfo(): PathInfo? {
        return mPathStack.peek() ?: return getRootPathInfo()
    }

    fun getRootPathInfo(): PathInfo? {
        return mRootPathInfo
    }

    /**
     * Whether have path info in stack, exclude the root path.
     *
     * @return true if has, otherwise return false.
     */
    fun hasUp(): Boolean {
        // Decrease the root path elements.
        return mPathStack.size - 1 > 0
    }

    fun isRootPath(path: String): Boolean {
        return mRootPath?.equals(path) ?: false
    }

    fun getCurrentShowPath(): String? {
        return getTopPathInfo()?.path
    }

    fun isRootMultiAppPath(path: String): Boolean {
        return path == KtConstants.LOCAL_VOLUME_MULTI_APP_PATH
    }


    open class PathInfo(private var mPath: String) {
        /**
         * The last list view position.
         */
        /**
         * Return the last list view position.
         */
        var position: Int = 0
        var y = 0

        /**
         * 额外的信息
         */
        var extra: String = ""

        var path: String
            get() = mPath
            set(path) {
                this.mPath = path
            }

        constructor(path: String, position: Int, y: Int) : this(path) {
            this.position = position
            this.y = y
        }

        constructor(path: String, extra: String, position: Int, y: Int) : this(path) {
            this.extra = extra
            this.position = position
            this.y = y
        }

        constructor(path: String, extra: String) : this(path) {
            this.extra = extra
        }

        override fun hashCode(): Int {
            val prime = 31
            var result = 1
            result = prime * result + mPath.hashCode()
            result = prime * result + position
            return result
        }

        override fun equals(obj: Any?): Boolean {
            if (this === obj) {
                return true
            }
            if (obj == null) {
                return false
            }
            if (javaClass != obj.javaClass) {
                return false
            }
            val other = obj as? PathInfo
            if (mPath == null) {
                if (other?.mPath != null) {
                    return false
                }
            } else if (mPath != other?.mPath) {
                return false
            }
            return true
        }

        override fun toString(): String {
            return "PathInfo(mPath='$mPath', extra=$extra, position=$position, y=$y)"
        }
    }
}