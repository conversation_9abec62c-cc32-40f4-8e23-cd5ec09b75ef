/*********************************************************************************
 * Copyright (C), 2008-2024, Oplus, All rights reserved.
 *
 * File: - OplusDocThumbnailErrors.kt
 * Description:
 *     The error codes when load doc thumbnail failed.
 *
 * Version: 1.0
 * Date: 2024-12-16
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-12-16   1.0    Create this module
 *********************************************************************************/
package com.oplus.documentsreader.thumbnail

import androidx.annotation.IntDef

@IntDef(
    OplusDocThumbnailErrors.ERROR_UNKNOWN,
    OplusDocThumbnailErrors.ERROR_UNSUPPORTED,
    OplusDocThumbnailErrors.ERROR_LOAD_FAILED,
    OplusDocThumbnailErrors.ERROR_TIMEOUT,
    OplusDocThumbnailErrors.ERROR_NO_CONNECTION,
    OplusDocThumbnailErrors.ERROR_DIED_CONNECTION,
    OplusDocThumbnailErrors.ERROR_RELEASED_LOADER,
)
@Retention(AnnotationRetention.SOURCE)
annotation class OplusDocThumbnailErrors {
    companion object {
        /**
         * 未知加载错误
         */
        const val ERROR_UNKNOWN = 2000

        /**
         * 不支持加载文档缩略图
         */
        const val ERROR_UNSUPPORTED = 2001

        /**
         * 加载文档缩略图失败
         */
        const val ERROR_LOAD_FAILED = 2002

        /**
         * 加载文档缩略图有超时
         */
        const val ERROR_TIMEOUT = 2003

        /**
         * 无法与文档缩略图能力导出服务建立连接
         */
        const val ERROR_NO_CONNECTION = 2004

        /**
         * 与文档缩略图能力导出服务的连接异常终止
         */
        const val ERROR_DIED_CONNECTION = 2005

        /**
         * Loader已被释放后调用
         */
        const val ERROR_RELEASED_LOADER = 2006
    }
}