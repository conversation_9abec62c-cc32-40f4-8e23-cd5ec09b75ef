/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - CountRecentFileAction.kt
 * Description:
 *     The action for query the count of recently opened files.
 *
 * Version: 1.0
 * Date: 2024-06-24
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-06-24   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.recent.actions

import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.recent.database.RecentFileDBHelper
import android.content.Context
import kotlinx.coroutines.CoroutineScope

internal class CountRecentFileAction(actionScope: CoroutineScope) :
    AbsRecentFileAction<Unit, Long>(actionScope), INoParamRecentFileAction<Long> {

    override val actionName: String = TAG

    override suspend fun onExecuteAction(context: Context, param: Unit): Long {
        val dbHelper = RecentFileDBHelper(context)
        val count = dbHelper.getFileRecordCount()
        Log.d(TAG, "onExecuteAction: count=$count")
        return count
    }

    private companion object {
        private const val TAG = "CountRecentFileAction"
    }
}