/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SellModeApi
 Description:
 Version: 1.0
 Date: 2022/12/13
 Author: W9013333(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/12/13 1.0 create
 */

package com.soundrecorder.sellmode

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.sellmode.SellModeService.Companion.checkAndStartSellModeService

class SellModeReceiver : BroadcastReceiver() {
    companion object {
        private const val TAG = "SellModeReceiver"
        private const val REQUEST_RESTORE_DATA = "com.oplus.daydreamvideo.REQUEST_RESTORE_DATA"
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent == null) {
            return
        }
        /**
         * 卖场模式预置数据广播:om.oplus.daydreamvideo.REQUEST_RESTORE_DATA
         * 录音无自启动权限时，录音进程不存活时，接收不到广播消息
         */
        val action = intent.action
        DebugUtil.i(TAG, "sell mode receiver,action = $action")
        if (REQUEST_RESTORE_DATA == action) {
            context.applicationContext.checkAndStartSellModeService()
        }
    }
}