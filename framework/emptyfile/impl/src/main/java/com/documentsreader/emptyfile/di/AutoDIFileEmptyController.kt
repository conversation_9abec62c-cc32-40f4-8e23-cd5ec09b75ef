/*********************************************************************
 * * Copyright (C), 2024, OPlus. All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/6/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.documentsreader.emptyfile.di

import com.documentsreader.emptyfile.FileEmptyController
import com.documentsreader.emptyfile.IFileEmptyController
import com.oplus.assistantscreen.mic.interfaces.KoinModuleMarker
import com.oplus.assistantscreen.mic.interfaces.MarkField
import org.koin.dsl.module

@KoinModuleMarker
class AutoDIFileEmptyController {
    @MarkField
    val icpRegistrationManager = module {
        factory<IFileEmptyController> {
            FileEmptyController()
        }
    }
}