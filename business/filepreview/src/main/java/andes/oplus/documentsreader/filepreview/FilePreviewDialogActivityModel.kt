/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FilePreviewDialogActivityModel
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/2/21 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  wanglei        2024/2/21       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.filepreview

import andes.oplus.documentsreader.core.cloudconfig.interfaze.ICloudConfigManager
import andes.oplus.documentsreader.core.common.BaseViewModel
import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.FileMediaHelper
import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.IntentUtils
import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.KtConstants.FLOATING_OPEN_TYPE_AUDIO
import andes.oplus.documentsreader.core.common.KtConstants.FLOATING_OPEN_TYPE_IMAGE
import andes.oplus.documentsreader.core.common.KtConstants.FLOATING_OPEN_TYPE_VIDEO
import andes.oplus.documentsreader.core.common.KtConstants.ONE_DAY
import andes.oplus.documentsreader.core.common.KtConstants.PREVIEW_TYPE_ARCHIVE
import andes.oplus.documentsreader.core.common.KtConstants.PREVIEW_TYPE_AUDIO
import andes.oplus.documentsreader.core.common.KtConstants.PREVIEW_TYPE_DOC
import andes.oplus.documentsreader.core.common.KtConstants.PREVIEW_TYPE_IMAGE
import andes.oplus.documentsreader.core.common.KtConstants.PREVIEW_TYPE_PRO_DOC
import andes.oplus.documentsreader.core.common.KtConstants.PREVIEW_TYPE_TXT
import andes.oplus.documentsreader.core.common.KtConstants.PREVIEW_TYPE_VIDEO
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.MimeTypeHelper
import andes.oplus.documentsreader.core.common.PreferencesUtils
import andes.oplus.documentsreader.core.common.UriHelper
import andes.oplus.documentsreader.core.common.utils.HashUtils
import andes.oplus.documentsreader.filepreview.bean.CopyState
import andes.oplus.documentsreader.filepreview.bean.PreviewFileBean
import andes.oplus.documentsreader.filepreview.util.PreviewFileTypeUtils
import andes.oplus.documentsreader.interfaces.IUserStatementManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import java.io.File

class FilePreviewDialogActivityModel : BaseViewModel() {
    private val _previewDestination = MutableSharedFlow<PreviewDestinationState>(replay = 1)
    val previewDestination: SharedFlow<PreviewDestinationState> = _previewDestination.asSharedFlow()


    private val _copyState = MutableSharedFlow<CopyState>()
    val copyState: SharedFlow<CopyState> = _copyState.asSharedFlow()
    private val userStatementManager by lazy {
        Injector.injectFactory<IUserStatementManager>()
    }

    /**
     * 初始化FileBean
     * @param activity FilePreviewDialogActivity
     * @param fileOriUri Uri
     * @param intent Intent?
     * @return FileBean?
     */
    fun getFileAttr(
        activity: FilePreviewDialogActivity,
        fileOriUri: Uri,
        intent: Intent?
    ): PreviewFileBean? {
        //从Intent去读fileName、fileSize、fileLastModifyTime
        var fileName = IntentUtils.getString(intent, KtConstants.FILE_PREVIEW_NAME) ?: ""
        var fileSize = IntentUtils.getLong(intent, KtConstants.FILE_PREVIEW_SIZE, -1)
        var fileLastModifyTime = IntentUtils.getLong(intent, KtConstants.FILE_MODIFY_TIME, 0L)
        Log.d(
            TAG, "getFileAttr from intent -> filename = $fileName; fileSize = $fileSize" +
                    "; modifyTime = $fileLastModifyTime"
        )

        //Intent如果没有传，就从URI去获取
        if (fileName.isBlank() || fileSize <= 0) {
            val fileProperty =
                FileMediaHelper.getFileProperty(ContextGetter.context, fileOriUri) ?: return null
            fileName = fileProperty.fileName
            fileSize = fileProperty.fileSize
            fileLastModifyTime = fileProperty.fileLastModify
        }

        //获取拷贝文件的目标文件夹路径
        val rootPath = getRootPath()
        if (rootPath.isNullOrEmpty()) {
            Log.e(TAG, "getFileAttr root path is null, do nothing")
            return null
        }

        //拷贝文件的目标文件路径
        val filePath = getFilePath(activity, rootPath, fileOriUri, fileName, fileSize, fileLastModifyTime)
        Log.d(TAG, "filePath = $filePath")
        val fileUri = UriHelper.getFileProviderUri(filePath) ?: return null
        val mimeType = MimeTypeHelper.getMimeTypeFromPath(filePath)
        var fromPackageName = IntentUtils.getString(intent, KtConstants.INTENT_FROM)
        if (fromPackageName.isNullOrEmpty()) {
            Log.d(TAG, "getFileAttr from intent -> fromPackageName not empty")
            fromPackageName = IntentUtils.getCallingAppInfo(activity)
        }
        val previewType = PreviewFileTypeUtils.getPreviewType(fileName, fileSize, mimeType)
        Log.d(TAG, "mimeType = $mimeType")

        val file = File(filePath)
        if (file.exists()) {
            fileLastModifyTime = file.lastModified()
            Log.d(TAG, "update last modify, fileLastModifyTime $fileLastModifyTime")
        }
        val sidebarParam = IntentUtils.getString(intent, KtConstants.PANEL_SHOW_INFO) ?: ""
        return PreviewFileBean(
            fileOriUri,
            filePath,
            fileUri,
            fileName,
            fileSize,
            mimeType,
            fileLastModifyTime,
            fromPackageName,
            previewType,
            KtConstants.PREVIEW_NO_ERROR,
            sidebarParam
        )
    }


    /**
     * 通过fileName,获取文件后缀名，来确定需要分发到哪个Fragment
     *
     * @param previewFileBean String
     * @return String
     */
    fun getPreviewDestination(previewFileBean: PreviewFileBean): Destination {
        Log.d(TAG, "getPreviewRoute fileBean.previewType:${previewFileBean.previewType}")
        if (previewFileBean.previewErrorType > 0) {
            //如果出现拷贝失败、文件损坏、文件过大等特殊原因均直接使用通用预览
            return NormalDestination()
        }
        return when (previewFileBean.previewType) {
            //如果大小超出限制、拷贝失败等都用normal
            PREVIEW_TYPE_ARCHIVE -> ArchiveDestination()
            PREVIEW_TYPE_AUDIO -> AudioDestination()
            PREVIEW_TYPE_DOC -> NormalDocDestination()
            PREVIEW_TYPE_PRO_DOC -> ProDocDestination()
            PREVIEW_TYPE_IMAGE -> ImageDestination()
            PREVIEW_TYPE_TXT -> TextDestination()
            PREVIEW_TYPE_VIDEO -> VideoDestination()
            FLOATING_OPEN_TYPE_AUDIO -> FloatOpenAudioDestination()
            FLOATING_OPEN_TYPE_VIDEO -> FloatOpenVideoDestination()
            FLOATING_OPEN_TYPE_IMAGE -> FloatOpenImageDestination()
            else -> NormalDestination()
        }
    }

    private fun getRootPath(): String? {
        return ContextGetter.context.externalCacheDir?.let {
                "${it.absolutePath}${File.separator}$TEMP_FILE_HIDE"
            }
    }

    /**
     * 拷贝文件的目标文件路径
     * @param rootPath String
     * @param uri Uri
     * @param fileName String
     * @param fileSize Long
     * @param fileLastModifyTime Long
     * @return String
     */
    private fun getFilePath(
        context: Context,
        rootPath: String,
        uri: Uri,
        fileName: String,
        fileSize: Long,
        fileLastModifyTime: Long
    ): String {
        val filePath = if (FileMediaHelper.isFileSchemeUri(uri)) {
            uri.path ?: ""
        } else {
            FileMediaHelper.getFilePath(context, uri) ?: ""
        }
        Log.d(TAG, "getFilePath filePath = $filePath")
        return if (File(filePath).exists()) {
            filePath
        } else if (FileMediaHelper.isFileSchemeUri(uri) && UriHelper.fileUriIsAndroidDataDir(uri).not()) {
            uri.path.toString()
        } else {
            val dirPath = "${uri}_${fileSize}_$fileLastModifyTime"
            val dirPathHash = HashUtils.hash(dirPath)
            "$rootPath${File.separator}$dirPathHash${File.separator}$fileName"
        }
    }

    /**
     * 更新云控配置的预览文件大小，间隔24小时
     */
    fun getCloudFileSizeLimit() {
        val lastUpdateTime = PreferencesUtils.getLong(
            key = KtConstants.PREVIEW_SIZE_EXCEED_LIMIT_UPDATE_TIME,
            default = -1L
        )
        if (System.currentTimeMillis() - lastUpdateTime < ONE_DAY) {
            Log.d(TAG, "getCloudFileSizeLimit:update time < 24hour")
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            val canUseNetwork = userStatementManager?.checkCanUseNetwork(ContextGetter.context) ?: true
            if (canUseNetwork) {
                Injector.injectFactory<ICloudConfigManager>()?.initCloudConfig()
            }
        }
    }

    /**
     * 分发到对应的预览类型
     * @param previewDestination Destination
     */
    fun initNavigationInfo(previewDestination: Destination) {
        viewModelScope.launch {
            Log.e(TAG, "initNavigationInfo -> 发送destination = $previewDestination")
            _previewDestination.emit(
                PreviewDestinationState(
                    previewDestination.route,
                    previewDestination
                )
            )
        }
    }

    /**
     * 拷贝状态
     * @param copyResult Boolean
     */
    fun updateCopyState(copyResult: Boolean) {
        viewModelScope.launch {
            Log.d(TAG, "updateCopyState: emit copy result $copyResult ")
            _copyState.emit(CopyState(copyResult))
        }
    }

    companion object {
        private const val TAG = "FilePreviewDialogActivityModel"
        private const val TEMP_FILE_HIDE = ".tempfile"

        val Factory: ViewModelProvider.Factory = object : ViewModelProvider.Factory {
            @Suppress("UNCHECKED_CAST")
            override fun <T : ViewModel> create(modelClass: Class<T>): T {
                return FilePreviewDialogActivityModel() as T
            }
        }
    }
}