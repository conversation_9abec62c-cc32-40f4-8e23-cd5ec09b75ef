/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - NormalPreviewFragment.kt
 * Description:
 *     The default file preview fragment for all file types.
 *
 * Version: 1.0
 * Date: 2024-10-17
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-10-17   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.sidepreview.normal

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.sidepreview.core.AbsPreviewFragment
import andes.oplus.documentsreader.sidepreview.core.FilePreviewViewModel
import andes.oplus.documentsreader.sidepreview.impl.R
import andes.oplus.documentsreader.sidepreview.utils.PreviewScrollAreaHelper
import andes.oplus.documentsreader.sidepreview.widget.PreviewFileInfoSuite
import andes.oplus.documentsreader.sidepreview.widget.PreviewFilePathItem
import android.content.Context
import android.view.View
import androidx.fragment.app.Fragment

internal class NormalPreviewFragment :
    AbsPreviewFragment<FilePreviewViewModel>(R.layout.oplusdoc_fragment_preview_normal) {

    private companion object {
        private const val TAG = "NormalPreviewFragment"
    }

    override val logTag: String = TAG
    override val fragmentInstance: Fragment = this
    override val viewModelClass: Class<FilePreviewViewModel> = FilePreviewViewModel::class.java
    override val operationsBarId: Int = R.id.preview_operations_bar
    private var scrollAreaHelper: PreviewScrollAreaHelper? = null

    private var filePreview: INormalFilePreview? = null

    override fun isPreviewFileApproved(context: Context, fileBean: BaseFileBean): Boolean = true

    override fun onViewModelCreated(view: View, viewModel: FilePreviewViewModel) {
        filePreview = prepareFilePreview(view, viewModel)
        scrollAreaHelper = PreviewScrollAreaHelper(
            scrollView = view.findViewById(R.id.scroller_view),
            operationsBar = view.findViewById(R.id.preview_operations_bar),
        )
    }

    override fun onCreateFilePathItem(view: View): PreviewFilePathItem =
        view.findViewById<PreviewFileInfoSuite>(R.id.preview_file_info).filePathView

    override fun onDestroyView() {
        super.onDestroyView()
        filePreview?.release()
        filePreview = null
        scrollAreaHelper?.release()
    }

    private fun prepareFilePreview(
        view: View,
        viewModel: FilePreviewViewModel
    ): INormalFilePreview {
        val container = view.findViewById<PreviewFileInfoSuite>(R.id.preview_file_info)
        val previewImpl = INormalFilePreview.obtain(this, viewModel)
        previewImpl.attachToContainer(container)
        return previewImpl
    }

    override fun applyFilePathIntoItem(previewFile: BaseFileBean?) {
        super.applyFilePathIntoItem(previewFile)
        scrollAreaHelper?.enableScrollViewportMinHeight()
        scrollAreaHelper?.checkShowDivider()
    }
}