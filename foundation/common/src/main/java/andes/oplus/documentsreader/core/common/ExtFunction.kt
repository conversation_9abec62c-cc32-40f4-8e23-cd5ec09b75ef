/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  ExtFunction.kt
 ** Description: define extension methods
 ** Version: 1.0
 ** Date : 2021/02/25
 ** Author: W9001165
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  W9001165    2021/02/25      1.0        create
 ****************************************************************/
package andes.oplus.documentsreader.core.common

import android.app.Activity
import android.content.Context
import android.view.ViewTreeObserver
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.withResumed
import com.oplus.anim.EffectiveAnimationView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * If the context is Activity.class and isFinishing() or isDestroyed() return true.
 * Otherwise, return false
 */
fun Context.isActivityAndInvalid(): Boolean {
    return (this is Activity) && (isFinishing || isDestroyed)
}

/**
 * If Activity isFinishing() or isDestroyed() return true.
 * Otherwise, return false
 */
fun Activity.isInvalid(): Boolean {
    return (isFinishing || isDestroyed)
}

/**
 * 扩展方法直接返回当前属性变量的值，可以生成getter/setter方法在kotlin中
 * 可用于单元测试mock这些属性变量
 * 用例：
 * var member: T
 *      get() = field.noMoreAction()
 *      set(value) { field = value.noMoreAction() }
 */
inline fun <reified T> T.noMoreAction(): T = this

fun AppCompatActivity.runOnMainThread(block: CoroutineScope.() -> Unit) {
    lifecycleScope.launch(Dispatchers.Main) {
        withResumed {
            block()
        }
    }
}

fun AlertDialog?.addOnWindowAttachListener() {
    this?.window?.decorView?.apply {
        val rotatingView = findViewById<EffectiveAnimationView>(R.id.progress)
        viewTreeObserver?.addOnWindowAttachListener(object :
                                                        ViewTreeObserver.OnWindowAttachListener {
            override fun onWindowAttached() {
                rotatingView?.playAnimation()
            }

            override fun onWindowDetached() {
                rotatingView.pauseAnimation()
            }
        })
    }
}

/**
 * Closes the given Closeable and swallows any IOException that may occur.
 */
fun AutoCloseable?.closeQuietly() {
    if (this != null) {
        try {
            this.close()
        } catch (e: Exception) {
        }
    }
}