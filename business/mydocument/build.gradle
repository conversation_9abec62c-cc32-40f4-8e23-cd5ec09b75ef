plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'com.oplus.assistantscreen.plugin.mic'
}

apply from: rootProject.file("script/compile.gradle")
apply from: rootProject.file("script/unitTest.gradle")

android {
    namespace 'andes.oplus.documentsreader.mydocument'
}

dependencies {
    implementation project(":foundation:common")
    implementation project(':framework:statistics')
    implementation project(':framework:emptyfile:interface')
    implementation project(':framework:navigation:interface')
    implementation project(':framework:fileoperate:interface')
    implementation project(":framework:recent:interface")
    implementation project(':framework:selectdir:interface')
    implementation project(":framework:documentapi:interface")
    implementation project(":framework:newdocpanel:interface")
    implementation project(':framework:encrypt')
    implementation project(':framework:doctools:interface')
    implementation project(':framework:backupandrestore')
    implementation project(':framework:setting:interface')
    implementation project(':framework:dragdrop:interface')
    implementation project(':framework:sidepreview:interface')

    //base包为必须引用的包，prop_versionName需保持一致
    implementation(libs.coui.appcompat.panel)
    implementation(libs.coui.appcompat.responsiveui)
    implementation(libs.coui.appcompat.core)
    implementation(libs.coui.appcompat.recyclerview)
    implementation(libs.coui.appcompat.bottomnavigation)
    implementation(libs.coui.appcompat.toolbar)
    implementation(libs.coui.appcompat.poplist)
    implementation(libs.coui.appcompat.tablayout)
    implementation(libs.coui.appcompat.sidenavigationbar)
    implementation(libs.coui.appcompat.floatingactionbutton)
    implementation(libs.coui.appcompat.snackbar)

    implementation(libs.dragdrop)

    implementation(libs.koin.android)
    implementation(libs.oplus.assistantscreen.plugin.mic.api)
    implementation(libs.oplus.assistantscreen.plugin.mic.interfaces)
    kapt(libs.oplus.assistantscreen.plugin.mic.processor)
}