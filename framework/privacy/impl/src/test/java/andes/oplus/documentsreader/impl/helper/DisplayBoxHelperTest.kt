/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: DisplayBoxHelperTest
 * * Description: DisplayBoxHelperTest
 * * Version: 1.0
 * * Date : 2024/03/01
 * * Author:ztf
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *        ztf           2024/03/01      1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.impl.helper

import andes.oplus.documentsreader.core.common.ContextGetter
import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.net.Uri
import android.text.format.DateUtils
import android.util.DisplayMetrics
import android.view.View
import android.widget.TextView
import androidx.appcompat.widget.LinearLayoutCompat
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class DisplayBoxHelperTest {

    companion object {
        private const val TEXT = "text"
    }

    private lateinit var context: Context
    private lateinit var view: View
    private lateinit var linearLayoutCompat: LinearLayoutCompat
    private lateinit var dpMetrics: DisplayMetrics
    private lateinit var res: Resources
    private lateinit var textView: TextView

    @Before
    fun setUp() {
        context = mockk()
        view = mockk(relaxed = true)
        dpMetrics = mockk(relaxed = true)
        res = mockk(relaxed = true)
        linearLayoutCompat = mockk(relaxed = true)
        textView = mockk(relaxed = true)
        mockkObject(ContextGetter)
        mockkStatic(Resources::class)
        mockkStatic(View::class)
        mockkStatic(Uri::class)
        mockkStatic(Intent::class)
        every { ContextGetter.context }.returns(context)
    }

    @After
    fun tearDown() {
        unmockkObject(ContextGetter)
        unmockkStatic(Resources::class)
        unmockkStatic(View::class)
        unmockkStatic(Uri::class)
        unmockkStatic(Intent::class)
    }

    @Test
    fun `should not null when call addSpace`() {
        //given
        every { Resources.getSystem().displayMetrics } returns dpMetrics
        every { view.setPadding(any(), any(), any(), any()) } just runs
        //when
        val compat = linearLayoutCompat.addSpace(view, 0f)
        //then
        Assert.assertNotNull(compat)
    }

    @Test
    fun `should add line space when call lineSpace`() {
        //given
        every { textView.lineSpace(5f) }.answers { callOriginal() }
        //when
        textView.lineSpace(5f)
        //then
        verify { textView.setLineSpacing(any(), 1f) }
    }

    @Test
    fun `should return false when call appendText`() {
        //given
        every { textView.appendText(TEXT) }.answers { callOriginal() }
        //when
        textView.appendText(TEXT)
        //then
        verify { textView.append(TEXT) }
    }

    @Test
    fun `should not null when call bold`() {
        //given
        every { textView.bold() }.answers { callOriginal() }
        //when
        textView.bold()
        //then
        val result = textView.typeface
        Assert.assertNotNull(result)
    }

    @Test
    fun `should return false when call appendLine`() {
        //given
        every { textView.appendLine() }.answers { callOriginal() }
        //when
        textView.appendLine()
        //then
        verify { textView.append("\n") }
    }

    @Test
    fun `should not null when call dateString`() {
        //given
        val time = 1714492800000L
        mockkStatic(DateUtils::class)
        every { DateUtils.formatDateTime(context, time, any()) } returns ""
        //when
        val result = dateString(context, time)
        //then
        Assert.assertNotNull(result)
    }
}