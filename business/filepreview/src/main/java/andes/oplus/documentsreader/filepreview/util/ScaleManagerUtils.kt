/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: VideoPreviewFragment
 * * Description: the ScaleManager util
 * * Version: 1.0
 * * Date : 2024/3/12
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2024/3/12       1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.filepreview.util

import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.Utils
import andes.oplus.documentsreader.filepreview.bean.PointF
import andes.oplus.documentsreader.filepreview.bean.RectF
import andes.oplus.documentsreader.filepreview.image.IScaleListener
import andes.oplus.documentsreader.filepreview.image.ScaleManager
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

object ScaleManagerUtils {
    /**
     * 计算最大缩放倍数
     */
    @JvmStatic
    fun initMaxBoundary(listener: IScaleListener, dmWith: Int, dmHeight: Int): Float {
        listener.let {
            val mediaSize = it.getMediaSize()
            val mediaRadio = mediaSize.width / mediaSize.height
            val screenRadio = dmWith * 1f / dmHeight
            val insideScale = if (mediaRadio > screenRadio) {
                dmWith / mediaSize.width
            } else {
                dmHeight / mediaSize.height
            }
            val realSizeScale = max(
                dmWith / (mediaSize.width * insideScale),
                dmHeight / (mediaSize.height * insideScale)
            )
            val temp = mediaSize.width * mediaSize.height

            val max = if (temp < ScaleManager.SIZE_1) {
                ScaleManager.SCALE_1
            } else if (temp >= ScaleManager.SIZE_1 && temp < ScaleManager.SIZE_2) {
                ScaleManager.SCALE_2
            } else if (temp >= ScaleManager.SIZE_2 && temp < ScaleManager.SIZE_3) {
                ScaleManager.SCALE_3
            } else {
                ScaleManager.SCALE_4
            }
            return max(max, realSizeScale * 2)
        }
    }

    /**
     * 获取缩放后的位置，按屏幕中心计算
     */
    @JvmStatic
    fun getPosByScreenCenter(
        listener: IScaleListener,
        targetScale: Float,
        toDefault: Boolean
    ): PointF {
        val targetPosition = PointF()
        listener.apply {
            val largeSize = getLargeSize()
            val defaultSize = getDefaultSize()
            val viewWidth = largeSize.width
            val viewHeight = largeSize.height
            val targetSize = getSizeByScale(targetScale)
            var targetX = viewWidth / 2 - targetSize.width / 2
            if (Utils.isRtl()) {
                targetX = viewWidth - targetX
            }
            var targetY = viewHeight / 2 - targetSize.height / 2
            if (!toDefault && targetSize.height < defaultSize.height) {
                val currentPos = getCurrentPosition() // 放大后高度小于默认高度时，不需要垂直居中
                targetY =
                    currentPos.y - getSizeByScale(targetScale - getCurrentScale()).height / 2f
            }
            targetPosition.set(targetX, targetY)
        }
        return targetPosition
    }

    /**
     * 获取缩放后的位置，按当前缩放中心计算
     */
    @JvmStatic
    fun getPosByScaleCenter(
        listener: IScaleListener,
        targetScale: Float,
        scalePos: PointF
    ): PointF {
        val targetPosition = PointF()
        listener.apply {
            val scaleChanged = targetScale - getCurrentScale()
            val sizeChanged = getSizeByScale(scaleChanged) // 缩放后的变化量
            var offsetX = sizeChanged.width / 2f
            var offsetY = sizeChanged.height / 2f
            val largeSize = getLargeSize()
            if (sizeChanged.width != 0f) {
                val distanceX = largeSize.width / 2f - scalePos.x // 缩放中心到屏幕中心的距离
                offsetX -= distanceX * scaleChanged
            }
            if (sizeChanged.height != 0f) {
                val distanceY = largeSize.height / 2f - scalePos.y // 缩放中心到屏幕中心的距离
                offsetY -= distanceY * scaleChanged
            }
            val currentPos = getCurrentPosition()
            targetPosition.set(currentPos.x - offsetX, currentPos.y - offsetY)
        }
        return targetPosition
    }


    /**
     * 调整坐标，保证放大后充满父布局
     */
    @JvmStatic
    fun moveToFillParent(
        listener: IScaleListener,
        targetScale: Float,
        targetPoint: PointF
    ): PointF {
        listener.apply {
            val parentRect = getParentRect(this)
            val targetRect = getTargetRect(this, targetScale, targetPoint)
            if (targetRect.top > parentRect.top) { // 上方有空隙，需要向上移动充满屏幕
                targetPoint.y = parentRect.top
            }
            if (targetRect.left > parentRect.left) { // 左方有空隙，需要向左移动充满屏幕
                targetPoint.x = parentRect.left
            }
            if (targetRect.bottom < parentRect.bottom) { // 下方有空隙，需要向下移动充满屏幕
                targetPoint.y += parentRect.bottom - targetRect.bottom
            }
            if (targetRect.right < parentRect.right) { // 右方有空隙，需要向右移动充满屏幕
                targetPoint.x += parentRect.right - targetRect.right
            }
        }
        return targetPoint
    }

    @JvmStatic
    fun getTargetRect(listener: IScaleListener, targetScale: Float? = null, targetPoint: PointF? = null): RectF {
        listener.apply {
            val position = targetPoint ?: getCurrentPosition()
            val size = if (targetScale != null) {
                getSizeByScale(targetScale)
            } else {
                getCurrentSize()
            }
            return RectF(position.x, position.y, position.x + size.width, position.y + size.height)
        }
    }

    @JvmStatic
    fun getParentRect(listener: IScaleListener): RectF {
        listener.apply {
            val parentSize = getLargeSize()
            return RectF(0f, 0f, parentSize.width, parentSize.height)
        }
    }

    @JvmStatic
    fun scaleAndMove(listener: IScaleListener, isInit: Boolean, toScale: Float, targetPos: PointF) {
        listener.apply {
            val fromScale = getCurrentScale()
            val offsetScale = abs(toScale - fromScale)
            val viewWidth = getLargeSize().width
            if (isInit) {
                scaleTo(toScale, PointF(targetPos.x, targetPos.y))
                return
            }
            if (toScale <= ScaleManager.DEFAULT_SCALE_EXTRA || offsetScale <= ScaleManager.DEFAULT_SCALE_EXTRA) {
                if (Utils.isRtl()) {
                    scaleTo(toScale, PointF(viewWidth - targetPos.x, targetPos.y))
                } else {
                    scaleTo(toScale, PointF(targetPos.x, targetPos.y))
                }
                return
            }
            val currentXY = getCurrentPosition()
            val fromX = currentXY.x
            val fromY = currentXY.y
            val offsetY = targetPos.y - fromY
            val offsetX = targetPos.x - fromX
            AnimUtils.doAnim(fromScale, toScale, ScaleManager.ANIMA_DURING) { scale ->
                val rate = abs(scale - fromScale) / offsetScale
                var toX = fromX + offsetX * rate
                if (Utils.isRtl()) {
                    toX = viewWidth - toX
                }
                val toY = fromY + offsetY * rate
                scaleTo(scale, PointF(toX, toY))
            }
        }
    }

    @JvmStatic
    fun getPointIntParent(listener: IScaleListener, x: Float, y: Float): PointF {
        val pointF = PointF(x, y)
        listener.apply {
            val largeSize = getLargeSize()
            val viewWidth = largeSize.width
            val viewHeight = largeSize.height
            if (x < 0f) { // 折叠屏展开或者大屏横竖屏切换的时候需要注意
                pointF.x = 0f
            } else if (x > viewWidth) {
                pointF.x = viewWidth
            }
            if (y < 0f) {
                pointF.y = 0f
            } else if (y > viewHeight) {
                pointF.y = viewHeight
            }
        }
        return pointF
    }

    @JvmStatic
    fun getReboundHorizontalPos(listener: IScaleListener, dragBeginX: Float?): Float? {
        if (dragBeginX == null) {
            return null
        }
        listener.apply {
            val current = getTargetRect(this)
            val parent = getParentRect(this)
            val currentSize = getCurrentSize()
            if (current.left > dragBeginX) { // 向右移动了
                if (current.left <= parent.left) {  // 左边不需要回弹
                    return null
                }
                return max(parent.left, dragBeginX) // 向左回弹
            } else if (current.left < dragBeginX) { // 向左移动了
                if (current.right >= parent.right) {  // 右边不需要回弹
                    return null
                }
                return min(dragBeginX, parent.right - currentSize.width) // 向右回弹
            }
            return null
        }
    }

    @JvmStatic
    fun getReboundVerticalPos(listener: IScaleListener, dragBeginY: Float?): Float? {
        if (dragBeginY == null) {
            return null
        }
        listener.apply {
            val current = getTargetRect(this)
            val parent = getParentRect(this)
            val currentSize = getCurrentSize()
            if (current.top > dragBeginY) { // 向下移动了
                if (current.top <= parent.top) {  // 上边不需要回弹
                    return null
                }
                return max(parent.top, dragBeginY)  // 向上回弹
            } else if (current.top < dragBeginY) { // 向上移动了
                if (current.bottom >= parent.bottom) {  // 底部不需要回弹
                    return null
                }
                return min(dragBeginY, parent.bottom - currentSize.height)  // 向下回弹
            }
            return null
        }
    }

    @JvmStatic
    fun isVerticalMode(listener: IScaleListener, offset: Float): Boolean {
        return getDragMode(listener, 0f, offset) in arrayListOf(
            ScaleManager.DRAG_DOWN,
            ScaleManager.DRAG_UP
        )
    }

    @JvmStatic
    fun isHorizontalMode(listener: IScaleListener, offset: Float): Boolean {
        return getDragMode(listener, offset, 0f) in arrayListOf(
            ScaleManager.DRAG_LEFT,
            ScaleManager.DRAG_RIGHT
        )
    }

    @JvmStatic
    private fun getDragMode(listener: IScaleListener, offsetX: Float, offsetY: Float): Int {
        listener.apply {
            val position = getCurrentPosition()
            position.set(position.x + offsetX, position.y + offsetY)
            val current = getTargetRect(this, getCurrentScale(), position)
            val parent = getParentRect(this)
            val modeHorizontal = if (offsetX > 0 && current.left >= parent.left) {  // 像右移动
                ScaleManager.DRAG_LEFT
            } else if (offsetX < 0f && current.right <= parent.right) {  // 像左移动
                ScaleManager.DRAG_RIGHT
            } else {
                ScaleManager.DRAG_NON
            }
            val modeVertical = if (offsetY > 0f && current.top >= parent.top) {  // 像下移动
                ScaleManager.DRAG_DOWN
            } else if (offsetY < 0f && current.bottom <= parent.bottom) {  // 像上移动
                ScaleManager.DRAG_UP
            } else {
                ScaleManager.DRAG_NON
            }
            // 只能有一种模式 ,哪个方向偏移量大，就使用该方向的值
            return if (abs(offsetX) > abs(offsetY)) {
                modeHorizontal
            } else {
                modeVertical
            }
        }
    }

    @JvmStatic
    fun pointInView(listener: IScaleListener, pointF: PointF): Boolean {
        listener.apply {
            val currentPosition = getCurrentPosition()
            val currentSize = getCurrentSize()
            val mediaLeft = currentPosition.x
            val mediaRight = mediaLeft + currentSize.width
            val mediaTop = currentPosition.y
            val mediaBottom = mediaTop + currentSize.height
            if (pointF.x < mediaLeft || pointF.x > mediaRight) {
                return false
            }
            if (pointF.y < mediaTop || pointF.y > mediaBottom) {
                return false
            }
            return true
        }
    }
}