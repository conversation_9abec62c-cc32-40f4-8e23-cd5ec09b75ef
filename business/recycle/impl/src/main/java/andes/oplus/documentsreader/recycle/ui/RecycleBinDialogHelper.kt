/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - RecycleBinDialogHelper.kt
 * Description:
 *     The helper to create dialog for recycle bin operations.
 *
 * Version: 1.0
 * Date: 2024-07-14
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-07-14   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.recycle.ui

import andes.oplus.documentsreader.recycle.utils.RecycleBinUtils
import andes.oplus.documentsreader.recycle.viewmodel.RecycleFileBean
import android.content.Context
import android.view.Gravity
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder

internal class RecycleBinDialogHelper(private val context: Context) {

    fun confirmDelete(
        selectedFiles: Collection<RecycleFileBean>,
        isSelectedAll: Boolean,
        onApproved: (Context, Collection<RecycleFileBean>, Boolean) -> Unit
    ): COUIAlertDialogBuilder {
        return COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setBlurBackgroundDrawable(true)
            .setWindowGravity(Gravity.BOTTOM)
            .setTitle(RecycleBinUtils.getDeleteTitle(context, selectedFiles, isSelectedAll))
            .setMessage(RecycleBinUtils.getDeleteMessage(context, selectedFiles, isSelectedAll))
            .setNeutralButton(RecycleBinUtils.getDeleteButtonText(context, isSelectedAll)) { _, _ ->
                onApproved(context, selectedFiles, isSelectedAll)
                RecycleBinUtils.playDeleteEffect()
            }
            .setNegativeButton(andes.oplus.documentsreader.recycle.R.string.dialog_cancel, null)
    }

    fun confirmRestore(
        selectedFiles: Collection<RecycleFileBean>,
        isSelectedAll: Boolean,
        onApproved: (Context, Collection<RecycleFileBean>, Boolean) -> Unit
    ): COUIAlertDialogBuilder {
        return COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setBlurBackgroundDrawable(true)
            .setWindowGravity(Gravity.BOTTOM)
            .setTitle(RecycleBinUtils.getRestoreTitle(context, selectedFiles, isSelectedAll))
            .setNeutralButton(RecycleBinUtils.getRestoreButtonText(context, isSelectedAll)) { _, _ ->
                onApproved(context, selectedFiles, isSelectedAll)
            }
            .setNegativeButton(andes.oplus.documentsreader.recycle.R.string.dialog_cancel, null)
    }

    fun deleteProgress(): AlertDialog =
        progressDialog(andes.oplus.documentsreader.recycle.R.string.dialog_deleting)

    fun restoreProgress(): AlertDialog =
        progressDialog(andes.oplus.documentsreader.recycle.R.string.send_file_message)

    private fun progressDialog(@StringRes titleId: Int): AlertDialog =
        COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Progress)
            .setBlurBackgroundDrawable(true)
            .setTitle(titleId)
            .setCancelable(false)
            .create().apply {
                setCanceledOnTouchOutside(false)
            }
}