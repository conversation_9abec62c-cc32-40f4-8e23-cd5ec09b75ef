/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PersonalListActivity
 * * Description: 收集个人信息明示清单
 * * Version: 1.0
 * * Date : 2024/03/01
 * * Author:ztf
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *        ztf           2024/03/01      1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.impl.ui.personal

import andes.oplus.documentsreader.core.common.BaseVMActivity
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.StatusBarUtils
import andes.oplus.documentsreader.core.common.WindowUtils
import andes.oplus.documentsreader.core.common.compat.FeatureCompat
import andes.oplus.documentsreader.core.common.uiconfig.type.IUIConfig
import andes.oplus.documentsreader.impl.utils.FlexibleWindowUtil
import andes.oplus.documentsreader.userprivacy.impl.R
import android.annotation.SuppressLint
import android.app.Activity
import android.app.ActivityOptions
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.view.ViewTreeObserver
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.core.widget.NestedScrollView
import com.coui.appcompat.toolbar.COUIToolbar
import com.google.android.material.appbar.AppBarLayout
import com.oplus.flexiblewindow.FlexibleWindowManager

class PersonalListActivity : BaseVMActivity() {

    private lateinit var appBarLayout: AppBarLayout
    private lateinit var toolbar: COUIToolbar

    override fun getLayoutResId(): Int {
        return R.layout.activity_personal_list
    }

    override fun initView() {
        registerVmChangedReceiver(null)
        appBarLayout = findViewById(R.id.appbar_layout)
        toolbar = findViewById(R.id.toolbar)

        initToolbar()
        setBarBg()
    }

    override fun startObserve() {
    }

    override fun initData() {
    }

    @SuppressLint("PrivateResource")
    private fun initToolbar() {
        toolbar.title = resources.getString(R.string.personal_information_collection_list)
        toolbar.isTitleCenterStyle = false
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        initLayoutPadding()
        initFragment()
        findViewById<NestedScrollView>(R.id.ll_activity_setting)?.apply {
            val listener = object : ViewTreeObserver.OnPreDrawListener {
                override fun onPreDraw(): Boolean {
                    viewTreeObserver.removeOnPreDrawListener(this)
                    val appBarHeight = <EMAIL><AppBarLayout>(R.id.appbar_layout)?.measuredHeight ?: 0
                    updatePadding(top = appBarHeight)
                    clipToPadding = false
                    return true
                }
            }
            viewTreeObserver.addOnPreDrawListener(listener)
            ViewCompat.setNestedScrollingEnabled(this, true)
        }
    }

    private fun initLayoutPadding() {
        var navigationIcon = R.drawable.coui_back_arrow
        var toolbarTopPadding = StatusBarUtils.getStatusBarHeight(this)
        if (FeatureCompat.isApplicableForFlexibleWindow &&
            WindowUtils.isMiddleAndLargeScreen(this) && !isInMultiWindowMode
        ) {
            navigationIcon = R.drawable.coui_menu_ic_cancel
            toolbarTopPadding = resources.getDimensionPixelOffset(R.dimen.dimen_4dp)
        }
        findViewById<CoordinatorLayout>(R.id.coordinator)?.apply {
            setPadding(paddingLeft, toolbarTopPadding, paddingRight, paddingBottom)
        }
        toolbar.setNavigationIcon(navigationIcon)
    }

    private fun setBarBg() {
        FlexibleWindowUtil.setBarBackgroundColor(toolbar, appBarLayout, window.decorView, resources.configuration, this)
    }

    private fun initFragment() {
        var fragment = supportFragmentManager.findFragmentByTag(TAG)
        if ((fragment == null) || (fragment !is PersonalListFragment)) {
            fragment = PersonalListFragment()
        }
        val ft = supportFragmentManager.beginTransaction()
        ft.replace(R.id.fragment_container, fragment, TAG)
        ft.show(fragment)
        ft.commitAllowingStateLoss()
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            finish()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        Log.d(TAG, "onUIConfigChanged")
        initToolbar()
        setBarBg()
    }

    override fun onWindowInsetsCallback(showNavigationBar: Boolean, systemBarInsetsBottom: Int) {
        super.onWindowInsetsCallback(showNavigationBar, systemBarInsetsBottom)
        if (showNavigationBar) {
            findViewById<CoordinatorLayout>(R.id.coordinator)?.updatePadding(bottom = systemBarInsetsBottom)
        } else {
            findViewById<CoordinatorLayout>(R.id.coordinator)?.updatePadding(bottom = 0)
        }
    }

    companion object {
        private const val TAG = "PersonalListActivity"

        fun start(activity: Activity) {
            runCatching {
                val intent = Intent(activity, PersonalListActivity::class.java)
                intent.setPackage(activity.packageName)
                if (FeatureCompat.isApplicableForFlexibleWindow) {
                    val exBundle = Bundle()
                    exBundle.putInt(
                        FlexibleWindowManager.KEY_FLEXIBLE_ACTIVITY_POSITION,
                        FlexibleWindowUtil.checkOrientation()
                    )
                    exBundle.putBoolean(FlexibleWindowManager.KEY_FLEXIBLE_START_ACTIVITY, true)
                    exBundle.putBoolean(
                        FlexibleWindowManager.KEY_FLEXIBLE_ACTIVITY_DESCENDANT,
                        true
                    )

                    val options = ActivityOptions.makeBasic()
                    activity.startActivity(
                        intent,
                        FlexibleWindowManager.getInstance().setExtraBundle(options, exBundle)
                    )
                } else {
                    activity.startActivity(intent)
                }
            }.onFailure {
                Log.e(TAG, "start personal list activity error, ${it.message}")
            }
        }
    }
}