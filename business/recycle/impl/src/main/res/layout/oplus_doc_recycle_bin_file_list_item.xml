<?xml version="1.0" encoding="utf-8"?>
<andes.oplus.documentsreader.core.common.SelectItemLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/recyle_bin_list_item_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:forceDarkAllowed="false"
    android:background="@drawable/select_list_item_background_selector"
    android:minHeight="@dimen/dimen_48dp">

    <andes.oplus.documentsreader.core.common.view.FileThumbView
        android:id="@+id/file_list_item_icon"
        android:layout_width="@dimen/file_list_frame_size"
        android:layout_height="@dimen/file_list_frame_size"
        android:forceDarkAllowed="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <andes.oplus.documentsreader.core.common.TextViewSnippet
        android:id="@+id/file_list_item_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/file_list_item_info_margin"
        android:layout_marginTop="@dimen/file_list_right_layout_margin_vertical"
        android:layout_marginEnd="@dimen/default_margin"
        android:maxLines="2"
        android:textAppearance="?android:attr/textAppearanceLarge"
        android:textSize="@dimen/file_list_item_title_text_size"
        android:includeFontPadding="false"
        app:layout_constraintBottom_toTopOf="@id/file_list_item_detail"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/file_list_item_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/file_list_item_detail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_below="@id/file_list_item_title"
        android:layout_gravity="center_vertical"
        android:layout_marginTop="@dimen/file_list_item_detail_margin_top"
        android:layout_marginBottom="@dimen/file_list_right_layout_margin_vertical"
        android:adjustViewBounds="true"
        android:ellipsize="end"
        android:maxLines="2"
        android:textAppearance="@style/couiTextBodyS"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textSize="@dimen/font_size_14"
        android:includeFontPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/file_list_item_title"
        app:layout_constraintStart_toStartOf="@id/file_list_item_title"
        app:layout_constraintTop_toBottomOf="@id/file_list_item_title" />

    <com.coui.appcompat.checkbox.COUICheckBox
        android:id="@+id/listview_scrollchoice_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="@dimen/sort_item_padding_right_out"
        android:background="@null"
        android:clickable="false"
        android:focusable="false" />

</andes.oplus.documentsreader.core.common.SelectItemLayout>