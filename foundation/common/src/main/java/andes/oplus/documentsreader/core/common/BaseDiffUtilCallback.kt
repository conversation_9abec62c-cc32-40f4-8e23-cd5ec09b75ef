/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/08/05, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package andes.oplus.documentsreader.core.common

import androidx.recyclerview.widget.DiffUtil

open class BaseDiffUtilCallback<T>(
    private val oldList: List<T>,
    private val newList: List<T>,
    private val areItemsTheSame: (T, T) -> <PERSON>olean,
    private val areContentsTheSame: (T, T, Int, Int) -> Boolean
) : DiffUtil.Callback() {

    override fun getOldListSize() = oldList.size

    override fun getNewListSize() = newList.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        val (oldItem, newItem) = getItem(oldItemPosition, newItemPosition)
        return areItemsTheSame(oldItem, newItem)
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        val (oldItem, newItem) = getItem(oldItemPosition, newItemPosition)
        return areContentsTheSame(oldItem, newItem, oldItemPosition, newItemPosition)
    }

    private fun getItem(oldItemPosition: Int, newItemPosition: Int): Pair<T, T> {
        return oldList[oldItemPosition] to newList[newItemPosition]
    }
}