/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DataBase
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/6/26 11:16
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/6/26       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.core.database

import andes.oplus.documentsreader.core.database.dao.PreviewCacheDao
import andes.oplus.documentsreader.core.database.dao.PreviewDataDao
import andes.oplus.documentsreader.core.database.data.PreviewCacheEntity
import andes.oplus.documentsreader.core.database.data.PreviewDataEntity
import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

private const val DATABASE_NAME = "file-viewer.db"
private const val DATABASE_VERSION = 2

@Database(
    entities = [
        PreviewDataEntity::class,
        PreviewCacheEntity::class
    ], version = DATABASE_VERSION, exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {

    /**
     * Create previewDataDao.
     */
    abstract fun previewDataDao(): PreviewDataDao

    /**
     * Create preview cache data access object.
     */
    abstract fun previewCacheDao(): PreviewCacheDao

    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null

        private val MIGRATION_1_2: Migration = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("ALTER TABLE preview_data ADD COLUMN `file_size` INTEGER NOT NULL DEFAULT 0")
            }
        }

        fun getInstance(context: Context): AppDatabase = INSTANCE ?: synchronized(this) {
            INSTANCE ?: buildDatabase(context.applicationContext).also { INSTANCE = it }
        }

        private fun buildDatabase(context: Context) = Room.databaseBuilder(
            context, AppDatabase::class.java, DATABASE_NAME
        ).enableMultiInstanceInvalidation().addMigrations(MIGRATION_1_2).build()
    }
}