/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/04/25, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package andes.oplus.documentsreader.util

import andes.oplus.documentsreader.formatconvert.contants.CloudConstants
import andes.oplus.documentsreader.formatconvert.util.SecretUtils
import andes.oplus.documentsreader.formatconvert.util.SecretUtils.AES_ALGORITHM
import andes.oplus.documentsreader.formatconvert.util.SecretUtils.RSA_ALGORITHM
import org.junit.Test
import kotlin.test.assertTrue

@Suppress("MagicNumber")
class SecretUtilsTest {

    @Test
    fun should_build_secret_key_when_encode_string_is_ok() {
        //Given
        val aesSecretKeyString = "X5MdCCIy+/UW/omB/TVqBA=="

        //When
        val result = SecretUtils.buildAESSecretKey(aesSecretKeyString)

        //Then
        assertTrue { result.algorithm == AES_ALGORITHM }
    }

    @Test
    fun should_build_rsa_key_when_encode_string_is_ok() {
        //Given
        val rsaKey = CloudConstants.RSA_FORMULA_KEY

        //When
        val result = SecretUtils.buildRSAPublicKey(rsaKey)

        //Then
        assertTrue { result.algorithm == RSA_ALGORITHM }
    }

    @Test
    fun should_build_aes_key() {
        //When
        val result = SecretUtils.generateSecretKey()

        //Then
        assertTrue { result.algorithm == AES_ALGORITHM }
    }

    @Test
    fun should_build_correct_iv_key() {
        //Given
        val ivString = "3o8e+TjgIByC7XgW"
        //When
        val result = SecretUtils.generateIVByString(ivString)

        //Then
        assertTrue { result.tLen == 128 }
    }

    @Test
    fun should_build_correct_sing() {
        //Given
        val sign = "ai-auth-v1/1234/1650938689612/e33e396ef8e17fdf09fd8b10c8eb59e2c5feb3988629c5a43a58dd7419ed6a53"

        //When
        val result = SecretUtils.getHeaderAuthorization(mapOf(), "", "1234", "5678", 1650938689612)

        //Then
        assertTrue { result == sign }
    }
}