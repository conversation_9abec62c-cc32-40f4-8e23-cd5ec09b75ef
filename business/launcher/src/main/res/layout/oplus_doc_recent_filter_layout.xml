<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/function_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/couiColorBackground"
    android:paddingStart="@dimen/dimen_24dp">

    <TextView
        android:id="@+id/recent_title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_marginEnd="12dp"
        android:layout_toLeftOf="@id/filter_layout"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:minHeight="@dimen/tablayout_small_layout_height"
        android:text="@string/main_tab_recently_doc"
        android:textAppearance="@style/couiTextHeadlineXS"
        android:textColor="?attr/couiColorLabelPrimary" />

    <LinearLayout
        android:id="@+id/filter_layout"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/dimen_4dp">

        <TextView
            android:id="@+id/filtrate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dimen_2dp"
            android:fontFamily="sans-serif-medium"
            android:text="@string/filtrate"
            android:textAppearance="?attr/couiTextBodyM"
            android:textColor="?attr/couiColorLabelPrimary" />

        <ImageView
            android:id="@+id/filter_expend"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_4dp"
            android:layout_marginEnd="@dimen/dimen_20dp"
            android:contentDescription="@string/filtrate"
            android:src="@drawable/home_tab_filter_icon_selector" />
    </LinearLayout>

</RelativeLayout>