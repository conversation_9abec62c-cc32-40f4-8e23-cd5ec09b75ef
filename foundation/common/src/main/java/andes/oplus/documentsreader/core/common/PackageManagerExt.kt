/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : PackageManagerExt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/4/16 16:14
 ** Author      : 80262777
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/4/16       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.core.common

import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.content.pm.PackageManager.PackageInfoFlags
import android.os.Build

fun PackageManager.getPackageInfoCompact(pkgName: String, flag: Int): PackageInfo {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        return this.getPackageInfo(pkgName, PackageInfoFlags.of(flag.toLong()))
    } else {
        return this.getPackageInfo(pkgName, flag)
    }
}