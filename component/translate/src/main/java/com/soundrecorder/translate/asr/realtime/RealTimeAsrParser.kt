/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RealTimeAsrParser
 * Description:
 * Version: 1.0
 * Date: 2025/4/16
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/4/16 1.0 create
 */

package com.soundrecorder.translate.asr.realtime

import com.oplus.ai.asrkit.data.response.AsrAudioAck
import com.soundrecorder.translate.asr.listener.IRealTimeAsrListener

class RealTimeAsrParser {
    companion object {
        /*中间态，连续*/
        private const val ASR_RESULT_TYPE_INTERMEDIATE = "INTERMEDIATE"
        private const val ASR_RESULT_TYPE_FINAL = "FINAL"

        /*最终的结果句子*/
        private const val ASR_RESULT_TYPE_VAD_FINAL = "VAD_FINAL"

        /*服务端主动断开链路标志*/
        const val BIZ_TYPE_SERVER_END_ASK = "SERVER_END_ASK"
        const val BIZ_TYPE_END_ASK = "END_ASK"
        const val BIZ_TYPE_START_ASK = "START_ASK"
        const val BIZ_TYPE_AUDIO = "AUDIO"

        /*数据未上传完成*/
        const val ERROR_DATA_UPLOAD = 3000703
        /*找不到channelId*/
        const val ERROR_NOT_FOUND_CHANNEL_ID = 1002

        /*未知错误*/
        const val ERROR_UNKNOWN = 3009999

        /*响应解密时发生异常*/
        const val ERROR_DECRYPT_ACK = 100100

        /*等待FINAL超时*/
        const val ERROR_WAIT_FINAL_TIMEOUT = 100101

        /*语音识别处理异常*/
        const val ERROR_PROCESS = 100102

        /*网络未初始化*/
        const val ERROR_NO_INIT = 100103

        /*网络返回异常*/
        const val ERROR_NET_FAILURE = 100104

        /*网络未连接*/
        const val ERROR_NET_DISCONNECT = 100105

        /*网络已连接*/
        const val STATUS_NET_CONNECTED = 100201

        const val STATUS_RESULT_DATA = 100202

        /*initAsr成功*/
        const val STATUS_INIT_SUCCESS = 100203

        /*initAsr失败*/
        const val STATUS_INIT_ERROR = 100204

        /*接收到ASR END回调*/
        const val STATUS_ASR_COMPLETE_SUCCESS = 100205

        /*发送数据，channel找不到*/
        const val AUDIO_ERROR_CODE_CHANNEL_NOT_FOUND = 1002
    }

    fun onAsr(channelId: String?, audioAck: AsrAudioAck, listener: IRealTimeAsrListener?) {
        when (audioAck.type) {
            ASR_RESULT_TYPE_INTERMEDIATE, ASR_RESULT_TYPE_VAD_FINAL -> {
                if (!audioAck.text.isNullOrBlank()) {
                    listener?.onAsrResult(channelId, audioAck.msgId, audioAck.type!!, audioAck.startOffset
                            ?: 0, audioAck.endOffset ?: 0, audioAck.text!!)
                }
            }
        }
    }
}