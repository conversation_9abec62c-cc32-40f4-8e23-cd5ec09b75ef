/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - NormalFilePreviewImpl.kt
 * Description:
 *     The implementation to show file icon to preview it.
 *
 * Version: 1.0
 * Date: 2024-10-17
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-10-17   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.sidepreview.normal

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.KtThumbnailHelper
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.MimeTypeHelper
import andes.oplus.documentsreader.sidepreview.core.FilePreviewViewModel
import andes.oplus.documentsreader.sidepreview.utils.getMediaType
import andes.oplus.documentsreader.sidepreview.widget.PreviewFileInfoSuite
import androidx.lifecycle.Observer

internal class NormalFilePreviewImpl(
    private val fragment: NormalPreviewFragment,
    private val previewModel: FilePreviewViewModel
) : INormalFilePreview {

    private companion object {
        private const val TAG = "NormalFilePreviewImpl"
    }

    private val defIconPlaceholder =
        KtThumbnailHelper.getClassifyResId(MimeTypeHelper.UNKNOWN_TYPE)
    private val onFileLoaded = Observer<BaseFileBean?> { displayOnContainer(it) }
    private val lifecycleOwner = fragment.viewLifecycleOwner

    private var isObserved = false
    private var infoContainer: PreviewFileInfoSuite? = null

    override fun release() {
        previewModel.releaseLoader(onFileLoaded)
        infoContainer = null
        isObserved = false
    }

    override fun attachToContainer(container: PreviewFileInfoSuite) {
        infoContainer = container
        container.setFileIcon(defIconPlaceholder)
        if (isObserved) {
            return
        }
        isObserved = true
        container.fileIconView.setOnClickListener { fragment.actionOpen() }
        previewModel.loadPreviewFile(lifecycleOwner, onFileLoaded)
    }

    private fun displayOnContainer(fileBean: BaseFileBean?) {
        val container = infoContainer ?: return
        container.setFileName(fileBean?.mDisplayName)
        val type = fileBean?.getMediaType(container.context)
        Log.d(TAG, "displayOnContainer: type=$type")
        if (type == null) {
            container.setFileIcon(defIconPlaceholder)
            return
        }
        val iconRes = KtThumbnailHelper.getClassifyResId(type)
        container.setFileIcon(iconRes)
    }
}