plugins {
    id 'com.android.library'
    id 'kotlin-kapt'
}
apply from: rootProject.file("script/compile.gradle")
apply from: rootProject.file("script/unitTest.gradle")

android {
    namespace 'andes.oplus.documentsreader.core.fileoperate.interfaces'
}

dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)

    // Koin
    implementation(libs.koin.android)
    implementation project(":foundation:common")
    implementation project(':framework:dragdrop:interface')
}