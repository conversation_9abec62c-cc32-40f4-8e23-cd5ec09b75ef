/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : RecyclerViewFastScroller.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2020/12/3 11:05
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2020/12/3      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package andes.oplus.documentsreader.core.common.fastscroll

import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.R
import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.annotation.VisibleForTesting
import androidx.core.view.forEach
import androidx.core.view.isVisible
import androidx.core.view.marginBottom
import androidx.core.view.marginTop
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.SCROLL_STATE_IDLE
import andes.oplus.documentsreader.core.common.fastscroll.MathUtil.clamp
import andes.oplus.documentsreader.core.common.view.CommonRecyclerView
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt

class RecyclerViewFastScroller @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr) {
    var scrollerEnable: Boolean = true

    var scrollerVisible: Boolean
        get() = trackView.isVisible && thumbView.isVisible
        set(value) {
            trackView.visibility = if (value) View.VISIBLE else View.INVISIBLE
            thumbView.visibility = if (value) View.VISIBLE else View.INVISIBLE
        }

    var thumbWidth: Int = LayoutParams.WRAP_CONTENT
        set(value) {
            field = value
            thumbView.layoutParams = LinearLayout.LayoutParams(thumbWidth, thumbHeight)
        }

    var thumbHeight: Int = LayoutParams.WRAP_CONTENT
        set(value) {
            field = value
            thumbView.layoutParams = LinearLayout.LayoutParams(thumbWidth, thumbHeight)
        }

    var trackMarginStart: Int = 0
        set(value) {
            if (field != value) {
                field = value
                refreshTrackMargin()
            }
        }

    var trackMarginEnd: Int = 0
        set(value) {
            if (field != value) {
                field = value
                refreshTrackMargin()
            }
        }

    var trackMarginTop: Int = 0
        set(value) {
            if (field != value) {
                field = value
                refreshTrackMargin()
            }
        }

    var trackMarginBottom: Int = 0
        set(value) {
            if (field != value) {
                field = value
                refreshTrackMargin()
            }
        }

    var thumbVisibilityDuration: Int = -1

    private lateinit var thumbView: FullScreenFastScroller
    private lateinit var trackView: LinearLayout

    @VisibleForTesting
    lateinit var recyclerView: RecyclerView
    private var visibleCountMax: Int = 0
    private var lastY = 0f

    private val trackLength: Float
        get() = trackView.height.toFloat()

    private val thumbLength: Float
        get() = thumbView.height.toFloat()

    private val hideThumbRunnable = Runnable { thumbView.isVisible = false }

    private val fastScrollerMinHeight: Int by lazy { resources.getDimensionPixelSize(R.dimen.common_full_screen_scroller_min_height) }
    private var needRefreshThumbViewOnLayout = false

    private val onScrollListener = object : RecyclerView.OnScrollListener() {
        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            if (!needRefreshThumbViewOnLayout) {
                refreshThumbView(recyclerView)
            }
        }

        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            onScrolled(recyclerView, 0, 0)
        }
    }

    private val thumbTouchListener: OnTouchListener = object : OnTouchListener {
        override fun onTouch(v: View, event: MotionEvent): Boolean {
            if (!thumbView.isVisible || (thumbView.alpha == 0f)) return false

            val yAbsPosition = -thumbView.translationY + thumbLength / 2f
            val eventY = if (abs(event.y - thumbLength / 2f) < THUMB_MOVE_MISTAKE_GAP) thumbLength / 2f else event.y
            val y = clamp(eventY - yAbsPosition, 0f, trackLength - thumbLength)

            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    parent.requestDisallowInterceptTouchEvent(true)
                    if (!adapterDataObserver.isInitialized()) {
                        recyclerView.adapter?.registerAdapterDataObserver(adapterDataObserver.value)
                    }
                    if (recyclerView is COUIRecyclerView) {
                        (recyclerView as COUIRecyclerView).viewFlinger.stop()
                    }
                    // 停止滚动，并将滚动状态设置为SCROLL_STATE_IDLE
                    recyclerView.stopScroll()
                    lastY = y
                }

                MotionEvent.ACTION_MOVE -> {
                    if (abs(y - lastY) > SCROLL_SLOP) {
                        recyclerView.computePositionForOffsetAndScroll(y)
                        onScrollListener.onScrolled(recyclerView, 0, 0)
                        lastY = y
                    }
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL ->
                    parent.requestDisallowInterceptTouchEvent(false)
            }
            return true
        }
    }

    init {
        // 选择模式下，滑选结合快速滑动条快速滑动时，主线程操作太频繁，响应不及时，会造成ANR，因此禁止多点触控
        isMotionEventSplittingEnabled = false

        initThumbAndTrack()

        context.theme.obtainStyledAttributes(
            attrs, R.styleable.CommonRecyclerViewFastScroller, 0, 0
        ).let { typedArray ->
            scrollerEnable = typedArray.getBoolean(R.styleable.CommonRecyclerViewFastScroller_common_scroller_enable, true)
            scrollerVisible = typedArray.getBoolean(R.styleable.CommonRecyclerViewFastScroller_common_scroller_visible, false)

            thumbVisibilityDuration = typedArray.getInt(
                R.styleable.CommonRecyclerViewFastScroller_common_thumb_visibleDuration,
                VISIBLE_DURATION_DEFAULT.toInt()
            )

            thumbHeight = typedArray.getDimensionPixelSize(
                R.styleable.CommonRecyclerViewFastScroller_common_thumb_height,
                LayoutParams.WRAP_CONTENT
            )

            thumbWidth = typedArray.getDimensionPixelSize(
                R.styleable.CommonRecyclerViewFastScroller_common_thumb_width,
                LayoutParams.WRAP_CONTENT
            )

            trackMarginStart = typedArray.getDimensionPixelSize(
                R.styleable.CommonRecyclerViewFastScroller_common_track_marginStart, 0
            )

            trackMarginEnd = typedArray.getDimensionPixelSize(
                R.styleable.CommonRecyclerViewFastScroller_common_track_marginEnd, 0
            )

            trackMarginTop = typedArray.getDimensionPixelSize(
                R.styleable.CommonRecyclerViewFastScroller_common_track_marginTop, 0
            )

            trackMarginBottom = typedArray.getDimensionPixelSize(
                R.styleable.CommonRecyclerViewFastScroller_common_track_marginBottom, 0
            )

            typedArray.recycle()
        }

        alignThumbAndTrack()
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        forEach {
            if (it is RecyclerView) {
                removeView(it)
                addView(it, 0)
                attachFastScrollerToRecyclerView(it)
                return@forEach
            }
        }
    }

    private fun alignThumbAndTrack() {
        trackView.layoutParams = LayoutParams(
            LayoutParams.WRAP_CONTENT,
            LayoutParams.MATCH_PARENT
        ).also {
            it.addRule(ALIGN_PARENT_END)
        }
        post {
            thumbView.x = 0F
            onScrollListener.onScrolled(recyclerView, 0, 0)
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initThumbAndTrack() {
        View.inflate(context, R.layout.oplus_doc_common_fastscroller_thumb_track, this)
        thumbView = findViewById(R.id.thumbView)
        trackView = findViewById(R.id.trackView)
    }

    private fun RecyclerView.computePositionForOffsetAndScroll(y: Float) {
        val layoutManager: RecyclerView.LayoutManager? = this.layoutManager
        val itemCount = this.adapter?.itemCount ?: 0

        val trackRatio = y / (trackLength - thumbLength)
        val scrollOffset: Int =
            (trackRatio * (recyclerView.computeVerticalScrollRange() - recyclerView.height + recyclerView.paddingBottom + recyclerView.paddingTop)
                    - recyclerView.computeVerticalScrollOffset()).roundToInt()
        when (layoutManager) {
            is LinearLayoutManager -> {
                // 滚动距离大于一屏时使用RecyclerView.scrollToPosition()方式直接滚动到item，提高性能
                if (abs(scrollOffset) > recyclerView.height) {
                    //宫格模式 视频分类底部有footerlayout 计算可见item加上列数/2，保证safeScrollToPosition(toScrollPosition, offset) 滑到最后一页时设置offset为0
                    val visibleCount = layoutManager.getTotalCompletelyVisibleItemCount() +
                            if (layoutManager is GridLayoutManager && layoutManager.spanCount > 1) layoutManager.spanCount / SPAN_COUNT_OFFSET else 0
                    if (visibleCount < 0) return
                    visibleCountMax = max(visibleCountMax, visibleCount)
                    val itemScrollLimit = itemCount - (visibleCountMax + 1)
                    val position = if (layoutManager.reverseLayout) {
                        clamp(itemCount - (trackRatio * (itemCount - visibleCount)).roundToInt(), 0, itemCount)
                    } else {
                        clamp((trackRatio * (itemCount - visibleCount)).roundToInt(), 0, itemCount)
                    }

                    val toScrollPosition = min(itemScrollLimit, position)

                    val firstView = recyclerView.getChildAt(0)
                    var offset = firstView.top + scrollOffset % firstView.height
                    // 滚动到上边界或下边界，offset为0
                    if ((toScrollPosition <= 1) || (toScrollPosition >= itemScrollLimit)) offset = 0
                    safeScrollToPosition(toScrollPosition, offset)
                    if ((toScrollPosition >= itemScrollLimit)) {
                        // safeScrollToPosition 只能滑动到接近的位置，滑动到底部时最后一次需要在下一帧调一下 scrollBy 从接近的位置精确滑动到底部
                        recyclerView.post {
                            recyclerView.scrollBy(
                                0,
                                (recyclerView.computeVerticalScrollRange() - recyclerView.height) - recyclerView.computeVerticalScrollOffset()
                            )
                        }
                    }
                } else {
                    recyclerView.scrollBy(0, scrollOffset)
                }
                if (y == (trackLength - thumbLength)) {
                    // safeScrollToPosition 只能滑动到接近的位置，滑动到底部时最后一次需要在下一帧调一下 scrollBy 从接近的位置精确滑动到底部
                    recyclerView.post {
                        recyclerView.scrollBy(
                            0,
                            (recyclerView.computeVerticalScrollRange() - recyclerView.height
                                    + recyclerView.paddingBottom + recyclerView.paddingTop)
                                    - recyclerView.computeVerticalScrollOffset()
                        )
                    }
                }
            }

            else -> safeScrollToPosition((trackRatio * itemCount).roundToInt())
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration?) {
        super.onConfigurationChanged(newConfig)
        visibleCountMax = 0
        // 切换分屏时隐藏快滑条，否则会出现快滑条即将隐藏时切换分屏后不显示快滑条
        trackView.isVisible = false
        thumbView.isVisible = false
        // 切换分屏后需要刷新快滑条，否则会出现快滑条位置显示不正确
        needRefreshThumbViewOnLayout = true
    }

    private fun canShowFastScroller(): Boolean {
        val fastScrollerSpace = recyclerView.height - trackView.marginBottom - trackView.marginTop
        return fastScrollerSpace > fastScrollerMinHeight
    }

    private fun LinearLayoutManager.getTotalCompletelyVisibleItemCount(): Int {
        val firstVisibleItemPosition =
            findFirstCompletelyVisibleItemPosition().takeIf { it != RecyclerView.NO_POSITION }
                ?: findFirstVisibleItemPosition()

        val lastVisibleItemPosition =
            findLastCompletelyVisibleItemPosition().takeIf { it != RecyclerView.NO_POSITION }
                ?: findLastVisibleItemPosition()

        return if ((firstVisibleItemPosition == RecyclerView.NO_POSITION) ||
            (lastVisibleItemPosition == RecyclerView.NO_POSITION)
        ) {
            RecyclerView.NO_POSITION
        } else {
            lastVisibleItemPosition - firstVisibleItemPosition
        }
    }

    private fun RecyclerView.safeScrollToPosition(position: Int, offset: Int = 0) {
        with(layoutManager) {
            when (this) {
                is LinearLayoutManager -> scrollToPositionWithOffset(position, offset)
                is RecyclerView.LayoutManager -> scrollToPosition(position)
            }
        }
    }

    private val adapterDataObserver = lazy {
        object : RecyclerView.AdapterDataObserver() {
            override fun onChanged() {
                super.onChanged()
                visibleCountMax = 0
            }

            override fun onItemRangeRemoved(positionStart: Int, itemCount: Int) {
                super.onItemRangeRemoved(positionStart, itemCount)
                visibleCountMax = 0
            }
        }
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        super.onLayout(changed, l, t, r, b)
        trackMarginTop = recyclerView.paddingTop
        trackMarginBottom = recyclerView.paddingBottom
        if (needRefreshThumbViewOnLayout) {
            recyclerView.post {
                refreshThumbView(recyclerView)
            }
            needRefreshThumbViewOnLayout = false
        }
    }

    private fun refreshThumbView(recyclerView: RecyclerView) {
        val range = recyclerView.computeVerticalScrollRange()
        val offset = recyclerView.computeVerticalScrollOffset()

        //val needScroller = range > recyclerView.height * SHOW_SCROLLER_PAGE_COUNT
        val itemCount = recyclerView.adapter?.itemCount ?: 0
        val needScroller = itemCount >= getShowMinListSize()
        val canShowScroller = canShowFastScroller()
        if (!scrollerEnable || !needScroller || (offset !in 0..range) || !canShowScroller) {
            scrollerVisible = false
            return
        }

        if (!scrollerVisible) {
            scrollerVisible = true
        }
        if (scrollerVisible && trackView.width == 0) {
            trackView.requestLayout()
        }
        val scrollRatio = clamp(offset / (range - recyclerView.height + recyclerView.paddingBottom + recyclerView.paddingTop).toFloat(), 0f, 1f)
        val finalOffset: Float = scrollRatio * (trackLength - thumbLength)
        refreshThumbView(finalOffset)
    }

    @VisibleForTesting
    fun getShowMinListSize(): Int {
        return recyclerView.layoutManager.let {
            when (it) {
                is GridLayoutManager -> {
                    when {
                        it.spanCount >= GRID_ITEM_COUNT_4 -> {
                            (SPAN_COUNT_FOUR_SCROLLER_MIN_SIZE * it.spanCount) / GRID_ITEM_COUNT_4
                        }

                        else -> SPAN_COUNT_ONE_SCROLLER_MIN_SIZE
                    }
                }

                else -> SPAN_COUNT_ONE_SCROLLER_MIN_SIZE
            }
        }
    }

    private fun refreshThumbView(offset: Float) {
        removeCallbacks(hideThumbRunnable)
        if (recyclerView.scrollState == SCROLL_STATE_IDLE) {
            postDelayed(hideThumbRunnable, VISIBLE_DURATION_DEFAULT)
        }
        Log.d(TAG, "refreshThumbView 1: $offset")
        thumbView.translationY = clamp(offset, 0f, trackLength - thumbLength)
        Log.d(TAG, "refreshThumbView 2: translationY ${thumbView.translationY}")
    }

    private fun refreshTrackMargin() {
        trackView.updateLayoutParams<MarginLayoutParams> {
            val isRTL = resources.configuration.layoutDirection == View.LAYOUT_DIRECTION_RTL
            leftMargin = if (isRTL) trackMarginEnd else trackMarginStart
            topMargin = trackMarginTop
            rightMargin = if (isRTL) trackMarginStart else trackMarginEnd
            bottomMargin = trackMarginBottom
        }
    }

    override fun onAttachedToWindow() {
        if (this::recyclerView.isInitialized) {
            attachFastScrollerToRecyclerView(recyclerView)
        }
        super.onAttachedToWindow()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun attachFastScrollerToRecyclerView(recyclerView: RecyclerView) {
        this.recyclerView = recyclerView
        recyclerView.adapter?.registerAdapterDataObserver(adapterDataObserver.value)
        recyclerView.addOnScrollListener(onScrollListener)
        ((recyclerView as? CommonRecyclerView))?.apply {
            addOnShowHideListener(object : ShowHideListener {
                override fun setFastScrollerShowEnable(showEnable: Boolean) {
                    scrollerEnable = showEnable
                }
            })
        }
        thumbView.setOnTouchListener(thumbTouchListener)
    }

    override fun onDetachedFromWindow() {
        detachFastScrollerFromRecyclerView()
        super.onDetachedFromWindow()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun detachFastScrollerFromRecyclerView() {
        if (adapterDataObserver.isInitialized()) {
            recyclerView.adapter?.unregisterAdapterDataObserver(adapterDataObserver.value)
        }
        thumbView.setOnTouchListener(null)
        recyclerView.removeOnScrollListener(onScrollListener)
    }

    interface ShowHideListener {
        fun setFastScrollerShowEnable(showEnable: Boolean)
    }

    companion object {
        private const val TAG = "RecyclerViewFastScroller"
        private const val VISIBLE_DURATION_DEFAULT = 2000L
        private const val ANIMATION_DURATION_DEFAULT = 200L
        private const val SHOW_SCROLLER_PAGE_COUNT = 5f
        private const val SCROLL_SLOP = 0
        private const val SPAN_COUNT_OFFSET = 2
        private const val THUMB_MOVE_MISTAKE_GAP = 3
        const val SPAN_COUNT_ONE_SCROLLER_MIN_SIZE = 60
        const val SPAN_COUNT_FOUR_SCROLLER_MIN_SIZE = 140
        const val GRID_ITEM_COUNT_4 = 4
    }
}

