/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/06/07, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package andes.oplus.documentsreader.task

import andes.oplus.documentsreader.formatconvert.contants.ErrorCode
import andes.oplus.documentsreader.formatconvert.interfaces.FormulaListener
import andes.oplus.documentsreader.formatconvert.interfaces.IFormulaProxyTask
import andes.oplus.documentsreader.formatconvert.interfaces.INetConfig
import andes.oplus.documentsreader.formatconvert.interfaces.LatexBean
import andes.oplus.documentsreader.formatconvert.networks.util.NetEnv
import andes.oplus.documentsreader.formatconvert.task.FormulaProxyTask
import andes.oplus.documentsreader.formatconvert.task.FormulaTask
import andes.oplus.documentsreader.formatconvert.util.NetConfig
import andes.oplus.documentsreader.formatconvert.util.NetUtil
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.koin.core.context.GlobalContext
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import java.lang.ref.WeakReference
import kotlin.test.assertTrue

class FormulaTaskTest {

    var task: FormulaTask? = null
    var code = 0

    @Before
    fun setup() {
        mockkStatic(NetUtil::class)

        val formulaTask = FormulaProxyTask()
        val netConfig = NetConfig()
        val koinApp = koinApplication {
            modules(module {
                factory<IFormulaProxyTask> { formulaTask }
            }, module {
                factory<INetConfig> { netConfig }
            })
        }
        GlobalContext.startKoin(koinApp)

        val listener = object : FormulaListener {
            override fun onFetchSuccess(latexList: ArrayList<LatexBean>?, htmlUrl: String?) {}

            override fun onFetchFailed(errorCode: Int) {
                code = errorCode
            }
        }
        task = FormulaTask.Builder()
            .context(WeakReference(mockk()))
            .environment(NetEnv.RELEASE)
            .imageBitmap(mockk())
            .formulaListener(listener)
            .build()
    }

    @Test
    fun should_fetch_failed_when_networks_unavailable() {
        //Given
        every { NetUtil.isNetworkAvailable(any()) }.returns(false)

        //When
        task?.fetchFormula()

        //Then
        assertTrue { code == ErrorCode.NETWORK_DISCONNECT }
    }


    @After
    fun endWith() {
        unmockkStatic(NetUtil::class)
    }
}