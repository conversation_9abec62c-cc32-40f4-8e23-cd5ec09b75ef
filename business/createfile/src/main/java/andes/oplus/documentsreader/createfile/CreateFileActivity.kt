/*********************************************************************
 * * Copyright (C), 2010-2023 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : CreateFileActivity.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/11
 * * Author      : W9062821
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    yuepeng                2024/4/11      1      create
 ***********************************************************************/
package andes.oplus.documentsreader.createfile

import andes.oplus.documentsreader.core.common.BaseVMActivity
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.MainScope
import andes.oplus.documentsreader.core.common.StatusBarUtils
import andes.oplus.documentsreader.createfile.databinding.CreateFileDialogBinding
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Suppress("DEPRECATION")
class CreateFileActivity : BaseVMActivity() {

    companion object {
        private const val TAG = "CreateFileActivity"
        private const val ACTION_CREATE_WP = "OPPO_CREATE_WP"
        private const val ACTION_CREATE_SS = "OPPO_CREATE_SS"
        private const val ACTION_CREATE_PG = "OPPO_CREATE_PG"
        private const val ACTION_CREATE_TXT = "OPPO_CREATE_TXT"
        private const val DELAY_FINISH_TIME = 200L
    }

    private var createFileDialog: AlertDialog? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        StatusBarUtils.adaptTaskbar(this, this.window.decorView)
        Log.d(TAG, "CreateFileActivity onCreate")
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "CreateFileActivity onResume")
        showCreateFileDialog(this)
    }

    override fun getLayoutResId(): Int {
        return R.layout.create_file_layout
    }

    override fun initView() {
    }

    override fun startObserve() {
    }

    override fun initData() {
    }

    private fun showCreateFileDialog(activity: Activity) {
        dismissCreateFileDialog()
        val binding = CreateFileDialogBinding.inflate(LayoutInflater.from(this), null, false)
        createFileDialog = COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Center)
            .setBlurBackgroundDrawable(true)
            .setView(binding.root)
            .setOnCancelListener {
                dismissCreateFileDialog()
            }
            .setTitle(R.string.yozo_res_oppo_common_new)
            .setPositiveButton(R.string.yozo_res_oppo_common_close) { _, _ ->
                dismissCreateFileDialog()
            }
            .setCancelable(true)
            .setOnDismissListener {
                Log.d(TAG, "onDismiss")
                delayFinishActivity(activity)
            }
            .create()
        binding.layoutCreateWord.setOnClickListener {
            Log.d(TAG, "create Word")
            launchActivity(this, ACTION_CREATE_WP)
        }
        binding.layoutCreateExcel.setOnClickListener {
            Log.d(TAG, "create Excel")
            launchActivity(this, ACTION_CREATE_SS)
        }
        binding.layoutCreatePpt.setOnClickListener {
            Log.d(TAG, "create PPT")
            launchActivity(this, ACTION_CREATE_PG)
        }
        binding.layoutCreateTxt.setOnClickListener {
            Log.d(TAG, "create TXT")
            launchActivity(this, ACTION_CREATE_TXT)
        }
        createFileDialog?.show()
    }

    private fun delayFinishActivity(activity: Activity) {
        MainScope.launch(Dispatchers.Default) {
            delay(DELAY_FINISH_TIME)
            activity.finish()
            activity.overridePendingTransition(0, 0)
        }
    }

    private fun dismissCreateFileDialog() {
        if (createFileDialog?.isShowing == true) {
            createFileDialog?.dismiss()
        }
    }

    override fun onDestroy() {
        dismissCreateFileDialog()
        super.onDestroy()
    }

    private fun launchActivity(context: Context, action: String) {
        dismissCreateFileDialog()
        try {
            val intent = Intent(action)
            context.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            Log.e(TAG, e.message)
        }
        this.finish()
    }
}