/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - RecycleBinModule.kt
 * Description:
 *     The api impl of recycle bin module.
 *
 * Version: 1.0
 * Date: 2024-06-25
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-06-25   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.recycle

import andes.oplus.documentsreader.recycle.actions.CleanObsoleteRecycleBinAction
import andes.oplus.documentsreader.recycle.actions.ClearAllRecycleBinAction
import andes.oplus.documentsreader.recycle.actions.CountInRecycleBinAction
import andes.oplus.documentsreader.recycle.actions.DropFromRecycleBinAction
import andes.oplus.documentsreader.recycle.actions.EraseToRecycleBinAction
import andes.oplus.documentsreader.recycle.actions.IClearAllRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.IDropFromRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.IEraseToRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.INoParamRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.IRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.IRestoreFromRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.IUnitRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.ListInRecycleBinAction
import andes.oplus.documentsreader.recycle.actions.RestoreFromRecycleBinAction
import andes.oplus.documentsreader.recycle.data.IRecycleFileRecord
import andes.oplus.documentsreader.recycle.interfaces.IRecycleBinApi
import andes.oplus.documentsreader.recycle.params.IRecycleBinEraseParam
import andes.oplus.documentsreader.recycle.params.RecycleBinQueryParam
import kotlinx.coroutines.CoroutineScope

/**
 * Will be registered by [andes.oplus.documentsreader.recycle.di.AutoDIForRecycleBin.recycleBinModule]
 */

internal class RecycleBinModule : IRecycleBinApi {
    override fun actionCleanObsolete(actionScope: CoroutineScope): IUnitRecycleFileAction =
        CleanObsoleteRecycleBinAction(actionScope)

    override fun actionClearAll(actionScope: CoroutineScope): IClearAllRecycleFileAction =
        ClearAllRecycleBinAction(actionScope)

    override fun actionCount(actionScope: CoroutineScope): INoParamRecycleFileAction<Long> =
        CountInRecycleBinAction(actionScope)

    override fun actionDrop(actionScope: CoroutineScope): IDropFromRecycleFileAction =
        DropFromRecycleBinAction(actionScope)

    override fun actionErase(
        actionScope: CoroutineScope,
        eraseParam: IRecycleBinEraseParam
    ): IEraseToRecycleFileAction = EraseToRecycleBinAction(actionScope, eraseParam)

    override fun actionList(
        actionScope: CoroutineScope
    ): IRecycleFileAction<RecycleBinQueryParam?, Collection<IRecycleFileRecord>> =
        ListInRecycleBinAction(actionScope)

    override fun actionRestore(actionScope: CoroutineScope): IRestoreFromRecycleFileAction =
        RestoreFromRecycleBinAction(actionScope)
}