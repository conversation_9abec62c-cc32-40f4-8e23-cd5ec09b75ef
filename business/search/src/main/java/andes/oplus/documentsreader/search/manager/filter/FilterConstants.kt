/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.globalsearch
 * * Version     : 1.0
 * * Date        : 2020/07/30
 * * Author      : w9007122
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.search.manager.filter

import andes.oplus.documentsreader.core.common.stringResource
import andes.oplus.documentsreader.search.R
import android.util.ArrayMap


object FilterConstants {

    const val FILTER_ITEM_ALL = -1

    const val FILTER_TIME = 1
    const val FILTER_TIME_TODAY = 1 shl 0
    const val FILTER_TIME_3_DAY = 1 shl 1
    const val FILTER_TIME_7_DAY = 1 shl 2
    const val FILTER_TIME_30_DAY = 1 shl 3

    const val FILTER_FROM = 2

    const val FILTER_IMG = 3
    const val FILTER_IMG_JPG = 1 shl 0
    const val FILTER_IMG_PNG = 1 shl 1
    const val FILTER_IMG_GIF = 1 shl 2
    const val FILTER_IMG_BMP = 1 shl 3
    const val FILTER_IMG_HEIF = 1 shl 4

    const val FILTER_VIDEO = 4
    const val FILTER_VIDEO_MP4 = 1 shl 0
    const val FILTER_VIDEO_MKV = 1 shl 1
    const val FILTER_VIDEO_AVI = 1 shl 2
    const val FILTER_VIDEO_WMV = 1 shl 3
    const val FILTER_VIDEO_3GP = 1 shl 4

    const val FILTER_AUDIO = 5
    const val FILTER_AUDIO_MP3 = 1 shl 0
    const val FILTER_AUDIO_M4A = 1 shl 1
    const val FILTER_AUDIO_AMR = 1 shl 2
    const val FILTER_AUDIO_OGG = 1 shl 3
    const val FILTER_AUDIO_AAC = 1 shl 4

    const val FILTER_DOC = 6
    const val FILTER_DOC_DOC = 1 shl 0
    const val FILTER_DOC_XLS = 1 shl 1
    const val FILTER_DOC_PPT = 1 shl 2
    const val FILTER_DOC_PDF = 1 shl 3
    const val FILTER_DOC_OFD = 1 shl 4
    const val FILTER_DOC_IWORK = 1 shl 5
    const val FILTER_DOC_XMIND = 1 shl 6
    const val FILTER_DOC_VISIO = 1 shl 7
    const val FILTER_DOC_TXT = 1 shl 8
    const val FILTER_DOC_CAD = 1 shl 9
    const val FILTER_DOC_PSD = 1 shl 10
    const val FILTER_DOC_AI = 1 shl 11
    const val FILTER_DOC_MD = 1 shl 12

    const val FILTER_COMPRESS = 7
    const val FILTER_COMPRESS_ZIP = 1 shl 0
    const val FILTER_COMPRESS_RAR = 1 shl 1
    const val FILTER_COMPRESS_7ZIP = 1 shl 2
    const val FILTER_COMPRESS_JAR = 1 shl 3

    const val FILTER_FROM_SUPER_APP_ONE = 1 shl 0
    const val FILTER_FROM_SUPER_APP_TWO = 1 shl 1
    const val FILTER_FROM_DOWNLOAD = 1 shl 5
    const val FILTER_FROM_TRANSFER = 1 shl 6
    const val FILTER_FROM_BLUETOOTH = 1 shl 7
    const val FILTER_FROM_PC_CONNECT = 1 shl 8
    const val FILTER_FROM_CURRENT = 1 shl 9
    const val FILTER_FROM_OWORK = 1 shl 10

    const val FILTER_THIRD_APP = 9
    const val FILTER_THIRD_APP_QQ = 1 shl 11
    const val FILTER_THIRD_APP_WECHAT = 1 shl 12
    const val FILTER_THIRD_APP_WEWORK = 1 shl 13
    const val FILTER_THIRD_APP_DINGTALK = 1 shl 14
    const val FILTER_THIRD_APP_FEISHU = 1 shl 15

    const val FILTER_THIRD_APP_OR_OLD_FROM_APP_QQ = 1 shl 16
    const val FILTER_THIRD_APP_OR_OLD_FROM_APP_WECHAT = 1 shl 17

    /**
     * Not use static to store it to avoid app cache increased
     */
    fun getAllFilterConditionDesc(): ArrayMap<Int, String> {
        return ArrayMap<Int, String>().apply {
            val desc = arrayOf(
                R.string.search_filtrate_time,
                R.string.search_filtrate_source,
                R.string.search_filtrate_format,
                R.string.search_filtrate_source,
                R.string.search_filtrate_format,
                R.string.search_filtrate_format,
                R.string.search_filtrate_format,
                R.string.search_filtrate_format
            )
            getAllFilterConditions().forEachIndexed { index, filter ->
                put(filter, stringResource(desc[index]))
            }
        }
    }

    /**
     * Not use static to store it to avoid app cache increased
     */
    fun getAllFilterConditions(): List<Int> {
        return mutableListOf(
            FILTER_TIME,
            FILTER_FROM,
            FILTER_DOC,
            FILTER_THIRD_APP
        )
    }
}
