<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="itemRecord"
            type="com.soundrecorder.browsefile.search.load.ItemSearchViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/summary_info_root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/dp2">

        <TextView
            android:id="@+id/convert_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:paddingVertical="@dimen/dp16"
            android:fontFamily="sans-serif-regular"
            android:maxLines="2"
            android:textColor="@color/item_record_extra_info_color"
            android:textSize="@dimen/text_search_result_text_size"
            android:gravity="center_vertical"
            app:layout_constraintEnd_toStartOf="@id/iv_convert_more"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="this is the convert info" />

        <ImageView
            android:id="@+id/iv_convert_more"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:src="@drawable/ic_search_arrow_right"
            app:layout_constraintBottom_toBottomOf="@id/convert_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/convert_text" />

        <View
            android:id="@+id/divider_line_convert_top"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp0_5"
            android:background="@color/coui_color_divider"
            app:layout_constraintBottom_toTopOf="@id/convert_text"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>


        <TextView
            android:id="@+id/summary_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:paddingVertical="@dimen/dp16"
            android:fontFamily="sans-serif-regular"
            android:maxLines="2"
            android:textColor="@color/item_record_extra_info_color"
            android:textSize="@dimen/text_search_result_text_size"
            android:gravity="center_vertical"
            app:layout_constraintEnd_toStartOf="@id/iv_summary_more"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toTopOf="@id/divider_line_convert_top"
            tools:text="this is the summary info" />

        <ImageView
            android:id="@+id/iv_summary_more"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:src="@drawable/ic_search_arrow_right"
            app:layout_constraintBottom_toBottomOf="@id/summary_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/summary_text" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_summary_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="summary_text,iv_summary_more" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
