/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - RecycleBinCleanTriggerTest.kt
 * Description:
 * The test cases for RecycleBinCleanTrigger
 *
 * Version: 1.0
 * Date: 2024-07-05
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-07-05   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.recycle

import andes.oplus.documentsreader.recycle.actions.IUnitRecycleFileAction
import andes.oplus.documentsreader.recycle.interfaces.IRecycleBinApi
import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.SystemClock
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class RecycleBinCleanTriggerTest : Assert() {

    private companion object {
        private const val PACKAGE_NAME = "andes.oplus.documentsreader"
    }

    private lateinit var activity: Activity
    private lateinit var appContext: Context

    private fun mockRecycleBin(isIntegrated: Boolean): IUnitRecycleFileAction {
        val cleanObsoleteAction = mockk<IUnitRecycleFileAction>(relaxed = true)
        if (isIntegrated) {
            val recycleBinApi = mockk<IRecycleBinApi> {
                every { actionCleanObsolete(any()) } returns cleanObsoleteAction
            }
            every { RecycleBinCleanTrigger.obtainRecycleBinApi() } returns recycleBinApi
        }
        return cleanObsoleteAction
    }

    private fun mockCurrentProcess(isMainProcess: Boolean) {
        if (isMainProcess) {
            every { Application.getProcessName() } returns PACKAGE_NAME
        } else {
            every { Application.getProcessName() } returns "$PACKAGE_NAME:sub"
        }
    }

    private fun mockTriggerTime(last: Long?, currentOffset: Long) {
        last?.let { RecycleBinCleanTrigger.lastTriggerTime = it }
        every { SystemClock.uptimeMillis() } returns ((last ?: 0) + currentOffset)
    }

    @Before
    fun setUp() {
        mockkStatic(RecycleBinCleanTrigger::class, SystemClock::class, Application::class)
        RecycleBinCleanTrigger.reset()
        appContext = mockk {
            every { packageName } returns PACKAGE_NAME
            every { applicationContext } returns this
        }
        activity = mockk {
            every { packageName } returns PACKAGE_NAME
            every { applicationContext } returns appContext
            every { componentName } returns mockk()
        }
    }

    @After
    fun tearDown() {
        unmockkStatic(RecycleBinCleanTrigger::class, SystemClock::class, Application::class)
        RecycleBinCleanTrigger.reset()
    }

    @Test
    fun `should not trigger when triggerCleanObsolete if recycle bin is not integrated`() {
        // Given
        val cleanAction = mockRecycleBin(false)
        mockCurrentProcess(true)
        mockTriggerTime(null, 0L)

        // When
        RecycleBinCleanTrigger.triggerCleanObsolete(activity)

        // Then
        verify { cleanAction wasNot Called }
    }

    @Test
    fun `should not trigger when triggerCleanObsolete if triggered not long ago`() {
        // Given
        val cleanAction = mockRecycleBin(true)
        mockCurrentProcess(true)
        mockTriggerTime(100000L, 100L)

        // When
        RecycleBinCleanTrigger.triggerCleanObsolete(activity)

        // Then
        verify { cleanAction wasNot Called }
    }

    @Test
    fun `should not trigger when triggerCleanObsolete if not main process`() {
        // Given
        val cleanAction = mockRecycleBin(true)
        mockCurrentProcess(false)
        mockTriggerTime(null, 0L)

        // When
        RecycleBinCleanTrigger.triggerCleanObsolete(activity)

        // Then
        verify { cleanAction wasNot Called }
    }

    @Test
    fun `should trigger when triggerCleanObsolete if can trigger it`() {
        // Given
        val cleanAction = mockRecycleBin(true)
        mockCurrentProcess(true)
        mockTriggerTime(null, 0L)

        // When
        RecycleBinCleanTrigger.triggerCleanObsolete(activity)

        // Then
        verify { cleanAction.execute(appContext) }
    }
}