/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  PlaybackSummaryFragment
 * * Description: 播放页面摘要Tab Fragment
 * * Version: 1.0
 * * Date : 2024/12/19
 * * Author: Assistant
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  Assistant    2024/12/19   1.0    build this module
 ****************************************************************/
package com.soundrecorder.playback.aisummary

import android.app.Activity
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.SmartSummaryResult
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.modulerouter.BrowseFileAction
import com.soundrecorder.modulerouter.aisummary.AISummaryAction
import com.soundrecorder.modulerouter.aisummary.IAISummaryCallback
import com.soundrecorder.modulerouter.aisummary.IAISummaryManager
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.databinding.FragmentPlaybackSummaryBinding
import com.soundrecorder.summary.RecordSummaryManager.SUMMARY_STATE_CLIENT_END
import com.soundrecorder.summary.RecordSummaryManager.SUMMARY_STATE_CLIENT_GENERATING
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 播放页面摘要Fragment
 * 显示AI摘要内容和状态
 * 实现IAISummaryManager接口以处理权限回调
 */
class PlaybackSummaryFragment : Fragment(), IAISummaryManager {

    companion object {
        private const val TAG = "PlaybackSummaryFragment"
        private const val PAGE_FROM_PLAYBACK = 1 // 参考PlaybackContainerFragment的常量
    }

    private var _binding: FragmentPlaybackSummaryBinding? = null
    private val binding get() = _binding!!

    private lateinit var mViewModel: PlaybackActivityViewModel
    private val summaryViewModel: AISummaryViewModel by viewModels()
    private var summaryAdapter: SummaryContentAdapter? = null
    private var isPageSelected = false
    private var currentRecordId: Long = -1
    private var isCallbackRegistered = false
    private var currentSummaryContent: String? = null

    private var mAISummaryManagerImpl: AISummaryManagerImpl? = null

    // 删除重复的aiSummaryCallback，统一使用AISummaryViewModel中的回调管理

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPlaybackSummaryBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        mViewModel = ViewModelProvider(requireActivity())[PlaybackActivityViewModel::class.java]

        initViews()
        initObservers()
        initAISummaryManagerImpl()
        // 移除重复的回调注册，统一由AISummaryViewModel管理
    }

    private fun initViews() {
        // 初始化RecyclerView
        summaryAdapter = SummaryContentAdapter()
        binding.recyclerSummary.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = summaryAdapter
        }

        // 生成摘要按钮点击事件 - 使用完整流程（包含权限检查、插件下载、文件检查）
        binding.btnGenerateSummary.setOnClickListener {
            summaryViewModel.generateSummaryWithManager(activity, mAISummaryManagerImpl)
        }

        // 重新生成按钮点击事件
        binding.btnRegenerateSummary.setOnClickListener {
            summaryViewModel.regenerateSummary()
        }

        // 初始状态由SummaryViewModel管理
    }

    /**
     * 初始化AISummaryManagerImpl，参考PlaybackContainerFragment的实现
     */
    private fun initAISummaryManagerImpl() {
        if (mAISummaryManagerImpl == null) {
            mAISummaryManagerImpl = AISummaryManagerImpl()
        }
        mViewModel.let {
            mAISummaryManagerImpl?.registerForAISummary(this, !it.mIsFromOtherApp)
        }
    }

    private fun initObservers() {
        DebugUtil.d(TAG, "initObservers: start")

        // 1. 观察SummaryViewModel的数据变化
        initSummaryViewModelObserver()

        // 2. 观察BrowseFileActivityViewModel的数据变化（主要数据源）
        initBrowseActivityViewModelObserver()

        // 3. 观察PlaybackActivityViewModel的摘要状态变化
        initViewModelObserver()

        // 4. 检查当前是否已经有有效的recordId
        checkCurrentRecordId()

        DebugUtil.d(TAG, "initObservers: completed")
    }

    /**
     * 观察SummaryViewModel的数据变化
     */
    private fun initSummaryViewModelObserver() {
        // 观察摘要状态
        summaryViewModel.summaryState.observe(viewLifecycleOwner) { state ->
            DebugUtil.d(TAG, "SummaryViewModel state changed: $state")
            updateSummaryState(state)
        }

        // 观察摘要项列表
        summaryViewModel.summaryItems.observe(viewLifecycleOwner) { items ->
            DebugUtil.d(TAG, "SummaryViewModel items changed: ${items.size}")
            summaryAdapter?.updateData(items)
        }

        // 观察错误信息
        summaryViewModel.errorMessage.observe(viewLifecycleOwner) { errorMsg ->
            if (errorMsg.isNotEmpty()) {
                DebugUtil.e(TAG, "SummaryViewModel error: $errorMsg")
                showErrorMessage(errorMsg)
            }
        }

        // 观察摘要内容
        summaryViewModel.summaryContent.observe(viewLifecycleOwner) { content ->
            if (content != null) {
                if (content.isNotEmpty()) {
                    DebugUtil.d(TAG, "SummaryViewModel content updated: ${content.length} chars")
                    updateSummaryInfo(content)
                }
            }
        }
    }

    /**
     * 观察BrowseFileActivityViewModel的数据变化
     */
    private fun initBrowseActivityViewModelObserver() {
        try {
            val browseFileActivityViewModel = BrowseFileAction.getBrowseActivityViewModel(requireActivity() as AppCompatActivity)
            BrowseFileAction.getViewModelPlayData<StartPlayModel>(browseFileActivityViewModel)?.observe(viewLifecycleOwner) { startPlayModel ->
                DebugUtil.d(TAG, "BrowseFileActivityViewModel data changed: $startPlayModel")

                if (startPlayModel == null) {
                    DebugUtil.i(TAG, "play data to be null")
                    return@observe
                }

                if (startPlayModel.mediaId == currentRecordId) {
                    DebugUtil.i(TAG, "play data id not change, id=${startPlayModel.mediaId}")
                    return@observe
                }

                DebugUtil.i(TAG, "play record data changed to $startPlayModel")

                // 音频发生变化，处理recordId变化
                val newRecordId = startPlayModel.mediaId
                if (newRecordId > 0) {
                    handleRecordIdChange(newRecordId)
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "Failed to observe BrowseFileActivityViewModel: ${e.message}")
            // 如果观察失败，使用延迟检查作为备用方案
            scheduleDelayedRecordIdCheck()
        }
    }

    /**
     * 观察PlaybackActivityViewModel的相关状态
     */
    private fun initViewModelObserver() {
        // 观察摘要状态变化
        mViewModel.mSummaryStatus?.observe(viewLifecycleOwner) { summaryStatus ->
            DebugUtil.d(TAG, "Summary status changed: $summaryStatus")
            handleSummaryStatusChange(summaryStatus)
        }
    }

    /**
     * 检查当前是否已经有有效的recordId
     */
    private fun checkCurrentRecordId() {
        // 检查startPlayModel
        mViewModel.startPlayModel?.let { startPlayModel ->
            val recordId = startPlayModel.mediaId
            if (recordId > 0 && recordId != currentRecordId) {
                DebugUtil.d(TAG, "checkCurrentRecordId: startPlayModel recordId=$recordId")
                handleRecordIdChange(recordId)
                return
            }
        }

        // 检查直接的recordId
        if (mViewModel.recordId > 0 && mViewModel.recordId != currentRecordId) {
            DebugUtil.d(TAG, "checkCurrentRecordId: direct recordId=${mViewModel.recordId}")
            handleRecordIdChange(mViewModel.recordId)
            return
        }

        // 如果都没有，延迟检查
        DebugUtil.d(TAG, "checkCurrentRecordId: no valid recordId found, scheduling delayed check")
        scheduleDelayedRecordIdCheck()
    }

    private fun scheduleDelayedRecordIdCheck() {
        lifecycleScope.launch {
            // 延迟500ms再检查recordId
            kotlinx.coroutines.delay(500)

            if (currentRecordId <= 0 && mViewModel.recordId > 0) {
                DebugUtil.d(TAG, "scheduleDelayedRecordIdCheck: found recordId=${mViewModel.recordId}")
                handleRecordIdChange(mViewModel.recordId)
            } else if (currentRecordId <= 0) {
                DebugUtil.w(TAG, "scheduleDelayedRecordIdCheck: recordId still not available after delay")
                // 再延迟1秒检查一次
                kotlinx.coroutines.delay(1000)
                if (currentRecordId <= 0 && mViewModel.recordId > 0) {
                    DebugUtil.d(TAG, "scheduleDelayedRecordIdCheck: found recordId=${mViewModel.recordId} after 1.5s")
                    handleRecordIdChange(mViewModel.recordId)
                }
            }
        }
    }

    private fun handleRecordIdChange(newRecordId: Long) {
        DebugUtil.d(TAG, "handleRecordIdChange: $currentRecordId -> $newRecordId")

        // 更新当前录音ID
        currentRecordId = newRecordId

        // 通知SummaryViewModel recordId变化
        summaryViewModel.setRecordId(newRecordId)
    }

    private fun handleSummaryStatusChange(summaryStatus: Int?) {
        when (summaryStatus) {
            SUMMARY_STATE_CLIENT_GENERATING -> {
                if (isPageSelected) {
                    updateSummaryState(AISummaryViewModel.SummaryState.GENERATING)
                }
            }
            SUMMARY_STATE_CLIENT_END -> {
                if (isPageSelected) {
                    // 摘要生成完成，重新检查摘要内容
                    checkExistingSummary()
                }
            }
        }
    }

    // 删除重复的回调注册和注销方法，统一由AISummaryViewModel管理

    private fun checkExistingSummary() {
        // 优先使用currentRecordId，如果没有则使用mViewModel.recordId
        val recordId = if (currentRecordId > 0) {
            currentRecordId
        } else {
            mViewModel.recordId
        }

        if (recordId <= 0) {
            DebugUtil.w(TAG, "checkExistingSummary: Invalid recordId: $recordId (currentRecordId=$currentRecordId, mViewModel.recordId=${mViewModel.recordId})")
            return
        }

        DebugUtil.d(TAG, "checkExistingSummary for recordId: $recordId")

        lifecycleScope.launch {
            try {
                // 检查是否正在生成摘要
                val isGenerating = withContext(Dispatchers.IO) {
                    AISummaryAction.checkIsTaskRunning(recordId)
                }

                // 在主线程更新UI
                withContext(Dispatchers.Main) {
                    DebugUtil.d(TAG, "checkExistingSummary: isGenerating=$isGenerating")
                    if (isGenerating) {
                        updateSummaryState(AISummaryViewModel.SummaryState.GENERATING)
                    } else {
                        updateSummaryState(AISummaryViewModel.SummaryState.EMPTY)
                    }
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "Error checking existing summary: ${e.message}")
                withContext(Dispatchers.Main) {
                    updateSummaryState(AISummaryViewModel.SummaryState.EMPTY)
                }
            }
        }
    }

    // 删除重复的generateSummary方法，统一使用AISummaryViewModel的方法



    // 删除重复的regenerateSummary方法，统一使用AISummaryViewModel的方法

    private fun updateSummaryState(state: AISummaryViewModel.SummaryState) {
        safeUpdateUI {
            DebugUtil.d(TAG, "updateSummaryState: $state")

            when (state) {
                AISummaryViewModel.SummaryState.EMPTY -> {
                    binding.layoutEmpty.visibility = View.VISIBLE
                    binding.layoutGenerating.visibility = View.GONE
                    binding.layoutContent.visibility = View.GONE
                    binding.layoutError.visibility = View.GONE
                }
                AISummaryViewModel.SummaryState.GENERATING -> {
                    binding.layoutEmpty.visibility = View.GONE
                    binding.layoutGenerating.visibility = View.VISIBLE
                    binding.layoutContent.visibility = View.GONE
                    binding.layoutError.visibility = View.GONE

                    // 开始动画
                    binding.progressBar.visibility = View.VISIBLE
                    binding.textGenerating.text = "正在生成摘要..."
                }
                AISummaryViewModel.SummaryState.COMPLETED -> {
                    binding.layoutEmpty.visibility = View.GONE
                    binding.layoutGenerating.visibility = View.GONE
                    binding.layoutContent.visibility = View.VISIBLE
                    binding.layoutError.visibility = View.GONE
                }
                AISummaryViewModel.SummaryState.ERROR -> {
                    binding.layoutEmpty.visibility = View.GONE
                    binding.layoutGenerating.visibility = View.GONE
                    binding.layoutContent.visibility = View.GONE
                    binding.layoutError.visibility = View.VISIBLE
                }
                AISummaryViewModel.SummaryState.STOPPED -> {
                    binding.layoutEmpty.visibility = View.VISIBLE
                    binding.layoutGenerating.visibility = View.GONE
                    binding.layoutContent.visibility = View.GONE
                    binding.layoutError.visibility = View.GONE
                }
            }
        }
    }

    /**
     * 显示错误信息
     */
    private fun showErrorMessage(errorMsg: String) {
        safeUpdateUI {
            binding.textErrorMessage.text = errorMsg
        }
    }

    /**
     * 更新摘要信息
     */
    private fun updateSummaryInfo(content: String) {
        safeUpdateUI {
            // 可以在这里显示摘要的统计信息，如字数、项目数等
            val items = SummaryItem.parseContent(content)
            val contentCount = items.count { it.type == SummaryItemType.CONTENT }

            val infoText = "共${items.size}项内容"
            // 如果有相应的UI组件，可以显示这个信息
            DebugUtil.d(TAG, "Summary info: $infoText")
        }
    }

    private fun displaySummaryResult(jsonResult: String) {
        try {
            val summaryResult = GsonUtil.fromJson(jsonResult, SmartSummaryResult::class.java)

            val summaryContent = summaryResult?.lastSummary
            if (!summaryContent.isNullOrEmpty()) {
                displaySummaryContent(summaryContent)
            } else {
                showErrorMessage(AISummaryAction.CONTENT_LESS_ERROR, "摘要内容为空")
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "Failed to parse summary result: ${e.message}")
            showErrorMessage(AISummaryAction.DATA_PARSE_ERROR, "解析摘要结果失败")
        }
    }

    private fun displaySummaryContent(content: String) {
        // 保存摘要内容
        currentSummaryContent = content

        // 解析摘要内容并显示
        val summaryItems = parseSummaryContent(content)
        summaryAdapter?.updateData(summaryItems)

        binding.textSummaryTitle.text = "录音摘要"
        binding.textSummaryTime.text = "生成时间：${getCurrentTimeString()}"
    }

    private fun parseSummaryContent(content: String): List<SummaryItem> {
        // 使用统一的类型管理系统，简化解析逻辑
        return SummaryItem.parseContent(content)
    }

    private fun showErrorMessage(errorCode: Int, errorMsg: String?) {
        val message = when (errorCode) {
            AISummaryAction.NETWORK_ERROR -> "网络连接失败，请检查网络设置"
            AISummaryAction.CONTENT_LESS_ERROR -> "录音内容较少，无法生成摘要"
            AISummaryAction.SERVER_ERROR -> "服务异常，摘要生成失败"
            AISummaryAction.REQUEST_TIMEOUT -> "请求超时，请重试"
            else -> errorMsg ?: "生成摘要失败，错误码：$errorCode"
        }

        binding.textErrorMessage.text = message
        binding.btnRetry.setOnClickListener {
            summaryViewModel.generateSummaryWithManager(activity, mAISummaryManagerImpl)
        }
    }



    private fun clearSummaryData() {
        summaryAdapter?.updateData(emptyList())
    }

    private fun getCurrentTimeString(): String {
        return java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
            .format(java.util.Date())
    }

    /**
     * 页面选中状态变化
     */
    fun onPageSelectedStateChange(isSelected: Boolean) {
        val wasSelected = isPageSelected
        isPageSelected = isSelected
        DebugUtil.d(TAG, "onPageSelectedStateChange: $wasSelected -> $isSelected")

        if (isSelected && !wasSelected) {
            // 页面从未选中变为选中时
            DebugUtil.d(TAG, "Summary tab selected, checking summary status")

            // 如果recordId还没有准备好，先尝试延迟检查
            if (currentRecordId <= 0) {
                DebugUtil.d(TAG, "Summary tab selected but recordId not ready, scheduling check")
                scheduleDelayedRecordIdCheck()
            }

            // 检查摘要状态（回调管理已统一到AISummaryViewModel）
            checkExistingSummary()

            // 如果有缓存的摘要内容，直接显示
            currentSummaryContent?.let { content ->
                if (content.isNotEmpty()) {
                    displaySummaryContent(content)
                }
            }
        } else if (!isSelected && wasSelected) {
            // 页面从选中变为未选中时
            DebugUtil.d(TAG, "Summary tab deselected")
            // 可以在这里进行一些清理工作，但不注销回调，因为可能还会回到这个页面
        }
    }

    override fun onDestroyView() {
        DebugUtil.d(TAG, "onDestroyView")

        // 清理AISummaryManagerImpl
        mAISummaryManagerImpl = null

        // 清理资源
        summaryAdapter = null
        currentSummaryContent = null
        currentRecordId = -1
        isPageSelected = false

        // 回调注销已统一到AISummaryViewModel的onCleared方法中
        super.onDestroyView()
        _binding = null
    }

    override fun onPause() {
        super.onPause()
        DebugUtil.d(TAG, "onPause")
    }

    override fun onResume() {
        super.onResume()
        DebugUtil.d(TAG, "onResume")

        // 如果页面当前被选中，重新检查摘要状态
        if (isPageSelected) {
            checkExistingSummary()
        }
    }

    /**
     * 检查Fragment是否处于有效状态
     */
    private fun isFragmentValid(): Boolean {
        return isAdded && !isDetached && !isRemoving && activity != null && _binding != null
    }

    /**
     * 安全地更新UI状态
     */
    private fun safeUpdateUI(action: () -> Unit) {
        if (isFragmentValid()) {
            try {
                action()
            } catch (e: Exception) {
                DebugUtil.e(TAG, "Error updating UI: ${e.message}")
            }
        }
    }



    override fun registerForAISummary(viewModelStoreOwner: Fragment, fromMainProcess: Boolean) {
        // 这个方法在initAISummaryManagerImpl中已经通过mAISummaryManagerImpl调用
        DebugUtil.d(TAG, "registerForAISummary called on Fragment itself")
    }

    override fun startAISummaryClickHandle(
        activity: Activity?,
        selectedMediaIdList: MutableList<Long>?,
        pageFrom: Int?,
        isOpenSwitch: Boolean
    ) {
        // 委托给mAISummaryManagerImpl处理
        mAISummaryManagerImpl?.startAISummaryClickHandle(activity, selectedMediaIdList, pageFrom, isOpenSwitch)
    }

    override fun checkPluginsDownload(activity: Activity?, callback: ((Boolean) -> Unit)?, isOpenSwitch: Boolean) {
        // 委托给mAISummaryManagerImpl处理
        mAISummaryManagerImpl?.checkPluginsDownload(activity, callback, isOpenSwitch)
    }

    override fun startOrResumeAISummary(
        activity: Activity?,
        selectedMediaIdList: MutableList<Long>?,
        isOpenSwitch: Boolean
    ) {
        // 委托给mAISummaryManagerImpl处理
        mAISummaryManagerImpl?.startOrResumeAISummary(activity, selectedMediaIdList, isOpenSwitch)
    }

    /**
     * 权限确认后的处理 - 这是关键方法
     * 当用户在权限对话框中点击确认后，会调用这个方法
     */
    override fun doClickPermissionAISummaryOK(activity: Activity?, pageFrom: Int?, isOpenSwitch: Boolean) {
        DebugUtil.d(TAG, "doClickPermissionAISummaryOK: pageFrom=$pageFrom, isOpenSwitch=$isOpenSwitch")

        // 委托给mAISummaryManagerImpl处理权限确认后的逻辑
        mAISummaryManagerImpl?.doClickPermissionAISummaryOK(activity, pageFrom, isOpenSwitch)

        // 同时更新UI状态，表示权限已确认，正在启动摘要
        safeUpdateUI {
            updateSummaryState(AISummaryViewModel.SummaryState.GENERATING)
        }
    }

    override fun release() {
        DebugUtil.d(TAG, "IAISummaryManager.release")
        // 委托给mAISummaryManagerImpl处理
        mAISummaryManagerImpl?.release()
    }

    override fun releaseAll() {
        DebugUtil.d(TAG, "IAISummaryManager.releaseAll")
        // 委托给mAISummaryManagerImpl处理
        mAISummaryManagerImpl?.releaseAll()
    }
}


