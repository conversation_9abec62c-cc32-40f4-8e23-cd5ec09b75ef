/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchHistoryDBHelper.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/20
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/9/20      1.0        create
 ***********************************************************************/
package andes.oplus.documentsreader.search.database

import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.database.AppDatabase
import andes.oplus.documentsreader.search.database.dao.SearchHistoryDao
import andes.oplus.documentsreader.search.database.data.SearchHistoryEntity
import android.content.Context
import androidx.annotation.VisibleForTesting
import androidx.annotation.WorkerThread

internal class SearchHistoryDBHelper(private val context: Context) {

    private val _searchHistoryDao by lazy { SearchHistoryDatabase.getInstance(context).searchHistoryDao() }

    @VisibleForTesting
    internal val searchHistoryDao: SearchHistoryDao
        get() = _searchHistoryDao

    @WorkerThread
    fun getAllSearchHistory(): List<SearchHistoryEntity>? {
        return searchHistoryDao.getAllSearchHistory()
    }

    @WorkerThread
    fun addSearchHistory(keyWords: String): Long? {
        return searchHistoryDao.insertItem(SearchHistoryEntity().apply {
            mId = null
            mSearchContent = keyWords
            mSearchTime = System.currentTimeMillis()
        })
    }

    @WorkerThread
    fun deleteSearchHistory(ids: List<Long>): Int {
        return searchHistoryDao.deleteSearchHistory(ids) ?: -1
    }

    @WorkerThread
    fun updateSearchHistory(id: Long, time: Long?): Int {
        return searchHistoryDao.updateSearchHistory(id, time ?: System.currentTimeMillis()) ?: -1
    }

    @WorkerThread
    fun clearHistory(): Int {
        return searchHistoryDao.clearHistory() ?: -1
    }
}