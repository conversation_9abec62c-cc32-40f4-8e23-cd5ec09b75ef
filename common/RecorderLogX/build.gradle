apply from:"../../common_build.gradle"

dependencies {

    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation "androidx.appcompat:appcompat:${prop_appcompatVersion}"

    //NearX-日志打捞sdk 主包
    implementation "com.oplus:log:$prop_nearxLogMainVersion"
    //NearX-日志打捞sdk 国内域名包（内销版本依赖）
    implementation "com.oplus:log-domain-cn:$prop_nearxLogDomesticVersion"

    implementation project(':common:modulerouter')
    implementation project(':common:RecorderLogBase')
    kapt "com.inno.ostitch:stitch-compile:${stitchCompileVersion}"
    implementation "com.inno.ostitch:stitch:${stitchVersion}"
    implementation "com.inno.ostitch:stitch-annotation:${stitchAnnotationVersion}"


}