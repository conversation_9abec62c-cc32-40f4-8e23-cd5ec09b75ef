/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: UserProtocolActivity
 * * Description: UserProtocolActivity
 * * Version: 1.0
 * * Date : 2024/03/01
 * * Author:ztf
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *        ztf           2024/03/01      1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.impl.ui

import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.compat.FeatureCompat
import andes.oplus.documentsreader.core.common.stringResource
import andes.oplus.documentsreader.core.common.utils.BrandUtil
import andes.oplus.documentsreader.impl.helper.LINE_MARGIN_LARGE
import andes.oplus.documentsreader.impl.helper.LINE_MARGIN_MIDDLE
import andes.oplus.documentsreader.impl.helper.addSpace
import andes.oplus.documentsreader.impl.helper.addText
import andes.oplus.documentsreader.impl.helper.addTitle
import andes.oplus.documentsreader.impl.helper.appendHtmlText
import andes.oplus.documentsreader.impl.helper.appendLine
import andes.oplus.documentsreader.impl.helper.appendText
import andes.oplus.documentsreader.impl.helper.bold
import andes.oplus.documentsreader.impl.helper.dateString
import andes.oplus.documentsreader.impl.utils.FlexibleWindowUtil
import andes.oplus.documentsreader.impl.utils.LanguageUtil
import andes.oplus.documentsreader.impl.utils.OpenOtherPageUtil
import andes.oplus.documentsreader.userprivacy.impl.R
import android.app.Activity
import android.app.ActivityOptions
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import androidx.appcompat.widget.LinearLayoutCompat
import com.oplus.flexiblewindow.FlexibleWindowManager

class UserProtocolActivity : BaseSeriesActivity() {

    override fun getLayoutResId(): Int {
        return R.layout.activity_user_protocol
    }

    override fun initData() {
        super.initData()
        fillContent(findViewById(R.id.container))
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        val id = item.itemId
        if (id == android.R.id.home) {
            finish()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    @Suppress("LongMethod")
    private fun fillContent(container: LinearLayoutCompat) {
        container.apply {
            addTitle(
                stringResource(
                    R.string.documents_user_protocol_app_title,
                    stringResource(R.string.documents_app_name)
                )
            )
            addDateContent()
            addText {
                appendHtmlText(getProtocolHeadContent())
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_02))
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_03))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendHtmlText(stringResource(R.string.user_protocol_content_04)) {
                    if (it == "link_url_protocol") {
                        OpenOtherPageUtil.openWebUrl(this@UserProtocolActivity, getUrlAccountUseProtocol())
                    } else {
                        OpenOtherPageUtil.openWebUrl(this@UserProtocolActivity, getUrlAccountPrivacyPolicy())
                    }
                }
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_05))
                addSpace(this, LINE_MARGIN_LARGE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_06))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addAboutServiceContent()
            addText {
                appendText(stringResource(R.string.user_protocol_1_2_content_20240724))
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_12))
                addSpace(this, LINE_MARGIN_LARGE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_13))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_14))
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_15))
                addSpace(this, LINE_MARGIN_LARGE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_16))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_17))
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_18))
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_19))
                addSpace(this, LINE_MARGIN_LARGE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_20))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_21))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_22))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_23))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_24))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_25))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_26))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_27))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_28))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_29))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_30))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_31))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_32))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_33))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_34))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_35))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_36))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_37))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_38))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_39))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_40))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_41))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_42))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_43))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_4_4_content_20240724))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_45))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_46))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_47))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_48))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_49))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_50))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_51))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_52))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_4_6_content_20240724))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_54))
                bold()
                addSpace(this, LINE_MARGIN_LARGE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_55))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_56))
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_5_2_content_20240724))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_5_3_content_20240724))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_59))
                bold()
                addSpace(this, LINE_MARGIN_LARGE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_60))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendHtmlText(stringResource(R.string.user_protocol_content_61))
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendHtmlText(stringResource(R.string.user_protocol_content_62))
                addSpace(this, LINE_MARGIN_LARGE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_63))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendHtmlText(
                    stringResource(
                        R.string.documents_user_protocol_content_64,
                        stringResource(R.string.documents_app_name)
                    )
                ) {
                    PrivacyPolicyActivity.start(this@UserProtocolActivity)
                }
                addSpace(this, LINE_MARGIN_LARGE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_65))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_66))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_67))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_68))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_69))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_70))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_71))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_72))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_73))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_74))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_75))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_76))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_77))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_78))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_79))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_80))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_81))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_82))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_83))
                bold()
                addSpace(this, LINE_MARGIN_LARGE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_84))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_85))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_86))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_87))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_88))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_89))
                appendLine()
                appendText(stringResource(R.string.user_protocol_content_90))
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_91))
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_92))
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_93))
                addSpace(this, LINE_MARGIN_LARGE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_94))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_95))
                addSpace(this, LINE_MARGIN_LARGE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_96))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_97))
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_98))
                addSpace(this, LINE_MARGIN_LARGE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_99))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_100))
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_101))
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_102))
                addSpace(this, LINE_MARGIN_LARGE)
            }
            addText {
                appendText(stringResource(R.string.user_protocol_content_103))
                bold()
                addSpace(this, LINE_MARGIN_MIDDLE)
            }
            addText {
                val contactLink = getContactLink()
                appendHtmlText(contactLink.first) {
                    if (contactLink.second) {
                        OpenOtherPageUtil.openEmailAddress(this@UserProtocolActivity, it)
                    } else {
                        OpenOtherPageUtil.openWebUrl(this@UserProtocolActivity, it)
                    }
                }
                addSpace(this, LINE_MARGIN_LARGE)
            }
        }
    }

    private fun LinearLayoutCompat.addAboutServiceContent() {
        addText {
            appendText(stringResource(R.string.user_protocol_content_1_1_title_20240725))
            appendLine()
            appendText(stringResource(R.string.user_protocol_content_08_v2_20240909))
            appendLine()
            appendText(stringResource(R.string.user_protocol_content_09_v1))
            appendLine()
            appendText(stringResource(R.string.user_protocol_content_10_pdf_20240909))
            appendLine()
            appendText(stringResource(R.string.user_protocol_content_10_v1))
            appendLine()
            appendText(stringResource(R.string.user_protocol_ertract_formula_content_v2))
            appendLine()
            appendText(stringResource(R.string.user_protocol_1_1_ertract_chart_content_20240724, SIX))
            appendLine()
            appendText(stringResource(R.string.user_protocol_1_1_rewrite_content_20240724, SEVEN))
            appendLine()
            appendText(stringResource(R.string.user_protocol_1_1_app_content_20240909, EIGHT))
            appendLine()
            appendText(stringResource(R.string.user_protocol_content_16_20241210))
            appendLine()
            appendText(stringResource(R.string.user_protocol_1_1_xmind_content_20250102, TEN))
            addSpace(this, LINE_MARGIN_MIDDLE)
        }
    }

    private fun LinearLayoutCompat.addDateContent() {
        val updateTimeMills = UPDATE_TIME_MILLS_915
        addText {
            appendHtmlText(
                stringResource(
                    R.string.user_protocol_update_date,
                    dateString(this@UserProtocolActivity, updateTimeMills)
                )
            )
            addSpace(this, LINE_MARGIN_LARGE)
        }
    }

    private fun getProtocolHeadContent(): String {
        return when {
            BrandUtil.isOnePlus() -> stringResource(R.string.documents_user_protocol_content_01_oneplus, stringResource(R.string.documents_app_name))
            BrandUtil.isRealMe() -> stringResource(R.string.documents_user_protocol_content_01_realme, stringResource(R.string.documents_app_name))
            else -> stringResource(R.string.documents_user_protocol_content_01_oppo, stringResource(R.string.documents_app_name))
        }
    }

    private fun getContactLink(): Pair<String, Boolean> {
        val contactLink = when {
            BrandUtil.isOnePlus() -> stringResource(R.string.user_protocol_content_104_oneplus)
            BrandUtil.isRealMe() -> stringResource(R.string.user_protocol_content_104_realme)
            else -> stringResource(R.string.user_protocol_content_104_oppo)
        }
        return Pair(contactLink, BrandUtil.isRealMe())
    }

    private fun getUrlAccountPrivacyPolicy(): String {
        return "$URL_ACCOUNT_PRIVACY_POLICY_START${LanguageUtil.getLinkLanguage()}$URL_ACCOUNT_PRIVACY_POLICY_END"
    }

    private fun getUrlAccountUseProtocol(): String {
        return "$URL_ACCOUNT_USE_PROTOCOL_START${LanguageUtil.getLinkLanguage()}$URL_ACCOUNT_USE_PROTOCOL_END"
    }

    companion object {

        private const val TAG = "UserProtocolActivity"
        private const val URL_ACCOUNT_PRIVACY_POLICY_START =
            "https://muc.heytap.com/agreement/privacy-policy/account/mainland/v20240331/index.html?language="
        private const val URL_ACCOUNT_PRIVACY_POLICY_END =
            "&isTranslucentBar=false&LoadInCurrentPage=false"
        private const val URL_ACCOUNT_USE_PROTOCOL_START =
            "https://muc.heytap.com/agreement/registration/account/mainland/register_"
        private const val URL_ACCOUNT_USE_PROTOCOL_END =
            ".html?isTranslucentBar=false&LoadInCurrentPage=false"

        fun start(activity: Activity) {
            runCatching {
                val intent = Intent(activity, UserProtocolActivity::class.java)
                intent.setPackage(activity.packageName)
                if (FeatureCompat.isApplicableForFlexibleWindow) {
                    val exBundle = Bundle()
                    exBundle.putInt(
                        FlexibleWindowManager.KEY_FLEXIBLE_ACTIVITY_POSITION,
                        FlexibleWindowUtil.checkOrientation()
                    )
                    exBundle.putBoolean(FlexibleWindowManager.KEY_FLEXIBLE_START_ACTIVITY, true)
                    exBundle.putBoolean(
                        FlexibleWindowManager.KEY_FLEXIBLE_ACTIVITY_DESCENDANT,
                        true
                    )

                    val options = ActivityOptions.makeBasic()
                    activity.startActivity(
                        intent,
                        FlexibleWindowManager.getInstance().setExtraBundle(options, exBundle)
                    )
                } else {
                    activity.startActivity(intent)
                }
            }.onFailure {
                Log.e(TAG, "start user protocol activity error, ${it.message}")
            }
        }
    }
}