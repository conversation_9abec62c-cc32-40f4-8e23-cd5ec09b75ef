/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ThirdPartyRecordNotification
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/07/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.third

import android.app.Notification
import android.app.PendingIntent
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import android.widget.RemoteViews
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.utils.AppCardUtils.addContinueFlag
import com.soundrecorder.common.utils.AppCardUtils.launchDisplay
import com.soundrecorder.common.utils.DisplayUtils
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.notification.R
import com.soundrecorder.notification.common.CommonNotificationResourceHelper

class ThirdPartyRecordNotification(groupId: Int, notificationId: Int) :
    ThirdPartyNotification(groupId, notificationId) {

    override var logTag: String
        get() = "ThirdPartyRecordNotification"
        set(value) {}

    override fun getOldChannelId(): String {
        return NotificationUtils.RECORDERSERVICE_OLD_CID
    }

    override fun getChannelId(): String {
        return NotificationUtils.RECORDERSERVICE_CID
    }

    override fun getChannelName(): String {
        return defaultContext.resources.getString(R.string.recording_channel_name)
    }

    override fun getLayoutId(): Int {
        return R.layout.service_status_bar
    }

    override fun initNotification() {
        val builder = getNotificationBuilder()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            builder.setForegroundServiceBehavior(Notification.FOREGROUND_SERVICE_IMMEDIATE)
        }
        val jumpIntent = getJumpIntent()?.addContinueFlag()
        if ((notificationModel?.canJumpIntent == true) && (jumpIntent != null)) {
            builder.setContentIntent(
                PendingIntent.getActivity(
                    defaultContext,
                    0,
                    jumpIntent,
                    PendingIntent.FLAG_IMMUTABLE,
                    DisplayUtils.mainId.launchDisplay()
                )
            )
        }

        notification = if (BaseUtil.isAndroidNOrLater) {
            builder.setAutoCancel(false)
                .setContentTitle(getContentTitle().first)
                .setContentText(getContentText().first)
                .setOnlyAlertOnce(true)
                .setSmallIcon(com.soundrecorder.common.R.drawable.ic_launcher_recorder)
                .build()
        } else {
            initRemoteView()
            setRemoteViewData(remoteViews)
            builder.setContent(remoteViews).build()
        }
    }

    @Suppress("UnsafeCallOnNullableType")
    override fun showImmediately() {
        if (service != null && notification != null) {
            runCatching {
                service!!.startForeground(getGroupId(), notification!!, ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE)
            }.onFailure {
                DebugUtil.e(logTag, "showImmediately startForeground error $it")
            }
        }
    }

    override fun setRemoteViewData(remoteViews: RemoteViews) {
        //标题
        getContentTitle().run {
            setRemoteViewText(R.id.line_1, first, second)
        }

        //内容
        getContentText().run {
            setRemoteViewText(R.id.line_2, first, second)
        }
    }

    /**
     * 标题显示录制时间，文本显示“正在录音...”
     */
    override fun getContentTitle(): Pair<String, String> {
        val time = notificationModel?.curTime?.value ?: 0
        return Pair(
            TimeUtils.getFormatTimeExclusiveMill(time),
            TimeUtils.getDurationHint(defaultContext, time)
        )
    }

    /**
     * 标题显示录制时间，文本显示“正在录音...”
     */
    override fun getContentText(): Pair<String, String> {
        val state = notificationModel?.saveState?.value
        val isSaving = state != null && state != RecorderViewModelAction.SaveFileState.INIT
        return CommonNotificationResourceHelper.getRecordStatusContent(defaultContext, isPlaying(), isSaving)
    }

    override fun getJumpIntent(): Intent? {
        return null
    }
}