import java.nio.file.Files

@SuppressWarnings("unused")
final class RepublishingLog {
    private static final BASE_TAG = "RepublishingPlugin"

    private static String printMsg(Project target, String subTag, String msg) {
        return "$BASE_TAG: ${target.path}: $subTag: $msg"
    }

    private static String printMsg(Project target, String msg) {
        return "$BASE_TAG: ${target.path}: $msg"
    }

    private static String printMsg(String subTag, String msg) {
        return "$BASE_TAG: $subTag: $msg"
    }

    private static String printMsg(String msg) {
        return "$BASE_TAG: $msg"
    }

    static void info(Project target, String subTag, String msg) {
        println(printMsg(target, subTag, msg))
    }

    static void info(Project target, String msg) {
        println(printMsg(target, msg))
    }

    static void info(String subTag, String msg) {
        println(printMsg(subTag, msg))
    }

    static void info(String msg) {
        println(printMsg(msg))
    }

    static void error(Project target, String subTag, String msg) {
        System.err.println(printMsg(target, subTag, "ERROR: $msg"))
    }

    static void error(Project target, String msg) {
        System.err.println(printMsg(target, "ERROR: $msg"))
    }

    static void error(String subTag, String msg) {
        System.err.println(printMsg(subTag, "ERROR: $msg"))
    }

    static void error(String msg) {
        System.err.println(printMsg("ERROR: $msg"))
    }
}

class RepublishArtifact {
    private static final String GRADLE_CACHES_PATH
            = "caches${File.separator}modules-2${File.separator}files-2.1"

    final String groupId
    final String artifactId
    final String version

    /**
     * Resolve后Gradle下载的缓存依赖aar/jar路径
     */
    final File bin

    /**
     * Resolve后Gradle下载的缓存依赖pom路径
     */
    final File pom

    RepublishArtifact(ResolvedDependency resolved, File parentPom, File resolvedPom) {
        this.groupId = resolved.moduleGroup
        this.artifactId = resolved.moduleName
        this.version = resolved.moduleVersion
        if (parentPom == null) {
            final moduleBin = resolved.moduleArtifacts[0].file
            this.bin = moduleBin
            if (resolvedPom == null) {
                final getPom = obtainPom(moduleBin)
                this.pom = getPom
            } else {
                this.pom = resolvedPom
            }
        } else {
            this.bin = null
            this.pom = parentPom
        }
    }

    String readPomFile() {
        try {
            return Files.readString(pom.toPath())
        } catch (IOException ignored) {
        }
        return null
    }

    String toShortString() {
        return "$groupId:$artifactId:$version"
    }

    @Override
    String toString() {
        return "RepublishArtifact{\n\tartifact=$groupId:$artifactId:$version,\n\tbin=$bin,\n\tpom=$pom\n}"
    }

    static RepublishArtifact safeCreate(
            Project project,
            ResolvedDependency resolved,
            File resolvedPom
    ) {
        File parentPom = null
        if (resolved.moduleArtifacts.isEmpty()) {
            if (resolvedPom != null) {
                parentPom = resolvedPom
            } else {
                parentPom = obtainParentPom(project, resolved)
            }
            if (parentPom == null) {
                RepublishingLog.error("RepublishArtifact", "ERROR! Can not pom for $resolved")
                return null
            }
        }
        if (resolvedPom == null) {
            RepublishingLog.error("RepublishArtifact", "ERROR! Can not pom for $resolved")
            return null
        }
        return new RepublishArtifact(resolved, parentPom, resolvedPom)
    }

    /**
     * ResolvedDependency及ResolvedArtifact中仅记录了aar/jar的路径。
     * 但Gradle依赖的缓存目录结构一般如下：
     *     <依赖版本的缓存目录>
     *         |----<以hash为名的目录>
     *                  |----<aar/jar>
     *         |----<以hash为名的目录>
     *                  |----<pom>
     *         ... ...
     * 故可以通过目录结构尝试根据aar/jar的路径逆向查找pom路径。
     *
     * P.S. 方案更新：
     * 会优先通过ArtifactResolutionQuery去尝试强制拉取pom文件，
     * 能直接拉取到pom时不再通过查找缓存路径的方式定位pom文件，
     * 具体参考ResolvedDependencyCollector.resolvePomFiles.
     */
    private static File obtainPom(File bin) {
        final cacheDir = bin.parentFile.parentFile
        return findPomInCache(cacheDir)
    }

    /**
     * maven parent等类型的依赖没有对应的aar/jar，无法直接反推其pom文件的所在。
     * 需要根据其groupId，artifactId及version从Gradle依赖的缓存根路径开始尝试查找pom文件。
     */
    private static File obtainParentPom(Project project, ResolvedDependency resolved) {
        final cachesRoot = new File(project.gradle.gradleUserHomeDir, GRADLE_CACHES_PATH)
        final cachePath = "${resolved.moduleGroup}${File.separator}" +
                "${resolved.moduleName}${File.separator}${resolved.moduleVersion}"
        final cacheDir = new File(cachesRoot, cachePath)
        return findPomInCache(cacheDir)
    }

    private static File findPomInCache(File cacheDir) {
        for (hashDir in cacheDir.listFiles()) {
            for (file in hashDir.listFiles()) {
                if (file.name.endsWith("pom")) {
                    return file
                }
            }
        }
        return null
    }
}

@SuppressWarnings("unused")
abstract class ArtifactMatchRule {
    protected final Set<String> groupIdRequires = new HashSet<>()
    protected final Set<String> artifactIdRequires = new HashSet<>()
    protected final Set<String> groupIdOptions = new HashSet<>()
    protected final Set<String> artifactIdOptions = new HashSet<>()

    void requireGroup(String... groupKeywords) {
        groupIdRequires.addAll(groupKeywords)
    }

    void requireArtifact(String... artifactKeywords) {
        artifactIdRequires.addAll(artifactKeywords)
    }

    void optionGroup(String... groupKeywords) {
        groupIdOptions.addAll(groupKeywords)
    }

    void optionArtifact(String... artifactKeywords) {
        artifactIdOptions.addAll(artifactKeywords)
    }
}

class ArtifactMatcher extends ArtifactMatchRule {

    boolean matched(String groupId, String artifactId) {
        def matchRequireGroup = groupIdRequires.isEmpty()
        groupIdRequires.each {
            if (groupId.contains(it)) {
                matchRequireGroup = true
            }
        }
        if (!matchRequireGroup) {
            return false
        }
        def mathRequireArtifact = artifactIdRequires.isEmpty()
        artifactIdRequires.each {
            if (artifactId.contains(it)) {
                mathRequireArtifact = true
            }
        }
        if (!mathRequireArtifact) {
            return false
        }
        def matchOptionGroup = !groupIdRequires.isEmpty()
        def mathOptionArtifact = !artifactIdRequires.isEmpty()
        groupIdOptions.each {
            if (groupId.contains(it)) {
                matchOptionGroup = true
            }
        }
        artifactIdOptions.each {
            if (artifactId.contains(it)) {
                mathOptionArtifact = true
            }
        }
        return matchOptionGroup || mathOptionArtifact
    }
}

class RepublishingTarget {
    String configuration = "implementation"
    String groupId = null
    String artifactId = null
}

@SuppressWarnings("unused")
class TomlRepublishingTarget extends RepublishingTarget {
    private final Project project

    TomlRepublishingTarget(Project project) {
        this.project = project
    }

    void importDependency(Provider<MinimalExternalModuleDependency> toml) {
        importDependency(toml, null)
    }

    void importDependency(Provider<MinimalExternalModuleDependency> toml, Closure configureClosure) {
        groupId = toml.get().group
        artifactId = toml.get().name
        if (configureClosure != null) {
            project.dependencies."$configuration"(toml, configureClosure)
        } else {
            project.dependencies."$configuration"(toml)
        }
    }

    void importDependency(String dependencyNotation) {
        importDependency(dependencyNotation, null)
    }

    void importDependency(String dependencyNotation, Closure configureClosure) {
        final fields = dependencyNotation.split(":")
        if (fields.size() != 3) {
            throw new IllegalArgumentException("Incorrect dependency notation $dependencyNotation")
        }
        groupId = fields[0]
        artifactId = fields[1]
        if (configureClosure != null) {
            project.dependencies."$configuration"(dependencyNotation, configureClosure)
        } else {
            project.dependencies."$configuration"(dependencyNotation)
        }
    }
}

@SuppressWarnings("unused")
class RelateDependency {
    final bindInfos = new HashSet<DependencyInfo>()
    private final Project project

    String configuration = "implementation"
    DependencyInfo baseInfo = null

    RelateDependency(Project project) {
        this.project = project
    }

    void base(Closure action) {
        final info = new DependencyInfo()
        action.delegate = info
        action.call()
        ensureVersion(info)
        baseInfo = info
    }

    void bind(Closure action) {
        final info = new DependencyInfo()
        action.delegate = info
        action.call()
        ensureVersion(info)
        bindInfos.add(info)
        // 显式导入关联依赖以便于resolve获取及识别
        project.dependencies."$configuration"("${info.groupId}:${info.artifactId}:${info.expectedVersion}")
    }

    private static void ensureVersion(DependencyInfo info) {
        if (info.expectedVersion == null) {
            throw new IllegalArgumentException("Must has version when configure relate dependency")
        }
    }

    class DependencyInfo {
        String groupId = null
        String artifactId = null
        String expectedVersion = null
    }
}

@SuppressWarnings("unused")
class RepublishingExtensions {
    final includeMatchers = new HashSet<ArtifactMatcher>()
    final excludeMatchers = new HashSet<ArtifactMatcher>()
    final targetsTable = new HashMap<String, Set<RepublishingTarget>>()
    final allTargets = new HashSet<RepublishingTarget>()
    final relatedDependsTable = new HashMap<String, Set<RelateDependency>>()
    final allRelatedDepends = new HashSet<RelateDependency>()

    /**
     * 设置重新整理依赖发布的本地repo库路径，路径将通过rootProject.file获取
     */
    String outputRepo = null

    /**
     * 对于未被include或exclude规则配置匹配的依赖是否默认导入重新发布，默认false
     */
    boolean defaultIncludeRecursive = false

    private Project project = null

    void prepare(Project project) {
        this.project = project
    }

    /**
     * 配置需要重新整理并发布的依赖目标。
     * 配置方法一：
     * target {
     *     configuration = <在dependencies导入依赖的方法，默认implementation>
     *     groupId = <目标依赖的Group ID，必须配置>
     *     artifactId = <目标依赖的Artifact ID，必须配置>
     * }
     * 通过配置方法一来配置时，该目标必须同时配置一个版本至dependencies中。
     *
     * 配置方法二：
     * target {
     *     configuration = <在dependencies导入依赖的方法，默认implementation>
     *     importDependency(<字符串描述的依赖版本或者引用toml的依赖版本，相当于dependencies中的配置>) {
     *         <可选闭包，相当于dependencies中导入依赖的额外artifact等闭包配置>
     *     }
     * }
     * 通过配置方法二来配置时，会自动导入目标依赖至project的dependencies中。
     */
    void target(Action<? extends RepublishingTarget> action) {
        final pubTarget = new TomlRepublishingTarget(project)
        action.execute(pubTarget)
        allTargets.add(pubTarget)
        def confMap = targetsTable.get(pubTarget.configuration)
        if (confMap == null) {
            confMap = new HashSet<RepublishingTarget>()
        }
        confMap.add(pubTarget)
        targetsTable.put(pubTarget.configuration, confMap)
    }

    /**
     * 配置包含目标及其依赖树递归依赖的过滤规则。
     * 配置方法：
     * include {
     *     requireGroup(<若干Group ID匹配关键字，必须至少匹配上一个，为空则不强制匹配Group ID>)
     *     requireArtifact(<若干Artifact ID匹配关键字，必须至少匹配上一个，为空则不强制匹配Artifact ID>)
     *     optionGroup(<若干Group ID匹配关键字，任意匹配上一个即认为匹配，优先级低于require开头的配置>)
     *     optionArtifact(<若干Artifact ID匹配关键字，任意匹配上一个即认为匹配，优先级低于require开头的配置>)
     * }
     */
    void include(Action<? extends ArtifactMatchRule> action) {
        final matcher = new ArtifactMatcher()
        action.execute(matcher)
        includeMatchers.add(matcher)
    }

    /**
     * 配置排除目标及其依赖树递归依赖的过滤规则。
     * 配置方法：
     * include {
     *     requireGroup(<若干Group ID匹配关键字，必须至少匹配上一个才认定匹配，为空则不强制匹配Group ID>)
     *     requireArtifact(<若干Artifact ID匹配关键字，必须至少匹配上一个才认定匹配，为空则不强制匹配Artifact ID>)
     *     optionGroup(<若干Group ID匹配关键字，任意匹配上一个即认为匹配，优先级低于require开头的配置>)
     *     optionArtifact(<若干Artifact ID匹配关键字，任意匹配上一个即认为匹配，优先级低于require开头的配置>)
     * }
     */
    void exclude(Action<? extends ArtifactMatchRule> action) {
        final matcher = new ArtifactMatcher()
        action.execute(matcher)
        excludeMatchers.add(matcher)
    }

    /**
     * 部分依赖具备maven parent，如esa:commons的parent依赖eas:commons-parent
     * 因为maven parent仅具备pom文件，无法通过ResolvedDependency递归导入进而识别到。
     * 需要配置以下规则来明确这类依赖与其maven parent的版本依赖关系。
     * 配置格式：
     * relate {
     *     configuration = <在dependencies导入依赖的方法，默认implementation>
     *     base {
     *         groupId = <基础依赖的groupId>
     *         artifactId = <基础依赖的artifactId>
     *         expectedVersion = <期待的基础依赖导入版本>
     *     }
     *     bind {
     *         groupId = <关联依赖的groupId>
     *         artifactId = <关联依赖的artifactId>
     *         expectedVersion = <期待的关联依赖导入版本>
     *     }
     * }
     *
     * 其中，基础依赖为会被自动导入的具备无法直接识别的parent等的依赖。
     * 在发现基础依赖时会自动包含进重发布的范围内，并自动一并导入关联依赖至重发布。
     * 基础依赖的版本号会在被识别到时用于版本校验以确保关联版本的对应性。
     * 关联依赖会自动通过设定的configuration去显式导入dependencies以便于resolve拉取依赖。
     * 关联依赖的版本号会在发现基础依赖的情况下用于版本校验以确保关联版本的对应性。
     */
    void relate(Action<RelateDependency> action) {
        final related = new RelateDependency(project)
        action.execute(related)
        def relatedInfos = relatedDependsTable.get(related.configuration)
        if (relatedInfos == null) {
            relatedInfos = new HashSet<>()
        }
        relatedInfos.add(related)
        relatedDependsTable.put(related.configuration, relatedInfos)
        allRelatedDepends.add(related)
    }
}

class ResolvedDependencyCollector {
    private final RepublishingExtensions republishingExt

    ResolvedDependencyCollector(RepublishingExtensions republishingExt) {
        this.republishingExt = republishingExt
    }

    Map<ResolvedDependency, File> collectTargetDependencyTree(Project project, Configuration conf) {
        final results = new HashMap<ResolvedDependency, File>()
        final collected = new HashSet<ResolvedDependency>()
        final confTargets = republishingExt.targetsTable.get(conf.name)
        if (confTargets == null) {
            return results
        }
        final confRelations = republishingExt.relatedDependsTable.get(conf.name)
        final copyConf = conf.copyRecursive()
        copyConf.canBeResolved = true
        filerTargetsInConf(project, copyConf, confTargets, confRelations)
        copyConf.resolve()
        copyConf.resolvedConfiguration.firstLevelModuleDependencies.each {
            collectChildDependency(collected, it)
        }
        // 因为关联依赖被先行resolve，对应基础依赖信息也只能在resolve后识别。
        // 需要在完成依赖收集后进行分析校验，并剔除无效的关联依赖。
        clearNonBaseRelations(project, collected, confRelations)
        // 尝试强制拉取pom文件至Gradle缓存中
        final pomMap = resolvePomFiles(project, collected)
        collected.each {
            results.put(it, pomMap.get(it))
        }
        return results
    }

    /**
     * Gradle缓存中module-metadata.bin等另行缓存pom数据，导致可能出现pom文件被清理的情况。
     * 此时能定位jar/aar的依赖内容但无法找到pom文件。
     * 因此通过ArtifactResolutionQuery强制对pom文件进行一次拉取以尽可能确保后续重新归档发布时能找到pom文件。
     */
    private static Map<ResolvedDependency, File> resolvePomFiles(
            Project project,
            Collection<ResolvedDependency> collected
    ) {
        final pomMap = new HashMap<ResolvedDependency, File>()
        final componentIdMap = new HashMap<ComponentIdentifier, ResolvedDependency>()
        final noArtifactMap = new HashMap<String, ResolvedDependency>()
        collected.each {
            if (it.moduleArtifacts.isEmpty()) {
                // maven-parent等类型的依赖没有ResolvedArtifact，无法直接获取到ComponentIdentifier
                // 故另行组装依赖的notation来映射ResolvedDependency方便拉取pom后重新关联
                final moduleId = it.module.id
                final notation = "${moduleId.group}:${moduleId.name}:${moduleId.version}"
                noArtifactMap.put(notation.toString(), it)
            } else {
                componentIdMap.put(it.moduleArtifacts[0].id.componentIdentifier, it)
            }
        }
        final pomQuery = project.dependencies.createArtifactResolutionQuery()
        pomQuery.forComponents(componentIdMap.keySet())
        noArtifactMap.values().each {
            final moduleId = it.module.id
            pomQuery.forModule(moduleId.group, moduleId.name, moduleId.version)
        }
        final pomResults = pomQuery.withArtifacts(MavenModule, MavenPomArtifact).execute()
        for (component in pomResults.resolvedComponents) {
            final componentId = component.id
            if (componentId instanceof ModuleComponentIdentifier) {
                def resolvedDep = componentIdMap.get(componentId)
                if (resolvedDep == null) {
                    final moduleId = componentId.moduleIdentifier
                    final notation = "${moduleId.group}:${moduleId.getName()}:${componentId.version}"
                    resolvedDep = noArtifactMap.get(notation.toString())
                }
                if (resolvedDep == null) {
                    throw new IllegalStateException("${project.path}: can not match ResolvedDependency for $componentId")
                }
                final pomFile = component.getArtifacts(MavenPomArtifact).first().file
                pomMap.put(resolvedDep, pomFile)
            }
        }
        return pomMap
    }

    private static void filerTargetsInConf(
            Project project,
            Configuration copyConf,
            Set<RepublishingTarget> confTargets,
            Set<RelateDependency> confRelations
    ) {
        final foundTargets = new HashSet<RepublishingTarget>()
        final removeNotTarget = new HashSet<Dependency>()
        copyConf.dependencies.each { dependency ->
            final target = matchTarget(confTargets, dependency)
            final relation = matchRelation(confRelations, dependency)
            if (target != null) {
                foundTargets.add(target)
            } else if (relation == null) {
                // 关联依赖可能是由子向父的反向依赖，也需要一并resolve，否则可能无法获取到
                removeNotTarget.add(dependency)
            }
        }
        ensureFoundAllTargets(project, confTargets, foundTargets)
        copyConf.dependencies.removeAll(removeNotTarget)
    }

    private static RepublishingTarget matchTarget(
            Set<RepublishingTarget> confTargets, Dependency dependency) {
        for (target in confTargets) {
            if (dependency.group == target.groupId && dependency.name == target.artifactId) {
                return target
            }
        }
        return null
    }

    private static RelateDependency matchRelation(
            Set<RelateDependency> confRelations, Dependency dependency) {
        for (relation in confRelations) {
            for (bind in relation.bindInfos) {
                if (dependency.group == bind.groupId && dependency.name == bind.artifactId) {
                    return relation
                }
            }
        }
        return null
    }

    private static void ensureFoundAllTargets(
            Project project,
            Set<RepublishingTarget> confTargets,
            Set<RepublishingTarget> foundTargets
    ) {
        confTargets.each {
            if (!foundTargets.contains(it)) {
                final errMsg = "ERROR! Can not find ${it.groupId}:${it.artifactId} " +
                        "with configuration ${it.configuration}. Pls check dependencies configs"
                RepublishingLog.error(project, "collectTargetDependencyTree", errMsg)
                throw new GradleException(errMsg)
            }
        }
    }

    private static void collectChildDependency(
            Set<ResolvedDependency> collected, ResolvedDependency parent) {
        collected.add(parent)
        parent.children.each { child ->
            collectChildDependency(collected, child)
        }
    }

    private static void clearNonBaseRelations(
            Project project,
            Set<ResolvedDependency> collected,
            Set<RelateDependency> confRelations
    ) {
        final unnecessary = new HashSet<ResolvedDependency>()
        for (related in confRelations) {
            final base = related.baseInfo
            final foundRelations = new HashSet<ResolvedDependency>()
            ResolvedDependency foundBase = null
            for (resolved in collected) {
                if ((resolved.moduleGroup == base.groupId)
                        && (resolved.moduleName == base.artifactId)) {
                    foundBase = resolved
                }
                for (bind in related.bindInfos) {
                    if ((resolved.moduleGroup == bind.groupId)
                            && (resolved.moduleName == bind.artifactId)) {
                        foundRelations.add(resolved)
                    }
                }
            }
            if (foundBase == null) {
                unnecessary.add(foundRelations)
                continue
            }
            if (base.expectedVersion != foundBase.moduleVersion) {
                handleUnexpectedVersion(project, base, foundBase)
            }
            for (bind in related.bindInfos) {
                for (foundBind in foundRelations) {
                    if (bind.expectedVersion != foundBind.moduleVersion) {
                        handleUnexpectedVersion(project, bind, foundBind)
                    }
                }
            }
        }
        collected.removeAll(unnecessary)
    }

    private static void handleUnexpectedVersion(
            Project project,
            RelateDependency.DependencyInfo expected,
            ResolvedDependency found
    ) {
        final depPath = "${expected.groupId}:${expected.artifactId}"
        final expectedVer = expected.expectedVersion
        final foundVer = found.moduleVersion
        final errMsg = "Found $depPath but version $foundVer is not as " +
                "relation expected $expectedVer. Pls check relate config in mavenRepublishing."
        RepublishingLog.error(project, "ERROR! $errMsg")
        throw new IllegalArgumentException(errMsg)
    }
}

class RepublishingDeployer {
    private final Project project
    private final RepublishingExtensions republishingExt

    RepublishingDeployer(Project project, RepublishingExtensions republishingExt) {
        this.project = project
        this.republishingExt = republishingExt
    }

    File getOutputRepo() {
        final outputPath = republishingExt.outputRepo
                .replace("/", File.separator).replace("\\", File.separator)
        return project.rootProject.file(outputPath)
    }

    void deployRepublishing() {
        final republishArtifacts = obtainRepublishArtifacts()
        project.publishing {
            publications {
                int i = 0
                for (republishArt in republishArtifacts) {
                    RepublishingLog.info(project, "deployRepublishing", "republishArt=$republishArt")
                    final pomContent = republishArt.readPomFile()
                    "republishMaven$i"(MavenPublication) {
                        if (republishArt.bin != null) {
                            artifact "${republishArt.bin.absolutePath}"
                        }
                        groupId = republishArt.groupId
                        artifactId = republishArt.artifactId
                        version = republishArt.version
                        if (pomContent != null) {
                            // 清空maven-publish插件自动生成的pom内容并替换为被重发布依赖的原生pom内容
                            pom.withXml {
                                final StringBuilder pomXml = asString()
                                pomXml.setLength(0)
                                pomXml.append(pomContent)
                            }
                        } else {
                            final warnMsg = "WARNING! Failed to get pom for ${republishArt.toShortString()}"
                            RepublishingLog.error(project, "deployRepublishing", warnMsg)
                        }
                    }
                    i++
                }
            }
            repositories {
                maven {
                    url = getOutputRepo().absolutePath
                }
            }
        }
    }

    private Set<RepublishArtifact> obtainRepublishArtifacts() {
        final collector = new ResolvedDependencyCollector(republishingExt)
        final allDepends = new HashMap<ResolvedDependency, File>()
        project.configurations.each {
            final collected = collector.collectTargetDependencyTree(project, it)
            if (!collected.isEmpty()) {
                allDepends.putAll(collected)
            }
        }
        final republishingArtifacts = new HashSet<RepublishArtifact>()
        allDepends.each { resolvedDepends, pomFile ->
            final art = RepublishArtifact.safeCreate(project, resolvedDepends, pomFile)
            if ((art != null) && shallRepublish(art)) {
                republishingArtifacts.add(art)
            }
        }
        return republishingArtifacts
    }

    /**
     * 以下几种条件下会重发布依赖：
     * 1. 依赖为目标依赖(target配置)
     * 2. 依赖为有效的关联依赖及其基础依赖(relate配置)
     * 3. 依赖满足包含规则(include配置)
     * 4. 依赖不满足排除配置(exclude配置)
     * 5. 默认是否导入重发布(defaultIncludeRecursive配置)
     */
    private boolean shallRepublish(RepublishArtifact artifact) {
        for (target in republishingExt.allTargets) {
            if ((artifact.groupId == target.groupId)
                    && (artifact.artifactId == target.artifactId)) {
                return true
            }
        }
        for (related in republishingExt.allRelatedDepends) {
            if ((artifact.groupId == related.baseInfo.groupId)
                    && (artifact.artifactId == related.baseInfo.artifactId)) {
                return true
            }
            for (bind in related.bindInfos) {
                if ((artifact.groupId == bind.groupId)
                        && (artifact.artifactId == bind.artifactId)) {
                    return true
                }
            }
        }
        for (include in republishingExt.includeMatchers) {
            if (include.matched(artifact.groupId, artifact.artifactId)) {
                return true
            }
        }
        for (exclude in republishingExt.excludeMatchers) {
            if (exclude.matched(artifact.groupId, artifact.artifactId)) {
                return false
            }
        }
        return republishingExt.defaultIncludeRecursive
    }
}

class RepublishingPlugin implements Plugin<Project> {
    private static final REPUBLISHING_EXTENSIONS_NAME = "mavenRepublishing"
    private static final REPUBLISH_TASK_NAME = "republishToOutputRepo"
    private static final PREPARE_TASK_NAME = "prepareRepublishOutputRepo"
    private static final HOOK_PUBLISH_TASK = "publish"

    @Override
    void apply(Project project) {
        ensureIsAndroidLib(project)
        project.apply plugin: 'maven-publish'
        final republishingExt =
                project.extensions.create(REPUBLISHING_EXTENSIONS_NAME, RepublishingExtensions)
        republishingExt.prepare(project)
        project.afterEvaluate {
            ensureCorrectExtensions(project, republishingExt)
            applyRepublishing(project, republishingExt)
        }
    }

    private static void ensureIsAndroidLib(Project project) {
        final isLibModule = project.plugins.hasPlugin("com.android.library")
        if (!isLibModule) {
            final errMsg = "must be applied to an Android library module. " +
                    "Please apply com.android.library first."
            RepublishingLog.error(project, "apply", errMsg)
            throw new GradleException(errMsg)
        }
    }

    private static void applyRepublishing(Project project, RepublishingExtensions republishingExt) {
        final deployer = new RepublishingDeployer(project, republishingExt)
        if (isRepublishingBuild(project)) {
            deployer.deployRepublishing()
        } else {
            final notifyMsg = "Not republishing cmd $REPUBLISH_TASK_NAME, ignore depolying."
            RepublishingLog.info(project, "apply", notifyMsg)
        }
        prepareRepublishTasks(project, deployer.getOutputRepo())
    }

    private static void prepareRepublishTasks(Project project, File outputRepo) {
        project.tasks.register(REPUBLISH_TASK_NAME) {
            it.group = "publishing"
            it.dependsOn(HOOK_PUBLISH_TASK)
        }
        project.tasks.register(PREPARE_TASK_NAME) {
            it.doFirst {
                project.delete outputRepo
            }
        }
        project.tasks.configureEach {
            if (it.name == HOOK_PUBLISH_TASK) {
                it.dependsOn(PREPARE_TASK_NAME)
            }
        }
    }

    private static boolean isRepublishingBuild(Project project) {
        for (taskRequest in project.gradle.startParameter.taskRequests) {
            for (taskArgs in taskRequest.args) {
                final taskParameter = taskArgs.toString().split(":").last()
                final isRepublishing = isRepublishingCommand(taskParameter)
                final logMsg = "taskParameter=$taskParameter, isRepublishing=$isRepublishing"
                RepublishingLog.info(project, "isRepublishingBuild", logMsg)
                if (isRepublishing) {
                    return true
                }
            }
        }
        return false
    }

    private static boolean isRepublishingCommand(String taskParameter) {
        if (taskParameter == REPUBLISH_TASK_NAME) {
            return true
        }
        return false
    }

    private static void ensureCorrectExtensions(
            Project project, RepublishingExtensions republishingExt) {
        if (republishingExt.outputRepo == null) {
            final errMsg = "ERROR! Must configure outputRepo in $REPUBLISHING_EXTENSIONS_NAME"
            RepublishingLog.info(project, "apply", errMsg)
            throw new IllegalArgumentException(errMsg)
        }
        republishingExt.allTargets.each {
            if (it.groupId == null) {
                final errMsg = "ERROR! Must configure groupId in target"
                RepublishingLog.info(project, "apply", errMsg)
                throw new IllegalArgumentException(errMsg)
            }
            if (it.artifactId == null) {
                final errMsg = "ERROR! Must configure artifactId in target"
                RepublishingLog.info(project, "apply", errMsg)
                throw new IllegalArgumentException(errMsg)
            }
            if (it.configuration == null) {
                final errMsg = "ERROR! Must configure configuration in target"
                RepublishingLog.info(project, "apply", errMsg)
                throw new IllegalArgumentException(errMsg)
            }
        }
    }
}

project.plugins.apply(RepublishingPlugin)