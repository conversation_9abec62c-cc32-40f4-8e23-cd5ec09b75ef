/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - RecycleBinViewModel.kt
 * Description:
 *     The viewModel for recycle bin page.
 *
 * Version: 1.0
 * Date: 2024-07-12
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-07-12   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.recycle.viewmodel

import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.FileUtils
import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.loader.LoaderController
import andes.oplus.documentsreader.core.common.loader.LoadingLoaderListener
import andes.oplus.documentsreader.core.common.selection.BaseStateModel
import andes.oplus.documentsreader.core.common.selection.BaseUiModel
import andes.oplus.documentsreader.core.common.selection.SelectionViewModel
import andes.oplus.documentsreader.recycle.ui.RecycleBinAdapter
import androidx.lifecycle.MutableLiveData
import com.documentsreader.navigation.NavigationInterface
import com.oplus.dropdrag.SelectionTracker

internal class RecycleBinViewModel :
    SelectionViewModel<BaseRecycleFileBean, BaseUiModel<BaseRecycleFileBean>>() {

    val modeState: BaseStateModel = BaseStateModel(MutableLiveData<Int>(KtConstants.LIST_NORMAL_MODE))

    private val loaderCallback = RecycleBinLoaderCallBack(this)

    fun initLoader(loaderController: LoaderController) {
        if (loaderCallback.getLoader() == null) {
            loaderController.initLoader(TAG.hashCode(), loaderCallback)
        } else {
            loadData()
        }
    }

    override fun getRealFileSize(): Int = getAllRecycleFiles().size

    fun hasRealFile(): Boolean {
        return getRealFileSize() > 0
    }

    fun getAllRecycleFiles(): List<RecycleFileBean> =
        uiState.value?.fileList?.mapNotNull { it as? RecycleFileBean } ?: emptyList()

    fun getSelectedRecycleFiles(): List<RecycleFileBean> =
        getSelectItems().mapNotNull { it as? RecycleFileBean }

    override fun loadData() {
        loaderCallback.getLoader()?.forceLoad()
    }

    fun sortReload() {
        loaderCallback.getLoader()?.setSort(-1)
        loadData()
    }

    override fun getRecyclerViewScanMode(): SelectionTracker.LAYOUT_TYPE =
        SelectionTracker.LAYOUT_TYPE.LIST

    fun onClickSelectAllOrDeSelectAll(adapter: RecycleBinAdapter?) {
        uiState.value?.run {
            //all Deselect
            if (adapter?.getRealFileItemCount() == selectedList.size) {
                selectedList.clear()
                return@run
            }
            selectedList.clear()
            //all select
            getAllRecycleFiles().forEach {
                it.itemId.let(selectedList::add)
            }
        }
        uiState.value = uiState.value
    }

    fun setNavigateItemAble(navigationInterface: NavigationInterface) {
        val isEnable = uiState.value?.selectedList?.isNotEmpty() ?: false
        val hasDrmFile = FileUtils.hasDrmFile(getSelectedRecycleFiles())
        Log.d(TAG, "setNavigateItemAble: isEnable=$isEnable, hasDrmFile=$hasDrmFile")
        navigationInterface.setNavigateItemAble(isEnable, hasDrmFile)
    }

    private companion object {
        private const val TAG = "RecycleBinViewModel"
    }

    private class RecycleBinLoaderCallBack(
        viewModel: RecycleBinViewModel
    ) : LoadingLoaderListener<RecycleBinViewModel, RecycleBinLoader, Collection<BaseRecycleFileBean>>(
        viewModel,
        viewModel.dataLoadState
    ) {
        override fun onCreateLoader(viewModel: RecycleBinViewModel?): RecycleBinLoader? =
            if (viewModel != null) {
                RecycleBinLoader(ContextGetter.context)
            } else {
                Log.e(TAG, "onCreateLoader: ERROR! no viewModel")
                null
            }

        override fun onLoadComplete(
            viewModel: RecycleBinViewModel?,
            result: Collection<BaseRecycleFileBean>?
        ) {
            viewModel ?: return
            Log.d(TAG, "onLoadComplete: result=${result?.size}")
            result ?: return
            viewModel.modeState.initState = true
            val oldSelected = viewModel.uiState.value?.selectedList ?: emptyList()
            val keyMap = hashMapOf<Int, BaseRecycleFileBean>()
            result.forEach { keyMap[it.itemId] = it }
            val newSelected = arrayListOf<Int>()
            oldSelected.forEach {
                if (keyMap.keys.contains(it)) {
                    newSelected.add(it)
                }
            }
            Log.d(TAG, "onLoadComplete: selected: ${oldSelected.size} to ${newSelected.size}")
            viewModel.modeState.listModel.run {
                if (result.isEmpty() && (value == KtConstants.LIST_SELECTED_MODE)) {
                    Log.d(TAG, "onLoadComplete mResultList is empty change to normal mode")
                    value = KtConstants.LIST_NORMAL_MODE
                }
            }
            val newUiModel = BaseUiModel(
                fileList = result.toList(),
                stateModel = viewModel.modeState,
                selectedList = newSelected,
                keyMap = keyMap
            )
            viewModel.uiState.postValue(newUiModel)
        }
    }
}