/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CommonUtil
 ** Description : 公共工具类，完成一些基本逻辑
 ** Version     : 1.0
 ** Date        : 2024/10/8 10:24
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2024/10/08       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.search.ui

import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.ThirdAppConstants.DINGTALK_PACKAGE
import andes.oplus.documentsreader.core.common.ThirdAppConstants.FEISHU_PACKAGE
import andes.oplus.documentsreader.core.common.ThirdAppConstants.QQ_PACKAGE
import andes.oplus.documentsreader.core.common.ThirdAppConstants.WECHAT_PACKAGE
import andes.oplus.documentsreader.core.common.ThirdAppConstants.WEWORK_PACKAGE
import andes.oplus.documentsreader.search.R
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.drawable.Drawable
import android.icu.text.SimpleDateFormat
import android.text.TextUtils
import java.util.Calendar
import java.util.Date

object CommonUtil {

    const val TAG = "CommonUtil"

    const val OPEN_P2P_TYPE_LOADER = 1
    const val OPEN_P2P_TYPE_INTO_SEARCH_ACTVITY = 2

    @JvmStatic
    fun getDateFormatForThirdAppFileTime(context: Context?, time: Long): String? {
        val calendar = Calendar.getInstance()
        val currentYear = calendar[Calendar.YEAR]
        calendar.timeInMillis = time
        val timeYear = calendar[Calendar.YEAR]
        var result = ""
        val date = Date()
        date.time = time
        result = if (currentYear == timeYear) {
            val sdf = SimpleDateFormat("MM/dd")
            sdf.format(date)
        } else {
            val sdf = SimpleDateFormat("yyyy/MM/dd")
            sdf.format(date)
        }
        Log.i(TAG, "getDateFormatForThirdAppFileTime input timeStamp: $time, result: $result")
        return result
    }

    @JvmStatic
    fun getTimeFormatStringForThirdAppFileTime(time: Long): String? {
        val date = Date()
        date.time = time
        val sdf = SimpleDateFormat("yyyy/MM/dd HH:mm:ss")
        val result = sdf.format(date)
        Log.i(TAG, "getDateFormatForThirdAppFileTime input timeStamp: $time, result: $result")
        return result
    }

    @JvmStatic
    fun getApplicationIconForPackage(context: Context, packageName: String?): Drawable? {
        var result: Drawable? = null
        try {
            val pm = context.packageManager
            val packageInfo = pm.getPackageInfo(packageName!!, PackageManager.GET_META_DATA)
            if (packageInfo != null) {
                val applicationInfo = packageInfo.applicationInfo
                if (applicationInfo != null) {
                    result = applicationInfo.loadIcon(pm)
                }
            }
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "getApplicationIconForPackage error, ", e)
        }
        return result
    }

    @JvmStatic
    fun getThirdAppNameForPackage(context: Context, packageName: String): String? {
        var result: String? = ""
        return if (TextUtils.isEmpty(packageName)) {
            Log.e(TAG, "getThirdAppNameForPackage packageName empty")
            result
        } else {
            result = if (packageName.equals(FEISHU_PACKAGE, ignoreCase = true)) {
                context.getString(R.string.search_item_substring_feishu)
            } else if (packageName.equals(WECHAT_PACKAGE, ignoreCase = true)) {
                context.getString(R.string.string_wechat)
            } else if (packageName.equals(DINGTALK_PACKAGE, ignoreCase = true)) {
                context.getString(R.string.search_item_substring_dingtalk)
            } else if (packageName.equals(WEWORK_PACKAGE, ignoreCase = true)) {
                context.getString(R.string.search_item_substring_wework)
            } else if (packageName.equals(QQ_PACKAGE, ignoreCase = true)) {
                context.getString(R.string.string_qq)
            } else {
                getApplicationNameForPackage(context, packageName)
            }
            result
        }
    }

    @JvmStatic
    fun getApplicationNameForPackage(context: Context, packageName: String?): String? {
        var result: String? = null
        try {
            val pm = context.packageManager
            val packageInfo = pm.getPackageInfo(packageName!!, 0)
            if (packageInfo != null) {
                val applicationInfo = packageInfo.applicationInfo
                if (applicationInfo != null) {
                    result = pm.getApplicationLabel(applicationInfo).toString()
                }
            }
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "getApplicationIconForPackage error, ", e)
        }
        return result
    }
}