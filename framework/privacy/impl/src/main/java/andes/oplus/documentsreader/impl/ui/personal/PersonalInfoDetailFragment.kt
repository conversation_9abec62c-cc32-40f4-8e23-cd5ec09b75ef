/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PersonalInfoDetailFragment
 * * Description: 个人信息明示清单的详情fragment
 * * Version: 1.0
 * * Date : 2024/08/22
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     chao.xue         2024/08/22      1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.impl.ui.personal

import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.parentchild.IMainViewApi
import andes.oplus.documentsreader.core.feedback.interfaze.IFeedback
import andes.oplus.documentsreader.impl.helper.PrivacyCollectIntroduction
import andes.oplus.documentsreader.impl.helper.PrivacyIntroductionFactory
import andes.oplus.documentsreader.impl.utils.CollectCountUtils
import andes.oplus.documentsreader.impl.utils.CollectTimeUtils
import andes.oplus.documentsreader.impl.utils.PersonalPrivacyCategory
import andes.oplus.documentsreader.impl.utils.StartNotesAppUtils
import andes.oplus.documentsreader.userprivacy.impl.R
import android.content.Context
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.clickablespan.COUIClickableSpan
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.coui.appcompat.preference.COUIPreference
import com.coui.appcompat.preference.COUIPreferenceCategory
import com.coui.appcompat.preference.COUIPreferenceWithAppbarFragment
import com.coui.appcompat.preference.COUISpannablePreference

class PersonalInfoDetailFragment : COUIPreferenceWithAppbarFragment() {

    companion object {
        private const val TAG = "PersonalInfoDetailFragment"
        private const val PREF_TIME_FILTER = "personal_detail_pref_time_filter"
        private const val PREF_USE_PURPOSE = "personal_detail_pref_use_purpose"
        private const val PREF_USE_SCENE = "personal_detail_pref_use_scene"
        private const val PREF_COLLECT_CONDITION = "personal_detail_pref_collect_condition"
        private const val PREF_INFO_CONTENT = "personal_detail_pref_info_content"
        private const val DESC_ROTATE_ANGLE = 0.0f
        private const val ASC_ROTATE_ANGLE = 180.0f
    }

    private var pref: String = ""
    private lateinit var introduction: PrivacyCollectIntroduction
    private var timeFilterPreference: COUIPreferenceCategory? = null
    private var collectConditionPref: COUIPreference? = null
    private var infoContentPref: COUISpannablePreference? = null
    private var timeFilterPopupWindow: COUIPopupListWindow? = null
    private var selectIndex = 0
    private var timeFilters = arrayOf<String>()
    private val mainApi: IMainViewApi? by lazy {
        Injector.injectFactory<IMainViewApi>()
    }

    private var viewModel: PersonalInfoDetailViewModel? = null

    private fun getLayoutResource(): Int {
        return R.xml.oplus_doc_personal_info_detail
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        pref = arguments?.getString(PersonalInfoDetailActivity.PREF, "") ?: ""
        introduction = PrivacyIntroductionFactory.create(pref)
        timeFilters = context.resources.getStringArray(R.array.collect_info_time_filter)
        Log.d(TAG, "attach $pref")
        viewModel = ViewModelProvider(this)[PersonalInfoDetailViewModel::class.java]
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        (view as ViewGroup).removeView(view.findViewById(R.id.appbar_layout))
        addPreferencesFromResource(getLayoutResource())
        initView()
        startObserve()
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        loadData(CollectTimeUtils.LAST_7_DAYS)
    }

    private fun initView() {
        initTimeFilterPreference()
        initUserPurposePreference()
        initUserScenePreference()
        initCollectConditionPreference()
        initInfoContentPreference()
    }

    private fun startObserve() {
        val activity = activity ?: return
        viewModel?.let {
            // 收集个数
            it.collectCount.observe(viewLifecycleOwner) {
                collectConditionPref?.setSummary(CollectCountUtils.getCountStr(activity, pref, getCollectConditionNumber(pref)))
            }
            // 收集内容
            it.collectContent.observe(viewLifecycleOwner) { content ->
                infoContentPref?.summary = content
            }
        }
    }

    private fun loadData(filter: Int) {
        if (filter < CollectTimeUtils.LAST_7_DAYS || filter > CollectTimeUtils.LAST_3_MONTHS) {
            Log.e(TAG, "loadData filter:$filter out range!!!")
            return
        }
        viewModel?.query(pref, filter)
    }

    /**
     * 过滤时间
     */
    private fun initTimeFilterPreference() {
        timeFilterPreference = findPreference(PREF_TIME_FILTER)
        timeFilterPreference?.setWidgetLayoutClickListener {
            Log.d(TAG, "click time filter")
            showTimeFilterPopupWindow(it)
        }
    }

    /**
     * 使用目的
     */
    private fun initUserPurposePreference() {
        val pref = findPreference<COUIPreference>(PREF_USE_PURPOSE)
        pref?.setSummary(introduction.usePurpose)
    }

    /**
     * 使用场景
     */
    private fun initUserScenePreference() {
        val pref = findPreference<COUIPreference>(PREF_USE_SCENE)
        pref?.setSummary(introduction.useScene)
    }

    /**
     * 收集情况
     */
    private fun initCollectConditionPreference() {
        val activity = activity ?: return
        collectConditionPref = findPreference(PREF_COLLECT_CONDITION)
        val count = getCollectConditionNumber(pref)
        collectConditionPref?.setSummary(CollectCountUtils.getCountStr(activity, pref, count))
    }

    private fun getCollectConditionNumber(pref: String): Int {
        var count = introduction.collectCount
        val feedBack = Injector.injectFactory<IFeedback>()
        feedBack?.run { count = getFeedBackCollectCondition(pref, count) }
        return count
    }

    /**
     * 信息内容
     */
    private fun initInfoContentPreference() {
        val activity = activity ?: return
        infoContentPref = findPreference(PREF_INFO_CONTENT)
        val content = introduction.getInfoContent(activity, null)
        var summary = getInfoContent(pref, content)
        val feedBack = Injector.injectFactory<IFeedback>()
        infoContentPref?.summary = summary
        feedBack?.run {
            summary = if (TextUtils.isEmpty(getFeedBackInfoContent(pref, summary))) {
                SpannableString("/")
            } else {
                SpannableString(getFeedBackInfoContent(pref, summary))
            }
            infoContentPref?.summary = summary
        }
    }

    /**
     * 显示时间过滤的PopupWindow
     */
    private fun showTimeFilterPopupWindow(widgetView: View?) {
        Log.d(TAG, "showTimeFilterPopupWindow selectIndex:$selectIndex")
        val anchorView = widgetView?.findViewById<View>(R.id.text_in_composition)
        val icon = widgetView?.findViewById<ImageView>(R.id.icon_in_composition)
        val activity = activity ?: return
        if (timeFilterPopupWindow == null) {
            timeFilterPopupWindow = COUIPopupListWindow(activity).apply {
                setDismissTouchOutside(true)
                setUseBackgroundBlur(true)
                setOnItemClickListener { _, _, position, _ ->
                    dismiss()
                    selectIndex = position
                    timeFilterPreference?.setTextInRight(timeFilters[position])
                    // 重新查询数据
                    loadData(position)
                }
            }
        }

        val list = ArrayList<PopupListItem>()
        val builder = PopupListItem.Builder()
        timeFilters.forEachIndexed { index, item ->
            builder.reset()
                .setTitle(item)
                .setIsChecked(selectIndex == index)
            list.add(builder.build())
        }
        timeFilterPopupWindow?.apply {
            setOnDismissListener {
                icon?.rotation = DESC_ROTATE_ANGLE
            }
            dismiss()
            itemList = list
            show(anchorView)
            icon?.rotation = ASC_ROTATE_ANGLE
        }
    }

    private fun getInfoContent(category: String, content: String): SpannableString {
        val isLink = PersonalPrivacyCategory.isLinkContent(category)
        Log.d(TAG, "getInfoContent category:$category isLink:$isLink")
        return if (isLink) {
            val ss = SpannableString(content)
            val span = COUIClickableSpan(activity)
            span.setStatusBarClickListener {
                if (PersonalPrivacyCategory.isJumpNotesApp(category)) {
                    jumpNotesApp()
                } else {
                    jumpMainActivity()
                }
            }
            ss.setSpan(span, 0, ss.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
            ss
        } else {
            SpannableString(content)
        }
    }

    /**
     * 跳转到便签app
     */
    private fun jumpNotesApp() {
        val activity = activity ?: return
        StartNotesAppUtils.jumpNotesApp(activity)
    }


    /**
     * 跳转到首页
     */
    private fun jumpMainActivity() {
        val activity = activity ?: return
        mainApi?.startMainPage(activity, 0, newTask = true)
    }


    override fun getTitle(): String {
        activity?.let {
            return it.title.toString()
        }
        return ""
    }
}