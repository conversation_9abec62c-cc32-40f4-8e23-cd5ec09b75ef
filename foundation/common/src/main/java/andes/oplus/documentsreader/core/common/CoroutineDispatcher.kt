/*********************************************************************
 ** Copyright (C), 2024-2034 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CoroutineDispatcher.kt
 ** Description : 一些需要协程处理，但是有需要有时序要求时使用该dispatcher
 ** Version     : 1.0
 ** Date        : 2024/7/17 14:46
 ** Author      : 80262777
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 ** 80262777        2024/7/17       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.core.common

import kotlinx.coroutines.asCoroutineDispatcher
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

private const val ALIVE_TIME = 30L
val synchronizedDispatcher = ThreadPoolExecutor(
    0, 1,
    ALIVE_TIME, TimeUnit.MILLISECONDS,
    LinkedBlockingQueue<Runnable>()
).asCoroutineDispatcher()