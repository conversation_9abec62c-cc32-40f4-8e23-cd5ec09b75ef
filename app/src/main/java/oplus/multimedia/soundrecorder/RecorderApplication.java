/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package oplus.multimedia.soundrecorder;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Build;

import com.coloros.soundrecorder.BuildConfig;
import com.oplus.recorderlog.util.CommonFlavor;
import com.oplus.statistics.OTrackConfig;
import com.oplus.statistics.OplusTrack;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.browsefile.search.load.center.filechange.CenterFileChangeObserver;
import com.soundrecorder.common.base.CommonApplication;
import com.soundrecorder.common.buryingpoint.RecorderUserAction;
import com.soundrecorder.common.fileobserve.MultiFileObserver;
import com.soundrecorder.common.permission.PermissionUtils;
import com.soundrecorder.common.task.ActivityTaskUtils;
import com.soundrecorder.common.utils.DisplayUtils;
import com.soundrecorder.modulerouter.cloudkit.CloudSyncAction;
import com.soundrecorder.modulerouter.cloudkit.tipstatus.CloudTipManagerAction;
import com.soundrecorder.modulerouter.notification.NotificationAction;
import com.soundrecorder.modulerouter.playback.PlaybackAction;
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction;
import com.soundrecorder.modulerouter.summary.SummaryAction;
import com.soundrecorder.player.speaker.SpeakerReceiver;
import com.soundrecorder.record.picturemark.PopTimeSliceManager;
import com.soundrecorder.wavemark.mark.ClearDataUtils;
import com.soundrecorder.wavemark.wave.WaveViewUtil;

import java.io.File;
import java.io.FileFilter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import oplus.multimedia.soundrecorder.slidebar.TransparentActivity;
import oplus.multimedia.soundrecorder.utils.RecorderUtil;

public class RecorderApplication extends CommonApplication {
    public static final String TAG = "RecorderApplication";
    private int densityDpi = 0;

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        CommonFlavor.getInstance().init(
                BuildConfig.DEBUG,
                BuildConfig.FLAVOR,
                BuildConfig.FLAVOR_B,
                BuildConfig.FLAVOR_P,
                BuildConfig.FLAVOR_region,
                BuildConfig.FLAVOR_apilevel
        );
        moveDataToCredentialStorage(base);
    }

    @Override
    protected void onCreateInit() {
        super.onCreateInit();
        DebugUtil.i(TAG, "RecorderApplication onCreate, current version sdk int:" + Build.VERSION.SDK_INT);
        OplusTrack.init(getApplicationContext(), new OTrackConfig.Builder().build());
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(RecorderUserAction.KEY_LAUNCH_RECORDER_APP, RecorderUserAction.DEFAULT_VALUE);
        RecorderUserAction.addCommonUserAction(this, RecorderUserAction.USER_ACTION_MAIN_VIEW_TAG,
                RecorderUserAction.EVENT_LAUNCH_RECORDER_APP, eventInfo, false);


        //#endif /* COLOROS_EDIT */
        RecorderUtil.enableBackgroundService(this);
        RecorderUtil.deleteLogs();

        ClearDataUtils.clearPictureMark();
        PopTimeSliceManager.INSTANCE.addRecorderListener();
        densityDpi = getResources().getDisplayMetrics().densityDpi;
    }

    //#ifdef COLOROS_EDIT
    //<EMAIL>.1376279, 2018/6/25, Add for demand 5.2
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        DebugUtil.i(TAG, "onApplicationConfigureChange  densityDpi = " + densityDpi + " , newConfig.densityDpi = " + newConfig.densityDpi);
        if (densityDpi != newConfig.densityDpi && DisplayUtils.isDefaultDisplay(DisplayUtils.currentDisplay())) {
            densityDpi = newConfig.densityDpi;
            WaveViewUtil.clearAll();
        }

        SummaryAction.loadSupportSummaryByCountry(newConfig.locale.getCountry());
    }
    //#endif /* COLOROS_EDIT */

    public void onDestroyedRelease(Activity activity) {
        if (activity.getClass().getName().equalsIgnoreCase(CloudTipManagerAction.getCloudPermissionActivityName())) {
            DebugUtil.e(TAG, "onDestroyedRelease activity is CloudPermissionActivity");
            return;
        }
        if (activity.getClass().getName().equalsIgnoreCase(CloudTipManagerAction.getRecordCloudSettingActivityName())) {
            DebugUtil.e(TAG, "onDestroyedRelease activity is SettingRecordSyncActivity");
            return;
        }
        if (activity.getClass().getName().equalsIgnoreCase(TransparentActivity.class.getName())) {
            DebugUtil.d(TAG, "onDestroyedRelease activity is TransparentActivity");
            return;
        }
        boolean isTaskEmpty = ActivityTaskUtils.isAllTaskEmpty();
        DebugUtil.i(TAG, "onActivityDestroyed isTaskEmpty is " + isTaskEmpty);
        if (isTaskEmpty) {
            if (!RecorderViewModelAction.hasInitRecorderService()) {
                //侧边栏启动时没有task栈没有activity,此时取消notification有问题
                NotificationAction.cancelAllNotification();
            }
            //退出应用时
            SpeakerReceiver.release();
            MultiFileObserver.getInstance().stopWatching(true);
            CenterFileChangeObserver.INSTANCE.release();
            CloudSyncAction.release();
            PlaybackAction.stopConvertService(this);
            //退出界面flush日志到xlog中,有读权限+已同意用户须知
            if (PermissionUtils.hasReadAudioPermission(this) && (PermissionUtils.getNextAction() != PermissionUtils.SHOULD_SHOW_USER_NOTICE)) {
                DebugUtil.aSyncPrintDbAndFlushLog();
                //RecorderLogger.INSTANCE.flushLog(false);
            }
        }
    }

    private void moveDataToCredentialStorage(Context context) {
        try {
            Context deviceProtectedStorageContext = context.createDeviceProtectedStorageContext();
            if (deviceProtectedStorageContext != null) {
                int dbExtensionLength = 3;
                int spExtensionLength = 4;

                String oldPath = deviceProtectedStorageContext.getDataDir().getAbsolutePath();
                File oldDirectory = new File(oldPath);
                String newPath = context.getDataDir().getAbsolutePath() + "/databases/recorder.db";
                File newFile = new File(newPath);

                File oldDbDirectory = new File(oldPath + "/databases");
                File[] oldDbFiles = oldDbDirectory.listFiles((FileFilter) file -> file.getName().endsWith(".db"));
                List<String> oldDbFilesWithoutExtension = null;
                if (oldDirectory.exists() && oldDirectory.isDirectory()) {
                    oldDbFilesWithoutExtension = getFileNamesWithoutExtension(oldDbFiles, dbExtensionLength);
                } else {
                    DebugUtil.d(TAG, "moveDataToCredentialStorage(): oldDirectory does not exist or is not a directory");
                }

                File oldSPDirectory = new File(oldPath + "/shared_prefs");
                File[] oldSPFiles = oldSPDirectory.listFiles((FileFilter) file -> file.getName().endsWith(".xml"));
                List<String> oldSPFilesWithoutExtension = null;
                if (oldSPDirectory.exists() && oldSPDirectory.isDirectory()) {
                    oldSPFilesWithoutExtension = getFileNamesWithoutExtension(oldSPFiles, spExtensionLength);
                } else {
                    DebugUtil.d(TAG, "moveDataToCredentialStorage(): oldSPDirectory does not exist or is not a directory");
                }

                if (oldDirectory.exists() && !newFile.exists()) {
                    if (oldDbFilesWithoutExtension != null && !oldDbFilesWithoutExtension.isEmpty()) {
                        for (String dbFile : oldDbFilesWithoutExtension) {
                            context.moveDatabaseFrom(deviceProtectedStorageContext, dbFile);
                        }
                    }
                    if (oldSPFilesWithoutExtension != null && !oldSPFilesWithoutExtension.isEmpty()) {
                        for (String spFile : oldSPFilesWithoutExtension) {
                            context.moveSharedPreferencesFrom(deviceProtectedStorageContext, spFile);
                        }
                    }
                }
            } else {
                DebugUtil.d(TAG, "moveDataToCredentialStorage(): deviceProtectedStorageContext is null");
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "moveDataToCredentialStorage() err :" + e);
        }
    }

    public List<String> getFileNamesWithoutExtension(File[] files, int spiltIndex) {
        List<String> fileNamesWithoutExtension = new ArrayList<>();
        if (files != null) {
            for (File file : files) {
                String fileNameWithoutExtension = file.getName().substring(0, file.getName().length() - spiltIndex);
                fileNamesWithoutExtension.add(fileNameWithoutExtension);
            }
        }
        return fileNamesWithoutExtension;
    }
}