/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileCutHelper.kt
 ** Description: methods about File.java
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package andes.oplus.documentsreader.core.fileoperate.cut

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.JavaFileHelper
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.MimeTypeHelper
import andes.oplus.documentsreader.core.common.SdkUtils
import andes.oplus.documentsreader.core.common.VolumeEnvironment
import andes.oplus.documentsreader.core.common.utils.PathUtils
import java.io.File

object FileCutHelper {
    private const val TAG = "FileCutHelper"
    private val mInternalPath = VolumeEnvironment.getInternalSdPath(ContextGetter.context)

    fun cutFile(sourceFile: File, destFile: File, checkParentSuccess: (path: String) -> Boolean): Boolean {
        fun internalCutFile(sourceFile: File, destFile: File): Boolean {
            return runCatching {
                if (!sourceFile.exists()) {
                    checkParentSuccess.invoke(sourceFile.absolutePath)
                } else {
                    sourceFile.renameTo(destFile).let {
                        if (!it) {
                            Log.d(TAG, "internalCutFile: failed[source_exist=${sourceFile.exists()}," +
                                    " dest_exist=${destFile.exists()}]: ${sourceFile.absolutePath}")
                        }
                        it
                    }
                }
            }.getOrElse {
                Log.e(TAG, "internalCutFile: failed to rename file: ${destFile.name}, ${it.message}")
                false
            }
        }

        if (sourceFile.isDirectory) {
            if (destFile.exists()) {
                JavaFileHelper.listFiles(sourceFile)?.forEach {
                    if (!cutFile(it, File(destFile, it.name), checkParentSuccess)) {
                        return false
                    }
                }
                sourceFile.delete()
                return true
            } else {
                return internalCutFile(sourceFile, destFile)
            }
        } else {
            if (destFile.exists()) {
                Log.d(TAG, "internalCutFile: delete dest: ${destFile.absolutePath}")
                destFile.delete()
            }
            return internalCutFile(sourceFile, destFile)
        }
    }

    fun isDirectoriesSameDisk(source: List<BaseFileBean>, target: String): Boolean {
        val sourceIsDfmPath = if (source.isNotEmpty() && source[0].mData != null) {
            PathUtils.checkIsDfmPath(source[0].mData!!)
        } else {
            false
        }
        val destIsDfmPath = PathUtils.checkIsDfmPath(target)
        if (sourceIsDfmPath != destIsDfmPath) {
            return false
        }
        if (sourceIsDfmPath && destIsDfmPath) {
            return true
        }

        val storageList = VolumeEnvironment.getVolumePathList()?.let { volumeList ->
            Log.d(TAG, "isDirectoriesSameDisk volumeList $volumeList")
            val specialNode = if (SdkUtils.isAtLeastR()) {
                arrayOf("Android/data", "Android/obb")
            } else {
                arrayOf("Android/obb")
            }
            val newList = mutableListOf<String>()
            volumeList.forEach {
                var tmpVolume = it
                var oldVolume = it
                if (it.endsWith(File.separator)) {
                    oldVolume = it.substring(0, it.length - 1)
                } else {
                    tmpVolume = it.plus(File.separator)
                }
                newList.add(oldVolume)
                specialNode.forEach { node ->
                     /*NOTE: Must add it into index 0, to make sure the special node
                     can be checked first in the next logic*/
                    newList.add(0, tmpVolume.plus(node))
                }
            }
            newList
        } ?: return true

        fun getVolume(path: String?): String? {
            if (path.isNullOrEmpty().not()) {
                for (volume in storageList) {
                    if (path!!.startsWith(volume, true)) {
                        return volume
                    }
                }
            }
            return null
        }

        val destVolume = getVolume(target)
        if (destVolume.isNullOrEmpty().not()) {
            source.forEach {
                if (getVolume(it.mData) != destVolume) {
                    return false
                }
            }
        }
        return true
    }

    /** Pair.first is size, second is mean has image in internal storage or not */
    fun checkSizeAndInternalStorageImage(file: File): Pair<Long, Boolean> {
        var size = 0L
        var hasImg = false

        fun innerCheckSizeAndImage(file: File) {
            size += file.length() // include the self of this dir
            if (file.isDirectory) {
                JavaFileHelper.listFiles(file)?.forEach {
                    innerCheckSizeAndImage(it)
                }
            } else if (!hasImg) {
                hasImg = (MimeTypeHelper.getTypeFromPath(file.name) == MimeTypeHelper.IMAGE_TYPE)
                        && file.absolutePath.startsWith(mInternalPath)
            }
        }

        innerCheckSizeAndImage(file)
        return Pair(size, hasImg)
    }
}