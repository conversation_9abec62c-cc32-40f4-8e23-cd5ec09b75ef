/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.select
 * * Version     : 1.0
 * * Date        : 2020/7/6
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.selectdir.impl.ui.path

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.MessageConstant
import andes.oplus.documentsreader.core.common.OnRecyclerItemClickListener
import andes.oplus.documentsreader.core.common.TransformNextFragmentListener
import andes.oplus.documentsreader.core.common.animation.FolderTransformAnimator
import andes.oplus.documentsreader.core.common.animation.FolderTransformAnimator.Companion.FILE_BROWSER_FOLDER_ANIM_TIME
import andes.oplus.documentsreader.core.common.interfaces.OnBackPressed
import andes.oplus.documentsreader.core.common.loader.LoaderViewModel
import andes.oplus.documentsreader.selectdir.impl.R
import android.annotation.SuppressLint
import android.content.res.Configuration
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.button.SingleButtonWrap
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.documentsreader.emptyfile.IFileEmptyController
import org.koin.android.ext.android.inject
import java.io.File
import java.util.Objects

open class SelectDirPathPanelFragment : COUIPanelFragment(), OnBackPressed, OnRecyclerItemClickListener {

    companion object {
        private const val TAG = "SelectDirPathPanelFragment"
        const val KEY_ACTION_CODE = "ACTION_CODE"
        const val DOUBLE_ACTION_INTERVAL = 2000
    }

    private lateinit var rootView: View
    private var initCurrentPath: String = ""
    private var path: String = ""
    private var actionCode: Int = 0
    private var viewModel: SelectDirPanelViewModel? = null
    private var mLastDragTime = 0L
    private var canClose = true
    private var recyclerView: RecyclerView? = null
    private var selectButton: COUIButton? = null
    private var verticalButtonWrap: SingleButtonWrap? = null
    private val mFolderTransformAnimator by lazy { FolderTransformAnimator() }
    private var layoutManager: LinearLayoutManager? = null
    private var adapter: SelectDirPathAdapter? = null

    private var selectPathListener: TransformNextFragmentListener? = null
    private val fileEmptyController: IFileEmptyController by inject<IFileEmptyController>()

    override fun initView(panelView: View?) {
        super.initView(panelView)
        rootView = LayoutInflater.from(activity).inflate(R.layout.fragment_select_dir_path, null, false)
        (contentView as? ViewGroup)?.addView(rootView)
        initData()
        initContentView(rootView)
        startObserver()
        onResumeLoadData()
    }

    private fun initData() {
        (activity as? TransformNextFragmentListener)?.run {
            selectPathListener = this
        }
        viewModel = ViewModelProvider(this).get(SelectDirPanelViewModel::class.java)
        val bundle = arguments ?: return
        path = initSelectDirPanelPath(bundle) ?: ""
        actionCode = bundle.getInt(KEY_ACTION_CODE)
        Log.d(TAG, "initData path:$path actionCode:$actionCode")
        viewModel?.initPathHelper(path)
    }

    private fun initSelectDirPanelPath(bundle: Bundle?): String? {
        initCurrentPath = bundle?.getString(KtConstants.P_CURRENT_PATH) ?: ""
        if (initCurrentPath.isNotEmpty()) {
            val file = File(initCurrentPath)
            if (!file.exists()) {
                Log.d(TAG, "initData path:$initCurrentPath not exists!!")
                file.mkdirs()
            }
        }
        val currentPath = if (viewModel?.positionModel?.value?.currentPath != null
            && !viewModel?.positionModel?.value?.currentPath.equals("")
        ) {
            viewModel?.positionModel?.value?.currentPath
        } else {
            initCurrentPath
        }
        return currentPath
    }

    private fun initContentView(view: View) {
        hideDragView()
        initToolbar()
        initPanelListener()
        initRecyclerView(view)
        initSelectButton(view)
    }


    private fun initToolbar() {
        toolbar = toolbar.apply {
            visibility = View.VISIBLE
            title = context.getString(R.string.my_documents)
            isTitleCenterStyle = true
            inflateMenu(R.menu.menu_select_dir_path)
            menu.findItem(R.id.cancel).apply {
                setOnMenuItemClickListener {
                    if (canClose) { // 点击返回， 直接退出
                        dismissPanel()
                    }
                    if (pressBack().not()) {
                        dismissPanel()
                    }
                    true
                }
            }
            menu.findItem(R.id.new_folder).apply {
                setOnMenuItemClickListener {
                    if (viewModel?.openCreateNewFolder == false) {
                        viewModel?.openCreateNewFolder = true
                        createNewFolder()
                    }
                    true
                }
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initPanelListener() {
        setOutSideViewOnTouchListener { _, event ->
            if (event.actionMasked == MotionEvent.ACTION_UP) {
                dismissPanel()
            }
            true
        }

        setDialogOnKeyListener { _, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                Log.d(TAG, "setDialogOnKeyListener back")
                pressBack()
            } else {
                false
            }
        }

        setPanelDragListener {
            if (System.currentTimeMillis() - mLastDragTime > DOUBLE_ACTION_INTERVAL) {
                mLastDragTime = System.currentTimeMillis()
                dismissPanel()
                true
            } else {
                false
            }
        }
    }

    private fun initRecyclerView(view: View) {
        recyclerView = view.findViewById(R.id.recycler_view)
        recyclerView?.let {
            layoutManager = LinearLayoutManager(context)
            it.layoutManager = layoutManager

            adapter = SelectDirPathAdapter(it.context).apply {
                setHasStableIds(true)
                setOnRecyclerItemClickListener(this@SelectDirPathPanelFragment)
            }
            it.adapter = adapter
            it.isNestedScrollingEnabled = true
            it.clipToPadding = false
            it.itemAnimator = mFolderTransformAnimator
            it.itemAnimator?.apply {
                changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
            }
        }
    }

    private fun initSelectButton(view: View) {
        selectButton = view.findViewById(R.id.select_button)
        selectButton?.let {
            verticalButtonWrap = SingleButtonWrap(it, SingleButtonWrap.Type.Large)
        }
        selectButton?.setOnClickListener {
            selectPathListener?.onSelect(actionCode, viewModel?.positionModel?.value?.currentPath)
            dismissPanel()
        }
        updateButtonView()
    }

    private fun updateButtonView() {
        when (actionCode) {
            MessageConstant.MSG_EDITOR_COPY -> updateButtonText(R.string.copy_selected_target)
            MessageConstant.MSG_EDITOR_CUT -> updateButtonText(R.string.move_selected_target)
            MessageConstant.MSG_EDITOR_DECOMPRESS -> updateButtonText(R.string.decompress_selected_target)
            MessageConstant.MSG_EDITOR_COMPRESS -> updateButtonText(R.string.compress_selected_target)
            MessageConstant.MSG_SAVE_TO_HERE -> updateButtonText(R.string.save_to_here)
            MessageConstant.MSG_SAVE -> updateButtonText(R.string.save_file)
            MessageConstant.MSG_CLOUD_DRIVE_DOWNLOAD -> updateButtonText(R.string.save_file)
            else -> updateButtonText(R.string.dialog_ok)
        }
    }

    private fun updateButtonText(strId: Int) {
        selectButton?.let {
            it.setText(strId)
            it.contentDescription = resources.getString(strId)
        }
    }


    private fun dismissPanel() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    private fun createNewFolder() {
        val activity = activity ?: return
        val dialog = getAttachDialog()
        val viewPosition = layoutManager?.findFirstVisibleItemPosition() ?: 0
        val offset = layoutManager?.findViewByPosition(viewPosition)?.top ?: recyclerView?.paddingTop ?: 0
        viewModel?.createNewFolder(activity, dialog, viewPosition, offset)
    }

    private fun startObserver() {
        startUIDataObserver()
        startPositionObserver()
    }

    private fun startUIDataObserver() {
        viewModel?.uiState?.observe(this) { fileUiModel ->
            Log.d(TAG, "uiState =" + fileUiModel.fileList.size + "," + fileUiModel.selectedList.size)
            if (fileUiModel.fileList.isEmpty() && (activity != null)) {
                fileEmptyController.showFileEmptyView(requireActivity(), rootView as ViewGroup, null, R.string.empty_file)
            } else {
                fileEmptyController.hideFileEmptyView()
            }
            adapter?.let {
                if (fileUiModel.fileList is ArrayList<BaseFileBean>) {
                    mFolderTransformAnimator.mIsFolderInAnimation = viewModel?.mIsFolderIn ?: true
                    it.setData(fileUiModel.fileList as ArrayList<BaseFileBean>)
                }
            }

        }
    }

    private fun startPositionObserver() {
        viewModel?.positionModel?.observe(this) { positionModel ->
            positionModel?.let {
                updateToolbar(it.currentPath)
                recyclerView?.run {
                    if (scrollState != RecyclerView.SCROLL_STATE_IDLE) {
                        stopScroll()
                    }
                }
                layoutManager?.scrollToPositionWithOffset(it.position, it.offset)
                viewModel?.positionModel?.value?.position = 0
                viewModel?.positionModel?.value?.offset = 0
                viewModel?.mNeedScroll = false
            }
        }
    }


    private fun updateToolbar(path: String) {
        if (actionCode != MessageConstant.MSG_SAVE && actionCode != MessageConstant.MSG_CLOUD_DRIVE_DOWNLOAD) {
            toolbar?.title = viewModel?.getCurrentDirName(path)
            // 根目录显示取消，二级目录显示返回
            val cancelMenu = toolbar.menu.findItem(R.id.cancel)
            canClose = Objects.equals(initCurrentPath, path) || viewModel?.isRootPath(path) ?: true
            if (canClose) {
                cancelMenu.setTitle(R.string.toolbar_cancel)
            } else {
                cancelMenu.setTitle(R.string.to_back)
            }
        }
    }


    private fun onResumeLoadData() {
        if (!isAdded) {
            Log.e(TAG, "onResumeLoadData fragment not add!!")
            return
        }
        viewModel?.initLoader(LoaderViewModel.getLoaderController(this), path)
    }


    override fun pressBack(): Boolean {
        Log.d(TAG, "pressBack()")
        // 回退到传入的目录时，直接退出
        if (TextUtils.equals(initCurrentPath, viewModel?.positionModel?.value?.currentPath)) {
            Log.e(TAG, "pressBack already back to currentPath $path")
            return false
        }
        return viewModel?.pressBack() ?: false
    }

    fun getSelectDirCurrentPath(): String? {
        return viewModel?.positionModel?.value?.currentPath
    }

    fun setSelectDirCurrentPath(path: String) {
        viewModel?.positionModel?.value?.currentPath = path
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        val bgColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBackgroundElevated)
        getAttachDialog()?.setPanelBackgroundTintColor(bgColor)
    }

    private fun getAttachDialog(): COUIBottomSheetDialog? {
        return (parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog
    }

    fun onUIConfigChanged(config: Configuration) {
    }

    override fun onDestroy() {
        super.onDestroy()
        verticalButtonWrap?.release()
        viewModel?.uiState?.removeObservers(this)
        viewModel?.positionModel?.removeObservers(this)
        viewModel?.onDestroy()
    }

    override fun onItemClick(view: View, position: Int) {
        recyclerView?.let {
            val viewPosition = layoutManager?.findFirstVisibleItemPosition() ?: 0
            val offset = layoutManager?.findViewByPosition(viewPosition)?.top ?: it.paddingTop
            viewModel?.onItemClick(activity, position, viewPosition, offset - it.paddingTop)
        }
    }

    override fun onItemLongClick(view: View, position: Int) {
    }
}