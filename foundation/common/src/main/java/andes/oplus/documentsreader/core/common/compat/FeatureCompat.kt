/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File: FeatureCompat.kt
 * * Description:Query and save the value of common features and query whether the feature exists
 * * Version:1.0
 * * Date :2020/8/17
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/8/17,        v1.0,           Create
 ****************************************************************/
package andes.oplus.documentsreader.core.common.compat

import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.SdkUtils
import andes.oplus.documentsreader.core.common.uiconfig.UIConfigMonitor
import andes.oplus.documentsreader.core.common.utils.ModelUtils
import andes.oplus.documentsreader.core.common.utils.PrivateSafeUtil
import android.os.Build
import andes.oplus.documentsreader.core.common.compat.compat29.FeatureCompatQ
import andes.oplus.documentsreader.core.common.compat.compat30.FeatureCompatR
import androidx.annotation.ChecksSdkIntAtLeast
import com.oplus.os.OplusBuild
import com.oplus.os.OplusVersion

object FeatureCompat {
    private const val TAG = "FeatureCompat"
    private const val OS_14 = 34
    private const val FEATURE_SE_CHIP = "com.oplus.hardware.secure_element.ese2"
    private const val FEATURE_NFC_CHIP = "com.oplus.hardware.secure_element.nfc_ese"

    private val sOPlusFeatureInterface by lazy {
        if (SdkUtils.isAtLeastR()) {
            FeatureCompatR
        } else {
            FeatureCompatQ
        }
    }

    @JvmStatic
    val sIsExpRom: Boolean
        get() = sOPlusFeatureInterface.isExpRom()

    @JvmStatic
    val sIsLightVersion: Boolean by lazy {
        val isLight = ModelUtils.isLight(ContextGetter.context)
        val lightOSByProperties = PropertyCompat.sIsLightOS
        Log.d(TAG, "isLightVersion = $isLight; lightOSByProperties = $lightOSByProperties")
        sOPlusFeatureInterface.isLightVersion() || isLight || lightOSByProperties || sOPlusFeatureInterface.isHighLevelLightOS()
    }

    @JvmStatic
    val sIsHighLevelLightOS: Boolean by lazy {
        sOPlusFeatureInterface.isHighLevelLightOS()
    }

    @JvmStatic
    val sIsNormalLightOS: Boolean by lazy {
        sIsLightVersion && sIsHighLevelLightOS.not()
    }

    @JvmStatic
    val sIsNotSupportSD: Boolean by lazy {
        sOPlusFeatureInterface.isNotSupportSD()
    }

    @JvmStatic
    val sIsSupportDrm: Boolean by lazy {
        sOPlusFeatureInterface.isSupportDrm()
    }

    /**
     * SDK_VERSION及SDK_SUB_VERSION都小于29的手机判断不适用跟手面板
     * 判断系统版本是否适用跟手面板
     */
    @JvmStatic
    val isApplicableForFlexibleWindow: Boolean by lazy {
        if (OplusVersion.isEarlierThan(OplusVersion.OS_13_0)) {
            //版本号为两位，从android-T 13.0 以上开始定义，13.0 以下无法获取
            false
        } else {
            val sdkVersion = OplusVersion.oplusOsVersion
            val subVersion = OplusVersion.oplusOsSubVersion
            Log.d(TAG, "isApplicableForFlexibleWindow SDK_VERSION:$sdkVersion SDK_SUB_VERSION:$subVersion")
            (sdkVersion >= OplusVersion.OS_13_2) && (subVersion >= OplusVersion.OS_13_2)
        }
    }

    /**
     * 手机是否支持安全芯片
     */
    @JvmStatic
    val isSupportSecurityChip: Boolean by lazy {
        ContextGetter.context.packageManager.hasSystemFeature(FEATURE_SE_CHIP)
    }

    /**
     * Get the pkg name and action of the PhoneManager.
     *
     * @return First: the package name of PhoneManager, Second: the action of PhoneManager.
     * If null, mean PhoneManager is not support
     */
    @JvmStatic
    val sPhoneManagerStartInfo: Pair<String, String>? by lazy {
        sOPlusFeatureInterface.getPhoneManagerStartInfo()
    }

    @JvmStatic
    val sIsSupportOTG: Boolean by lazy {
        sOPlusFeatureInterface.isSupportOTG()
    }

    @JvmStatic
    val sIsSupportMultiApp: Boolean by lazy {
        sOPlusFeatureInterface.isSupportMultiApp()
    }


    @JvmStatic
    val isSupportTaskbar: Boolean by lazy {
        sOPlusFeatureInterface.isSupportTaskbar()
    }

    /**
     * os 13.2以下的手机都认为是小屏手机
     * 判断是否是小屏手机（除了折叠屏，平板外的手机）
     */
    @JvmStatic
    val isSmallScreenPhone: Boolean by lazy {
        val context = ContextGetter.context
        val isTablet = ModelUtils.isTablet()
        val isFold = UIConfigMonitor.instance.isFoldable(context)
        val osVersion = OplusBuild.getOplusOSVERSION()
        Log.e(TAG, "isSmallScreenPhone osVersion:$osVersion isTablet:$isTablet isFold:$isFold")
        isTablet.not() && isFold.not()
    }

    /**
     * 手机是否支持NFC安全芯片
     */
    @JvmStatic
    val isSupportNFCSecurityChip: Boolean by lazy {
        ContextGetter.context.packageManager.hasSystemFeature(FEATURE_NFC_CHIP)
    }

    @ChecksSdkIntAtLeast(api = OS_14)
    fun isLargerThanOS14(): Boolean = Build.VERSION.SDK_INT >= OS_14

    @JvmStatic
    val sIsSupportEncryption: Boolean by lazy {
        sOPlusFeatureInterface.isSupportEncryption() && !PrivateSafeUtil.checkIsDisabledPrivateGarden()
    }

    @JvmStatic
    val sIsSupportLuxunVibrator: Boolean by lazy {
        sOPlusFeatureInterface.isSupportLuxunVibrator()
    }

    internal interface IOPlusFeatureInterface {

        fun isExpRom(): Boolean
        fun isLightVersion(): Boolean
        fun isNotSupportSD(): Boolean
        fun isSupportDrm(): Boolean
        fun isSupportEncryption(): Boolean
        fun getPhoneManagerStartInfo(): Pair<String, String>?
        fun isSupportRuntimePermissionAlert(): Boolean
        fun isOnlyUseBuildInMusicPlay(): Boolean
        fun isSupportOTG(): Boolean
        fun isNotSupportUnknownFile(): Boolean
        fun isSupportEncryptOTAOnly(): Boolean
        fun isSupportMultiApp(): Boolean

        /**
         * If it has this feature,show the size unit in 1024,else in 1000.
         * default false
         */
        fun isStorageUnitNormal(): Boolean

        /**
         * If it has this feature, show storage as 0B.
         */
        fun isStorageHidden(): Boolean
        fun isSupportLinearmotorVibrator(): Boolean

        fun isSupportTaskbar(): Boolean

        fun isSupportLuxunVibrator(): Boolean

        fun isHighLevelLightOS(): Boolean
    }
}