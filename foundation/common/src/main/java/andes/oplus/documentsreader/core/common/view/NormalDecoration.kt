/*
 * **********************************************************
 *  * * Copyright (C), 2010 - 2030 Oplus. All rights reserved..
 *  * * File: NormalDecoration
 *  * * Description: last item without line
 *  * * Version: 1.0
 *  * * Date : 2024/7/26
 *  * * Author: hades 80241271
 *  * *
 *  * * ---------------------Revision History: ---------------------
 *  * *     <author>          <data>      <version >        <desc>
 *  * *  hades 80241271    2024/7/26       1.0               create
 *  ***************************************************************
 *
 */

package andes.oplus.documentsreader.core.common.view

import android.content.Context
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.RecyclerView

class NormalDecoration(context: Context?) :
    COUIRecyclerView.COUIRecyclerViewItemDecoration(context) {

    override fun shouldDrawDivider(parent: RecyclerView?, index: Int): Boolean {
        val itemCount: Int = parent?.childCount ?: 0
        if (index == itemCount - 1 || itemCount == 0) {
            return false
        }
        return super.shouldDrawDivider(parent, index)
    }
}