/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RarDecompressHelper
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/2/21 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  wanglei        2024/2/21       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.core.archive

import andes.oplus.documentsreader.core.archive.bean.RarDecompressFile
import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.KtConstants.PREVIEW_FAILED
import andes.oplus.documentsreader.core.common.KtConstants.PREVIEW_OVER_COUNT
import andes.oplus.documentsreader.core.common.KtConstants.PREVIEW_SUCCESS
import andes.oplus.documentsreader.core.common.Log
import android.util.ArrayMap
import de.innosystec.unrar.Archive
import java.io.File

class RarDecompressHelper : DecompressHelper<RarDecompressFile>() {

    companion object {
        private const val TAG = "RarDecompressHelper"
        private const val MAX_COUNT_LIMIT = 200
        private const val SLEEP_TIMES: Long = 10
    }

    override fun createDecompressDirFile(path: String): RarDecompressFile {
        return RarDecompressFile(null).easyCreateDirFile(path) as RarDecompressFile
    }

    override fun internalPreview(sourceFile: BaseFileBean): Pair<Int, MutableMap<String, MutableList<RarDecompressFile>?>?> {
        return kotlin.runCatching {
            var archive = Archive(sourceFile.mData?.let { File(it) }, null, false)
            var fileHeaders = archive.fileHeaders
            Log.d(TAG, "internalPreview fileHeaders.size = ${fileHeaders.size}")
            if (fileHeaders.size == 0) {
                quietClose(archive)
                archive = Archive(sourceFile.mData?.let { File(it) }, null, false)
                fileHeaders = archive.fileHeaders
            }
            if (fileHeaders.size > MAX_COMPRESS_FILE_NUM) {
                return Pair(PREVIEW_OVER_COUNT, null)
            }

            val compressFiles = ArrayMap<String, MutableList<RarDecompressFile>?>()
            if (fileHeaders.size == 0) {
                return Pair(PREVIEW_FAILED, compressFiles)
            }
            fileHeaders.forEach {
                if (isValidPreviewFileName(RarDecompressFile.parsePath(it))) {
                    val file = RarDecompressFile(it)
                    createPreviewFileTree(file, compressFiles)
                }
            }
            Pair(PREVIEW_SUCCESS, compressFiles)
        }.onFailure {
            Log.e(TAG, "rar file internalPreview failed: ${it.message}")
        }.getOrDefault(Pair(PREVIEW_FAILED, null))
    }

    override fun internalIsEncrypted(sourceFile: BaseFileBean): Boolean {
        if (sourceFile.mData.isNullOrEmpty()) {
            return false
        }
        return kotlin.runCatching {
            val arch = Archive(sourceFile.mData?.let { File(it) }, null, false)
            var result = arch.isEncrypted
            if (result.not()) {
                Log.d(TAG, "isEncrypted check again use fileHeader")

                arch.fileHeaders.let {
                    if (it?.isNotEmpty() == true) {
                        result = it[0].isEncrypted
                    }
                }
            }
            result
        }.onFailure {
            Log.d(TAG, "isEncrypted create archive error: ${it.message}")
        }.getOrDefault(false)
    }
}