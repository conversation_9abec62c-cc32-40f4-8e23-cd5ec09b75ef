<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dimen_360dp"
    android:layout_height="@dimen/dimen_134dp"
    android:layout_gravity="center_horizontal"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <!--文字-->
    <RelativeLayout
        android:id="@+id/layout_create_word"
        android:layout_width="@dimen/dimen_76dp"
        android:layout_height="@dimen/dimen_94dp"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:paddingTop="@dimen/dimen_12dp">

        <ImageView
            android:layout_width="@dimen/dimen_46dp"
            android:layout_height="@dimen/dimen_46dp"
            android:layout_centerHorizontal="true"
            android:contentDescription="@null"
            android:src="@drawable/ic_file_doc" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:gravity="center"
            android:text="@string/yozo_ui_yozo_wp"
            android:textColor="?attr/couiColorPrimaryNeutral"
            android:textSize="@dimen/font_size_14" />
    </RelativeLayout>

    <!--表格-->
    <RelativeLayout
        android:id="@+id/layout_create_excel"
        android:layout_width="@dimen/dimen_76dp"
        android:layout_height="@dimen/dimen_94dp"
        android:layout_marginStart="@dimen/dimen_8dp"
        android:paddingTop="@dimen/dimen_12dp">

        <ImageView
            android:layout_width="@dimen/dimen_46dp"
            android:layout_height="@dimen/dimen_46dp"
            android:layout_centerHorizontal="true"
            android:contentDescription="@null"
            android:src="@drawable/ic_file_excl" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:gravity="center"
            android:text="@string/create_type_excel"
            android:textColor="?attr/couiColorPrimaryNeutral"
            android:textSize="@dimen/font_size_14" />
    </RelativeLayout>

    <!--演示-->
    <RelativeLayout
        android:id="@+id/layout_create_ppt"
        android:layout_width="@dimen/dimen_76dp"
        android:layout_height="@dimen/dimen_94dp"
        android:layout_marginStart="@dimen/dimen_8dp"
        android:paddingTop="@dimen/dimen_12dp">

        <ImageView
            android:layout_width="@dimen/dimen_46dp"
            android:layout_height="@dimen/dimen_46dp"
            android:layout_centerHorizontal="true"
            android:contentDescription="@null"
            android:src="@drawable/ic_file_ppt" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:gravity="center"
            android:text="@string/create_type_ppt"
            android:textColor="?attr/couiColorPrimaryNeutral"
            android:textSize="@dimen/font_size_14" />
    </RelativeLayout>

    <!--txt-->
    <RelativeLayout
        android:id="@+id/layout_create_txt"
        android:layout_width="@dimen/dimen_76dp"
        android:layout_height="@dimen/dimen_94dp"
        android:layout_marginStart="@dimen/dimen_8dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:paddingTop="@dimen/dimen_12dp">

        <ImageView
            android:layout_width="@dimen/dimen_46dp"
            android:layout_height="@dimen/dimen_46dp"
            android:layout_centerHorizontal="true"
            android:contentDescription="@null"
            android:src="@drawable/ic_file_txt" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:gravity="center"
            android:text="@string/txt"
            android:textColor="?attr/couiColorPrimaryNeutral"
            android:textSize="@dimen/font_size_14" />
    </RelativeLayout>
</LinearLayout>