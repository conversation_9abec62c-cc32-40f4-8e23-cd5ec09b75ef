/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.view
 * * Version     : 1.0
 * * Date        : 2020/3/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.documentsreader.emptyfile.view

import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.Log
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.documentsreader.emptyfile.FileEmptyUtils
import com.documentsreader.emptyfile.impl.R
import com.oplus.anim.EffectiveAnimationView

class FileEmptyView : ConstraintLayout {
    companion object {
        private const val TAG = "FileEmptyView"
    }

    private var mOppoEmptyBottle: TextView? = null
    private var mOppoEmptyBottleSummary: TextView? = null
    private var mOppoEmptyBottleGuide: TextView? = null
    private var mOppoEmptyLayout: View? = null
    private var mContentLayout: LinearLayout? = null
    private var mOppoEmptyEffectiveView: AppCompatImageView? = null
    private var mRootOnLayoutChangeListener: OnLayoutChangeListener? = null
    private var mEmptyOnLayoutChangeListener: OnLayoutChangeListener? = null
    private var mIsSmallLayout = false

    //以下字段，在分屏模式下，用于计算Title居中
    private var visibleTop = -1     //可见的高度，页面中有其他控件时，如Toolbar，找出此控件的底
    private var visibleBottom = -1  //可见的最高的底，页面中有其他控件时，如Bottom，找出此控件的Top值
    private var titleTop = -1       //标题控件的当前Top
    private var titleBottom = -1    //标题控件的当前Bottom

    //当前控件是否可见，在setEmptyVisible()的时候更新，用于在OnUIConfigChanged 状态变化时，是否需要执行setEmptyVisible()
    //在分屏模式下，需要隐藏ICON，在分屏恢复到全屏时，需要此判断，恢复ICON
    private var mVisibility: Int = View.GONE

    private var oldWidth: Int = 0
    private var oldHeight: Int = 0

    constructor(context: Context) : super(context) {
        initView(context)
    }

    constructor(context: Context, attr: AttributeSet) : super(context, attr) {
        initView(context)
    }

    constructor(context: Context, attr: AttributeSet, defStyleAttr: Int) : super(
        context,
        attr,
        defStyleAttr
    ) {
        initView(context)
    }

    private fun initView(context: Context) {
        mOppoEmptyLayout = LayoutInflater.from(context).inflate(R.layout.oplus_doc_empty_layout, this)
        mContentLayout = mOppoEmptyLayout?.findViewById(R.id.empty_content_layout)
        mOppoEmptyBottle = mOppoEmptyLayout?.findViewById(R.id.emptybottle)
        mOppoEmptyBottleSummary = mOppoEmptyLayout?.findViewById(R.id.empty_des_tv)
        mOppoEmptyBottleGuide = mOppoEmptyLayout?.findViewById(R.id.guide_tv)
        mOppoEmptyEffectiveView = mOppoEmptyLayout?.findViewById(R.id.empty_eav)
    }

    fun setSmallCenterLayout(smallLayout: Boolean) {
        mIsSmallLayout = smallLayout
        if (mIsSmallLayout) {
            FileEmptyUtils.updateIconScale(mOppoEmptyEffectiveView, 0, 0)
        }
    }

    fun getEmptyVisibility() = mVisibility

    fun setEmptyVisible(visible: Int) {
        mVisibility = visible
        if (mOppoEmptyLayout?.visibility != visible) {
            mOppoEmptyLayout?.visibility = visible
        }
        //当此设备是可见时 并且是分屏的时候，隐藏掉ICON
        if (visible == View.VISIBLE) {
            mOppoEmptyLayout?.post { updateEmptyMarginTop() }
        }
        (mOppoEmptyEffectiveView as? EffectiveAnimationView)?.apply {
            if (View.VISIBLE == visible) {
                if (isAnimating.not()) {
                    playAnimation()
                }
            } else {
                if (isAnimating) {
                    cancelAnimation()
                }
                resumeAnimation()
            }
        }
    }

    /**
     * 计算外部可用高度及可用底部，调整MarginTop
     */
    private fun updateEmptyMarginTop() {
        mOppoEmptyBottle ?: return
        mOppoEmptyLayout ?: return
        if (!mIsSmallLayout && visibleTop == -1 && visibleBottom == -1) {
            compareWithAllViews()
            mOppoEmptyLayout?.let { emptyLayout ->
                val appBarHeight = resources.getDimension(R.dimen.appbar_layout_height)
                val layoutMarginTop = if (emptyLayout.top < appBarHeight) {
                    visibleTop - COUIPanelMultiWindowUtils.getStatusBarHeight(ContextGetter.context)
                } else {
                    visibleTop - emptyLayout.top
                }
                val layoutParams = emptyLayout.layoutParams
                if (layoutParams is CoordinatorLayout.LayoutParams) {
                    layoutParams.topMargin = layoutMarginTop
                } else if (layoutParams is RelativeLayout.LayoutParams) {
                    layoutParams.topMargin = layoutMarginTop
                }
                setLayoutParams(layoutParams)
            }
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        val nowWidth = measuredWidth
        val nowHeight = measuredHeight
        if (nowWidth != oldWidth || nowHeight != oldHeight) {
            oldWidth = nowWidth
            oldHeight = nowHeight
            mOppoEmptyEffectiveView?.visibility = FileEmptyUtils.getEmptyIconVisible(context, height)
            if (mIsSmallLayout) {
                FileEmptyUtils.updateIconScale(mOppoEmptyEffectiveView, 0, 0)
            } else {
                FileEmptyUtils.updateIconScale(mOppoEmptyEffectiveView, nowWidth, nowHeight)
            }
        }
    }

    /**
     * 循环遍历整个页面中的控件,比较并找出可用于居中判断的visibleTop 及 visibleBottom
     */
    private fun compareWithAllViews(parentView: ViewGroup? = null) {
        visibleBottom = mOppoEmptyLayout!!.bottom
        titleTop = mOppoEmptyBottle!!.top
        titleBottom = titleTop + mOppoEmptyBottle!!.measuredHeight
        Log.d(TAG, "compareWithAllViews visibleBottom = $visibleBottom, titleTop = $titleTop, titleBottom = $titleBottom")
        val rootView = parentView ?: mOppoEmptyLayout?.parent as ViewGroup
        if (rootView.parent != null && rootView.parent is ViewGroup && rootView.id != android.R.id.content) {
            compareWithAllViews(rootView.parent as ViewGroup)
        }
        for (childIndex in 0..rootView.childCount) {
            val childView = rootView.getChildAt(childIndex)
            childView?.let {
                if (childView.visibility != View.VISIBLE) {
                    Log.w(
                        TAG, "getVisibleTop childView do not need this view: " +
                                "top:${childView.top}  " +
                                "b:${childView.bottom} v:${childView.visibility} h:${childView.measuredHeight} " +
                                "y:${childView.y} $childView "
                    )
                    return@let
                }
                val childTop = it.top + it.measuredHeight
                if (it.top < titleTop && childTop in visibleTop until visibleBottom) {
                    visibleTop = childTop
                    Log.i(TAG, "update visibleTop  $visibleTop  top:${it.top}  H:${it.measuredHeight}  y:${it.y}  bottom:${it.bottom} child:$it")
                }
                if (it.top in (titleBottom + 1) until visibleBottom) {
                    visibleBottom = it.top
                    Log.i(TAG, "update visibleBottom  $visibleBottom child:$it")
                }
            }
        }
    }

    fun setEmptyTextViewId(textId: Int) {
        mOppoEmptyBottle?.setText(textId)
    }

    fun setEmptyAnimation(asset: String) {
        (mOppoEmptyEffectiveView as? EffectiveAnimationView)?.setAnimation(asset)
    }

    fun setAnimationReset() {
        (mOppoEmptyEffectiveView as? EffectiveAnimationView)?.apply {
            if (isAnimating) {
                canAnimate()
                resumeAnimation()
            }
        }
    }

    override fun onDetachedFromWindow() {
        (mOppoEmptyEffectiveView as? EffectiveAnimationView)?.apply {
            runCatching {
                cancelAnimation()
                setImageDrawable(null)
            }.onFailure { error ->
                Log.e(TAG, " onDetachedFromWindow ${error.message}")
            }
        }
        mRootOnLayoutChangeListener = null
        mEmptyOnLayoutChangeListener = null
        super.onDetachedFromWindow()
    }

    fun setEmptyGuidVisibilityAndContent(visible: Int, content: String, clickListener: OnClickListener?) {
        mOppoEmptyBottleGuide?.let {
            COUIChangeTextUtil.adaptFontSize(it, COUIChangeTextUtil.G4)
            it.visibility = visible
            it.text = content
            COUITextViewCompatUtil.setPressRippleDrawable(it)
            it.setOnClickListener(clickListener)
        }
    }

    fun setEmptySummaryVisibilityAndContent(visible: Int, content: String) {
        mOppoEmptyBottleSummary?.let {
            it.visibility = visible
            it.text = content
        }
    }
}