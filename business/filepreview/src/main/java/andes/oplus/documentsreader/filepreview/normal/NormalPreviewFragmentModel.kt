/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : NormalPreviewFragmentModel
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/2/21 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  wanglei        2024/2/21       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.filepreview.normal

import andes.oplus.documentsreader.core.common.ApkInfo
import andes.oplus.documentsreader.core.common.AppUtils
import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.FileUtils
import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.KtConstants.PREVIEW_NO_ERROR
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.MimeType
import andes.oplus.documentsreader.filepreview.PreviewFragmentModel
import andes.oplus.documentsreader.filepreview.util.OpenAnyUtil
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.annotation.VisibleForTesting
import androidx.core.content.FileProvider
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

class NormalPreviewFragmentModel : PreviewFragmentModel() {
    companion object {
        private const val TAG = "NormalPreviewFragmentModel"
        @VisibleForTesting
        const val TYPE_APK = "apk"

        val Factory: ViewModelProvider.Factory = object : ViewModelProvider.Factory {
            @Suppress("UNCHECKED_CAST")
            override fun <T : ViewModel> create(modelClass: Class<T>): T {
                return NormalPreviewFragmentModel() as T
            }
        }
    }

    var isAPKtType: Boolean = false
    var apkInfo = MutableLiveData<ApkInfo>()
    var canOpenByApp = MutableLiveData<Boolean>()
    var previewErrorType = PREVIEW_NO_ERROR

    /**
     * 获取文件相关的信息
     *
     * @param arguments Bundle?
     */
    override fun initFileData(arguments: Bundle?) {
        super.initFileData(arguments)
        //通过intent获取到数据 --Archive压缩包类型
        val bundle = arguments ?: return
        previewErrorType = bundle.getInt(KtConstants.INTENT_PREVIEW_ERROR_TYPE, -1)
    }

    fun checkInfo() {
        if (TYPE_APK == FileUtils.getExtension(filePath)) {
            isAPKtType = true
            checkApkInfo()
        }
        checkCanOpenByApp()
    }

    @VisibleForTesting
    fun checkApkInfo() {
        viewModelScope.launch(Dispatchers.IO) {
            val info = AppUtils.getAppInfoByPath(ContextGetter.context, filePath)
            apkInfo.postValue(info)
        }
    }

    @VisibleForTesting
    fun checkCanOpenByApp() {
        viewModelScope.launch(Dispatchers.IO) {
            val canOpenByApp = if (fileType == MimeType.MIMETYPE_UNKNOWN) {
                false
            } else {
                OpenAnyUtil.canOpenByApp(ContextGetter.context, filePath, fileType)
            }
            <EMAIL>(canOpenByApp)
        }
    }

    /**
     * 通过filePath安装APK
     * @param path String
     */
    fun installAPKByFilePath(activity: Activity, path: String) {
        val apkFileUri = FileProvider.getUriForFile(
            ContextGetter.context,
            KtConstants.PREVIEW_AUTHORITY,
            File(path)
        )
        kotlin.runCatching {
            val intent = Intent(Intent.ACTION_VIEW)
            intent.setDataAndType(
                apkFileUri,
                MimeType.MIMETYPE_APPLICATION
            )
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            intent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
            activity.startActivity(intent)
        }.onFailure {
            Log.e(TAG, "installAPKByFilePath：install error:$it")
        }
    }
}