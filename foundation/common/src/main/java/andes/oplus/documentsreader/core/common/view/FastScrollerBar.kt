/***********************************************************
 * * Copyright (C), 2007-2021, Oplus. All rights reserved
 * *
 * * File:com.coloros.filemanager.filerefactor.base/FastScrollerBar.kt
 * * Description:
 * * Version:1.0
 * * Date :2021/1/12
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2021/1/12,        v1.0,           Create
 ****************************************************************/
package andes.oplus.documentsreader.core.common.view

import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.Log
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.animation.ValueAnimator.AnimatorUpdateListener
import android.content.Context
import android.graphics.Canvas
import android.graphics.drawable.Drawable
import android.graphics.drawable.StateListDrawable
import android.view.MotionEvent
import android.view.View
import androidx.annotation.IntDef
import androidx.annotation.VisibleForTesting
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import kotlin.math.abs
import kotlin.math.ceil
import kotlin.math.max
import kotlin.math.min

/**
 * Class responsible to animate and provide a fast scroller.
 */
class FastScrollerBar(
    val context: Context,
    recyclerView: COUIRecyclerView,
    val mVerticalThumbDrawable: StateListDrawable,
    val mVerticalTrackDrawable: Drawable,
    private val mHorizontalThumbDrawable: StateListDrawable,
    private val mHorizontalTrackDrawable: Drawable
) : ItemDecoration(), RecyclerView.OnItemTouchListener, View.OnAttachStateChangeListener {


    @IntDef(STATE_HIDDEN, STATE_VISIBLE, STATE_DRAGGING)
    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    private annotation class State

    @IntDef(DRAG_X, DRAG_Y, DRAG_NONE)
    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    private annotation class DragState

    @IntDef(ANIMATION_STATE_OUT, ANIMATION_STATE_FADING_IN, ANIMATION_STATE_IN, ANIMATION_STATE_FADING_OUT)
    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    private annotation class AnimationState

    companion object {
        // Scroll thumb not showing
        private const val TAG = "FastScrollBar"
        const val SPAN_COUNT_ONE_SCROLLER_MIN_SIZE = 60
        const val SPAN_COUNT_FOUR_SCROLLER_MIN_SIZE = 140
        private const val STATE_HIDDEN = 0

        // Scroll thumb visible and moving along with the scrollbar
        private const val STATE_VISIBLE = 1

        // Scroll thumb being dragged by user
        private const val STATE_DRAGGING = 2
        private const val DRAG_NONE = 0
        private const val DRAG_X = 1
        private const val DRAG_Y = 2
        private const val ANIMATION_STATE_OUT = 0
        private const val ANIMATION_STATE_FADING_IN = 1
        private const val ANIMATION_STATE_IN = 2
        private const val ANIMATION_STATE_FADING_OUT = 3
        private const val SHOW_DURATION_MS = 500
        private const val HIDE_DELAY_AFTER_VISIBLE_MS = 1500
        private const val HIDE_DELAY_AFTER_DRAGGING_MS = 1200
        private const val HIDE_DURATION_MS = 500
        private const val SCROLLBAR_FULL_OPAQUE = 255
        private val PRESSED_STATE_SET = intArrayOf(android.R.attr.state_pressed)
        private val EMPTY_STATE_SET = intArrayOf()
    }

    private val defaultWidth = context.resources.getDimensionPixelSize(androidx.recyclerview.R.dimen.fastscroll_default_thickness)
    private val scrollbarMinimumRange = context.resources.getDimensionPixelSize(androidx.recyclerview.R.dimen.fastscroll_minimum_range)
    var margin = context.resources.getDimensionPixelOffset(androidx.recyclerview.R.dimen.fastscroll_margin)

    // Dynamic values for the vertical scroll bar
    var mVerticalThumbHeight = 0

    var mVerticalThumbCenterY = 0

    var mVerticalDragY = 0f

    // Dynamic values for the horizontal scroll bar
    var mHorizontalThumbWidth = 0

    var mHorizontalThumbCenterX = 0

    var mHorizontalDragX = 0f

    /* synthetic access */
    @SuppressWarnings("WeakerAccess")
    val mShowHideAnimator: ValueAnimator = ValueAnimator.ofFloat(0f, 1f)

    @SuppressWarnings("WeakerAccess")
    /* synthetic access */
    @AnimationState
    var mAnimationState: Int = ANIMATION_STATE_OUT

    var mShowEnable = true

    val isDragging: Boolean
        get() = mState == STATE_DRAGGING

    val isVisible: Boolean
        get() = mState == STATE_VISIBLE


    val horizontalThumbDrawable: Drawable
        get() = mHorizontalThumbDrawable

    val verticalThumbDrawable: Drawable
        get() = mVerticalThumbDrawable

    private val mRecyclerViewPaddingTop: Int
        get() {
            return mRecyclerView?.paddingTop ?: 0
        }

    private val mRecyclerViewPaddingBottom: Int
        get() {
            return mRecyclerView?.paddingBottom ?: 0
        }

    /**
     * Gets the (min, max) vertical positions of the vertical scroll bar.
     */
    private val verticalRange: IntArray
        get() { // change code
            // mVerticalRange[0] = mMargin
            // mVerticalRange[1] = mRecyclerViewHeight - mMargin
            mVerticalRange[0] = mMarginTop + mRecyclerViewPaddingTop + (mVerticalThumbHeight / 2)
            mVerticalRange[1] =
                mRecyclerViewHeight - mMarginBottom - mRecyclerViewPaddingBottom - (mVerticalThumbHeight / 2)
            return mVerticalRange
        }

    /**
     * Gets the (min, max) horizontal positions of the horizontal scroll bar.
     */
    private val horizontalRange: IntArray
        get() {
            mHorizontalRange[0] = mMarginStart
            mHorizontalRange[1] = mRecyclerViewWidth - mMarginEnd
            return mHorizontalRange
        }

    private val mScrollbarMinimumRange: Int

    private val mVerticalThumbWidth: Int
    private val mVerticalTrackWidth: Int

    private val mHorizontalThumbHeight: Int
    private val mHorizontalTrackHeight: Int

    private var mRecyclerViewWidth = 0
    private var mRecyclerViewHeight = 0
    private var mRecyclerView: COUIRecyclerView? = null
    private var mMarginTop: Int = 0
    private var mMarginBottom: Int = 0
    private var mMarginStart: Int = 0
    private var mMarginEnd: Int = 0

    /**
     * Whether the document is long/wide enough to require scrolling. If not, we don't show the
     * relevant scroller.
     */
    private var mNeedVerticalScrollbar = false
    private var mNeedHorizontalScrollbar = false

    @State
    private var mState: Int = STATE_HIDDEN

    @DragState
    private var mDragState: Int = DRAG_NONE
    private val mVerticalRange = IntArray(2)
    private val mHorizontalRange = IntArray(2)

    private val mHideRunnable = Runnable { hide(HIDE_DURATION_MS) }
    private val mOnScrollListener: RecyclerView.OnScrollListener = object : RecyclerView.OnScrollListener() {
        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            if (mState == STATE_DRAGGING) {
                return
            }
            if (mShowEnable.not()) {
                if (mState != STATE_HIDDEN) {
                    setState(STATE_HIDDEN)
                }
                return
            }
            /**
             * add check list row
             * Check if the number of rows in the list reaches the minimum number of rows to display the slider
             */
            val contentLength = recyclerView.computeVerticalScrollRange()
            if (checkContentTooShort(contentLength)) {
                return
            }

            //add check list row end
            updateScrollPosition(
                recyclerView.computeHorizontalScrollOffset(),
                recyclerView.computeVerticalScrollOffset(), contentLength
            )
        }
    }

    private fun checkContentTooShort(contentLength: Int): Boolean {
        fun getShowMinListSize(): Int {
            return mRecyclerView?.layoutManager.let {
                when (it) {
                    is GridLayoutManager -> {
                        when {
                            it.spanCount >= KtConstants.GRID_ITEM_COUNT_4 -> {
                                (SPAN_COUNT_FOUR_SCROLLER_MIN_SIZE * it.spanCount) / KtConstants.GRID_ITEM_COUNT_4
                            }

                            else -> SPAN_COUNT_ONE_SCROLLER_MIN_SIZE
                        }
                    }

                    else -> SPAN_COUNT_ONE_SCROLLER_MIN_SIZE
                }
            }
        }

        if (mRecyclerViewHeight <= 0) {
            return true
        }
        val itemCount = mRecyclerView?.adapter?.itemCount ?: 0
        var minListSize = 0
        if (itemCount > 0) {
            minListSize = getShowMinListSize()
        }
        Log.d(TAG, "checkContentTooShort, itemCount=$itemCount, minListSize=$minListSize")
        if (itemCount <= minListSize) {
            mNeedVerticalScrollbar = false
            if (mState != STATE_HIDDEN) {
                setState(STATE_HIDDEN)
                cancelHide()
                hide(0)
            }
            return true
        }
        return false
    }

    private val isLayoutRTL: Boolean
        get() = ViewCompat.getLayoutDirection(mRecyclerView!!) == ViewCompat.LAYOUT_DIRECTION_RTL


    init {
        mVerticalThumbWidth = max(defaultWidth, mVerticalThumbDrawable.intrinsicWidth)
        /**
         * change code
         * scroll bar height to wrap content
         */
        mVerticalThumbHeight = max(defaultWidth, mVerticalThumbDrawable.intrinsicHeight)
        mVerticalTrackWidth = max(defaultWidth, mVerticalTrackDrawable.intrinsicWidth)

        mHorizontalThumbHeight = max(defaultWidth, mHorizontalThumbDrawable.intrinsicWidth)
        mHorizontalTrackHeight = max(defaultWidth, mHorizontalTrackDrawable.intrinsicWidth)
        mScrollbarMinimumRange = scrollbarMinimumRange
        mMarginTop = margin
        mMarginBottom = margin

        mVerticalThumbDrawable.alpha = SCROLLBAR_FULL_OPAQUE
        mVerticalTrackDrawable.alpha = SCROLLBAR_FULL_OPAQUE
        mShowHideAnimator.addListener(AnimatorListener())
        mShowHideAnimator.addUpdateListener(AnimatorUpdater())
        attachToRecyclerView(recyclerView)
    }

    private fun attachToRecyclerView(recyclerView: COUIRecyclerView?) {
        if (mRecyclerView === recyclerView) {
            // nothing to do
            return
        }
        if (mRecyclerView != null) {
            destroyCallbacks()
        }
        mRecyclerView = recyclerView
        if (mRecyclerView != null) {
            setupCallbacks()
            mRecyclerView!!.addOnAttachStateChangeListener(this)
        }
    }

    private fun setupCallbacks() {
        mRecyclerView!!.addItemDecoration(this)
        mRecyclerView!!.addOnItemTouchListener(this)
        mRecyclerView!!.addOnScrollListener(mOnScrollListener)
    }

    fun destroyCallbacks() {
        mRecyclerView!!.removeItemDecoration(this)
        mRecyclerView!!.removeOnItemTouchListener(this)
        mRecyclerView!!.removeOnScrollListener(mOnScrollListener)
        cancelHide()
    }

    fun removeAnimatorListenersAndCancel() {
        mShowHideAnimator.removeAllUpdateListeners()
        mShowHideAnimator.removeAllListeners()
        mShowHideAnimator.cancel()
    }

    override fun onViewDetachedFromWindow(p0: View) {
        removeAnimatorListenersAndCancel()
    }

    override fun onViewAttachedToWindow(v: View) {
        mShowHideAnimator.addListener(AnimatorListener())
        mShowHideAnimator.addUpdateListener(AnimatorUpdater())
        //can not be disappear after new attached
        setState(STATE_VISIBLE)
    }

    fun requestRedraw() {
        mRecyclerView!!.invalidate()
    }

    fun setState(@State state: Int) {
        if ((state == STATE_DRAGGING) && (mState != STATE_DRAGGING)) {
            mVerticalThumbDrawable.state = PRESSED_STATE_SET
            cancelHide()
        }
        if (state == STATE_HIDDEN) {
            requestRedraw()
        } else {
            show()
        }
        if ((mState == STATE_DRAGGING) && (state != STATE_DRAGGING)) {
            mVerticalThumbDrawable.state = EMPTY_STATE_SET
            resetHideDelay(HIDE_DELAY_AFTER_DRAGGING_MS)
        } else if (state == STATE_VISIBLE) {
            resetHideDelay(HIDE_DELAY_AFTER_VISIBLE_MS)
        } else if (state == STATE_HIDDEN) {
            resetHideDelay(0)
        }
        mState = state
    }


    fun show() {
        when (mAnimationState) {
            ANIMATION_STATE_FADING_OUT -> {
                mShowHideAnimator.cancel()
                mAnimationState = ANIMATION_STATE_FADING_IN
                mShowHideAnimator.setFloatValues(mShowHideAnimator.animatedValue as Float, 1f)
                mShowHideAnimator.duration = SHOW_DURATION_MS.toLong()
                mShowHideAnimator.startDelay = 0
                mShowHideAnimator.start()
            }

            ANIMATION_STATE_OUT -> {
                mAnimationState = ANIMATION_STATE_FADING_IN
                mShowHideAnimator.setFloatValues(mShowHideAnimator.animatedValue as Float, 1f)
                mShowHideAnimator.duration = SHOW_DURATION_MS.toLong()
                mShowHideAnimator.startDelay = 0
                mShowHideAnimator.start()
            }
        }
    }

    @VisibleForTesting
    fun hide(duration: Int) {
        when (mAnimationState) {
            ANIMATION_STATE_FADING_IN -> {
                mShowHideAnimator.cancel()
                mAnimationState = ANIMATION_STATE_FADING_OUT
                mShowHideAnimator.setFloatValues(mShowHideAnimator.animatedValue as Float, 0f)
                mShowHideAnimator.duration = duration.toLong()
                mShowHideAnimator.start()
            }

            ANIMATION_STATE_IN -> {
                mAnimationState = ANIMATION_STATE_FADING_OUT
                mShowHideAnimator.setFloatValues(mShowHideAnimator.animatedValue as Float, 0f)
                mShowHideAnimator.duration = duration.toLong()
                mShowHideAnimator.start()
            }
        }
    }

    private fun cancelHide() {
        mRecyclerView!!.removeCallbacks(mHideRunnable)
    }

    private fun resetHideDelay(delay: Int) {
        cancelHide()
        mRecyclerView!!.postDelayed(mHideRunnable, delay.toLong())
    }

    override fun onDrawOver(canvas: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        if ((mRecyclerViewWidth != mRecyclerView!!.width)
            || (mRecyclerViewHeight != mRecyclerView!!.height)
        ) {
            mRecyclerViewWidth = mRecyclerView!!.width
            mRecyclerViewHeight =
                mRecyclerView!!.height
            if (checkContentTooShort(mRecyclerView!!.computeVerticalScrollRange())) {
                return
            }
            updateScrollPosition(
                mRecyclerView!!.computeHorizontalScrollOffset(),
                mRecyclerView!!.computeVerticalScrollOffset(),
                mRecyclerView!!.computeVerticalScrollRange()
            )
        }
        if (mAnimationState != ANIMATION_STATE_OUT) {
            if (mNeedVerticalScrollbar) {
                drawVerticalScrollbar(canvas)
            }
            if (mNeedHorizontalScrollbar) {
                drawHorizontalScrollbar(canvas)
            }
        }
    }

    private fun drawVerticalScrollbar(canvas: Canvas) {
        val viewWidth = mRecyclerViewWidth
        val left = viewWidth - mVerticalThumbWidth
        val top = mVerticalThumbCenterY - mVerticalThumbHeight / 2
        mVerticalThumbDrawable.setBounds(0, 0, mVerticalThumbWidth, mVerticalThumbHeight)
        mVerticalTrackDrawable
            .setBounds(0, 0, mVerticalTrackWidth, mRecyclerViewHeight)
        if (isLayoutRTL) {
            mVerticalTrackDrawable.draw(canvas)
            canvas.translate(mVerticalThumbWidth.toFloat(), top.toFloat())
            canvas.scale(-1f, 1f)
            mVerticalThumbDrawable.draw(canvas)
            canvas.scale(1f, 1f)
            canvas.translate(-mVerticalThumbWidth.toFloat(), -top.toFloat())
        } else {
            canvas.translate(left.toFloat(), 0f)
            mVerticalTrackDrawable.draw(canvas)
            canvas.translate(0f, top.toFloat())
            mVerticalThumbDrawable.draw(canvas)
            canvas.translate(-left.toFloat(), -top.toFloat())
        }
    }

    private fun drawHorizontalScrollbar(canvas: Canvas) {
        val viewHeight = mRecyclerViewHeight
        val top = viewHeight - mHorizontalThumbHeight
        val left = mHorizontalThumbCenterX - mHorizontalThumbWidth / 2
        mHorizontalThumbDrawable.setBounds(0, 0, mHorizontalThumbWidth, mHorizontalThumbHeight)
        mHorizontalTrackDrawable
            .setBounds(0, 0, mRecyclerViewWidth, mHorizontalTrackHeight)
        canvas.translate(0f, top.toFloat())
        mHorizontalTrackDrawable.draw(canvas)
        canvas.translate(left.toFloat(), 0f)
        mHorizontalThumbDrawable.draw(canvas)
        canvas.translate(-left.toFloat(), -top.toFloat())
    }

    /**
     * Notify the scroller of external change of the scroll, e.g. through dragging or flinging on
     * the view itself.
     *
     * @param offsetX The new scroll X offset.
     * @param offsetY The new scroll Y offset.
     */
    fun updateScrollPosition(offsetX: Int, offsetY: Int, contentLength: Int) {
        val verticalVisibleLength = mRecyclerViewHeight - mRecyclerViewPaddingTop - mRecyclerViewPaddingBottom
        mNeedVerticalScrollbar = ((contentLength - verticalVisibleLength > 0)
                && (mRecyclerViewHeight >= mScrollbarMinimumRange))
        val horizontalContentLength = mRecyclerView!!.computeHorizontalScrollRange()
        val horizontalVisibleLength = mRecyclerViewWidth
        mNeedHorizontalScrollbar = ((horizontalContentLength - horizontalVisibleLength > 0)
                && (mRecyclerViewWidth >= mScrollbarMinimumRange))
        if (!mNeedVerticalScrollbar && !mNeedHorizontalScrollbar) {
            if (mState != STATE_HIDDEN) {
                setState(STATE_HIDDEN)
            }
            return
        }
        if (mNeedVerticalScrollbar) {
            val enableScrollLength = (contentLength - verticalVisibleLength).toFloat()
            val startOffsetY = mMarginTop + mRecyclerViewPaddingTop + (mVerticalThumbHeight / 2)
            val percent = offsetY / enableScrollLength
            val scrollerRangeLength = (verticalVisibleLength - mVerticalThumbHeight - mMarginTop - mMarginBottom)
            mVerticalThumbCenterY = startOffsetY + (scrollerRangeLength * percent).toInt()
        }
        if (mNeedHorizontalScrollbar) {
            val middleScreenPos = offsetX + horizontalVisibleLength / 2.0f
            mHorizontalThumbCenterX = (horizontalVisibleLength * middleScreenPos / horizontalContentLength).toInt()
            mHorizontalThumbWidth = min(
                horizontalVisibleLength,
                horizontalVisibleLength * horizontalVisibleLength / horizontalContentLength
            )
        }
        if ((mState == STATE_HIDDEN) || (mState == STATE_VISIBLE)) {
            setState(STATE_VISIBLE)
        }
    }

    override fun onInterceptTouchEvent(
        recyclerView: RecyclerView,
        ev: MotionEvent
    ): Boolean {
        val handled: Boolean
        if (mState == STATE_VISIBLE) {
            val insideVerticalThumb = isPointInsideVerticalThumb(ev.x, ev.y)
            val insideHorizontalThumb = isPointInsideHorizontalThumb(ev.x, ev.y)
            if ((ev.action == MotionEvent.ACTION_DOWN)
                && (insideVerticalThumb || insideHorizontalThumb)
            ) {
                if (insideHorizontalThumb) {
                    mDragState = DRAG_X
                    mHorizontalDragX = ev.x
                } else if (insideVerticalThumb) {
                    mDragState = DRAG_Y
                    mVerticalDragY = ev.y
                }
                setState(STATE_DRAGGING)
                handled = true
            } else {
                handled = false
            }
        } else handled = mState == STATE_DRAGGING
        return handled
    }

    override fun onTouchEvent(recyclerView: RecyclerView, me: MotionEvent) {
        if (mState == STATE_HIDDEN) {
            return
        }
        if (me.action == MotionEvent.ACTION_DOWN) {
            val insideVerticalThumb = isPointInsideVerticalThumb(me.x, me.y)
            val insideHorizontalThumb = isPointInsideHorizontalThumb(me.x, me.y)
            if (insideVerticalThumb || insideHorizontalThumb) {
                if (insideHorizontalThumb) {
                    mDragState = DRAG_X
                    mHorizontalDragX = me.x
                } else if (insideVerticalThumb) {
                    mDragState = DRAG_Y
                    mVerticalDragY = me.y
                }
                setState(STATE_DRAGGING)
            }
        } else if ((me.action == MotionEvent.ACTION_UP) && (mState == STATE_DRAGGING)) {
            mVerticalDragY = 0f
            mHorizontalDragX = 0f
            setState(STATE_VISIBLE)
            mDragState = DRAG_NONE
        } else if ((me.action == MotionEvent.ACTION_MOVE) && (mState == STATE_DRAGGING)) {
            show()
            if (mDragState == DRAG_X) {
                horizontalScrollTo(me.x)
            }
            if (mDragState == DRAG_Y) {
                verticalScrollTo(me.y)
            }
        }
    }

    override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {}

    private fun verticalScrollTo(y: Float) {
        var floatY = y
        val scrollbarRange = verticalRange
        if (abs(mVerticalThumbCenterY - floatY) < 2) {
            return
        }
        val viewLength = mRecyclerViewHeight - mRecyclerViewPaddingTop - mRecyclerViewPaddingBottom
        val listScrollRange = mRecyclerView!!.computeVerticalScrollRange()
        val listOffset = mRecyclerView!!.computeVerticalScrollOffset()
        val scrollingBy = scrollTo(
            mVerticalDragY, floatY, scrollbarRange,
            listScrollRange, listOffset, viewLength
        )
        Log.d(TAG, "verticalScrollTo scrollingBy: $scrollingBy")
        if (scrollingBy != 0) {
            /**
             * If the sliding distance is too long, it will freeze.
             * So when the distance exceeds one screen, it will be converted into position and executed.
             */
            if (abs(scrollingBy) > viewLength) {
                val toPosition = ceil((listOffset + scrollingBy) / listScrollRange.toFloat() * (mRecyclerView!!.adapter!!.itemCount - 1))
                val offset = mRecyclerView!!.getChildAt(0)?.let {
                    it.top + scrollingBy % it.height
                } ?: 0
                Log.d(TAG, "verticalScrollTo scrollToPosition: p=$toPosition, offset=$offset")
                with(mRecyclerView!!.layoutManager) {
                    when (this) {
                        is LinearLayoutManager -> scrollToPositionWithOffset(toPosition.toInt(), offset)

                        is RecyclerView.LayoutManager -> scrollToPosition(toPosition.toInt())
                    }
                }
            } else {
                mRecyclerView!!.scrollBy(0, scrollingBy)
            }
        }
        mVerticalDragY = floatY
        if (mState == STATE_DRAGGING) {
            if (floatY > scrollbarRange[1]) {
                floatY = scrollbarRange[1].toFloat()
            } else if (floatY < scrollbarRange[0]) {
                floatY = scrollbarRange[0].toFloat()
            }
            mVerticalThumbCenterY = floatY.toInt()
        }
    }

    private fun horizontalScrollTo(x: Float) {
        var floatX = x
        val scrollbarRange = horizontalRange
        floatX = max(scrollbarRange[0].toFloat(), min(scrollbarRange[1].toFloat(), floatX))
        if (abs(mHorizontalThumbCenterX - floatX) < 2) {
            return
        }
        val scrollingBy = scrollTo(
            mHorizontalDragX, floatX, scrollbarRange,
            mRecyclerView!!.computeHorizontalScrollRange(),
            mRecyclerView!!.computeHorizontalScrollOffset(), mRecyclerViewWidth
        )
        if (scrollingBy != 0) {
            mRecyclerView!!.scrollBy(scrollingBy, 0)
        }
        mHorizontalDragX = floatX
    }

    private fun scrollTo(
        oldDragPos: Float,
        newDragPos: Float,
        scrollbarRange: IntArray,
        scrollRange: Int,
        scrollOffset: Int,
        viewLength: Int
    ): Int {
        val scrollbarLength = scrollbarRange[1] - scrollbarRange[0]
        if (scrollbarLength == 0) {
            return 0
        }

        var newPosi = newDragPos
        if (newPosi < scrollbarRange[0]) {
            newPosi = scrollbarRange[0].toFloat()
        } else if (newPosi > scrollbarRange[1]) {
            newPosi = scrollbarRange[1].toFloat()
        }
        val percentage = (newPosi - scrollbarRange[0]) / scrollbarLength.toFloat()
        val absOffSet = ((scrollRange - viewLength) * percentage).toInt()
        val scrollerBy = absOffSet - scrollOffset
        return if (absOffSet in 0 until scrollRange) {
            scrollerBy
        } else if (scrollOffset < absOffSet) {
            //check list is scroller to top or bottom,if no scroller
            absOffSet - scrollOffset
        } else {
            0
        }
    }

    @VisibleForTesting
    fun isPointInsideVerticalThumb(x: Float, y: Float): Boolean {
        return ((if (isLayoutRTL) {
            x <= mVerticalThumbWidth
        } else {
            x >= mRecyclerViewWidth - mVerticalThumbWidth
        })
                && (y >= mVerticalThumbCenterY - mVerticalThumbHeight / 2) && (y <= mVerticalThumbCenterY + mVerticalThumbHeight / 2))
    }

    @VisibleForTesting
    fun isPointInsideHorizontalThumb(x: Float, y: Float): Boolean {
        return ((y >= mRecyclerViewHeight - mHorizontalThumbHeight)
                && (x >= mHorizontalThumbCenterX - mHorizontalThumbWidth / 2) && (x <= mHorizontalThumbCenterX + mHorizontalThumbWidth / 2))
    }

    fun setMargin(start: Int, top: Int, end: Int, bottom: Int) {
        mMarginStart = start
        mMarginTop = top
        mMarginEnd = end
        mMarginBottom = bottom
    }

    private inner class AnimatorListener : AnimatorListenerAdapter() {
        private var mCanceled = false
        override fun onAnimationEnd(animation: Animator) {
            // Cancel is always followed by a new directive, so don't update state.
            if (mCanceled) {
                mCanceled = false
                return
            }
            if (mShowHideAnimator.animatedValue as Float == 0f) {
                mAnimationState = ANIMATION_STATE_OUT
                setState(STATE_HIDDEN)
            } else {
                mAnimationState = ANIMATION_STATE_IN
                requestRedraw()
            }
        }

        override fun onAnimationCancel(animation: Animator) {
            mCanceled = true
        }
    }

    private inner class AnimatorUpdater : AnimatorUpdateListener {
        override fun onAnimationUpdate(valueAnimator: ValueAnimator) {
            val alpha = (SCROLLBAR_FULL_OPAQUE * valueAnimator.animatedValue as Float).toInt()
            mVerticalThumbDrawable.alpha = alpha
            mVerticalTrackDrawable.alpha = alpha
            requestRedraw()
        }
    }
}