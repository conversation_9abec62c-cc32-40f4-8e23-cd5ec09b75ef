[versions]
env = "1.0.0"
envVersion = "0.0.7"
kotlinGradlePlugin = "1.9.22"
kotlinSerialization = "1.9.22"
kotlinCoroutines = "1.8.0"
oapmPerf = "3.0.7.2"
obuildVersion = "1.7.0"
androidGradlePlugin = "8.2.2"
androidxCore = "1.12.0"
androidxCompat = "1.6.1"
androidxConstraintLayout = "2.1.4"
androidXActivity = "1.7.2"
androidxFragment = "1.6.1"
androidxLifecycle = "2.6.2"
androidxRoom = "2.5.2"
androidxAnnotation = "1.6.0"
gson = "2.10.1"
material = "1.12.0"
olint = "1.2.6"
okio = "3.7.0"
statistics = "3.0.13"
sau = "3.0.3"
couiVersion = "16.0.2"
cloudConfigVersion = "2.4.3.0"
nearxTrackVersion = "1.1.3.1"
nearxUtilsVersion = "1.0.8.security"
apiAdapterOplus = "13.0.0"
oplusAddonSdk = "15.0.36.45"
commonsIO = "2.15.1"
appFeatureHelper = "1.1.1"
formatfile = "2.1.8"
moduleInfoCollector = "1.0.9"
koin = "3.4.3"
koinCoroutines = "3.4.1"
stdid = "1.0.6"
#tika版本需和formatfile SDK中的版本保持一致
tika = "2.6.0"
retrofit2 = "2.9.0"
commons_codec = "1.16.0"
okhttp3 = "4.12.0"
rxjava = "3.1.8"
rxandroid = "3.0.2"
aiunit = "2.0.1"
aiunit_sub = "2.0.0-betaded10a4-SNAPSHOT"
aiunitOintent = "1.1.4"
aiunit_rewrite = "1.4.5"
aiunit_graphic_abstract = "1.4.5"
aiunit_plugin = "1.4.3-alpha02e4b7d-SNAPSHOT"
aiunit_llm = "1.4.5"
plugin_translate = "1.4.3-alpha5c2056a-SNAPSHOT"
graphicreconstruction = "15.0.0-alphaec62797-SNAPSHOT"
dmpConnect = "2.3.6-beta3ccc979-SNAPSHOT"
dmpAiAsk = "1.1.8"
ucAccountSDK = "2.5.1"
localbroadcastmanager = "1.0.0"
nlp = "1.4.3"
fileManagerSimulateClick = "16.0.0-alphac1e2c7c-SNAPSHOT"
docThumbnailExporter = "15.8.1-alphaa2e51b4-SNAPSHOT"
ai_unified_summary = "0.0.8-SNAPSHOT"
ai_unit_convert = "1.0.1-alphaae9ec97-SNAPSHOT"
aikit_sdk = "15.2.0-alphad5afd6f-SNAPSHOT"
commonmark = "0.24.0"

#压缩相关的3个依赖
apacheCommonsCompress = "1.26.1"
zip4j = "2.11.2"
javaUnrar = "1.0.0"

oliveViewplayer = "1.0.0"
oliveDecoder = "1.0.0"

fileManagerDragDrop = "15.2.2-alphafacae09-SNAPSHOT"
fileManagerThumbnail = "15.9.6-alpha38f04c8-SNAPSHOT"
glide = "4.16.0"

#搬家依赖
backup_sdk = "2.0.2"

# For unit test:
jacoco = "0.8.8"
junit = "4.13.2"
mockito = "3.11.2"
mockk = "1.12.0"
kotlinTestJunit = "1.8.22"
robolectric = "4.10"
powermock = "2.0.2"
googleTruth = "0.44"
hamcrest = "1.3"
mockwebserver = "4.7.2"
androidxArchTest = "2.1.0"
androidxEspresso = "3.5.1"
androidxExtJunit = "1.1.5"
adnroidxExtJunitKtx = "1.1.1"
androidxTestKtx = "1.3.0"
androidxTestRules = "1.4.0"
otestPlatform = "1.4.1"
otestCoverage = "2.0.6"
coverageInstPluginVersion = "1.5.7"

# For Yozosoft package AAR repo
openany = "15.2.0-alpha987d3e6-SNAPSHOT"
wpsSdk = "1.1.1"
ipeSdk = "2.0.2beta1"
forecast = "3.2.0-release"
cloudDrive = "1.0.9"
cloudBase = "1.0.2"

#feedback
openidVersion = "1.0.8"
feedbackSdkCdpVersion = "15.0.4"
feedbackSdkEnvVersion = "1.0.1"

#HLog
hlog = "*******"
fastJson = "2.0.28"
[libraries]
android-gradle-plugin = { module = "com.android.tools.build:gradle", version.ref = "androidGradlePlugin" }
heytap-env = { module = "com.nearx.test:env", version.ref = "env" }
kotlin-gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlinGradlePlugin" }
kotlin-serialization-plugin = { module = "org.jetbrains.kotlin:kotlin-serialization", version.ref = "kotlinSerialization" }
oapm-perf-plugin = { module = "com.oppo.test:oapm-perf", version.ref = "oapmPerf" }
oplus-build-plugin = { module = "com.inno.buildplugin:build-plugin", version.ref = "obuildVersion" }
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "androidxCore" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "androidxCompat" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "androidxConstraintLayout" }
androidx-activity-ktx = { module = "androidx.activity:activity-ktx", version.ref = "androidXActivity" }
androidx-fragment-ktx = { module = "androidx.fragment:fragment-ktx", version.ref = "androidxFragment" }
androidx-lifecycle-service = { module = "androidx.lifecycle:lifecycle-service", version.ref = "androidxLifecycle" }
androidx-lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "androidxLifecycle" }
androidx-lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "androidxLifecycle" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "androidxRoom" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "androidxRoom" }
androidx-annotation = { module = "androidx.annotation:annotation", version.ref = "androidxAnnotation" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
koin-core = { module = "io.insert-koin:koin-core", version.ref = "koin" }
koin-core-coroutines = { module = "io.insert-koin:koin-core-coroutines", version.ref = "koinCoroutines" }
koin-android = { module = "io.insert-koin:koin-android", version.ref = "koin" }
material = { module = "com.google.android.material:material", version.ref = "material" }
commons-io = { module = "commons-io:commons-io", version.ref = "commonsIO" }
coui-appcompat-component = { module = "com.oplus.appcompat:component", version.ref = "couiVersion" }
coui-appcompat-panel = { module = "com.oplus.appcompat:panel", version.ref = "couiVersion" }
coui-appcompat-responsiveui = { module = "com.oplus.appcompat:responsiveui", version.ref = "couiVersion" }
coui-appcompat-nearx = { module = "com.oplus.appcompat:nearx", version.ref = "couiVersion" }
coui-appcompat-core = { module = "com.oplus.appcompat:core", version.ref = "couiVersion" }
coui-appcompat-toolbar = { module = "com.oplus.appcompat:toolbar", version.ref = "couiVersion" }
coui-appcompat-dialog = { module = "com.oplus.appcompat:dialog", version.ref = "couiVersion" }
coui-appcompat-recyclerview = { module = "com.oplus.appcompat:recyclerview", version.ref = "couiVersion" }
coui-appcompat-seekbar = { module = "com.oplus.appcompat:seekbar", version.ref = "couiVersion" }
coui-appcompat-poplist = { module = "com.oplus.appcompat:poplist", version.ref = "couiVersion" }
coui-appcompat-sidenavigationbar = { module = "com.oplus.appcompat:sidenavigationbar", version.ref = "couiVersion" }
coui-appcompat-card = { module = "com.oplus.appcompat:card", version.ref = "couiVersion" }
coui-appcompat-preference = { module = "com.oplus.appcompat:preference", version.ref = "couiVersion" }
coui-appcompat-rotateview = { module = "com.oplus.appcompat:rotateview", version.ref = "couiVersion" }
coui-appcompat-bottomnavigation = { module = "com.oplus.appcompat:bottomnavigation", version.ref = "couiVersion" }
coui-appcompat-scrollbar = { module = "com.oplus.appcompat:scrollbar", version.ref = "couiVersion" }
coui-appcompat-progressbar = { module = "com.oplus.appcompat:progressbar", version.ref = "couiVersion" }
coui-appcompat-chip = { module = "com.oplus.appcompat:chip", version.ref = "couiVersion" }
coui-appcompat-floatingactionbutton = { module = "com.oplus.appcompat:floatingactionbutton", version.ref = "couiVersion" }
coui-appcompat-tablayout = { module = "com.oplus.appcompat:tablayout", version.ref = "couiVersion" }
coui-appcompat-snackbar = { module = "com.oplus.appcompat:snackbar", version.ref = "couiVersion" }
coui-appcompat-clickablespan = { module = "com.oplus.appcompat:clickablespan", version.ref = "couiVersion" }
coui-appcompat-tips = { module = "com.oplus.appcompat:tips", version.ref = "couiVersion" }
coui-appcompat-button = { module = "com.oplus.appcompat:button", version.ref = "couiVersion" }
coui-appcompat-grid = { module = "com.oplus.appcompat:grid", version.ref = "couiVersion" }
coui-appcompat-viewpager = { module = "com.oplus.appcompat:viewpager", version.ref = "couiVersion" }
coui-appcompat-scroll = { module = "com.oplus.appcompat:scroll", version.ref = "couiVersion" }
coui-appcompat-scrollview = { module = "com.oplus.appcompat:scrollview", version.ref = "couiVersion" }
coui-appcompat-reddot = { module = "com.oplus.appcompat:reddot", version.ref = "couiVersion" }
coui-appcompat-listview = { module = "com.oplus.appcompat:listview", version.ref = "couiVersion" }
coui-appcompat-input = { module = "com.oplus.appcompat:input", version.ref = "couiVersion" }
coui-appcompat-statement = { module = "com.oplus.appcompat:statement", version.ref = "couiVersion" }
coui-appcompat-privacypolicy = { module = "com.oplus.appcompat:privacypolicy", version.ref = "couiVersion" }
coui-appcompat-searchhistory = { module = "com.oplus.appcompat:searchhistory", version.ref = "couiVersion" }
coui-appcompat-calendar = { module = "com.oplus.appcompat:calendar", version.ref = "couiVersion" }
coui-appcompat-lockview = { module = "com.oplus.appcompat:lockview", version.ref = "couiVersion" }
coui-appcompat-picker = { module = "com.oplus.appcompat:picker", version.ref = "couiVersion" }
coui-appcompat-expandable = { module = "com.oplus.appcompat:expandable", version.ref = "couiVersion" }
coui-appcompat-touchsearchview = { module = "com.oplus.appcompat:touchsearchview", version.ref = "couiVersion" }
coui-appcompat-slideview = { module = "com.oplus.appcompat:slideview", version.ref = "couiVersion" }
coui-appcompat-segmentbutton = { module = "com.oplus.appcompat:segmentbutton", version.ref = "couiVersion" }
oplus-coreapp-appfeature = { module = "com.oplus.coreapp.appfeature:AppFeatureHelper", version.ref = "appFeatureHelper" }
oplus-support-api-adapter-oplus = { module = "com.oplus.support:api-adapter-oplus", version.ref = "apiAdapterOplus" }
oplus-sdk-addon = { module = "com.oplus.sdk:addon", version.ref = "oplusAddonSdk" }
oplus-nearx-formatfile = { module = "com.oplus.nearx:formatfile", version.ref = "formatfile" }
oplus-statistics-track = { module = "com.oplus.statistics:track", version.ref = "statistics" }
oplus-sauaar-coui-sauaar = { module = "com.oplus.sauaar:coui-sauaar", version.ref = "sau" }
oplus-assistantscreen-plugin-mic-plugin = { module = "com.oplus.assistantscreen.plugin:mic", version.ref = "moduleInfoCollector" }
oplus-assistantscreen-plugin-mic-api = { module = "com.oplus.assistantscreen.plugin:mic-api", version.ref = "moduleInfoCollector" }
oplus-assistantscreen-plugin-mic-interfaces = { module = "com.oplus.assistantscreen.plugin:mic-interfaces", version.ref = "moduleInfoCollector" }
oplus-assistantscreen-plugin-mic-processor = { module = "com.oplus.assistantscreen.plugin:mic-processor", version.ref = "moduleInfoCollector" }
oplus-filemanager-simulateClickEngine = { module = "com.oplus.filemanager:simulate_click_engine", version.ref = "fileManagerSimulateClick" }
heytap-nearx-cloudconfig = { module = "com.heytap.nearx:cloudconfig", version.ref = "cloudConfigVersion" }
heytap-nearx-cloudconfigEnv = { module = "com.heytap.nearx:cloudconfig-env", version.ref = "cloudConfigVersion" }
heytap-nearx-cloudconfigEnvOverSea = { module = "com.heytap.nearx:cloudconfig-env-oversea", version.ref = "cloudConfigVersion" }
heytap-nearx-cloudconfigArea = { module = "com.heytap.nearx:cloudconfig-area", version.ref = "cloudConfigVersion" }
olint = { module = "com.sectools.check:olint", version.ref = "olint" }
heytap-nearx-track = { module = "com.heytap.nearx:track", version.ref = "nearxTrackVersion" }
heytap-nearx-utils = { module = "com.heytap.nearx:utils", version.ref = "nearxUtilsVersion" }
okio = { module = "com.squareup.okio:okio", version.ref = "okio" }
stdid = { module = "com.oplus.stdid.sdk:sdk", version.ref = "stdid" }
heytap-test-env = { module = "com.heytap.test:env", version.ref = "envVersion" }
tika = { module = "org.apache.tika:tika-core", version.ref = "tika" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinCoroutines" }
retrofit2-retrofit2 = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit2" }
retrofit2-converter = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofit2" }
retrofit2-adapter = { module = "com.squareup.retrofit2:adapter-rxjava3", version.ref = "retrofit2" }
commons-codec = { module = "commons-codec:commons-codec", version.ref = "commons_codec" }
okhttp3-okhttp3 = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp3" }
rxjava3-rxjava = { module = "io.reactivex.rxjava3:rxjava", version.ref = "rxjava" }
rxjava3-rxandroid = { module = "io.reactivex.rxjava3:rxandroid", version.ref = "rxandroid" }
oplus-aiunit-open-toolkits = { module = "com.oplus.aiunit.open:toolkits", version.ref = "aiunit" }
oplus-aiunit-open-download = { module = "com.oplus.aiunit.open:download", version.ref = "aiunit" }
oplus-aiunit-open-core = { module = "com.oplus.aiunit.open:core", version.ref = "aiunit" }
oplus-aiunit-ointent-api = { module = "com.oplus.aiunit.scene.ointent:api", version.ref = "aiunitOintent" }
oplus-aiunit-ointent-regex = { module = "com.oplus.aiunit.scene.ointent:regex", version.ref = "aiunitOintent" }
oplus-aiunit-ointent-aiunit = { module = "com.oplus.aiunit.scene.ointent:aiunit", version.ref = "aiunitOintent" }
oplus-aiunit-ointent-token = { module = "com.oplus.aiunit.scene.ointent:token", version.ref = "aiunitOintent" }
oplus-aiunit-ointent-detect = { module = "com.oplus.aiunit.scene.ointent:detect", version.ref = "aiunitOintent" }
oplus-aiunit-ointent-online = { module = "com.oplus.aiunit.scene.ointent:online", version.ref = "aiunitOintent" }
oplus-aiunit-open-aisub = { module = "com.oplus.aiunit.open:aisubsystem", version.ref = "aiunit_sub" }
oplus-aiunit-open-nlp = { module = "com.oplus.aiunit.open:nlp", version.ref = "nlp" }
oplus-graphicreconstruction = { module = "com.oplus.graphicreconstruction:graphicreconstruction", version.ref = "graphicreconstruction" }
heytap-account-uc = { module = "com.heytap.accountsdk:UCAccountSDK_Base_heytap", version.ref = "ucAccountSDK" }
androidx-localbroadcast = { module = "androidx.localbroadcastmanager:localbroadcastmanager", version.ref = "localbroadcastmanager" }
oplus_aiunit-open-graphic-abstract = { module = "com.oplus.aiunit.open:graphic-abstract", version.ref = "aiunit_llm" }
oplus_aiunit-open-doc-rewrite = { module = "com.oplus.aiunit.open:doc-rewrite", version.ref = "aiunit_llm" }
oplus_aiunit-open-doc-summary = { module = "com.oplus.aiunit.open:doc-summary", version.ref = "aiunit_plugin" }
oplus_aiunit-open-doc-translate = { module = "com.oplus.aiunit.open:doc-translate", version.ref = "plugin_translate" }
oplus_ai-unified_summary = { module = "com.oplus.ai:unified_summary", version.ref = "ai_unified_summary" }
oplus_aiunit_convert = { module = "com.oplus.aiunit.open:aidocument", version.ref = "ai_unit_convert" }
oplus_aikit_sdk = { module = "com.oplus.aikit:sdk", version.ref = "aikit_sdk" }

heytap-cloud-drive = { module = "com.heytap.cloud:clouddrive", version.ref = "cloudDrive" }
heytap-cloud-base = { module = "com.heytap.cloud:base", version.ref = "cloudBase" }
org-apache-commons-commonsCompress = { module = "org.apache.commons:commons-compress", version.ref = "apacheCommonsCompress" }
net-lingala-zip4j-zip4j = { module = "net.lingala.zip4j:zip4j", version.ref = "zip4j" }
de-innosystec-unrar-javaUnrar = { module = "de.innosystec.unrar:java-unrar", version.ref = "javaUnrar" }
oplus-gallery-olive-viewplayer = { module = "com.oplus.gallery:olive-viewplayer", version.ref = "oliveViewplayer" }
oplus-gallery-olive-decoder = { module = "com.oplus.gallery:olive-decoder", version.ref = "oliveDecoder" }
dragdrop = { module = "com.oplus.filemanager:dragdrop", version.ref = "fileManagerDragDrop" }
oplus-filemanager-thumbnail = { module = "com.oplus.filemanager:thumbnail", version.ref = "fileManagerThumbnail" }
bumptech-glide-compiler = { module = "com.github.bumptech.glide:compiler", version.ref = "glide" }
bumptech-glide-base = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
oplus-dmp-sdk-domestic = { module = "com.oplus.dmp.sdk:connect-domestic", version.ref = "dmpConnect" }
oplus-dmp-aiask-ui = { module = "com.oplus.dmp.sdk:aiask-ui", version.ref = "dmpAiAsk" }
oplus-dmp-aiask-base = { module = "com.oplus.dmp.sdk:aiask", version.ref = "dmpAiAsk" }
oplus-doc-thumbnail-exporter = { module = "com.oplus.documentsreader:thumbnail-exporter", version.ref = "docThumbnailExporter" }

# commonmark, the library for markdown parsing and rendering
org-commonmark-commonmark = { module = "org.commonmark:commonmark", version.ref = "commonmark" }
org-commonmark-commonmark-ext-gfm-strikethrough = { module = "org.commonmark:commonmark-ext-gfm-strikethrough", version.ref = "commonmark" }
org-commonmark-commonmark-ext-gfm-tables = { module = "org.commonmark:commonmark-ext-gfm-tables", version.ref = "commonmark" }

# For unit & android test:
junit = { module = "junit:junit", version.ref = "junit" }
mockito-core = { module = "org.mockito:mockito-core", version.ref = "mockito" }
mockito-inline = { module = "org.mockito:mockito-inline", version.ref = "mockito" }
mockk = { module = "io.mockk:mockk", version.ref = "mockk" }
kotlin-test-junit = { module = "org.jetbrains.kotlin:kotlin-test-junit", version.ref = "kotlinTestJunit" }
kotlinx-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "kotlinCoroutines" }
robolectric-base = { module = "org.robolectric:robolectric", version.ref = "robolectric" }
robolectric-annotations = { module = "org.robolectric:annotations", version.ref = "robolectric" }
powermock-api-mockito2 = { module = "org.powermock:powermock-api-mockito2", version.ref = "powermock" }
powermock-module-junit4 = { module = "org.powermock:powermock-module-junit4", version.ref = "powermock" }
powermock-core = { module = "org.powermock:powermock-core", version.ref = "powermock" }
powermock-module-junit4Rule = { module = "org.powermock:powermock-module-junit4-rule", version.ref = "powermock" }
androidx-test-junit = { module = "androidx.test.ext:junit", version.ref = "androidxExtJunit" }
google-truth = { module = "com.google.truth:truth", version.ref = "googleTruth" }
hamcrest = { module = "org.hamcrest:hamcrest-all", version.ref = "hamcrest" }
okhttp3-mockwebserver = { module = "com.squareup.okhttp3:mockwebserver", version.ref = "mockwebserver" }
androidx-arch-core-testing = { module = "androidx.arch.core:core-testing", version.ref = "androidxArchTest" }
androidx-espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "androidxEspresso" }
androidx-espresso-contrib = { module = "androidx.test.espresso:espresso-contrib", version.ref = "androidxEspresso" }
androidx-espresso-intents = { module = "androidx.test.espresso:espresso-intents", version.ref = "androidxEspresso" }
androidx-espresso-web = { module = "androidx.test.espresso:espresso-web", version.ref = "androidxEspresso" }
androidx-espresso-idling-concurrent = { module = "androidx.test.espresso.idling:idling-concurrent", version.ref = "androidxEspresso" }
androidx-espresso-idling-resource = { module = "androidx.test.espresso:espresso-idling-resource", version.ref = "androidxEspresso" }
androidx-test-junitKtx = { module = "androidx.test.ext:junit-ktx", version.ref = "adnroidxExtJunitKtx" }
androidx-test-ktx = { module = "androidx.test:core-ktx", version.ref = "androidxTestKtx" }
androidx-test-rules = { module = "androidx.test:rules", version.ref = "androidxTestRules" }
otestPlatform = { module = "otestPlatform:testLib", version.ref = "otestPlatform" }
otestCoverage = { module = "otestPlatform:coverageLibDisk", version.ref = "otestCoverage" }
autotest-opasm = { module = "com.autotest.opasm:CoverageInstPlugin", version.ref = "coverageInstPluginVersion" }

#backup and restore
com_oplus_backup = { module = "com.oplus.backup:backup-sdk", version.ref = "backup_sdk" }
#HLog
com_heytap_log = { module = "com.oplus:log", version.ref = "hlog" }
com_heytap_log_domin_cn = { module = "com.oplus:log-domain-cn", version.ref = "hlog" }
com_alibaba_fastjson = { module = "com.alibaba:fastjson", version.ref = "fastJson" }

# For Yozosoft package AAR repo
andes-oplus-openany = { module = "andes.oplus.documentsreader:merged-to-yozosoft", version.ref = "openany" }
wps-open = { module = "com.wps.sdk:open", version.ref = "wpsSdk" }
oplus-ipemanager = { module = "com.oplus.ipemanager:ipe_sdk", version.ref = "ipeSdk" }
oplusos-vfxsdk-forecast = { module = "com.oplusos.vfxsdk:forecast", version.ref = "forecast" }

#feedback
heytap-openid-sdk-api = { module = "com.heytap.openid.sdk:sdk", version.ref = "openidVersion" }
customer-feedback-sdk-cdp-api = { module = "com.customer.feedback.sdk:feedback-cdp", version.ref = "feedbackSdkCdpVersion" }
customer-feedback-sdk-env-api = { module = "com.customer.feedback.sdk:feedback-env-domestic", version.ref = "feedbackSdkEnvVersion" }
