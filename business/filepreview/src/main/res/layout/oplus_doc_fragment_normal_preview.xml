<?xml version="1.0" encoding="utf-8"?><!--   通用内容展示区域-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fragment_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingStart="@dimen/dimen_16dp"
    android:paddingEnd="@dimen/dimen_16dp">

    <LinearLayout
        android:id="@+id/card_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="?attr/couiColorBackgroundElevatedWithCard"
        android:minHeight="160dp"
        android:orientation="vertical">

        <!-- 通用顶部文件信息-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/normal_top_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_20dp"
            android:paddingBottom="@dimen/dimen_20dp">

            <ImageView
                android:id="@+id/normal_file_icon"
                android:layout_width="@dimen/preview_header_image_width"
                android:layout_height="@dimen/preview_header_image_height"
                android:layout_marginStart="@dimen/preview_header_image_start_margin"
                android:src="@drawable/ic_file_other_icon"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/preview_header_info_margin_start"
                android:layout_marginEnd="@dimen/dimen_20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/normal_file_icon"
                app:layout_constraintTop_toTopOf="parent">

                <andes.oplus.documentsreader.core.common.TextViewSnippet
                    android:id="@+id/normal_info_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="2"
                    android:fontFamily="sans-serif-medium"
                    android:textFontWeight="500"
                    android:textColor="@color/coui_color_primary_neutral"
                    android:textSize="@dimen/file_list_item_title_text_size" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/normal_info_detail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/normal_info_title"
                    android:layout_marginTop="@dimen/preview_header_detail_margin_top"
                    android:ellipsize="marquee"
                    android:gravity="center_vertical"
                    android:fontFamily="sans-serif-regular"
                    android:maxWidth="@dimen/file_list_item_info_selected_width_new_new"
                    android:singleLine="true"
                    android:textFontWeight="400"
                    android:textColor="@color/coui_color_secondary_neutral"
                    android:textSize="@dimen/preview_header_detail_text_size" />

            </RelativeLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/divider_line"
            android:layout_width="match_parent"
            android:layout_height="0.33dp"
            android:forceDarkAllowed="false"
            android:background="?attr/couiColorDivider"/>
        <!--通用底部按钮-->
        <LinearLayout
            android:id="@+id/normal_bottom_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_56dp"
            android:layout_gravity="bottom"
            android:forceDarkAllowed="false"
            android:background="@color/oplus_bottom_bar_bg_state"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/normal_bottom_button_icon"
                android:layout_width="@dimen/dimen_20dp"
                android:layout_height="@dimen/dimen_20dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_file_open" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/normal_bottom_button_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:fontFamily="sans-serif-medium"
                android:text="@string/open"
                android:textFontWeight="500"
                android:textColor="@color/coui_color_primary_neutral"
                android:textSize="@dimen/font_size_14" />
        </LinearLayout>
    </LinearLayout>
</FrameLayout>
