/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ChunkNameUtil.java
 * * Description: ChunkNameUtil.java
 * *              util to generation opus file name
 * * Version: 1.0
 * * Date : 2019/9/25
 * * Author: huangyuanwang
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * huangyuanwang  2019/9/25      1.0    build this module
 ****************************************************************/

package com.soundrecorder.base.utils;

import android.text.TextUtils;

import java.util.Arrays;

public class ChunkNameUtil {

    private static final String TAG = "ChunkNameUtil";

    private static final String PREFIX_OPUS = "vad";
    private static final String UNDER_LINE = "_";

    public static long parseStartTimeFromChunkName(String chunkName) {
        if (TextUtils.isEmpty(chunkName)) {
            DebugUtil.i(TAG, "parseStartTimeFromChunkName, input chunkName is empty");
            return -1;
        }
        chunkName = chunkName.toLowerCase();
        if ((!chunkName.contains(PREFIX_OPUS)) || (!chunkName.contains(UNDER_LINE))) {
            DebugUtil.i(TAG, "parseStartTimeFromChunkName, illegal format for input chunkName: " + chunkName);
            return -1;
        }
        String[] splitString = chunkName.split(UNDER_LINE);
        DebugUtil.i(TAG, "parseStartTimeFromChunkName: " + Arrays.asList(splitString));
        long result = -1;
        if (splitString.length == 5) {
            try {
                result = Long.parseLong(splitString[2]);
            } catch (NumberFormatException e) {
                DebugUtil.e(TAG, "parseStartTimeFromChunkName, not number ", e);
            }
        }
        return result;
    }

    public static long parseEndTimeFromChunkName(String chunkName) {
        if (TextUtils.isEmpty(chunkName)) {
            DebugUtil.i(TAG, "parseEndTimeFromChunkName, input chunkName is empty");
            return -1;
        }
        chunkName = chunkName.toLowerCase();
        if ((!chunkName.contains(PREFIX_OPUS)) || (!chunkName.contains(UNDER_LINE))) {
            DebugUtil.i(TAG, "parseEndTimeFromChunkName, illegal format for input chunkName: " + chunkName);
            return -1;
        }
        String[] splitString = chunkName.split(UNDER_LINE);
        DebugUtil.i(TAG, "parseEndTimeFromChunkName: " + Arrays.asList(splitString));
        long result = -1;
        if (splitString.length == 5) {
            try {
                result = Long.parseLong(splitString[3]);
            } catch (NumberFormatException e) {
                DebugUtil.e(TAG, "parseEndTimeFromChunkName, not number ", e);
            }
        }
        return result;
    }
}
