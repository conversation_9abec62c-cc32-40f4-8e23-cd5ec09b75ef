/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - MainGlideConfigModule.kt
 * Description:
 *     The AppGlideModule for local aar wrapper demo app.
 *
 * Version: 1.0
 * Date: 2024-08-02
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-08-02   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.glide

import andes.oplus.documentsreader.exposetoyozo.OplusGlideLibraryApi
import android.content.Context
import com.bumptech.glide.Glide
import com.bumptech.glide.GlideBuilder
import com.bumptech.glide.Registry
import com.bumptech.glide.annotation.GlideModule
import com.bumptech.glide.load.engine.bitmap_recycle.LruBitmapPool
import com.bumptech.glide.load.engine.cache.InternalCacheDiskCacheFactory
import com.bumptech.glide.load.engine.cache.LruResourceCache
import com.bumptech.glide.load.engine.cache.MemorySizeCalculator
import com.bumptech.glide.manager.ConnectivityMonitor
import com.bumptech.glide.manager.ConnectivityMonitor.ConnectivityListener
import com.bumptech.glide.manager.ConnectivityMonitorFactory
import com.bumptech.glide.module.AppGlideModule

@GlideModule
class MainGlideConfigModule : AppGlideModule() {

    override fun applyOptions(context: Context, builder: GlideBuilder) {
        builder.setDiskCache(InternalCacheDiskCacheFactory(context, MAX_DISK_CACHE_SIZE))
        val calculator = MemorySizeCalculator.Builder(context)
            .setMemoryCacheScreens(1f)
            .build()
        builder.setMemoryCache(LruResourceCache((RATIO_CALCULATOR_SIZE * calculator.memoryCacheSize).toLong()))
        builder.setBitmapPool(LruBitmapPool((RATIO_CALCULATOR_SIZE * calculator.bitmapPoolSize).toLong()))
        builder.setConnectivityMonitorFactory(EmptyConnectivityMonitorFactory())
    }

    override fun registerComponents(context: Context, glide: Glide, registry: Registry) {
        super.registerComponents(context, glide, registry)
        OplusGlideLibraryApi.registerComponents(context, glide, registry)
    }

    private companion object {
        private const val MAX_DISK_CACHE_SIZE = 200 * 1024 * 1024L
        private const val RATIO_CALCULATOR_SIZE = 0.7
    }

    private class EmptyConnectivityMonitorFactory : ConnectivityMonitorFactory {
        override fun build(context: Context, listener: ConnectivityListener): ConnectivityMonitor {
            return EmptyConnectivityMonitor()
        }
    }

    private class EmptyConnectivityMonitor : ConnectivityMonitor {
        override fun onStart() {
            // Do nothing.
        }

        override fun onStop() {
            // Do nothing.
        }

        override fun onDestroy() {
            // Do nothing.
        }
    }
}