<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="BaseTheme" parent="@style/Theme.COUI.Main">
        <item name="android:textAlignment">gravity</item>
        <item name="android:textDirection">locale</item>
        <item name="viewInflaterClass">com.coui.appcompat.theme.COUIComponentsViewInflater</item>
        <item name="android:forceDarkAllowed">true</item>
        <item name="android:isLightTheme">true</item>
        <item name="enableFollowSystemForceDarkRank">true</item>
        <item name="android:fastScrollThumbDrawable">@drawable/icon_fast_scroll</item>
        <item name="android:fastScrollTrackDrawable">@null</item>
        <item name="android:windowBackground">?attr/couiColorBackgroundWithCard</item>
    </style>

    <style name="AppBaseTheme" parent="BaseTheme">
        <!--
            Theme customizations available in newer API levels can go in
            res/values-vXX/styles.xml, while customizations related to
            backward-compatibility can go here.
        -->
    </style>

    <!-- Application theme. -->
    <style name="AppTheme" parent="AppBaseTheme">
        <item name="preferenceTheme">@style/PreferenceThemeOverlay.COUITheme.WithToolBar</item>
        <!-- All customizations that are NOT specific to a particular API-level can go here. -->
    </style>

    <style name="AppNoTitleTheme" parent="AppTheme">
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="COUICardLockScreenPwdInputStyleEdit">@style/Widget.COUI.LockScreeenPwdInputEdit</item>
        <item name="COUICardLockScreenPwdInputStyleEditDesktop">@style/Widget.COUI.LockScreeenPwdInputEdit.Desktop</item>
    </style>

    <style name="AppNoTitleThemeNoActivityAnimator" parent="AppNoTitleTheme">
        <item name="android:windowAnimationStyle">@null</item>
    </style>

    <!-- 支持卡片样式的Preference列表theme, 非卡片式列表，请勿使用 -->
    <style name="AppNoTitleTheme.PreferenceFragment">
        <!-- 必须设置 -->
        <item name="android:windowBackground">?attr/couiColorBackgroundWithCard</item>
        <item name="android:navigationBarColor">?attr/couiColorBackgroundWithCard</item>
        <!-- 必须设置 -->
    </style>

</resources>