/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date: 2020/8/3
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package andes.oplus.documentsreader.search.manager.filter

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.search.bean.SearchRecycleWrapper
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

class FilterTimeDataHelper : FilterDataHelper {

    companion object {
        const val TAG = "FilterTimeDataHelper"
    }

    override fun filterData(filterItem: FilterItem, dataList: MutableList<BaseFileBean>): MutableList<BaseFileBean> {

        fun isInTimeDuration(fileBean: BaseFileBean, start: Long, end: Long): Boolean {
            val time = if (fileBean is SearchRecycleWrapper) {
                fileBean.recycleDate
            } else {
                fileBean.mDateModified
            }
            return time in start until end
        }

        val calendar = Calendar.getInstance()
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59)
        // "/ 1000 * 1000" used to make the part of millisecond is 000
        val todayMaxMills = calendar.timeInMillis / 1000 * 1000 + 1000
        val oneDayMills: Long = 24 * 60 * 60 * 1000

        return when (filterItem.id) {
            FilterConstants.FILTER_TIME_TODAY -> {
                val start = todayMaxMills - oneDayMills
                printTimeDuration(start, todayMaxMills)
                dataList.filter {
                    isInTimeDuration(it, start, todayMaxMills)
                } as MutableList
            }
            FilterConstants.FILTER_TIME_3_DAY -> {
                val start = todayMaxMills - 3 * oneDayMills
                printTimeDuration(start, todayMaxMills)
                dataList.filter {
                    isInTimeDuration(it, start, todayMaxMills)
                } as MutableList
            }
            FilterConstants.FILTER_TIME_7_DAY -> {
                val start = todayMaxMills - 7 * oneDayMills
                printTimeDuration(start, todayMaxMills)
                dataList.filter {
                    isInTimeDuration(it, start, todayMaxMills)
                } as MutableList
            }
            FilterConstants.FILTER_TIME_30_DAY -> {
                val start = todayMaxMills - 30 * oneDayMills
                printTimeDuration(start, todayMaxMills)
                dataList.filter {
                    isInTimeDuration(it, start, todayMaxMills)
                } as MutableList
            }
            else -> dataList
        }
    }

    private fun printTimeDuration(start: Long, end: Long) {
        val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        Log.d("FilterTimeDataHelper", "start: ${formatter.format(Date(start))}, end: ${formatter.format(Date(end))}")
    }
}