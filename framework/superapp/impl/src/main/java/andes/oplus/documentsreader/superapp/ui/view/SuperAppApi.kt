/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: SuperAppApi.kt
 ** Description:  SuperAppApi
 ** Version: 1.0
 ** Date: 2021/5/8
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package andes.oplus.documentsreader.superapp.ui.view

import andes.oplus.documentsreader.core.common.CategoryHelper
import andes.oplus.documentsreader.core.common.Constants
import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.interfaces.IBaseVMFragment
import andes.oplus.documentsreader.core.common.parentchild.IMainViewApi
import andes.oplus.documentsreader.core.common.parentchild.MainCategoryItemsBean
import andes.oplus.documentsreader.core.common.uiconfig.UIConfigMonitor
import andes.oplus.documentsreader.core.common.uiconfig.type.IUIConfig
import andes.oplus.documentsreader.sidepreview.IPreviewListFragment
import andes.oplus.documentsreader.sidepreview.IPreviewListFragmentCreator
import andes.oplus.documentsreader.sidepreview.IPreviewOperate
import andes.oplus.documentsreader.superapp.ISuperApp
import andes.oplus.documentsreader.superapp.hepler.MainCategoryItemsBeanFactory
import andes.oplus.documentsreader.superapp.hepler.SuperAppHelper
import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.SparseArray
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.fragment.app.Fragment
import com.coui.appcompat.sidenavigation.COUISideNavigationBar

object SuperAppApi : ISuperApp {

    const val TAG = "SuperAppApi"
    private const val DIR_DEPTH_DEFAULT = 0
    private const val DIR_DEPTH_DOWNLOAD = 2
    private const val SUPER_APP_ACTIVITY = "andes.oplus.documentsreader.superapp.ui.view.SuperAppActivity"

    override fun startSuperApp(activity: Activity?, data: MainCategoryItemsBean) {
        activity?.let {
            val dirDepth = getDirDepthByItem(data)
            val intent = Intent().apply {
                putExtra(Constants.TITLE, data.name)
                putExtra(Constants.TITLE_RES_ID, data.nameResId)
                putExtra(KtConstants.P_SUPER_PATH_LIST, data.fileList)
                putExtra(KtConstants.P_PACKAGE, data.packageName)
                putExtra(KtConstants.SUPER_DIR_DEPTH, dirDepth)
                setClassName(it.packageName, SUPER_APP_ACTIVITY)
            }
            it.startActivity(intent)
        }
    }

    override fun startSuperAppFragment(activity: Activity, data: MainCategoryItemsBean) {
        val bundle = Bundle()
        val dirDepth = getDirDepthByItem(data)
        bundle.putString(Constants.TITLE, data.name)
        bundle.putInt(Constants.TITLE_RES_ID, data.nameResId)
        bundle.putStringArray(KtConstants.P_SUPER_PATH_LIST, data.fileList)
        bundle.putString(KtConstants.P_PACKAGE, data.packageName)
        bundle.putInt(KtConstants.SUPER_DIR_DEPTH, dirDepth)

        val mainViewApi = Injector.injectFactory<IMainViewApi>()
        mainViewApi?.enterNextFragment(activity, data.sideCategoryType, bundle)
    }

    override fun getDirDepthByItem(data: MainCategoryItemsBean): Int {
        val dirDepth = when (data.itemType) {
            CategoryHelper.CATEGORY_DOWNLOAD -> DIR_DEPTH_DOWNLOAD
            else -> DIR_DEPTH_DEFAULT
        }
        return dirDepth
    }

    override fun getCategoryItems(context: Context): MutableList<MainCategoryItemsBean> {
        return SuperAppHelper.getCategoryItems(context)
    }

    override fun updateSupperAppPaths(context: Context): ArrayList<String?> {
        return SuperAppHelper.updateSupperAppPaths(context)
    }

    override fun preloadSuperSharePreferences(application: Application) {
        SuperAppHelper.preloadSuperSharePreferences(application)
    }

    override fun getMainSuperInitList(): ArrayList<MainCategoryItemsBean>? {
        return SuperAppHelper.getMainSuperInitList()
    }

    override fun isIgnoredPath(
        context: Context,
        type: Int,
        path: String?,
        internalPath: String?,
        ignoredPaths: SparseArray<String?>?
    ): Boolean {
        return SuperAppHelper.isIgnoredPath(
            context,
            type,
            path,
            internalPath,
            ignoredPaths
        )
    }

    override fun isNotQQAndWeChatFile(
        path: String?,
        context: Context?,
        items: List<MainCategoryItemsBean>
    ): Boolean {
        return SuperAppHelper.isNotQQAndWeChatFile(path, context, items)
    }

    override fun checkOWorkSuperAppCondition(context: Context): Boolean {
        return andes.oplus.documentsreader.superapp.hepler.checkOWorkSuperAppCondition(context)
    }

    override fun lastRecordCount(category: Int): String {
        return MainCategoryItemsBeanFactory.LAST_RECORD_COUNT[category]
    }

    override fun lastRecordSize(category: Int): String {
        return MainCategoryItemsBeanFactory.LAST_RECORD_SIZE[category]
    }

    override fun getCategoryItemLastData(position: Int, context: Context): LongArray {
        return MainCategoryItemsBeanFactory.getCategoryItemLastData(position, context)
    }

    override fun getShowSourceFilePaths(fragment: Fragment?): Array<String>? {
        if (fragment is SuperAppParentFragment) {
            return fragment.getFilePaths()
        } else if (fragment is IPreviewOperate) {
            return (fragment.getCurrentPreviewListFragment()
                ?.getFragmentInstance() as? SuperAppParentFragment)?.getFilePaths()
        }
        return null
    }

    override fun getShowSourceFilePaths(activity: Activity): Array<String>? {
        if (activity is SuperAppActivity) {
            return activity.getShowSuperAppFilePath()
        }
        return null
    }

    override fun getFragment(activity: Activity, category: Int): Fragment {
        val previewOperate = Injector.injectFactory<IPreviewOperate>() ?: return SuperAppParentFragment()
        return if (previewOperate.isSupportPreview()) {
            previewOperate.setPreviewListFragmentCreator(object : IPreviewListFragmentCreator {
                override fun create(): IPreviewListFragment {
                    return SuperAppParentFragment()
                }
            })
            previewOperate.getFragment()
        } else {
            SuperAppParentFragment()
        }
    }

    override fun onResumeLoadData(fragment: Fragment) {
        Log.d(TAG, "onResumeLoadData")
        (fragment as? IBaseVMFragment)?.onResumeLoadData()
    }

    override fun onCreateOptionsMenu(fragment: Fragment, menu: Menu, inflater: MenuInflater) {
        fragment.onCreateOptionsMenu(menu, inflater)
    }

    override fun onOptionsMenuItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onMenuItemSelected")
        return fragment.onOptionsItemSelected(item)
    }

    override fun pressBack(fragment: Fragment): Boolean {
        Log.d(TAG, "pressBack")
        return (fragment as? IBaseVMFragment)?.pressBack() ?: false
    }

    override fun fromSelectPathResult(fragment: Fragment, requestCode: Int, path: String?) {
        Log.d(TAG, "fromSelectPathResult")
        (fragment as? IBaseVMFragment)?.fromSelectPathResult(requestCode, path)
    }

    override fun setIsHalfScreen(fragment: Fragment, category: Int, isHalfScreen: Boolean) {
        (fragment as? IBaseVMFragment)?.setIsHalfScreen(isHalfScreen)
    }

    override fun onUIConfigChanged(fragment: Fragment, configList: MutableCollection<IUIConfig>) {
        (fragment as? UIConfigMonitor.OnUIConfigChangeListener)?.onUIConfigChanged(configList)
    }

    override fun backToTop(fragment: Fragment) {
        Log.d(TAG, "backToTop")
        (fragment as? IBaseVMFragment)?.backToTop()
    }

    override fun onWindowInsetsCallback(
        fragment: Fragment,
        showNavigationBar: Boolean,
        systemBarInsetsBottom: Int
    ) {
        (fragment as? IBaseVMFragment)?.onWindowInsetsCallback(showNavigationBar, systemBarInsetsBottom)
    }

    override fun openDrawer(fragment: Fragment, fragmentWidth: Int) {
        (fragment as? IBaseVMFragment)?.openDrawer(fragmentWidth)
    }

    override fun closeDrawer(fragment: Fragment, fragmentWidth: Int) {
        (fragment as? IBaseVMFragment)?.closeDrawer(fragmentWidth)
    }

    override fun notifyDrawerRight(fragment: Fragment, right: Int) {
        (fragment as? IBaseVMFragment)?.notifyDrawerRight(right)
    }

    override fun onSideNavigationClicked(
        fragment: Fragment,
        sideNavigationContainer: COUISideNavigationBar?,
        isOpen: Boolean
    ): Boolean {
        return (fragment as? IBaseVMFragment)?.onSideNavigationClicked(
            sideNavigationContainer,
            isOpen
        ) ?: false
    }

    override fun updateFragmentData(fragment: Fragment, bundle: Bundle?) {
        (fragment as? IBaseVMFragment)?.updateFragmentData(bundle)
    }

    override fun exitSelectionMode(fragment: Fragment) {
        (fragment as? IBaseVMFragment)?.exitSelectionMode()
    }
}