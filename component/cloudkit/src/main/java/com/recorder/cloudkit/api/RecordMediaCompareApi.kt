package com.recorder.cloudkit.api

import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.RecordMediaCompareAction
import com.soundrecorder.common.sync.db.RecordDataSync

@Component(RecordMediaCompareAction.COMPONENT_NAME)
object RecordMediaCompareApi {
    private const val TAG = "RecordMediaCompareApi"

    /**
     * 停止当前同步且清楚锚点
     */
    @Action(RecordMediaCompareAction.DO_MEDIA_COMPARE)
    @JvmStatic
    fun doMediaCompare(trigCloudSyncRightNow: Boolean, syncType: Int = -1) {
        DebugUtil.i(TAG, "doMediaCompare, trigCloudSyncRightNow:$trigCloudSyncRightNow,syncType $syncType ")
        RecordDataSync.getInstance().syncAllRecordDataFromMedia(BaseApplication.getAppContext(), true, trigCloudSyncRightNow, syncType)
    }

    @Action(RecordMediaCompareAction.DO_STOP_MEDIA_COMPARE)
    @JvmStatic
    fun doStopMediaCompare(stop: Boolean) {
        DebugUtil.i(TAG, "doStopMediaCompare:$stop")
        RecordDataSync.getInstance().setStopDiff(stop)
    }

    @Action(RecordMediaCompareAction.IS_MEDIA_COMPARING)
    @JvmStatic
    fun isMediaComparing(): Boolean {
        return RecordDataSync.getInstance().isMediaComparing
    }
}