/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: AllToolsActivity
 * * Description: AllToolsActivity
 * * Version: 1.0
 * * Date : 2024/07/03
 * * Author:ztf
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *        ztf           2024/07/03       1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.impl.ui

import andes.oplus.documentsreader.core.common.BaseVMActivity
import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.doctools.impl.R
import andes.oplus.documentsreader.impl.util.FlexibleWindowUtil
import andes.oplus.documentsreader.interfaces.IUserStatementManager
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem

class AllToolsActivity : BaseVMActivity() {

    companion object {
        const val TAG = "AllToolsActivity"

        fun start(activity: Activity?) {
            try {
                activity?.apply {
                    val intent = Intent(activity, AllToolsActivity::class.java)
                    FlexibleWindowUtil.startActivityForFlexibleWindow(this, intent)
                }
            } catch (e: Exception) {
                Log.w(TAG, "start: ${e.message}")
            }
        }
    }

    private var hasShowStatement = false
    private val userStatementManager = Injector.injectFactory<IUserStatementManager>()

    override fun onCreate(savedInstanceState: Bundle?) {
        hasShowStatement = userStatementManager?.checkShowStatement(this) ?: true
        Log.d(TAG, "checkShowStatement -> hasShowStatement = $hasShowStatement")
        super.onCreate(savedInstanceState)
    }

    override fun getLayoutResId(): Int {
        return R.layout.activity_all_tools
    }

    override fun initView() {
        var fragment = supportFragmentManager.findFragmentByTag(TAG)
        if ((fragment == null) || (fragment !is AllToolsFragment)) {
            fragment = AllToolsFragment()
        }
        val ft = supportFragmentManager.beginTransaction()
        ft.replace(R.id.fragment_container, fragment, TAG)
        ft.show(fragment)
        ft.commitAllowingStateLoss()
    }

    override fun startObserve() {
        if (!hasShowStatement) {
            userStatementManager?.showStatementDialog(this) { resultCode ->
                when (resultCode) {
                    IUserStatementManager.CODE_EXIT -> finish()
                    IUserStatementManager.CODE_AGREE -> hasShowStatement = true
                }
            }
        }
    }

    override fun initData() {
    }

    override fun onWindowInsetsCallback(showNavigationBar: Boolean, systemBarInsetsBottom: Int) {
        super.onWindowInsetsCallback(showNavigationBar, systemBarInsetsBottom)
        val fragment = supportFragmentManager.findFragmentByTag(TAG)
        (fragment as? AllToolsFragment)?.onWindowInsetsCallback(showNavigationBar, systemBarInsetsBottom)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            finish()
            return true
        }
        return super.onOptionsItemSelected(item)
    }
}