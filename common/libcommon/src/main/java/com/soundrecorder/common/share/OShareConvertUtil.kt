/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  OShareConvertUtil
 * * Description: OShareConvertUtil
 * * Version: 1.0
 * * Date : 2025/3/6
 * * Author: W9066446
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9066446    2025/3/31   1.0    build this module
 ****************************************************************/
package com.soundrecorder.common.share

import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.Context
import android.os.Environment
import android.provider.MediaStore
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.BaseUtil.getPhoneStorageFile
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.MediaDataScanner
import com.soundrecorder.base.utils.RecorderICUFormateUtils
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.CursorHelper
import com.soundrecorder.common.db.GroupInfoDbUtil
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.sync.db.RecordDataSync
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.ConvertDbUtil.CONVERT_COMP_STATUS_COMPLETE
import com.soundrecorder.common.utils.ConvertDbUtil.SHOW_SWITH_TRUE
import com.soundrecorder.common.utils.RecordModeUtil
import com.soundrecorder.common.utils.SettingsAdapter
import java.io.BufferedReader
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.io.InputStreamReader
import java.nio.charset.StandardCharsets
import java.util.Collections
import java.util.Date
import java.util.UUID

object OShareConvertUtil {
    private const val TAG = "OShareConvertUtil"
    const val KEY_START_FLAG: String = "<"
    const val KEY_END_FLAG: String = ">"
    const val TXT_SUFFIX = ".txt"

    //用于标识互传相关，send_entrance是互传自定义参数,值为录音包名，用于在传输完成后判读启动录音
    const val OPPO_SHARE_SEND_ENTRANCE: String = "send_entrance"
    const val EXTRA_OSHARE_RECORD_PATH: String = "AUDIO_FILE_PATH"
    const val EXTRA_OSHARE_CONVERT_TEXT_PATH: String = "CONVERT_TEXT_PATH"

    /**
     * 获取互传转文本文件的保存路径
     */
    @JvmStatic
    fun getConvertSavePath(): String {
        return CursorHelper.DEFAULT_DIR + File.separator + ShareAction.relativePathOShareText
    }

    /**
     * 获取转文本文件名
     */
    @JvmStatic
    fun getOShareTextFileName(recordName: String, recordMD5: String): String {
        if (recordMD5.isEmpty() || recordName.isEmpty()) return ""
        val baseName = BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.export_save_file_name, recordName)
        return "$baseName-$recordMD5"
    }

    /**
     * 获取OShare目录下录音的分组ID
     */
    @JvmStatic
    fun getGroupId(): Int = GroupInfo.INT_DEFAULT_COMMON

    /**
     * 获取OShare目录下录音的分组UUID
     */
    @JvmStatic
    fun getGroupUuid(): String = GroupInfoDbUtil.DEFAULT_COMMON_UUID

    /**
     *
     * 文件解析位置：ConvertToUtils.readOShareConvertContent
     *
     * 拼接互传分享转文本文件内容
     * 文本内容格式：
     * 文本标题
     *
     * 时间：2025年2月7日 17:03:30 time:unix时间戳
     * 主题:  录音文件名称
     * 参会人：<讲话人序号><讲话人 1>， <讲话人序号><讲话人2>
     *
     * 讲话人 1 该句话起始时间字符串00:01 <讲话人序号 该句话起始unix时间戳>
     * 说话内容
     *
     * 讲话人 1该句话起始时间字符串00:03 <讲话人序号 该句话起始unix时间戳>
     * 说话内容
     */
    @JvmStatic
    @SuppressLint("StringFormatInvalid")
    fun getTextContent(record: Record, contents: List<ConvertContentItem?>?): String {
        if (contents == null) {
            DebugUtil.i(TAG, "getTextContentWithTimestamp contents is null")
            return ""
        }
        val context = BaseApplication.getAppContext()
        val fileName = record.displayName
        //标题
        val originalFileName = fileName.title()
        val title = context.getString(
            com.soundrecorder.common.R.string.export_save_file_name,
            originalFileName
        )
        //时间
        val time = RecorderDBUtil.getInstance(BaseApplication.getAppContext())
            .getCreateTimeByPath(record.id, fileName.endsWith(".amr"))
        val dateString = RecorderICUFormateUtils.formatDateTime(Date(time))
        val timeString =
            context.getString(com.soundrecorder.common.R.string.time) + "  $dateString"
        //主题
        val subject =
            context.getString(com.soundrecorder.common.R.string.export_content_theme) + "  $originalFileName"
        //添加参会人信息
        val roles = getRolesString(contents)
        val speakerString = context.getString(
            com.soundrecorder.common.R.string.export_content_person,
            "  "
        ) + roles
        val lineSeparator = System.lineSeparator()
        val txtString = StringBuilder()
        txtString.append("$title$lineSeparator$lineSeparator") // 文本标题
        txtString.append("$timeString time:$time") // 时间：2025年2月7日 17:03:30 time:unix时间戳
        txtString.append(lineSeparator)
        txtString.append("$subject$lineSeparator") // 主题:  录音文件名称
        txtString.append("$speakerString$lineSeparator$lineSeparator") //参会人：<讲话人序号><讲话人 1>， <讲话人序号><讲话人2>
        //添加讲话人、时间和讲话内容
        contents.forEach {
            it?.apply {
                txtString.append("$roleName ${startTime.durationInMsFormatTimeExclusive()}") // 讲话人 1 该句话起始时间字符串00:01
                txtString.append("$KEY_START_FLAG$roleId $startTime$KEY_END_FLAG$lineSeparator") // <讲话人序号 该句话起始unix时间戳>
                txtString.append("$textContent$lineSeparator$lineSeparator") //说话内容
            }
        }
        return txtString.toString()
    }

    /**
     * 获取header里面参会人信息
     */
    @JvmStatic
    private fun getRolesString(readConvertContent: List<ConvertContentItem?>): String {
        val stringBuilder = StringBuilder()
        val rolesList = mutableListOf<String>()
        for (item in readConvertContent) {
            item?.roleName?.let {
                if (it.isNotBlank() && !rolesList.contains(it)) {
                    rolesList.add(it)
                    stringBuilder.append("$KEY_START_FLAG${item.roleId}$KEY_END_FLAG")
                    stringBuilder.append("$KEY_START_FLAG$it$KEY_END_FLAG,  ")
                }
            }
        }
        if (rolesList.isNotEmpty()) {
            return stringBuilder.deleteCharAt(stringBuilder.lastIndexOf(",")).toString()
        }
        return ""
    }

    @JvmStatic
    fun syncOShareDB() {
        syncOShareConvertDB()
        syncOShareRecordDB()
    }

    @JvmStatic
    private fun syncOShareConvertDB() {
        DebugUtil.d(TAG, "syncOShareConvertDB")
        val context = BaseApplication.getAppContext()
        val internalSdDirFilePath = getPhoneStorageFile(context)?.path
        if (internalSdDirFilePath.isNullOrEmpty()) {
            DebugUtil.i(TAG, "syncOShareConvertDB internalSdDirFilePath is null")
            return
        }
        val pathsFromDb: List<String>? = ConvertDbUtil.selectByConvertTextFilePathPrefix(internalSdDirFilePath)
        if (pathsFromDb.isNullOrEmpty()) {
            DebugUtil.i(TAG, "syncOShareConvertDB pathsFromDb is null")
            return
        }
        for (path in pathsFromDb) {
            val file = File(path)
            if (!file.exists()) {
                ConvertDbUtil.deleteByConvertTextFilePath(path)
            }
        }
    }

    @JvmStatic
    private fun syncOShareRecordDB() {
        DebugUtil.d(TAG, "syncOShareRecordDB")
        val context = BaseApplication.getAppContext()
        val internalSdDirFilePath = getPhoneStorageFile(context)?.path
        if (internalSdDirFilePath.isNullOrEmpty()) {
            DebugUtil.i(TAG, "syncOShareRecordDB internalSdDirFilePath is null")
            return
        }
        val records: List<Record>? = RecorderDBUtil.getInstance(context).getRecordFilePathsByPathPrefix(internalSdDirFilePath)
        if (records.isNullOrEmpty()) {
            DebugUtil.i(TAG, "syncOShareRecordDB records is null")
            return
        }
        for (record in records) {
            if (record.mData.isNullOrEmpty()) {
                DebugUtil.i(TAG, "syncOShareRecordDB record.mData is null")
                continue
            }
            val file = File(record.mData)
            if (!file.exists() && !record.isDeleted) {  // 该音频文件不是在录音APP内部被删除，则删除对应的数据
                RecorderDBUtil.getInstance(context).deleteRecordByPath(record.mData)
                ConvertDbUtil.deleteByMediaPath(record.mData)
            }
        }
    }

    @JvmStatic
    fun deleteOShareDBData(filePath: String?) {
        if (PermissionUtils.getNextAction() == PermissionUtils.SHOULD_SHOW_USER_NOTICE) {
            DebugUtil.d(TAG, "deleteOShareDBData , The user notice was not agreed")
            return
        }
        if (filePath.isNullOrEmpty()) {
            DebugUtil.i(TAG, "deleteOShareDBData , filePath is null")
            return
        }
        val file = File(filePath)
        if (!file.exists()) {
            when {
                filePath.contains(ShareAction.relativePathOShare) -> {
                    RecorderDBUtil.getInstance(BaseApplication.getAppContext()).deleteRecordByPath(filePath)
                    ConvertDbUtil.deleteByMediaPath(filePath)
                }

                filePath.contains(ShareAction.relativePathOShareText) ->
                    ConvertDbUtil.deleteByConvertTextFilePath(filePath)
            }
        }
    }

    @JvmStatic
    fun deleteAllOShareDBData(filePath: String?) {
        if (PermissionUtils.getNextAction() == PermissionUtils.SHOULD_SHOW_USER_NOTICE) {
            DebugUtil.d(TAG, "deleteAllOShareDBData , The user notice was not agreed")
            return
        }
        if (filePath.isNullOrEmpty()) {
            DebugUtil.i(TAG, "deleteAllOShareDBData , recordPath is null")
            return
        }
        val file = File(filePath)
        if (!file.exists()) {
            val storageSdcard = SettingsAdapter.getInstance().storageSdcard + File.separatorChar
            val oShareRecordRootPath = storageSdcard + Constants.OPPO_SHARE_RECORDINGS_RELATIVE_ROOT_PATH
            val oShareRecordTextRootPath = storageSdcard + Constants.OPPO_SHARE_RECORDINGS_TEXT_ROOT_PATH
            when {
                filePath.contains(oShareRecordRootPath) || filePath.contains(ShareAction.relativePathOShare) -> {
                    ConvertDbUtil.deleteConvertDataByMediaPathPrefix(filePath)
                    RecorderDBUtil.getInstance(BaseApplication.getAppContext()).deleteRecordDataByPathPrefix(filePath)
                }

                filePath.contains(oShareRecordTextRootPath) || filePath.contains(ShareAction.relativePathOShareText) ->
                    ConvertDbUtil.deleteConvertDataByConvertTextFilePrefix(filePath)
            }
        }
    }

    /**
     * 将OShare目录下的录音保存到RecordDB中
     * @param recordPath 音频路径
     */
    @JvmStatic
    fun syncOShareFile(context: Context, recordPath: String, convertTextPath: String?) {
        val selection = MediaStore.Audio.Media.DATA + " COLLATE NOCASE = ? "
        val selectionArgs = arrayOf(recordPath)
        val mediaRecords = RecordDataSync.getMediaData(
            context,
            MediaDBUtils.BASE_URI,
            selection,
            selectionArgs,
            -1,
            -1
        )
        if (mediaRecords != null && mediaRecords.size > 0) {
            val mediaRecord = mediaRecords[0]
            if (!mediaRecord.isOShareFile) {
                DebugUtil.i(TAG, "syncOShareFile mediaRecord is not OShare file")
                return
            }
            val record = RecorderDBUtil.getInstance(context).qureyRecordByPath(recordPath)
            if (record == null) {
                //插入Record数据库
                insertNewOShareRecord(context, mediaRecord, convertTextPath)
            } else {
                // 转写文本扫描
                scanOShareConvertFile(mediaRecord.id, record, convertTextPath)
            }
        }
    }

    @JvmStatic
    private fun insertNewOShareRecord(context: Context, mediaRecord: Record, convertTextPath: String?) {
        val uuIdString = UUID.randomUUID().toString()
        mediaRecord.groupId = getGroupId()
        mediaRecord.groupUuid = getGroupUuid()
        mediaRecord.uuid = uuIdString
        mediaRecord.recordType = RecordModeUtil.getRecordTypeForMediaRecord(mediaRecord)
        mediaRecord.dirty = RecordConstant.RECORD_DIRTY_MEGA_ONLY
        mediaRecord.checkMd5()
        mediaRecord.setSyncUploadStatus(RecordConstant.SYNC_STATUS_BACKUP_START)
        mediaRecord.checkMd5()
        RecorderDBUtil.insertRecordData(context, mediaRecord.convertToContentValues())
        DebugUtil.i(TAG, "insertNewOShareRecord: no record found in syncDb, insert media record $mediaRecord")
        // 转写文本扫描
        scanOShareConvertFile(mediaRecord.id, mediaRecord, convertTextPath)
    }

    /**
     * 扫描OShare目录下录音文件的转文本文件
     */
    @JvmStatic
    fun scanOShareConvertFile(mediaId: Long, record: Record, convertTextPath: String?, openBlurScan: Boolean = false) {
        DebugUtil.d(TAG, "scanOShareConvertFile openBlurScan=$openBlurScan")
        if (record.isScanOshareText) {
            val convertRecord = ConvertDbUtil.selectByMediaPath(record.data)
            val convertRecordFile = convertRecord?.convertTextfilePath?.let { File(it) }
            if (convertRecordFile != null && convertRecordFile.exists()) {
                DebugUtil.w(TAG, "scanOShareConvertFile isScanOShareText and file exists")
                return
            }
        }
       var realityConvertTextPath = convertTextPath
        if (convertTextPath.isNullOrEmpty()) {
            if (!openBlurScan) {
                DebugUtil.w(TAG, "scanOShareConvertFile convertTextPath is null")
                return
            } else {
                realityConvertTextPath = findConvertFileByMd5(record.mD5)
            }
        }
        if (realityConvertTextPath.isNullOrEmpty()) {
            DebugUtil.w(TAG, "scanOShareConvertFile realityConvertTextPath is null")
            return
        }
        if (record.mD5.isNullOrEmpty() || !realityConvertTextPath.contains(record.mD5)) {
            DebugUtil.w(TAG, "scanOShareConvertFile md5 is null or realityConvertTextPath not included MD5")
            return
        }
        val convertFile = File(realityConvertTextPath)
        // 存在转写文件，将转写文件信息存入转写数据库
        if (convertFile.exists()) {
            val convertRecord = ConvertRecord(mediaId).apply {
                mediaPath = record.data
                convertTextfilePath = convertFile.path
                convertStatus = ConvertStatus.CONVERT_STATUS_QUERY_SUC
                uploadRecordList = Collections.emptyList()
                completeStatus = CONVERT_COMP_STATUS_COMPLETE
                canShowSpeakerRole = SHOW_SWITH_TRUE
            }
            ConvertDbUtil.insertCheckRecordId(convertRecord)
            val covertTime = readOShareConvertTime(convertFile.path)
            DebugUtil.d(TAG, "scanOShareConvertFile covertTime:$covertTime")
            // 文本转换界面显示的时间与音频文件更新时间有关，将音频文件更新时间设置为文本转换时间
            if (covertTime != -1L) {
                val modifyTime = covertTime / TimeUtils.TIME_ONE_SECOND
                // 文件设置更新时间
                val result = FileUtils.modifyFileLastModifyTimeByShell(record.data, modifyTime)
                DebugUtil.d(TAG, "scanOShareConvertFile set modifyTime result:$result")
                // 刷新媒体库
                MediaDataScanner.getInstance().mediaScan(BaseApplication.getAppContext(), arrayOf(record.data))
            }
        }
        //更新录音数据库COLUMN_NAME_SCAN_OSHARE_TEXT为1，表示已经扫描过转文本文件
        val context = BaseApplication.getAppContext()
        val selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID + "= ?"
        val values = ContentValues()
        values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_SCAN_OSHARE_TEXT, 1)
        RecorderDBUtil.updateRecordData(context, values, selection, arrayOf(record.uuid))
    }

    /**
     * 扫描转换目录查找包含指定MD5值的文件
     * @param md5 要查找的文件MD5标识
     * @return 找到的文件完整路径，若未找到则返回null
     */
    @JvmStatic
    private fun findConvertFileByMd5(md5: String?): String? {
        DebugUtil.d(TAG, "findConvertFileByMd5")
        if (md5.isNullOrEmpty()) {
            DebugUtil.e(TAG, "findConvertFileByMd5, MD5 is null or empty")
            return null
        }
        val convertPath = getConvertSavePath()
        val convertDir = File(convertPath).takeIf { it.exists() && it.isDirectory } ?: run {
            DebugUtil.e(TAG, "findConvertFileByMd5, convert path does not exist or is not a directory")
            return null
        }
        return convertDir.listFiles()?.firstOrNull { file ->
            file.isFile && file.path.contains(md5)
        }?.path
    }

    /**
     * 解析OShare目录下的转文本文件的时间
     * 文本内容格式：
     * 文本标题
     *
     * 时间：2025年2月7日 17:03:30 time:unix时间戳
     * 主题:  录音文件名称
     * 参会人：<讲话人序号><讲话人 1>， <讲话人序号><讲话人2>
     *
     * 讲话人 1 该句话起始时间字符串00:01 <讲话人序号 该句话起始unix时间戳>
     * 说话内容
     *
     * 讲话人 1该句话起始时间字符串00:03 <讲话人序号 该句话起始unix时间戳>
     * 说话内容
     */
    @Suppress("LongMethod", "TooGenericExceptionCaught")
    @Synchronized
    @Throws(IOException::class, NumberFormatException::class)
    @JvmStatic
    fun readOShareConvertTime(filePath: String): Long {
        val startTime = System.currentTimeMillis()
        if (!isExternalStorageReadable()) {
            DebugUtil.w(TAG, "readOShareConvertTime external storage not readable!")
            return -1
        }
        val file = File(filePath)
        DebugUtil.d(TAG, "readOShareConvertTime: " + file.name)
        if (!file.exists()) {
            DebugUtil.w(TAG, "readOShareConvertTime $filePath not found!")
            return -1
        }
        val convertContentItems = ArrayList<ConvertContentItem>()
        val br = BufferedReader(InputStreamReader(FileInputStream(file), StandardCharsets.UTF_8))
        var title: String? = null
        var dateString: String? = null
        try {
            while (true) {
                val line = br.readLine()
                if (line == null || dateString != null) {
                    break
                }
                if (line.isEmpty() || title == null) {
                    if (line.isNotEmpty()) {
                        title = line
                    }
                    continue
                }
                dateString = line
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "readOShareConvertTime exception!" + e.message)
        } finally {
            DebugUtil.i(TAG, "readOShareConvertTime finally")
            br.close()
        }
        val endTime = System.currentTimeMillis()
        DebugUtil.d(
            TAG,
            "readOShareConvertTime: size:" + convertContentItems.size + ", time:" + (endTime - startTime)
        )
        if (dateString.isNullOrEmpty()) {
            DebugUtil.w(TAG, "readOShareConvertTime dateString isNullOrEmpty")
            return -1
        }
        val times = dateString.split(":") // 时间：2025年2月7日 17:03:30 time:unix时间戳
        if (times.size < 2) {
            DebugUtil.w(TAG, "readOShareConvertTime times size < 2")
            return -1
        }
        var time: Long = 0
        kotlin.runCatching {
            time = times.last().toLong()
        }.onFailure {
            DebugUtil.w(TAG, "readOShareConvertTime times toLong failed:$it")
        }
        return time
    }

    @JvmStatic
    fun isExternalStorageReadable(): Boolean {
        val state = Environment.getExternalStorageState()
        return Environment.MEDIA_MOUNTED == state || Environment.MEDIA_MOUNTED_READ_ONLY == state
    }
}