/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IRecycleBinApi.kt
 * Description:
 *     The exported api of recycle bin module.
 *
 * Version: 1.0
 * Date: 2024-06-28
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-06-28   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.recycle.interfaces

import andes.oplus.documentsreader.recycle.actions.IBlockingDropFromRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.IBlockingEraseToRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.IBlockingRestoreFromRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.IClearAllRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.IDropFromRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.IEraseToRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.INoParamRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.INoParamRecycleFileBlockingAction
import andes.oplus.documentsreader.recycle.actions.IRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.IRecycleFileBlockingAction
import andes.oplus.documentsreader.recycle.actions.IRestoreFromRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.IUnitRecycleFileAction
import andes.oplus.documentsreader.recycle.actions.IUnitRecycleFileBlockingAction
import andes.oplus.documentsreader.recycle.data.IRecycleFileRecord
import andes.oplus.documentsreader.recycle.params.IRecycleBinEraseParam
import andes.oplus.documentsreader.recycle.params.RecycleBinQueryParam
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers

interface IRecycleBinApi {

    /**
     * Obtain an action to remove overdue recycled files.
     */
    fun actionCleanObsolete(actionScope: CoroutineScope): IUnitRecycleFileAction

    /**
     * Same as [actionCleanObsolete] but can only blocking execute
     */
    fun blockingCleanObsolete(): IUnitRecycleFileBlockingAction =
        actionCleanObsolete(CoroutineScope(Dispatchers.Unconfined))

    /**
     * Obtain an action to remove all recycled files.
     */
    fun actionClearAll(actionScope: CoroutineScope): IClearAllRecycleFileAction

    /**
     * Same as [actionClearAll] but can only blocking execute
     */
    fun blockingClearAll(): IUnitRecycleFileBlockingAction =
        actionClearAll(CoroutineScope(Dispatchers.Unconfined))

    /**
     * Obtain an action to query the count of recycled files.
     */
    fun actionCount(actionScope: CoroutineScope): INoParamRecycleFileAction<Long>

    /**
     * Same as [actionCount] but can only blocking execute
     */
    fun blockingCount(): INoParamRecycleFileBlockingAction<Long> =
        actionCount(CoroutineScope(Dispatchers.Unconfined))

    /**
     * Obtain an action to remove specify recycled files.
     */
    fun actionDrop(actionScope: CoroutineScope): IDropFromRecycleFileAction

    /**
     * Same as [actionDrop] but can only blocking execute
     */
    fun blockingDrop(): IBlockingDropFromRecycleFileAction =
        actionDrop(CoroutineScope(Dispatchers.Unconfined))

    /**
     * Obtain an action to remove specify files and recycle them into recycle bin.
     */
    fun actionErase(
        actionScope: CoroutineScope,
        eraseParam: IRecycleBinEraseParam
    ): IEraseToRecycleFileAction

    /**
     * Same as [actionErase] but can only blocking execute
     */
    fun blockingErase(eraseParam: IRecycleBinEraseParam): IBlockingEraseToRecycleFileAction =
        actionErase(CoroutineScope(Dispatchers.Unconfined), eraseParam)

    /**
     * Obtain an action to query recycled files in recycle bin.
     */
    fun actionList(
        actionScope: CoroutineScope
    ): IRecycleFileAction<RecycleBinQueryParam?, Collection<IRecycleFileRecord>>

    /**
     * Same as [actionList] but can only blocking execute
     */
    fun blockingList(): IRecycleFileBlockingAction<RecycleBinQueryParam?, Collection<IRecycleFileRecord>> =
        actionList(CoroutineScope(Dispatchers.Unconfined))

    /**
     * Obtain an action to restore recycled files to their origin paths.
     */
    fun actionRestore(actionScope: CoroutineScope): IRestoreFromRecycleFileAction

    /**
     * Same as [actionRestore] but can only blocking execute
     */
    fun blockingRestore(): IBlockingRestoreFromRecycleFileAction =
        actionRestore(CoroutineScope(Dispatchers.Unconfined))
}