/***********************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File:   SelectDocLoader
 * * Description: SelectDocLoader
 * * Version:1.0
 * * Date :2024/7/3
 * * Author:yangqichang
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 ****************************************************************/
package andes.oplus.documentsreader.selectdoc.ui

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.CategoryHelper
import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.FileTypeUtils
import andes.oplus.documentsreader.core.common.FileUtils
import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.JavaFileHelper
import andes.oplus.documentsreader.core.common.LoaderUtils
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.MediaHelper
import andes.oplus.documentsreader.core.common.MimeTypeHelper
import andes.oplus.documentsreader.core.common.loader.BaseUriLoader
import andes.oplus.documentsreader.core.common.sort.SortModeUtils
import andes.oplus.documentsreader.core.common.sort.SortRecordModeFactory
import andes.oplus.documentsreader.recent.interfaces.IRecentFileApi
import andes.oplus.documentsreader.selectdoc.utils.SelectDocSortUtils
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.provider.MediaStore
import android.text.TextUtils
import com.documentsreader.documentapi.IDocumentExtensionType
import java.io.File
import java.util.Locale

class SelectDocLoader(
    context: Context,
    private val tabPosition: Int,
    private val tabExtensionList: ArrayList<String?>?,
    private val tabTitleFileExtensionMap: MutableMap<String, ArrayList<String?>>?,
    private val currentPath: String?
) : BaseUriLoader<Int, BaseFileBean>(context) {
    companion object {
        private const val TAG = "SelectDocLoader"
        private const val INDEX_ID = 0
        private const val INDEX_DATA = 1
        private const val INDEX_DISPLAY_NAME = 2
        private const val INDEX_SIZE = 3
        private const val INDEX_DATE_MODIFIED = 4
        private const val INDEX_DATE_MIME_TYPE = 5
        private const val TIME_SECOND = 1000
        private val MEDIA_PROJECT = arrayOf(
            MediaStore.Files.FileColumns._ID,
            MediaStore.Files.FileColumns.DATA,
            MediaStore.Files.FileColumns.DISPLAY_NAME,
            MediaStore.Files.FileColumns.SIZE,
            MediaStore.Files.FileColumns.DATE_MODIFIED,
            MediaStore.Files.FileColumns.MIME_TYPE
        )
    }

    private var baseQuerySelection: String = ""
    private val documentExtensionApi = Injector.injectFactory<IDocumentExtensionType>()
    private var searchKey: String? = null

    init {
        super.initData()
        initSelection()
    }

    private fun initSelection() {
        val sql = StringBuilder()
        if (tabPosition == 0) {
            val fileExtensionList = ArrayList<String?>()
            tabTitleFileExtensionMap?.values?.forEach {
                fileExtensionList.addAll(it)
            }
            val sqlString = documentExtensionApi?.getDocumentSqlQuery(fileExtensionList)
            sql.append(sqlString)
        } else {
            tabExtensionList?.let {
                val sqlString = documentExtensionApi?.getDocumentSqlQuery(it)
                sql.append(sqlString)
            }
        }
        if (!TextUtils.isEmpty(sql)) {
            sql.append(" AND ")
        }
        currentPath?.let {
            sql.append(MediaStore.Files.FileColumns.DATA + " LIKE '%" + it + "%'")
        }
        baseQuerySelection = sql.toString()
    }

    fun updateSearchKey(key: String) {
        Log.d(TAG, "updateSearchKey [$key]")
        searchKey = key
    }

    override fun getUri(): Uri {
        return MediaHelper.FILE_URI
    }

    override fun getSelection(): String {
        val sql = StringBuilder()
        val searchCondition = LoaderUtils.createFuzzySearch(searchKey)
        if (!TextUtils.isEmpty(searchCondition)) {
            sql.append(MediaStore.Files.FileColumns.DISPLAY_NAME).append(searchCondition)
                .append(" AND ")
        }
        sql.append(baseQuerySelection)
        return sql.toString()
    }

    override fun getSelectionArgs(): Array<String>? {
        return null
    }

    override fun getObserverUri(): Array<Uri>? {
        return null
    }

    override fun getProjection(): Array<String> {
        return MEDIA_PROJECT
    }

    override fun createFromCursor(cursor: Cursor, uri: Uri?): BaseFileBean? {
        val id = cursor.getInt(INDEX_ID)
        val data = cursor.getString(INDEX_DATA)
        val displayName = cursor.getString(INDEX_DISPLAY_NAME)
        val size = cursor.getLong(INDEX_SIZE)
        val dateModified = cursor.getLong(INDEX_DATE_MODIFIED) * TIME_SECOND
        val mimeType = cursor.getString(INDEX_DATE_MIME_TYPE)
        if (FileUtils.isHiddenFile(displayName)) {
            Log.d(TAG, "createFromCursor file is hidden")
            return null
        }
        val baseFileBean = createBaseFileBean(id, data, displayName, mimeType, size, dateModified, MediaHelper.FILE_URI)
        if (baseFileBean.mData.isNullOrEmpty() || baseFileBean.mDisplayName.isNullOrEmpty()) {
            Log.d(TAG, "createFromCursor file data is empty")
            return null
        }
        if (JavaFileHelper.exists(data).not()) {
            Log.d(TAG, "createFromCursor file is exist, path is $data")
            return null
        }
        return baseFileBean
    }

    @Suppress("LongParameterList")
    private fun createBaseFileBean(
        id: Int?,
        absPath: String?,
        displayName: String?,
        mimeType: String?,
        size: Long,
        dateModified: Long,
        uri: Uri?
    ): BaseFileBean {
        val baseFileBean = BaseFileBean().apply {
            mData = absPath
            mDisplayName = displayName
            mSize = size
            mDateModified = dateModified
            mMimeType = mimeType
            mLocalFileUri = uri?.buildUpon()?.appendPath(id.toString())?.build()
            mData?.let {
                //mSize will return 0 sometimes when query from MediaStore, so need check from Java_File_Interface again
                if (mSize == 0L) {
                    val file = File(it)
                    mSize = file.length()
                    mDateModified = file.lastModified()
                }
            }
            mLocalType = MimeTypeHelper.getTypeFromExtension(FileTypeUtils.getExtension(mDisplayName))
                ?: MimeTypeHelper.UNKNOWN_TYPE
        }
        return baseFileBean
    }

    override fun preHandleResultBackground(list: MutableList<BaseFileBean>): MutableList<BaseFileBean> {
        findLastOpenTime(ContextGetter.context, list)
        val sort = SortModeUtils.getSharedSortMode(
            ContextGetter.context,
            SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC)
        )
        val isDesc = SortModeUtils.getSharedSortOrder(
            SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC)
        )
        SelectDocSortUtils.sortFiles(list, sort, isDesc)
        return list
    }

    private fun findLastOpenTime(context: Context, list: MutableList<BaseFileBean>) {
        val recentFileAction = Injector.injectFactory<IRecentFileApi>()
        val action = recentFileAction?.blockingActionGrep()
        val filePathList = list.mapNotNull { it.mData }
        val result = action?.blockingExecute(context, filePathList)
        list.forEach { data ->
            val timeEntity = result?.find { it.path == data.mData }
            timeEntity?.let {
                if (it.openTime == 0L) {
                    data.lastOpenTime = data.mDateModified
                } else {
                    data.lastOpenTime = it.openTime
                }
            }
            if (timeEntity == null) {
                data.lastOpenTime = data.mDateModified
            }
        }
    }

    override fun preHandleResultNoNeedUpdateKey(): Boolean {
        return false
    }

    override fun getItemKey(item: BaseFileBean): Int? {
        val path = item.mData
        if (path.isNullOrEmpty()) {
            return null
        }

        return path.lowercase(Locale.getDefault()).hashCode()
    }
}