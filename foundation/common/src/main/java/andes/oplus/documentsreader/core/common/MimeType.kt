/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MimeType
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/7/5 19:45
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/7/5       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.core.common

object MimeType {
    const val MIMETYPE_UNKNOWN = "*/*"

    const val MIMETYPE_IMAGE = "image/*"
    const val MIMETYPE_VIDEO = "video/*"
    const val MIMETYPE_AUDIO = "audio/*"
    const val MIMETYPE_PDF = "application/pdf"
    const val MIMETYPE_OFD = "application/ofd"
    const val MIMETYPE_DOC = "application/vnd.ms-word"
    const val MIMETYPE_XLS = "application/vnd.ms-excel"
    const val MIMETYPE_PPT = "application/vnd.ms-powerpoint"
    const val MIMETYPE_TXT = "text/plain"
    const val MIMETYPE_LRC = "text/plain"
    const val MIMETYPE_APPLICATION = "application/vnd.android.package-archive"
    const val MIMETYPE_HTML = "text/html"
    const val MIMETYPE_VCF = "text/x-vcard"
    const val MIMETYPE_P12 = "application/x-pkcs12"
    const val MIMETYPE_CER = "application/pkix-cert"

    const val MIMETYPE_KEYNOTE = "application/vnd.apple.keynote"
    const val MIMETYPE_PAGES = "application/vnd.apple.pages"
    const val MIMETYPE_NUMBERS = "application/vnd.apple.numbers"
    const val MIMETYPE_MARKDOWN = "text/markdown"

    const val MIMETYPE_CAD = "application/acad"
    const val MIMETYPE_AUTOCAD = "application/x-autocad"
    const val MIMETYPE_DWG = "image/vnd.dwg"
    const val MIMETYPE_DWG_DEFAULT_APP = "application/x-dwg"
    const val MIMETYPE_DXF = "image/vnd.dxf"
    const val MIMETYPE_X_DWG = "image/x-dwg"
    const val MIMETYPE_X_DXF = "image/x-dxf"

    const val MIMETYPE_XMIND = "application/xmind"
    const val MIMETYPE_X_XMIND = "application/x-xmind"
    const val MIMETYPE_FREE_MIND = "application/x-freemind"

    const val MIMETYPE_PSD = "application/photoshop"
    const val MIMETYPE_AI = "application/postscript"
    const val MIMETYPE_VISIO = "application/vnd.visio"
    const val MIMETYPE_VISIO_DEFAULT_APP = "application/vsdx"

    const val MIMETYPE_7Z = "application/x-7z-compressed"
    const val MIMETYPE_JAR = "application/java-archive"
    const val MIMETYPE_ZIP = "application/zip"
    const val MIMETYPE_RAR = "application/x-rar-compressed"


    const val MIMETYPE_OGG = "audio/ogg"
    const val MIMETYPE_APE = "audio/ape"
    const val MIMETYPE_DM = "application/x-android-drm-fl"
    const val MIMETYPE_DCF = "application/x-android-drm-fl"

    const val MIMETYPE_TIKA_DXF = "image/vnd.dxf; format\u003dascii"
    const val MIMETYPE_TIKA_DWG = MIMETYPE_DWG
    const val MIMETYPE_TIKA_KEYNOTE = MIMETYPE_KEYNOTE
    const val MIMETYPE_TIKA_PAGES = MIMETYPE_PAGES
    const val MIMETYPE_TIKA_NUMBERS = MIMETYPE_NUMBERS
    const val MIMETYPE_TIKA_PSD = "image/vnd.adobe.photoshop"
    const val MIMETYPE_TIKA_PDF = "application/pdf"
    const val MIMETYPE_TIKA_VISIO = "application/vnd.visio"
    const val MIMETYPE_TIKA_MARKDOWN = "text/x-web-markdown"
    const val MIMETYPE_TIKA_XMIND = MIMETYPE_X_XMIND

    const val MIMETYPE_STREAM = "application/octet-stream"

    const val MIMETYPE_VCS = "text/calendar"
}