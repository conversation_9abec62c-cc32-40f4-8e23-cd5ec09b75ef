/***********************************************************
 * * Copyright (C), 2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:FileCopyHelper.kt
 * * Description:the Top Function to help counting files of path
 * * Version:1.0
 * * Date :2020.1.9
 * * Author:liuzeming
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * * liuzeming    20200109   1.0       TopFunction to get files of path
 ****************************************************************/
package andes.oplus.documentsreader.core.fileoperate.copy

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.R
import andes.oplus.documentsreader.core.common.SdkUtils
import andes.oplus.documentsreader.core.common.VolumeEnvironment
import andes.oplus.documentsreader.core.common.utils.PathUtils
import android.content.Context
import android.net.Uri
import android.os.storage.StorageManager
import android.os.storage.StorageVolume
import android.text.TextUtils
import com.oplus.compat.os.storage.StorageVolumeNative
import com.oplus.compat.utils.util.UnSupportedApiVersionException
import com.oplus.os.storage.OplusStorageVolume
import java.io.BufferedInputStream
import java.io.BufferedOutputStream
import java.io.BufferedReader
import java.io.Closeable
import java.io.File
import java.io.FileOutputStream
import java.io.FileReader
import java.io.IOException
import java.io.InputStream
import java.io.RandomAccessFile
import java.nio.ByteBuffer
import java.nio.channels.FileChannel
import java.nio.file.Files
import java.nio.file.Path
import kotlin.io.path.deleteIfExists

object FileCopyHelper {
    private const val TAG = "FileCopyHelper"
    private const val COPY_BUF_SIZE = 512 * 1024
    private const val DEFAULT_LENTH = 1024 * 128.toLong()
    private const val ALLOCATE_LENTH = 1024 * 64
    private const val READ_ONLY_TYPE_PATH_SIZE = 3

    abstract class OnDealFileListener {
        private var mCurrentProgress = 0L

        internal fun addProgress(progress: Long) {
            mCurrentProgress += progress
            onProgressChanged(mCurrentProgress)
        }

        abstract var isCancel: () -> Boolean

        /**
         * callback when progress changed
         */
        abstract fun onProgressChanged(progress: Long)

        /**
         * 复制文件成功回调
         * @param sourceFile 来源文件
         * @param destFile 目标文件
         * @param isDirectory 是否为文件夹
         */
        abstract fun onSuccess(sourceFile: File, destFile: File, isDirectory: Boolean)

        /**
         * 复制文件失败回调
         * @param sourceFile 来源文件
         * @param destFile 目标文件
         */
        abstract fun onError(sourceFile: File, destFile: File): Boolean
    }

    @Volatile
    private var mForceInterruptState: Byte? = null

    fun init() {
        mForceInterruptState = null
    }

    fun forceInterrupt() {
        mForceInterruptState = 0
    }

    fun copyFile(sourceFile: File, destFile: File, isDirectory: Boolean, listener: OnDealFileListener): Boolean {
        fun internalCopyFile(sourceFile: File, destFile: File, isOtg: Boolean, isSdCard: Boolean, isDfm: Boolean, isDirectory: Boolean): Boolean {
            if (sourceFile.isDirectory) {
                if ((mForceInterruptState == null) && (destFile.exists() || destFile.mkdir())) {
                    listener.addProgress(sourceFile.length())
                    val fileList = sourceFile.listFiles()?.toList()
                    if (fileList == null) {
                        Log.d(TAG, "internalCopyFile ${sourceFile.absolutePath} listFiles is null")
                    } else {
                        fileList.forEach {
                            if (listener.isCancel.invoke()
                                    || !internalCopyFile(it, File(destFile, it.name), isOtg, isSdCard, isDfm, isDirectory)) {
                                return false
                            }
                        }
                    }
                    listener.onSuccess(sourceFile, destFile, isDirectory)
                    return true
                } else {
                    Log.d(TAG, "copyFile: failed to create dir: ${destFile.absolutePath}")
                    // If return true, skip this file, else cancel copy
                    return listener.onError(sourceFile, destFile)
                }
            } else {
                return copyFileData(sourceFile, destFile, isOtg, listener)
            }
        }

        val isOTGPath = VolumeEnvironment.isOTGPath(ContextGetter.context, destFile.absolutePath)
        val isSdcardPath = VolumeEnvironment.isSdcardPath(ContextGetter.context, destFile.absolutePath)
        val isDfmPath = PathUtils.checkIsDfmPath(destFile.absolutePath)
        Log.d(TAG, "copyFile: sourceFile=${sourceFile.absolutePath}, destDir=${destFile.absolutePath}")
        return internalCopyFile(sourceFile, destFile, isOTGPath, isSdcardPath, isDfmPath, isDirectory)
    }

    @Suppress("TooGenericExceptionCaught")
    private fun copyFileData(sourceFile: File, destFile: File, isDirectory: Boolean, listener: OnDealFileListener): Boolean {
        Log.d(TAG, "internalCopyFile: sourceFile=${sourceFile.absolutePath}, destFile=${destFile.absolutePath}")
        val isOtg = VolumeEnvironment.isOTGPath(ContextGetter.context, destFile.absolutePath)
        val isSdCard = VolumeEnvironment.isSdcardPath(ContextGetter.context, destFile.absolutePath)
        val isDfm = PathUtils.checkIsDfmPath(destFile.absolutePath)
        var destRAF: RandomAccessFile? = null
        var srcRAF: RandomAccessFile? = null
        var srcChannel: FileChannel? = null
        var destChannel: FileChannel? = null
        var currentLen: Long = 0
        var copyInterrupt = true
        var hasException = false
        try {
            destRAF = RandomAccessFile(destFile, "rw")
            srcRAF = RandomAccessFile(sourceFile, "r")
            srcChannel = srcRAF.channel
            destChannel = destRAF.channel
            val size = srcChannel.size()
            var length = DEFAULT_LENTH
            val len = ALLOCATE_LENTH
            val buff = ByteBuffer.allocate(len)
            if (size < Int.MAX_VALUE && !(isOtg || isSdCard || isDfm)) {
                while (!listener.isCancel.invoke()) {
                    if (size == currentLen) {
                        copyInterrupt = false
                        break
                    }
                    if (mForceInterruptState != null) { throw InterruptedException("Force interrupted by external") }
                    if (size - currentLen < length) {
                        length = size - currentLen
                    }
                    srcChannel.transferTo(currentLen, length, destChannel)
                    listener.addProgress(length)
                    currentLen += length
                }
            } else {
                while (!listener.isCancel.invoke()) {
                    val readLen = srcChannel.read(buff)
                    if (-1 == readLen) {
                        copyInterrupt = false
                        break
                    }
                    if (mForceInterruptState != null) { throw InterruptedException("Force interrupted by external") }
                    buff.flip()
                    destChannel.write(buff)
                    listener.addProgress(readLen.toLong())
                    buff.clear()
                    currentLen += readLen
                }
                doDestChannelForce(!listener.isCancel.invoke(), destChannel)
            }
        } catch (e: Exception) {
            Log.e(TAG, "copyFileData failed: ${e.message}")
            listener.addProgress(sourceFile.length() - currentLen)
            hasException = true
        } finally { fileCopyDataDoFinally(copyInterrupt, destFile, srcRAF, srcChannel, destChannel, destRAF) }
        return fileCopyDataReturn(listener, hasException, sourceFile, destFile, isDirectory)
    }

    private fun doDestChannelForce(isCancel: Boolean, destChannel: FileChannel) {
        Log.d(TAG, "destChannel.force start")
        if (!isCancel) {
            destChannel.force(true)
        }
        Log.d(TAG, "destChannel.force end")
    }

    private fun fileCopyDataDoFinally(
        copyInterrupt: Boolean,
        destFile: File,
        srcRAF: RandomAccessFile?,
        srcChannel: FileChannel?,
        destChannel: FileChannel?,
        destRAF: RandomAccessFile?
    ) {
        if (copyInterrupt && destFile.exists()) { // Delete the dest file when copy progress interrupt
            destFile.delete()
        }
        quietClose(srcRAF)
        quietClose(srcChannel)
        quietClose(destChannel)
        quietClose(destRAF)
    }

    private fun fileCopyDataReturn(
        listener: OnDealFileListener,
        hasException: Boolean,
        sourceFile: File,
        destFile: File,
        isDirectory: Boolean
    ): Boolean {
        return when {
            listener.isCancel.invoke() -> {
                Log.d(TAG, "copyFileData failed: listener.isCancel = true")
                if (destFile.exists()) {
                    Log.d(TAG, "fileCopyDataReturn isCancel delete")
                    destFile.delete()
                }
                false
            }
            hasException -> listener.onError(sourceFile, destFile)
            else -> {
                listener.onSuccess(sourceFile, destFile, isDirectory)
                true
            }
        }
    }

    private fun quietClose(stream: Closeable?) {
        runCatching {
            stream?.close()
        }.onFailure {
            Log.w(TAG, "quietClose: ${it.message}")
        }
    }

    fun checkInSameFolder(sourceList: List<BaseFileBean>, destDir: BaseFileBean): Pair<Boolean, String?> {
        if (destDir.mData.isNullOrEmpty()) {
            return Pair(false, null)
        }
        var sourceFile: File
        var errorMsg: String? = null
        for (sf in sourceList) {
            if (!TextUtils.isEmpty(sf.mData) && !TextUtils.isEmpty(sf.mDisplayName)) {
                sourceFile = File(destDir.mData, sf.mDisplayName)
                errorMsg = when {
                    sourceFile.absolutePath == sf.mData -> { // 源路径和目标路径相同
                        ContextGetter.context.getString(R.string.paste_same_file_normal)
                    }
                    sf.mData == destDir.mData -> { // 目标文件夹和源文件相同
                        String.format(ContextGetter.context.getString(R.string.paste_error_source_folder), sf.mData)
                    }
                    destDir.mData!!.startsWith(sf.mData!!.plus(File.separator)) -> {
                        String.format(ContextGetter.context.getString(R.string.paste_error_sub_source_folder), sf.mData)
                    }
                    else -> null
                }
                if (!errorMsg.isNullOrEmpty()) {
                    return Pair(true, errorMsg)
                }
            }
        }
        return Pair(false, null)
    }

    fun checkIsBadSdDialog(destPath: String?): Pair<Boolean, Boolean> {
        Log.d(TAG, "checkIsBadSdDialog: $destPath")
        try {
            val manager = ContextGetter.context.getSystemService(Context.STORAGE_SERVICE) as StorageManager
            manager.getStorageVolume(File(destPath))?.let { volume ->
                val type = getOplusReadOnlyType(volume)
                Log.d(TAG, "checkIsBadSdDialog: type=$type")
                var isReadOnlyType = false
                if ((type == 1) || (type == 2) || isReadOnlyType(destPath).also { isReadOnlyType = it }) {
                    return Pair(true, isReadOnlyType)
                }
            }
        } catch (e: NoSuchMethodError) { // ignored
        }
        return Pair(false, second = false)
    }


    fun getOplusReadOnlyType(volume: StorageVolume): Int {
        var type = -1
        if (SdkUtils.isAtLeastV()) {
            // 使用 下沉的接口
            type = OplusStorageVolume(volume).oplusReadOnlyType
        } else {
            // 使用历史接口
            try {
                type = StorageVolumeNative.getOplusReadOnlyType(volume)
            } catch (e: UnSupportedApiVersionException) {
                Log.e(TAG, "getOplusReadOnlyType has error ${e.message}")
            }
        }
        return type
    }

    fun isReadOnlyType(destPath: String?): Boolean {
        if (destPath.isNullOrEmpty() || !VolumeEnvironment.isSdcardPath(ContextGetter.context, destPath)) {
            return false
        }
        val paths = destPath.split("/").toTypedArray()
        if (paths.size < READ_ONLY_TYPE_PATH_SIZE) {
            return false
        }
        val name = paths[2]
        if (TextUtils.isEmpty(name)) {
            return false
        }

        val file = File("/proc/mounts")
        var reader: BufferedReader? = null

        var isContains = false
        runCatching {
            reader = BufferedReader(FileReader(file))
            var tempString: String?
            val condition1 = "/mnt/media_rw/$name"
            val condition2 = "ro,"
            reader?.let {
                while (it.readLine().also { tempString = it } != null) {
                    if (tempString!!.contains(condition1) && tempString!!.contains(condition2)) {
                        isContains = true
                        return@runCatching
                    }
                }
            }
        }.also {
            quietClose(reader)
        }.onFailure {
            Log.d(TAG, "isReadOnlyType failed: ${it.message}")
        }
        return isContains
    }

    fun checkTotalSize(file: File): Long {
        return if (file.isDirectory) {
            var total: Long = file.length() // include the self of this dir
            file.listFiles()?.toList()?.forEach {
                val result = checkTotalSize(it)
                total += result
            }
            total
        } else {
            file.length()
        }
    }

    fun copyToFileByUri(uri: Uri, destPath: Path, useBuffer: Boolean = false, listener: OnDealFileListener): Boolean {
        if (!Files.isDirectory(destPath.parent)) {
            Files.createDirectories(destPath.parent)
        }
        var fileOutputStream: FileOutputStream? = null
        var uriInputStream: InputStream? = null
        val buffer = ByteArray(COPY_BUF_SIZE)
        var bufferedInputStream: BufferedInputStream? = null
        var bufferedOutputStream: BufferedOutputStream? = null

        return runCatching {
            fileOutputStream = FileOutputStream(destPath.toFile())
            uriInputStream = ContextGetter.context.contentResolver.openInputStream(uri)
            if (useBuffer) {
                bufferedInputStream = BufferedInputStream(uriInputStream, COPY_BUF_SIZE)
                bufferedOutputStream = BufferedOutputStream(fileOutputStream, COPY_BUF_SIZE)
            } else {
                bufferedInputStream = BufferedInputStream(uriInputStream)
                bufferedOutputStream = BufferedOutputStream(fileOutputStream)
            }
            var n = 0
            bufferedInputStream?.let { inputStream ->
                while (0 < inputStream.read(buffer).also { n = it }) {
                    bufferedOutputStream?.let { outStream ->
                        outStream.write(buffer, 0, n)
                    }
                    listener.addProgress(n.toLong())
                }
            }
            listener.onSuccess(destPath.toFile(), destPath.toFile(), false)
            true
        }.also {
            try {
                bufferedInputStream?.close()
            } catch (e: IOException) {
                Log.e(TAG, "copyToFileByUri close e=$e")
            }
            try {
                bufferedOutputStream?.close()
            } catch (e: IOException) {
                Log.e(TAG, "copyToFileByUri close e=$e")
            }
            try {
                fileOutputStream?.close()
            } catch (e: IOException) {
                Log.e(TAG, "copyToFileByUri close e=$e")
            }
            try {
                uriInputStream?.close()
            } catch (e: IOException) {
                Log.e(TAG, "copyToFileByUri close e=$e")
            }
        }.getOrElse {
            Log.e(TAG, "copyToFileByUri copy e=$it")
            destPath.deleteIfExists()
            false
        }
    }
}