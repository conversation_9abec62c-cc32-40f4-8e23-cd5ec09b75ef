/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/12/12, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package andes.oplus.documentsreader.dragdrop

import andes.oplus.documentsreader.core.common.CustomToast
import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.parentchild.IMainViewApi
import andes.oplus.documentsreader.dragdrop.helper.FragmentPageDropHelper
import andes.oplus.documentsreader.dragdrop.interfaze.R
import android.app.Activity
import android.view.DragEvent
import android.view.View

class DropListener(private val activity: Activity) : View.OnDragListener {

    companion object {
        const val TAG = "DropListener"
    }

    private val fragmentPageDropHelper: FragmentPageDropHelper by lazy { FragmentPageDropHelper(activity) }

    override fun onDrag(view: View?, event: DragEvent?): Boolean {
        if (event?.action != DragEvent.ACTION_DRAG_LOCATION) {
            Log.d(TAG, "onDrag action: ${event?.action} activity $activity")
        }
        val isMainActivity = Injector.injectFactory<IMainViewApi>()?.isMainActivity(activity) ?: false
        if (isMainActivity) {
            Injector.injectFactory<IMainViewApi>()?.handleDragEvent(activity, view, event)
        } else if (activity is DropInterface) {
            fragmentPageDropHelper.handleDragEvent(activity, view, event)
        } else if (event?.action == DragEvent.ACTION_DROP) {
            CustomToast.showShort(R.string.oplus_doc_drag_not_support_position)
        }
        return true
    }
}