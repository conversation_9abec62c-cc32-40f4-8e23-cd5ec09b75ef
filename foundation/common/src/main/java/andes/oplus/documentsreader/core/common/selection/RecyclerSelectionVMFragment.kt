/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - RecyclerSelectionVMFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2020/06/02
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/06/02    1.0     create
 ****************************************************************/

package andes.oplus.documentsreader.core.common.selection

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.BaseVMFragment
import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.R
import andes.oplus.documentsreader.core.common.dragselection.DefaultDragListener
import andes.oplus.documentsreader.core.common.dragselection.DefaultSelectDelegate
import andes.oplus.documentsreader.core.common.dragselection.FileDragDropScanner
import andes.oplus.documentsreader.core.common.fastscroll.RecyclerViewFastScroller
import andes.oplus.documentsreader.core.common.interfaces.OnGetUIInfoListener
import andes.oplus.documentsreader.core.common.view.CommonRecyclerView
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import androidx.core.view.children
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.dropdrag.OnDragStartListener
import com.oplus.dropdrag.OnItemClickListener
import com.oplus.dropdrag.RecycleSelectionBuilder
import com.oplus.dropdrag.SelectionTracker
import com.oplus.dropdrag.base.DefaultDetailsLookup
import com.oplus.dropdrag.base.DefaultKeyProvider
import com.oplus.dropdrag.dragdrop.DragDropScanner

abstract class RecyclerSelectionVMFragment<VM : SelectionViewModel<out BaseFileBean, out BaseUiModel<out BaseFileBean>>>
    : BaseVMFragment<VM>(), OnDragStartListener, OnItemClickListener<Int>, OnGetUIInfoListener {
    companion object {
        private const val TAG = "RecyclerSelectionVMFragment"
    }

    protected var fragmentRecyclerView: CommonRecyclerView? = null
    protected var fragmentFastScroller: RecyclerViewFastScroller? = null
    protected var fragmentDragScanner: DragDropScanner<BaseFileBean>? = null
    protected var fragmentViewModel: VM? = null

    protected inline fun withFragmentRecyclerView(action: (CommonRecyclerView) -> Unit) {
        fragmentRecyclerView?.let(action)
    }

    protected inline fun runOnFragmentRecyclerView(action: CommonRecyclerView.() -> Unit) {
        fragmentRecyclerView?.run(action)
    }

    protected inline fun withFragmentFastScroller(action: (RecyclerViewFastScroller) -> Unit) {
        fragmentFastScroller?.let(action)
    }

    protected inline fun runOnFragmentFastScroller(action: RecyclerViewFastScroller.() -> Unit) {
        fragmentFastScroller?.run(action)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        fragmentViewModel = createViewModel()
        super.onViewCreated(view, savedInstanceState)
        initSelectionTracker()
    }

    /**
     * Implement create detailed view model in subclass.
     */
    protected abstract fun createViewModel(): VM?

    private fun initSelectionTracker() {
        val recyclerView = fragmentRecyclerView ?: run {
            Log.w(TAG, "initSelectionTracker error: mRecyclerView is null")
            return
        }
        recyclerView.setMIsSupportDragSlide(true)
        runCatching {
            val keyProvider = DefaultKeyProvider(recyclerView)
            val detailsLookup = DefaultDetailsLookup(recyclerView)
            val trackerBuilder = RecycleSelectionBuilder(
                javaClass.name,
                recyclerView,
                DefaultSelectDelegate(fragmentViewModel),
                keyProvider,
                detailsLookup
            )
            trackerBuilder.withSlideSelection(true)
            trackerBuilder.withSlideSelectionStateListener(recyclerView)
            trackerBuilder.withOnDragStartListener(this)
            trackerBuilder.withOnItemClickListener(this)
            recyclerView.layoutManager?.let {
                if ((it is GridLayoutManager) && (it.spanCount > 1)) {
                    SelectionTracker.LAYOUT_TYPE.GRID
                } else SelectionTracker.LAYOUT_TYPE.LIST
            } ?: SelectionTracker.LAYOUT_TYPE.LIST
            trackerBuilder.build()
        }.onFailure { ex ->
            Log.w(TAG, "initSelectionTracker error: $ex")
        }
    }

    override fun onDragStart(e: MotionEvent): Boolean {
        Log.d(TAG, "onDragStart")
        val view = fragmentRecyclerView?.findChildViewUnder(e.x, e.y) ?: return false
        val position = fragmentRecyclerView?.getChildAdapterPosition(view) ?: 0
        if (position == -1) {
            return false
        }
        val dragHoldDownFile = getDragHoldDownFile(position)
        val activityContext = this.activity ?: return false
        val selectList = fragmentViewModel?.getSelectItems() ?: return false
        val viewMode = fragmentViewModel?.getRecyclerViewScanMode()
        val dragHoldDrawable = view.findViewById<ImageView>(R.id.file_list_item_icon)?.drawable

        fragmentDragScanner = FileDragDropScanner(
            activityContext,
            DefaultDragListener(activityContext, view, dragHoldDownFile, dragHoldDrawable, getDropCategoryType()),
            viewMode,
            false
        )
        fragmentDragScanner?.takeIf {
            it.addData(selectList)
        }?.execute()
        Log.d(TAG, "onDragStart end")
        return true
    }

    open fun getDragHoldDownFile(position: Int): BaseFileBean? =
        fragmentViewModel?.uiState?.value?.fileList.takeUnless {
            it.isNullOrEmpty()
        }?.get(position)

    @SuppressLint("ObjectAnimatorBinding")
    fun changeActionModeAnim(
        toolbar: COUIToolbar,
        runnable: Runnable,
        isShowAnimation: Boolean? = true,
        doChildrenAnimation: Boolean? = false
    ) {
        fun doAnimation(view: View) {
            view.clearAnimation()
            val toolbarOutAnimation = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f)
            val toolbarInAnimation = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
            val toolbarAnimatorSet = AnimatorSet().apply {
                duration = KtConstants.ACTION_MODE_ALPHA_DURATION
                interpolator = LinearInterpolator()
                play(toolbarOutAnimation).before(toolbarInAnimation)
            }
            toolbarOutAnimation.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    runnable.run()
                }
            })
            toolbarAnimatorSet.start()
        }
        if (isShowAnimation == false) {
            runnable.run()
            return
        }
        if (doChildrenAnimation == true) {
            toolbar.children.forEach {
                doAnimation(it)
            }
        } else {
            doAnimation(toolbar)
        }
    }

    override fun getViewModel(): VM? = fragmentViewModel

    override fun getRecyclerView(): CommonRecyclerView? = fragmentRecyclerView
}