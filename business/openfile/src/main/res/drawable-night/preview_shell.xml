<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="46dp"
    android:height="46dp"
    android:viewportWidth="46"
    android:viewportHeight="46">
  <path
      android:pathData="M24.887,41.724C33.922,37.635 40.193,29.254 40.193,17.483L40.193,13.79C40.193,12.309 40.193,11.568 39.953,10.929C39.741,10.364 39.395,9.859 38.946,9.456C38.438,9 37.748,8.731 36.368,8.192L26.347,4.284C25.107,3.801 24.487,3.559 23.848,3.463C23.281,3.378 22.705,3.378 22.138,3.463C21.499,3.559 20.879,3.801 19.64,4.284L9.619,8.192C8.238,8.731 7.548,9 7.04,9.456C6.591,9.859 6.246,10.364 6.034,10.929C5.793,11.568 5.793,12.309 5.793,13.79L5.793,17.483C5.793,29.254 12.064,37.635 21.1,41.724C21.684,41.988 21.977,42.121 22.411,42.195C22.734,42.251 23.253,42.251 23.575,42.195C24.01,42.121 24.302,41.988 24.887,41.724Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="36.753"
          android:startY="6.845"
          android:endX="15.644"
          android:endY="38.121"
          android:type="linear">
        <item android:offset="0" android:color="#FF3D2C11"/>
        <item android:offset="1" android:color="#FF1E160A"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M2.793,0.399h41v45h-41zM24.887,41.724C33.922,37.635 40.193,29.254 40.193,17.483L40.193,13.79C40.193,12.309 40.193,11.568 39.953,10.929C39.741,10.364 39.395,9.859 38.946,9.456C38.438,9 37.748,8.731 36.368,8.192L26.347,4.284C25.107,3.801 24.487,3.559 23.848,3.463C23.281,3.378 22.705,3.378 22.138,3.463C21.499,3.559 20.879,3.801 19.64,4.284L9.619,8.192C8.238,8.731 7.548,9 7.04,9.456C6.591,9.859 6.246,10.364 6.034,10.929C5.793,11.568 5.793,12.309 5.793,13.79L5.793,17.483C5.793,29.254 12.064,37.635 21.1,41.724C21.684,41.988 21.977,42.121 22.411,42.195C22.734,42.251 23.253,42.251 23.575,42.195C24.01,42.121 24.302,41.988 24.887,41.724Z"/>
    <path
        android:pathData="M40.193,17.483L37.326,17.483L40.193,17.483ZM24.887,41.724L26.069,44.336L26.069,44.336L24.887,41.724ZM40.193,13.79L43.06,13.79L40.193,13.79ZM5.793,17.483L2.927,17.483L5.793,17.483ZM21.1,41.724L19.918,44.336L19.918,44.336L21.1,41.724ZM5.793,13.79L8.66,13.79L5.793,13.79ZM36.368,8.192L35.326,10.863L36.368,8.192ZM26.347,4.284L25.305,6.955L25.305,6.955L26.347,4.284ZM9.619,8.192L8.577,5.522L8.577,5.522L9.619,8.192ZM19.64,4.284L20.681,6.955L20.681,6.955L19.64,4.284ZM6.034,10.929L8.717,11.938L8.717,11.938L6.034,10.929ZM7.04,9.456L5.125,7.323L5.125,7.323L7.04,9.456ZM23.848,3.463L24.273,0.628L24.273,0.628L23.848,3.463ZM22.138,3.463L21.713,0.628L21.713,0.628L22.138,3.463ZM39.953,10.929L37.27,11.938L37.27,11.938L39.953,10.929ZM38.946,9.456L37.032,11.589L37.032,11.589L38.946,9.456ZM23.575,42.195L24.06,45.021L24.06,45.021L23.575,42.195ZM22.411,42.195L21.926,45.021L21.926,45.021L22.411,42.195ZM37.326,17.483C37.326,28.076 31.763,35.466 23.705,39.112L26.069,44.336C36.082,39.804 43.06,30.431 43.06,17.483L37.326,17.483ZM43.06,17.483L43.06,13.79L37.326,13.79L37.326,17.483L43.06,17.483ZM2.927,17.483C2.927,30.431 9.904,39.804 19.918,44.336L22.281,39.112C14.224,35.466 8.66,28.076 8.66,17.483L2.927,17.483ZM8.66,17.483L8.66,13.79L2.927,13.79L2.927,17.483L8.66,17.483ZM37.409,5.522L27.388,1.613L25.305,6.955L35.326,10.863L37.409,5.522ZM10.66,10.863L20.681,6.955L18.598,1.613L8.577,5.522L10.66,10.863ZM8.66,13.79C8.66,13.012 8.661,12.567 8.684,12.234C8.703,11.936 8.732,11.897 8.717,11.938L3.35,9.92C2.882,11.164 2.927,12.54 2.927,13.79L8.66,13.79ZM8.577,5.522C7.412,5.976 6.114,6.435 5.125,7.323L8.955,11.589C8.923,11.618 8.949,11.577 9.219,11.45C9.521,11.309 9.935,11.146 10.66,10.863L8.577,5.522ZM8.717,11.938C8.767,11.804 8.849,11.684 8.955,11.589L5.125,7.323C4.333,8.033 3.725,8.924 3.35,9.92L8.717,11.938ZM27.388,1.613C26.268,1.176 25.303,0.782 24.273,0.628L23.423,6.298C23.671,6.335 23.946,6.425 25.305,6.955L27.388,1.613ZM20.681,6.955C22.04,6.425 22.315,6.335 22.563,6.298L21.713,0.628C20.683,0.782 19.718,1.176 18.598,1.613L20.681,6.955ZM24.273,0.628C23.424,0.501 22.562,0.501 21.713,0.628L22.563,6.298C22.848,6.255 23.138,6.255 23.423,6.298L24.273,0.628ZM43.06,13.79C43.06,12.54 43.104,11.164 42.636,9.92L37.27,11.938C37.254,11.897 37.283,11.936 37.303,12.234C37.325,12.567 37.326,13.012 37.326,13.79L43.06,13.79ZM35.326,10.863C36.051,11.146 36.465,11.309 36.767,11.45C37.037,11.577 37.064,11.618 37.032,11.589L40.861,7.323C39.872,6.435 38.574,5.976 37.409,5.522L35.326,10.863ZM42.636,9.92C42.262,8.924 41.653,8.033 40.861,7.323L37.032,11.589C37.138,11.684 37.219,11.804 37.27,11.938L42.636,9.92ZM23.705,39.112C23.554,39.181 23.445,39.23 23.35,39.271C23.258,39.311 23.202,39.334 23.165,39.348C23.116,39.366 23.11,39.367 23.09,39.37L24.06,45.021C24.915,44.874 25.535,44.577 26.069,44.336L23.705,39.112ZM19.918,44.336C20.452,44.577 21.072,44.874 21.926,45.021L22.896,39.37C22.876,39.367 22.871,39.366 22.822,39.348C22.785,39.334 22.729,39.311 22.636,39.271C22.541,39.23 22.433,39.181 22.281,39.112L19.918,44.336ZM23.09,39.37C23.128,39.364 23.134,39.365 23.103,39.367C23.076,39.369 23.038,39.37 22.993,39.37C22.949,39.37 22.911,39.369 22.884,39.367C22.853,39.365 22.859,39.364 22.896,39.37L21.926,45.021C22.57,45.131 23.417,45.131 24.06,45.021L23.09,39.37Z"
        android:fillColor="#000001"/>
  </group>
  <path
      android:pathData="M25.771,19.697V16.83C25.771,15.247 24.488,13.964 22.904,13.964V13.964C21.321,13.964 20.038,15.247 20.038,16.83V16.958V19.697"
      android:strokeWidth="1.06476"
      android:fillColor="#00000000"
      android:strokeLineCap="round">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="25.771"
          android:startY="17.24"
          android:endX="25.771"
          android:endY="19.697"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFD88B"/>
        <item android:offset="1" android:color="#FFE6B363"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.843,19.594C17.521,19.594 17.26,19.855 17.26,20.177V26.272C17.26,27.56 18.304,28.603 19.591,28.603H26.396C27.683,28.603 28.727,27.56 28.727,26.272V20.177C28.727,19.855 28.466,19.594 28.144,19.594H17.843ZM24.133,23.383C24.133,23.877 23.841,24.303 23.42,24.498L23.704,25.917C23.724,26.019 23.647,26.113 23.544,26.113H22.305C22.202,26.113 22.124,26.019 22.144,25.917L22.425,24.515C21.985,24.328 21.676,23.892 21.676,23.383C21.676,22.705 22.226,22.154 22.904,22.154C23.583,22.154 24.133,22.705 24.133,23.383Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="22.994"
          android:startY="19.594"
          android:endX="22.994"
          android:endY="28.603"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFD88B"/>
        <item android:offset="1" android:color="#FFF7C56A"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
