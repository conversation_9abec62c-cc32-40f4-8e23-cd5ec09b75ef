package com.recorder.cloudkit.sync.config;

import static org.mockito.Mockito.mock;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.recorder.cloudkit.BuildConfig;
import com.soundrecorder.base.BaseApplication;
import com.recorder.cloudkit.shadows.ShadowFeatureOption;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class CloudConfigUtilTest {
    private Context mContext;
    private MockedStatic<BaseApplication> mMockApplication;

    @Before
    public void setUp() {
        mContext = Mockito.spy(ApplicationProvider.getApplicationContext());
        mMockApplication = Mockito.mockStatic(BaseApplication.class);
        BaseApplication mockApplication = mock(BaseApplication.class);
        mMockApplication.when(() -> BaseApplication.getAppContext()).thenReturn(mockApplication);
    }

    @After
    public void release() {
        if (mMockApplication != null) {
            mMockApplication.close();
            mMockApplication = null;
        }
        mContext = null;
    }

    @Ignore
    @Test
    public void should_return_true_when_checkNeedFetchCloudConfig() {
        boolean needFetch = CloudConfigUtil.checkNeedFetchCloudConfig();
        Assert.assertTrue(needFetch);
    }

    @Test
    public void should_return_false_when_checkNeedFetchCloudConfig() {
        CloudConfigUtil.saveCloudConfigToCache(mContext, Collections.emptyList());
        boolean needFetch = CloudConfigUtil.checkNeedFetchCloudConfig();
        Assert.assertFalse(needFetch);
    }

    @Test
    public void should_return_emptyList_when_getCloudConfigFromCache() {
        List data = CloudConfigUtil.getCloudConfigFromCache(mContext);
        Assert.assertNotNull(data);
        Assert.assertTrue(data.isEmpty());
    }

    @Test
    public void should_return_list_when_getCloudConfigFromCache() {
        List<CloudConfigBean> cloudConfigList = new ArrayList<>();
        cloudConfigList.add(new CloudConfigBean(""));
        cloudConfigList.add(new CloudConfigBean(""));
        CloudConfigUtil.saveCloudConfigToCache(mContext, cloudConfigList);
        List data = CloudConfigUtil.getCloudConfigFromCache(mContext);
        Assert.assertNotNull(data);
        Assert.assertFalse(data.isEmpty());
    }

    @Test
    public void should_return_null_when_getCloudAppH5LinkFromCache() {
        CloudConfigUtil.saveCloudConfigToCache(mContext, Collections.emptyList());
        String data = CloudConfigUtil.getCloudAppH5Link(mContext);
        Assert.assertNull(data);
    }

    @Test
    public void should_return_notNull_when_getCloudAppH5LinkFromCache() {
        List<CloudConfigBean> cloudConfigList = new ArrayList<>();
        CloudConfigBean configBean = new CloudConfigBean("record.ocloudH5");
        configBean.setRegion(Arrays.asList("all"));
        configBean.setBrand(Arrays.asList(BuildConfig.FLAVOR_B.toUpperCase()));
        configBean.setDataBean(new CloudConfigBean.DataBean("http://baidu.com", "h5", 1));
        cloudConfigList.add(configBean);
        CloudConfigUtil.saveCloudConfigToCache(mContext, cloudConfigList);
        String data = CloudConfigUtil.getCloudAppH5Link(mContext);
        Assert.assertNotNull(data);
        Assert.assertEquals("http://baidu.com", data);
    }

    @Test
    public void should_correct_when_getCloudAppH5LinkFromCache() {
        String dataDomestic = CloudConfigUtil.getCloudQuickAppLink(mContext);
        Assert.assertNull(dataDomestic);

        List<CloudConfigBean> cloudConfigList = new ArrayList<>();
        CloudConfigBean configBean = new CloudConfigBean("record.ocloudQuickApp");
        configBean.setRegion(Arrays.asList("all"));
        configBean.setBrand(Arrays.asList(BuildConfig.FLAVOR_B.toUpperCase()));
        configBean.setDataBean(new CloudConfigBean.DataBean("hap://app/com.heytap.cloudservice/page/audio", "quickapp", 1));
        cloudConfigList.add(configBean);
        CloudConfigUtil.saveCloudConfigToCache(mContext, cloudConfigList);
        String data = CloudConfigUtil.getCloudQuickAppLink(mContext);
        Assert.assertNotNull(data);
        Assert.assertEquals("hap://app/com.heytap.cloudservice/page/audio", data);
    }
}
