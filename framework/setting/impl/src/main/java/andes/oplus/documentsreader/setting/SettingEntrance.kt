/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File        : SettingApi
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/7/22
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  lixianyang     2024/7/22       1.0      create
 ***********************************************************************/

package andes.oplus.documentsreader.setting

import andes.oplus.documentsreader.core.common.AppUtils
import andes.oplus.documentsreader.core.common.CommonConstants
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.compat.FeatureCompat
import andes.oplus.documentsreader.core.common.utils.UserManagerUtils
import andes.oplus.documentsreader.interfaces.CollectPrivacyUtils
import andes.oplus.documentsreader.setting.impl.ui.SettingActivity
import andes.oplus.documentsreader.setting.impl.ui.about.SettingAboutActivity
import andes.oplus.documentsreader.setting.impl.ui.autosave.SettingAutosaveActivity
import andes.oplus.documentsreader.setting.impl.ui.opensourcelicense.OpenSourceActivity
import andes.oplus.documentsreader.setting.impl.ui.privacy.SettingPrivacyActivity
import android.app.Activity
import android.content.Context

object SettingEntrance : ISetting {

    private const val TAG = "SettingApi"

    override fun startSettingActivity(activity: Activity?) {
        Log.d(TAG, "startSettingActivity")
        SettingActivity.start(activity)
    }

    override fun startAboutActivity(activity: Activity?) {
        Log.d(TAG, "startAboutActivity")
        SettingAboutActivity.start(activity)
    }

    override fun startPrivacyActivity(activity: Activity?) {
        Log.d(TAG, "startAboutActivity")
        SettingPrivacyActivity.start(activity)
    }

    override fun startOpenSourceLicenseActivity(activity: Activity?) {
        OpenSourceActivity.start(activity)
    }

    override fun startAutoSaveActivity(activity: Activity?) {
        SettingAutosaveActivity.start(activity)
    }

    override fun isShowOWorkFunction(context: Context): Boolean {
        if (FeatureCompat.sIsLightVersion) {
            Log.e("SettingApi", "sIsLightVersion return")
            return false
        }
        if (FeatureCompat.sIsExpRom) {
            Log.e("SettingApi", "sIsExpRom return")
            return false
        }
        val isSystemUser = UserManagerUtils.checkIsSystemUser(context)
        if (isSystemUser.not()) {
            Log.e("SettingApi", "is not SystemUser return")
            return false
        }
        CollectPrivacyUtils.collectPackage(CommonConstants.OWORK)
        return AppUtils.isAppInstalledByPkgName(context, CommonConstants.OWORK)
    }
}