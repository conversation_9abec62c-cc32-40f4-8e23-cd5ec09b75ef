<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/sv_convert_empty"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:scrollbars="none">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_convert_empty"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.45" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/guide_convert_empty"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/guide_convert_empty">

            <com.soundrecorder.common.widget.OSImageView
                android:id="@+id/mEmptyConvertImage"
                android:layout_width="@dimen/os_image_def_width"
                android:layout_height="@dimen/os_image_def_height"
                app:anim_raw_json="@raw/ic_convert_content_empty"
                app:anim_raw_json_night="@raw/ic_convert_content_empty_night"
                app:img_draw="@drawable/ic_convert_content_empty" />

            <TextView
                android:id="@+id/empty_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="@dimen/sp4"
                android:text="@string/convert_text_empty"
                android:textColor="@color/coui_color_label_secondary"
                android:textSize="@dimen/dp16" />

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>