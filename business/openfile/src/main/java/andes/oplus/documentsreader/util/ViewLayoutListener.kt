/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ViewLayoutListener
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/7/15 18:00
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/7/15       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.util

import android.view.View
import android.view.ViewTreeObserver

inline fun View.waitForLayout(crossinline f: View.() -> Unit) {
    viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
        override fun onGlobalLayout() {
            viewTreeObserver.removeOnGlobalLayoutListener(this)
            f()
        }
    })
}