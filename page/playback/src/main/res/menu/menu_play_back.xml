<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <item
        android:id="@+id/speaker"
        android:enabled="true"
        android:icon="@drawable/ic_big_speaker_black"
        android:title="@string/talk_back_speaker_play"
        app:showAsAction="always" />

    <item
        android:id="@+id/export"
        android:enabled="true"
        android:title="@string/share"
        app:showAsAction="never" />

    <item
        android:id="@+id/rename"
        android:enabled="true"
        android:title="@string/rename"
        app:showAsAction="never" />

    <item
        android:id="@+id/rename_new"
        android:enabled="true"
        android:title="@string/rename"
        app:showAsAction="never" >
        <menu>
            <item
                android:id="@+id/custom_naming"
                android:checked="true"
                android:checkable="true"
                android:title="@string/custom_name"
                app:showAsAction="never" />

            <item
                android:id="@+id/smart_naming"
                android:title="@string/intelligent_name"
                app:showAsAction="never"
                android:checkable="true"/>
        </menu>
    </item>

    <item
        android:id="@+id/ai_summary"
        android:enabled="true"
        android:title="@string/ai_summary"
        app:showAsAction="never" />

    <item
        android:id="@+id/move"
        android:enabled="true"
        android:title="@string/move"
        app:showAsAction="never" />
    <item
        android:id="@+id/set_ringtone"
        android:enabled="true"
        android:title="@string/set_as"
        app:showAsAction="never" />
    <item
        android:id="@+id/detail"
        android:enabled="true"
        android:title="@string/talkback_detail"
        app:showAsAction="never" />
    <item
        android:id="@+id/delete"
        android:enabled="true"
        android:title="@string/delete"
        app:showAsAction="never" />
    <item
        android:id="@+id/search"
        android:enabled="true"
        android:visible="false"
        android:title="@string/search"
        app:actionViewClass="com.coui.appcompat.searchview.COUISearchBar"
        app:showAsAction="never" />
</menu>