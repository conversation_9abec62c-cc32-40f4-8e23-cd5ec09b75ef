/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.thread
 * * Version     : 1.0
 * * Date        : 2020/2/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.core.common.thread

import java.util.concurrent.BlockingQueue
import java.util.concurrent.ThreadFactory

class NormalExecutor(
    params: ThreadPoolParams,
    mThreadFactory: ThreadFactory,
    mBlockingQueue: BlockingQueue<Runnable>
) : BaseExecutor(params, mThreadFactory, mBlockingQueue)