/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDiForFileOperate
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.core.fileoperate.di

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.selection.BaseUiModel
import andes.oplus.documentsreader.core.common.selection.SelectionViewModel
import andes.oplus.documentsreader.core.fileoperate.FileOperateApi
import andes.oplus.documentsreader.core.fileoperate.FileOperatorListenerImpl
import andes.oplus.documentsreader.core.fileoperate.NormalFileOperateController
import andes.oplus.documentsreader.core.fileoperate.interfaces.IFileOperateApi
import andes.oplus.documentsreader.core.fileoperate.interfaces.INormalFileOperateController
import androidx.lifecycle.Lifecycle
import com.oplus.assistantscreen.mic.interfaces.KoinModuleMarker
import com.oplus.assistantscreen.mic.interfaces.MarkField
import org.koin.dsl.module

@KoinModuleMarker
class AutoDiForFileOperate {
    @MarkField
    val fileOperate = module {
        single<IFileOperateApi>(createdAtStart = true) {
            FileOperateApi()
        }
    }

    @MarkField
    val normalFileOperate = module {
        factory<INormalFileOperateController> { (lifecycle: Lifecycle, viewModel: SelectionViewModel<out BaseFileBean, out
        BaseUiModel<out BaseFileBean>>) ->
            NormalFileOperateController(lifecycle, viewModel).apply {
                setResultListener(FileOperatorListenerImpl(viewModel))
            }
        }
    }
}