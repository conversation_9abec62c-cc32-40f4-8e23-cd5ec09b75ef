/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.adapter
 * * Version     : 1.0
 * * Date        : 2020/4/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.superapp.ui.view

import andes.oplus.documentsreader.core.common.BaseDiffUtilCallback
import andes.oplus.documentsreader.core.common.BaseSelectionRecycleAdapter
import andes.oplus.documentsreader.core.common.BaseVMActivity
import andes.oplus.documentsreader.core.common.Constants
import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.KtAppUtils
import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.NewFunctionSwitch.SUPPORT_PRIVATE_DIRECTORY_ENTRY_PROMPT
import andes.oplus.documentsreader.core.common.Utils
import andes.oplus.documentsreader.core.common.isActivityAndInvalid
import andes.oplus.documentsreader.core.common.parentchild.MainCategoryItemsBean.QQ
import andes.oplus.documentsreader.core.common.parentchild.MainCategoryItemsBean.WECHAT
import andes.oplus.documentsreader.core.common.thread.ThreadManager
import andes.oplus.documentsreader.core.common.viewholder.DocumentViewHolder
import andes.oplus.documentsreader.interfaces.CollectPrivacyUtils
import andes.oplus.documentsreader.superapp.hepler.SuperAppHelper
import andes.oplus.documentsreader.superapp.impl.R
import andes.oplus.documentsreader.superapp.ui.util.hyperlinkText
import andes.oplus.documentsreader.superapp.ui.util.hyperlinkTextWithApp
import andes.oplus.documentsreader.superapp.ui.view.SuperListViewModel.Companion.VIEW_TYPE_ITEM_FOOTER
import andes.oplus.documentsreader.superapp.ui.view.SuperListViewModel.Companion.VIEW_TYPE_ITEM_LIST
import android.annotation.SuppressLint
import android.content.Context
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.core.view.updatePadding
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.android.documentsui.DocumentUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.Locale

class SuperListAdapter(
    content: Context,
    tabPosition: Int,
    lifecycle: Lifecycle,
    superAppPackage: String?,
    oWork: Boolean
) : BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, SuperFileWrapper>(content),
    LifecycleObserver {

    companion object {
        const val TAG = "SuperListAdapter"
    }

    private var mIsRtl = Utils.isRtl()

    @VisibleForTesting
    var mKeyWord: String? = null

    @VisibleForTesting
    val mSizeCache = HashMap<String, String>()

    @VisibleForTesting
    val mUiHandler: Handler = Handler(Looper.getMainLooper())

    @VisibleForTesting
    var mThreadManager: ThreadManager = ThreadManager(lifecycle)

    @VisibleForTesting
    val mTabPosition: Int = tabPosition

    var mSuperAppPackage: String? = superAppPackage
    var isOWork = oWork

    private var oldSelectionArray = ArrayList<Int>()

    init {
        lifecycle.addObserver(this)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: ArrayList<SuperFileWrapper>, selectionArray: ArrayList<Int>, forceRefresh: Boolean) {
        val oldList = ArrayList(mFiles)
        mFiles = data
        mIsRtl = Utils.isRtl()
        mSelectionArray = selectionArray
        if (oldList.isEmpty() || data.isEmpty() || forceRefresh) {
            fullRefresh(selectionArray)
        } else {
            diffRefresh(oldList, selectionArray)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun fullRefresh(selectionArray: ArrayList<Int>) {
        Log.d(TAG, "fullRefresh")
        checkComputingAndExecute { notifyDataSetChanged() }
        oldSelectionArray = ArrayList(selectionArray)
    }

    private fun diffRefresh(oldList: ArrayList<SuperFileWrapper>, selectionArray: ArrayList<Int>) {
        (mContext as? BaseVMActivity)?.lifecycleScope?.launch {
            val diffResult = withContext(Dispatchers.Default) {
                DiffUtil.calculateDiff(object : BaseDiffUtilCallback<SuperFileWrapper>(oldList, mFiles,
                    { oldItem, newItem ->
                        oldItem.id != VIEW_TYPE_ITEM_FOOTER && newItem.id != VIEW_TYPE_ITEM_FOOTER
                                && getItemKey(oldItem, -1) == getItemKey(newItem, -1) && oldItem.mData == newItem.mData
                    },
                    { oldItem, newItem, _, newItemPosition ->
                        val oldId = getItemKey(oldItem, -1)
                        val newId = getItemKey(newItem, -1)
                        oldItem.mSize == newItem.mSize
                                && oldItem.mDateModified == newItem.mDateModified
                                && newItemPosition != mFiles.size - 1
                                && oldSelectionArray.contains(oldId) == selectionArray.contains(newId)
                    }) {
                })
            }
            checkComputingAndExecute { diffResult.dispatchUpdatesTo(this@SuperListAdapter) }
            oldSelectionArray = ArrayList(selectionArray)
        } ?: fullRefresh(selectionArray)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_ITEM_FOOTER -> {
                FootViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(R.layout.oplus_doc_super_app_footer_item, parent, false)
                )
            }
            else -> {
                DocumentViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(DocumentViewHolder.getLayoutId(), parent, false)
                )
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if ((position < 0) || (position >= itemCount)) {
            return
        }
        if (mContext.isActivityAndInvalid()) {
            Log.d(TAG, "onBindViewHolder: Activity is Destroyed!")
            return
        }
        if (holder is FootViewHolder) {
            holder.loadData(
                mContext,
                KtConstants.SCAN_MODE_LIST,
                isOWork,
                mSuperAppPackage,
                mTabPosition
            )
            return
        }
        val file = mFiles[position]
        when (holder) {
            is DocumentViewHolder -> {
                holder.loadData(
                    mContext,
                    getItemKey(file, position),
                    file,
                    mChoiceMode,
                    mSelectionArray,
                    mSizeCache,
                    mThreadManager,
                    this
                )
                holder.updateDividerVisible(mFiles.size - 1, position)
                holder.itemView.updatePadding(left = itemPaddingLeft, right = itemPaddingRight)
                holder.itemView.isSelected = clickPreviewFile?.mData?.equals(file.mData) ?: false
            }
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        mSizeCache.clear()
        mUiHandler.removeCallbacksAndMessages(null)
    }

    fun setKeyWord(key: String) {
        mKeyWord = key
    }

    override fun getItemKey(item: SuperFileWrapper, position: Int): Int {
        val path = item.mData
        if (path.isNullOrEmpty()) {
            return position
        }

        return path.lowercase(Locale.getDefault()).hashCode()
    }

    override fun getItemId(position: Int): Long {
        return getItemKeyByPosition(position)?.toLong()
            ?: com.oplus.dropdrag.SelectionTracker.NO_LONG_ITEM_ID
    }

    override fun getItemViewType(position: Int): Int {
        val item = getItem(position)
        if (item?.id == VIEW_TYPE_ITEM_FOOTER) {
            return VIEW_TYPE_ITEM_FOOTER
        }
        return VIEW_TYPE_ITEM_LIST
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        setChoiceModeAnimFlag(flag)
    }

    internal class FootViewHolder(
        itemView: View,
    ) : RecyclerView.ViewHolder(itemView) {
        companion object {
            private const val PRIVATE_PATH = "Android"
            private const val PRIVATE_PATH2 = "data"
        }

        fun loadData(
            context: Context,
            scanViewMode: Int,
            isOWork: Boolean,
            superAppPackage: String?,
            tabPosition: Int
        ) {
            if (superAppPackage == null) {
                return
            }
            val footTextView: TextView? = itemView.findViewById(R.id.footer_view)
            if (isOWork) {
                footTextView?.hyperlinkText(
                    context,
                    hyperlinkTextId = R.string.owork_space,
                    all = R.string.owork_space_desc_one,
                    extra = R.string.owork_space_desc_two
                ) {
                    KtAppUtils.startOWork(context, KtAppUtils.ENTRANCE_FILE_MANAGER_WORK_SPACE)
                    CollectPrivacyUtils.collectPackage(KtAppUtils.OWOKR_PACKAGE)
                }
                footTextView?.gravity = Gravity.START
                footTextView?.let {
                    resetItemViewPadding(
                        it,
                        scanViewMode == KtConstants.SCAN_MODE_GRID,
                        tabPosition
                    )
                }
            } else {
                // 功能关闭时，使用旧功能
                if (!SUPPORT_PRIVATE_DIRECTORY_ENTRY_PROMPT) {
                    footTextView?.hyperlinkText(
                        context,
                        R.string.all_system_files,
                        R.string.file_not_found_tips
                    ) {
                        clickAction(context, superAppPackage)
                    }
                    return
                }
                val appStringRes = when (superAppPackage) {
                    WECHAT -> R.string.string_wechat
                    QQ -> R.string.string_qq
                    else -> 0
                }
                if (appStringRes == 0) {
                    footTextView?.hyperlinkText(
                        context,
                        R.string.all_system_files,
                        R.string.file_not_found_tips
                    ) {
                        clickAction(context, superAppPackage)
                    }
                } else {
                    footTextView?.hyperlinkTextWithApp(
                        context,
                        R.string.all_system_files,
                        R.string.privacy_file_not_found_tips,
                        appStringRes
                    ) {
                        clickAction(context, superAppPackage)
                    }
                }
            }
        }

        private fun clickAction(context: Context, superAppPackage: String) {
            val privatePath = SuperAppHelper.getPrivatePath(superAppPackage)
            var path = Environment.getExternalStorageDirectory().absolutePath + File.separator +
                    PRIVATE_PATH + File.separator + PRIVATE_PATH2 + File.separator + superAppPackage
            if (privatePath.isNotEmpty()) {
                path = path + File.separator + privatePath
            }
            runCatching {
                context.startActivity(DocumentUtil.getIntent(path))
            }.onFailure {
                Log.d(TAG, "clickAction $it")
            }
//            OptimizeStatisticsUtil.clickAllSystemFile(OptimizeStatisticsUtil.signalToPage(superAppPackage))
        }

        private fun resetItemViewPadding(textView: TextView, isGrid: Boolean, tabPosition: Int) {
            Log.d(TAG, "resetItemViewPadding -> isGrid = $isGrid")
            textView.let {
                /**
                 * In the video and image tabs, cause [RecyclerView.ItemDecoration] modifies the padding effect,
                 * we need add a 16dp start margin at footView to keep the tip positions consistent.
                 */
                if (isGrid && !isImageOrVideoTab(tabPosition)) {
                    it.setPadding(0, it.paddingTop, it.paddingRight, it.paddingBottom)
                } else {
                    it.setPadding(
                        ContextGetter.context.resources.getDimensionPixelSize(R.dimen.dimen_16dp),
                        it.paddingTop,
                        it.paddingRight,
                        it.paddingBottom
                    )
                }
            }
        }

        @VisibleForTesting
        fun isImageOrVideoTab(tabPosition: Int): Boolean {
            return tabPosition == Constants.TAB_VIDEO || tabPosition == Constants.TAB_IMAGE
        }
    }
}
