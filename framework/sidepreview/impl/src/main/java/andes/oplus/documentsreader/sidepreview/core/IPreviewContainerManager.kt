/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IPreviewContainerManager.kt
 * Description:
 *     The base interface to manage container ui and loading ui for previewing file
 *
 * Version: 1.0
 * Date: 2024-10-25
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-10-25   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.sidepreview.core

import andes.oplus.documentsreader.core.common.uiconfig.type.IUIConfig

internal interface IPreviewContainerManager : IUiConfigChangeObserver {
    /**
     * The container ui for previewing file as default icon
     */
    val defaultContainer: IWidgetDefFileIntroduce

    /**
     * Record the timestamp of changing to loading state.
     * Null is not loading state.
     */
    val startLoadingTime: Long?

    /**
     * Show page as loading state
     */
    fun showAsLoading()

    /**
     * Show page as preview state with its container.
     */
    fun showAsFileContainer(isDefault: Boolean)

    override fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>) {
        // Default do nothing
    }

    fun release()
}