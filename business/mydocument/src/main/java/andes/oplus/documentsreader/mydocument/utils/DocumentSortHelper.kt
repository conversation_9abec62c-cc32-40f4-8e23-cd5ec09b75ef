/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :
 * * Description : 文档排序规则
 * * Version     : 1.0
 * * Date        : 2024/3/21
 * * Author      : W9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.mydocument.utils

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.CategoryHelper
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.compat.FeatureCompat
import andes.oplus.documentsreader.core.common.sort.LastModifiedComparatorGenerator
import andes.oplus.documentsreader.core.common.sort.LastOpenTimeComparatorGenerator
import andes.oplus.documentsreader.core.common.sort.NameToPinyinComparatorGenerator
import andes.oplus.documentsreader.core.common.sort.SizeComparatorGenerator
import andes.oplus.documentsreader.core.common.sort.SortHelper
import andes.oplus.documentsreader.core.common.sort.TypeComparatorGenerator
import java.util.Collections

object DocumentSortHelper {

    const val TAG = "DocumentSortHelper"
    const val TOOL_EXPORT_FOLDER_NAME_FOR_DOMESTIC = "工具导出"
    const val TOOL_EXPORT_FOLDER_NAME_FOR_EXPORT = "Document Tools"

    fun sortFiles(files: List<BaseFileBean>, order: Int, isDesc: Boolean) {
        try {
            Collections.sort(files, getComparator(order, isDesc))
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, e.message)
        }
    }

    private fun getComparator(order: Int, isDesc: Boolean = true): Comparator<BaseFileBean>? {
        Log.d(TAG, "getComparatorCategory order = $order isDesc = $isDesc")
        when (order) {
            SortHelper.FILE_NAME_ORDER -> return NameToPinyinComparatorGenerator.getCategoryComparator(isDesc)

            SortHelper.FILE_TYPE_ORDER -> {
                TypeComparatorGenerator.setCategoryType(CategoryHelper.CATEGORY_DOC)
                return TypeComparatorGenerator.getComparator(TabUtils.getTabTitleSuffixList(), isDesc)
            }

            SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER -> return SizeComparatorGenerator.getComparator(isDesc)

            SortHelper.FILE_TIME_REVERSE_ORDER -> return LastModifiedComparatorGenerator.getComparator(isDesc)

            SortHelper.FILE_LAST_OPEN_TIME_ORDER -> return LastOpenTimeComparatorGenerator.getComparator(isDesc)
        }
        return null
    }

    fun toolExportTopSort(list: MutableList<BaseFileBean>) {
        val toolExportFolderName = if (FeatureCompat.sIsExpRom) {
            TOOL_EXPORT_FOLDER_NAME_FOR_EXPORT
        } else {
            TOOL_EXPORT_FOLDER_NAME_FOR_DOMESTIC
        }
        var toolExportFolderBean: BaseFileBean? = null
        list.forEach {
            if (it.mDisplayName?.equals(toolExportFolderName) == true) {
                toolExportFolderBean = it
                return@forEach
            }
        }
        toolExportFolderBean?.let {
            list.remove(it)
            list.add(0, it)
        }
    }
}