/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/12/11, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package andes.oplus.documentsreader.dragdrop

import android.app.Activity
import android.view.DragEvent
import android.view.View

interface IDropApi {

    fun registerDropListener(activity: Activity)

    fun setItemViewDropTag(itemView: View?, categoryType: Int)

    fun setFolderViewDropTag(folderView: View?, filePath: String?)

    /**
     * @param categoryType 每个页面唯一的类别，可为空，若为空，则提示不可拖拽
     */
    fun setFragmentViewDropTag(fragmentView: View?, categoryType: Int?)

    fun removeViewDropTag(view: View?)

    fun handleItemDropEvent(activity: Activity, rootView: View?, detectView: View?, dragEvent: DragEvent?, dragCallback: DragCallback)
}