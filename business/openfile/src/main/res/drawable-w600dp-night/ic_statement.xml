<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="1073dp"
    android:height="309dp"
    android:viewportWidth="1073"
    android:viewportHeight="309">
    <path
        android:fillColor="#259357"
        android:fillType="evenOdd"
        android:pathData="M620.8,34.3C618.1,31.6 616.7,30.3 615.1,29.3C613.7,28.4 612.2,27.8 610.6,27.4C608.8,27 606.9,27 603.1,27H560.1C553.5,27 550.2,27 547.7,28.3C545.5,29.4 543.7,31.2 542.6,33.4C541.4,35.9 541.4,39.2 541.4,45.7V125.2C541.4,131.8 541.4,135 542.6,137.5C543.7,139.7 545.5,141.5 547.7,142.6C550.2,143.9 553.5,143.9 560.1,143.9H620.1C626.6,143.9 629.9,143.9 632.4,142.6C634.6,141.5 636.4,139.7 637.5,137.5C638.8,135 638.8,131.8 638.8,125.2V62.7C638.8,58.9 638.8,56.9 638.4,55.2C638,53.6 637.3,52 636.5,50.6C635.5,49.1 634.2,47.7 631.5,45L620.8,34.3Z" />
    <path
        android:fillAlpha="0.2"
        android:fillType="evenOdd"
        android:pathData="M620.8,34.3C618.1,31.6 616.7,30.3 615.1,29.3C613.7,28.4 612.2,27.8 610.6,27.4C608.8,27 606.9,27 603.1,27H560.1C553.5,27 550.2,27 547.7,28.3C545.5,29.4 543.7,31.2 542.6,33.4C541.4,35.9 541.4,39.2 541.4,45.7V125.2C541.4,131.8 541.4,135 542.6,137.5C543.7,139.7 545.5,141.5 547.7,142.6C550.2,143.9 553.5,143.9 560.1,143.9H620.1C626.6,143.9 629.9,143.9 632.4,142.6C634.6,141.5 636.4,139.7 637.5,137.5C638.8,135 638.8,131.8 638.8,125.2V62.7C638.8,58.9 638.8,56.9 638.4,55.2C638,53.6 637.3,52 636.5,50.6C635.5,49.1 634.2,47.7 631.5,45L620.8,34.3Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="590.1"
                android:endY="143.9"
                android:startX="541.4"
                android:startY="27"
                android:type="linear">
                <item
                    android:color="#FFFFFFFF"
                    android:offset="0" />
                <item
                    android:color="#00FFFFFF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#00000000"
        android:pathData="M560.1,27.4H603.1C607,27.4 608.8,27.4 610.5,27.8C612.1,28.2 613.6,28.8 614.9,29.7C616.4,30.6 617.8,31.9 620.5,34.6L631.2,45.3C633.9,48 635.2,49.3 636.1,50.9C637,52.2 637.6,53.7 638,55.2C638.4,57 638.4,58.8 638.4,62.7V125.2C638.4,128.5 638.4,130.9 638.2,132.9C638.1,134.8 637.7,136.2 637.1,137.3C636.1,139.5 634.3,141.2 632.2,142.3C631,142.9 629.6,143.2 627.7,143.3C625.8,143.5 623.4,143.5 620.1,143.5H560.1C556.8,143.5 554.3,143.5 552.4,143.3C550.5,143.2 549.1,142.9 547.9,142.3C545.8,141.2 544.1,139.5 543,137.3C542.4,136.2 542.1,134.8 541.9,132.9C541.8,130.9 541.8,128.5 541.8,125.2V45.7C541.8,42.4 541.8,40 541.9,38.1C542.1,36.1 542.4,34.8 543,33.6C544.1,31.5 545.8,29.7 547.9,28.6C549.1,28 550.5,27.7 552.4,27.6C554.3,27.4 556.8,27.4 560.1,27.4Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.08"
        android:strokeColor="#000000" />
    <path
        android:fillColor="#2991FF"
        android:fillType="evenOdd"
        android:pathData="M512.2,34.3C509.5,31.6 508.1,30.3 506.6,29.3C505.2,28.4 503.7,27.8 502.1,27.4C500.3,27 498.4,27 494.6,27H451.5C444.9,27 441.7,27 439.2,28.3C437,29.4 435.2,31.2 434.1,33.4C432.8,35.9 432.8,39.2 432.8,45.7V125.2C432.8,131.8 432.8,135 434.1,137.5C435.2,139.7 437,141.5 439.2,142.6C441.7,143.9 444.9,143.9 451.5,143.9H511.5C518.1,143.9 521.3,143.9 523.8,142.6C526,141.5 527.8,139.7 528.9,137.5C530.2,135 530.2,131.8 530.2,125.2V62.7C530.2,58.9 530.2,56.9 529.8,55.2C529.4,53.6 528.8,52 527.9,50.6C527,49.1 525.6,47.7 522.9,45L512.2,34.3Z" />
    <path
        android:fillAlpha="0.2"
        android:fillType="evenOdd"
        android:pathData="M512.2,34.3C509.5,31.6 508.1,30.3 506.6,29.3C505.2,28.4 503.7,27.8 502.1,27.4C500.3,27 498.4,27 494.6,27H451.5C444.9,27 441.7,27 439.2,28.3C437,29.4 435.2,31.2 434.1,33.4C432.8,35.9 432.8,39.2 432.8,45.7V125.2C432.8,131.8 432.8,135 434.1,137.5C435.2,139.7 437,141.5 439.2,142.6C441.7,143.9 444.9,143.9 451.5,143.9H511.5C518.1,143.9 521.3,143.9 523.8,142.6C526,141.5 527.8,139.7 528.9,137.5C530.2,135 530.2,131.8 530.2,125.2V62.7C530.2,58.9 530.2,56.9 529.8,55.2C529.4,53.6 528.8,52 527.9,50.6C527,49.1 525.6,47.7 522.9,45L512.2,34.3Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="481.5"
                android:endY="143.9"
                android:startX="432.8"
                android:startY="27"
                android:type="linear">
                <item
                    android:color="#FFFFFFFF"
                    android:offset="0" />
                <item
                    android:color="#00FFFFFF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#00000000"
        android:pathData="M451.5,27.4H494.6C498.4,27.4 500.2,27.4 502,27.8C503.5,28.2 505,28.8 506.4,29.7C507.9,30.6 509.2,31.9 511.9,34.6L522.6,45.3C525.3,48 526.6,49.3 527.6,50.9C528.4,52.2 529,53.7 529.4,55.2C529.8,57 529.8,58.8 529.8,62.7V125.2C529.8,128.5 529.8,130.9 529.6,132.9C529.5,134.8 529.2,136.2 528.6,137.3C527.5,139.5 525.8,141.2 523.6,142.3C522.5,142.9 521.1,143.2 519.2,143.3C517.2,143.5 514.8,143.5 511.5,143.5H451.5C448.2,143.5 445.8,143.5 443.8,143.3C441.9,143.2 440.5,142.9 439.4,142.3C437.2,141.2 435.5,139.5 434.4,137.3C433.8,136.2 433.5,134.8 433.4,132.9C433.2,130.9 433.2,128.5 433.2,125.2V45.7C433.2,42.4 433.2,40 433.4,38.1C433.5,36.1 433.8,34.8 434.4,33.6C435.5,31.5 437.2,29.7 439.4,28.6C440.5,28 441.9,27.7 443.8,27.6C445.8,27.4 448.2,27.4 451.5,27.4Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.08"
        android:strokeColor="#000000" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M465.9,104L497.1,104A2.9,2.9 0,0 1,500 106.9L500,106.9A2.9,2.9 0,0 1,497.1 109.8L465.9,109.8A2.9,2.9 0,0 1,463 106.9L463,106.9A2.9,2.9 0,0 1,465.9 104z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M478.6,104H484.4L483.2,90.8C483.2,90.3 483.1,90 483,89.8C482.9,89.7 482.8,89.5 482.6,89.5C482.4,89.4 482.2,89.4 481.7,89.4H481.3C480.8,89.4 480.6,89.4 480.4,89.5C480.2,89.5 480.1,89.7 480,89.8C479.8,90 479.8,90.3 479.8,90.8L478.6,104Z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M462.4,86.4C460.6,86.4 459.7,86.4 459,86.1C458.4,85.7 458,85.2 457.7,84.6C457.4,83.9 457.6,83 457.8,81.2L459.7,65.2C459.9,63.8 460,63 460.3,62.5C460.6,62 461.1,61.6 461.6,61.4C462.2,61.1 462.9,61.1 464.4,61.1L498.6,61.1C500.1,61.1 500.8,61.1 501.4,61.4C501.9,61.6 502.4,62 502.7,62.5C503,63 503.1,63.8 503.3,65.2L505.2,81.2C505.4,83 505.5,83.9 505.3,84.6C505,85.2 504.6,85.7 504,86.1C503.3,86.4 502.4,86.4 500.6,86.4L462.4,86.4Z" />
    <path
        android:fillColor="#D94842"
        android:fillType="evenOdd"
        android:pathData="M620.4,162.3C617.7,159.6 616.4,158.3 614.8,157.3C613.4,156.4 611.9,155.8 610.3,155.4C608.5,155 606.6,155 602.8,155H559.7C553.2,155 549.9,155 547.4,156.3C545.2,157.4 543.4,159.2 542.3,161.4C541,163.9 541,167.2 541,173.7V253.2C541,259.8 541,263 542.3,265.5C543.4,267.7 545.2,269.5 547.4,270.6C549.9,271.9 553.2,271.9 559.7,271.9H619.7C626.3,271.9 629.5,271.9 632.1,270.6C634.3,269.5 636,267.7 637.2,265.5C638.4,263 638.4,259.8 638.4,253.2V190.7C638.4,186.9 638.4,184.9 638,183.2C637.6,181.6 637,180 636.1,178.6C635.2,177.1 633.8,175.7 631.1,173L620.4,162.3Z" />
    <path
        android:fillAlpha="0.2"
        android:fillType="evenOdd"
        android:pathData="M620.4,162.3C617.7,159.6 616.4,158.3 614.8,157.3C613.4,156.4 611.9,155.8 610.3,155.4C608.5,155 606.6,155 602.8,155H559.7C553.2,155 549.9,155 547.4,156.3C545.2,157.4 543.4,159.2 542.3,161.4C541,163.9 541,167.2 541,173.7V253.2C541,259.8 541,263 542.3,265.5C543.4,267.7 545.2,269.5 547.4,270.6C549.9,271.9 553.2,271.9 559.7,271.9H619.7C626.3,271.9 629.5,271.9 632.1,270.6C634.3,269.5 636,267.7 637.2,265.5C638.4,263 638.4,259.8 638.4,253.2V190.7C638.4,186.9 638.4,184.9 638,183.2C637.6,181.6 637,180 636.1,178.6C635.2,177.1 633.8,175.7 631.1,173L620.4,162.3Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="589.7"
                android:endY="271.9"
                android:startX="541"
                android:startY="155"
                android:type="linear">
                <item
                    android:color="#FFFFFFFF"
                    android:offset="0" />
                <item
                    android:color="#00FFFFFF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#00000000"
        android:pathData="M559.7,155.4H602.8C606.6,155.4 608.5,155.4 610.2,155.8C611.7,156.2 613.2,156.8 614.6,157.7C616.1,158.6 617.4,159.9 620.1,162.6L630.8,173.3C633.5,176 634.8,177.3 635.8,178.9C636.6,180.2 637.2,181.7 637.6,183.2C638,185 638,186.8 638,190.7V253.2C638,256.5 638,258.9 637.9,260.9C637.7,262.8 637.4,264.2 636.8,265.3C635.7,267.5 634,269.2 631.9,270.3C630.7,270.9 629.3,271.2 627.4,271.3C625.4,271.5 623,271.5 619.7,271.5H559.7C556.4,271.5 554,271.5 552.1,271.3C550.1,271.2 548.8,270.9 547.6,270.3C545.5,269.2 543.7,267.5 542.6,265.3C542,264.2 541.7,262.8 541.6,260.9C541.4,258.9 541.4,256.5 541.4,253.2V173.7C541.4,170.4 541.4,168 541.6,166.1C541.7,164.1 542,162.8 542.6,161.6C543.7,159.5 545.5,157.7 547.6,156.6C548.8,156 550.1,155.7 552.1,155.6C554,155.4 556.4,155.4 559.7,155.4Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.08"
        android:strokeColor="#000000" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M584.4,201.7L584.6,201.6L584.4,201.7L587.7,207.5L579.4,222H572.9C567.9,222 564.8,227.5 567.3,231.9C568.5,234.1 570.7,235.2 572.9,235.2C575.1,235.2 577.3,234.1 578.6,231.9L581.8,226.2H598.3L601.6,231.9L601.6,231.9C602.8,234.1 605,235.2 607.2,235.2C609.4,235.2 611.6,234.1 612.9,231.9L612.9,231.9C615.4,227.5 612.2,222 607.2,222H600.7L592.4,207.5L595.7,201.7L595.7,201.7C598.2,197.3 595.1,191.8 590.1,191.8C585,191.8 581.9,197.3 584.4,201.7ZM592.1,199.6L592.1,199.6L590.1,203.3L588,199.6L588,199.6C587.4,198.5 587.8,197.6 588,197.2C588.2,196.8 588.8,196 590.1,196C591.3,196 591.9,196.8 592.1,197.2C592.3,197.6 592.8,198.5 592.1,199.6ZM575.1,229.7H575.1L575,229.8C574.4,230.9 573.3,231.1 572.9,231.1C572.5,231.1 571.5,230.9 570.8,229.8L570.8,229.8C570.2,228.7 570.6,227.8 570.8,227.4C571.1,227 571.7,226.2 572.9,226.2H577.1L575.1,229.7ZM603.1,226.2H607.2C608.5,226.2 609.1,227 609.3,227.4C609.5,227.8 609.9,228.7 609.3,229.8L609.3,229.8C608.7,230.9 607.6,231.1 607.2,231.1C606.8,231.1 605.8,230.9 605.2,229.8L605.2,229.8L603.1,226.2ZM595.9,222H584.2L590.1,211.7L595.9,222Z"
        android:strokeWidth="0.584611"
        android:strokeColor="#ffffff" />
    <path
        android:fillColor="#DD5B36"
        android:fillType="evenOdd"
        android:pathData="M403.6,34.3C400.9,31.6 399.6,30.3 398,29.3C396.6,28.4 395.1,27.8 393.5,27.4C391.7,27 389.8,27 386,27H342.9C336.4,27 333.1,27 330.6,28.3C328.4,29.4 326.6,31.2 325.5,33.4C324.2,35.9 324.2,39.2 324.2,45.7V125.2C324.2,131.8 324.2,135 325.5,137.5C326.6,139.7 328.4,141.5 330.6,142.6C333.1,143.9 336.4,143.9 342.9,143.9H402.9C409.5,143.9 412.8,143.9 415.3,142.6C417.5,141.5 419.3,139.7 420.4,137.5C421.6,135 421.6,131.8 421.6,125.2V62.7C421.6,58.9 421.6,56.9 421.2,55.2C420.8,53.6 420.2,52 419.3,50.6C418.4,49.1 417,47.7 414.3,45L403.6,34.3Z" />
    <path
        android:fillAlpha="0.4"
        android:fillType="evenOdd"
        android:pathData="M403.6,34.3C400.9,31.6 399.6,30.3 398,29.3C396.6,28.4 395.1,27.8 393.5,27.4C391.7,27 389.8,27 386,27H342.9C336.4,27 333.1,27 330.6,28.3C328.4,29.4 326.6,31.2 325.5,33.4C324.2,35.9 324.2,39.2 324.2,45.7V125.2C324.2,131.8 324.2,135 325.5,137.5C326.6,139.7 328.4,141.5 330.6,142.6C333.1,143.9 336.4,143.9 342.9,143.9H402.9C409.5,143.9 412.8,143.9 415.3,142.6C417.5,141.5 419.3,139.7 420.4,137.5C421.6,135 421.6,131.8 421.6,125.2V62.7C421.6,58.9 421.6,56.9 421.2,55.2C420.8,53.6 420.2,52 419.3,50.6C418.4,49.1 417,47.7 414.3,45L403.6,34.3Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="372.9"
                android:endY="143.9"
                android:startX="324.2"
                android:startY="27"
                android:type="linear">
                <item
                    android:color="#FFFFFFFF"
                    android:offset="0" />
                <item
                    android:color="#00FFFFFF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#00000000"
        android:pathData="M342.9,27.4H386C389.8,27.4 391.7,27.4 393.4,27.8C394.9,28.2 396.4,28.8 397.8,29.7C399.3,30.6 400.6,31.9 403.3,34.6L414,45.3C416.8,48 418.1,49.3 419,50.9C419.8,52.2 420.4,53.7 420.8,55.2C421.2,57 421.2,58.8 421.2,62.7V125.2C421.2,128.5 421.2,130.9 421.1,132.9C420.9,134.8 420.6,136.2 420,137.3C418.9,139.5 417.2,141.2 415.1,142.3C413.9,142.9 412.5,143.2 410.6,143.3C408.7,143.5 406.2,143.5 402.9,143.5H342.9C339.6,143.5 337.2,143.5 335.3,143.3C333.3,143.2 332,142.9 330.8,142.3C328.7,141.2 326.9,139.5 325.9,137.3C325.3,136.2 324.9,134.8 324.8,132.9C324.6,130.9 324.6,128.5 324.6,125.2V45.7C324.6,42.4 324.6,40 324.8,38.1C324.9,36.1 325.3,34.8 325.9,33.6C326.9,31.5 328.7,29.7 330.8,28.6C332,28 333.3,27.7 335.3,27.6C337.2,27.4 339.6,27.4 342.9,27.4Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.08"
        android:strokeColor="#000000" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M389.3,87.4C390.4,87.4 390.9,87.4 391.3,87.7C391.7,87.9 392.1,88.3 392.2,88.7C392.4,89.1 392.3,89.6 392.2,90.5C391.7,93.7 390.6,96.7 388.8,99.3C386.4,102.8 383.1,105.6 379.2,107.2C375.3,108.8 371,109.3 366.8,108.4C362.6,107.6 358.8,105.6 355.8,102.6C352.8,99.6 350.8,95.7 350,91.6C349.1,87.4 349.6,83.1 351.2,79.2C352.8,75.3 355.5,71.9 359.1,69.6C361.7,67.8 364.7,66.7 367.9,66.2C368.8,66.1 369.3,66 369.7,66.2C370.1,66.3 370.5,66.7 370.7,67C371,67.5 371,68 371,69.1L371,84.3C371,85.4 371,85.9 371.2,86.3C371.4,86.7 371.7,87 372,87.2C372.5,87.4 373,87.4 374.1,87.4H389.3Z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M374.9,65.2C374.9,64.1 374.9,63.6 375.1,63.1C375.3,62.8 375.7,62.4 376.1,62.3C376.6,62.1 377.1,62.2 378,62.3C379.7,62.6 381.4,63 383.1,63.7C385.7,64.8 388,66.4 390,68.4C392,70.3 393.6,72.7 394.7,75.3C395.4,76.9 395.8,78.7 396.1,80.4C396.2,81.3 396.3,81.8 396.1,82.3C396,82.7 395.6,83.1 395.2,83.3C394.8,83.5 394.3,83.5 393.2,83.5L378,83.5C376.9,83.5 376.4,83.5 375.9,83.3C375.6,83.1 375.3,82.8 375.1,82.4C374.9,82 374.9,81.5 374.9,80.4L374.9,65.2Z" />
    <path
        android:fillColor="#FDA024"
        android:fillType="evenOdd"
        android:pathData="M512.6,162.8C509.9,160.1 508.6,158.7 507,157.8C505.6,156.9 504.1,156.3 502.5,155.9C500.7,155.5 498.8,155.5 495,155.5H451.9C445.4,155.5 442.1,155.5 439.6,156.8C437.4,157.9 435.6,159.7 434.5,161.9C433.2,164.4 433.2,167.6 433.2,174.2V253.7C433.2,260.2 433.2,263.5 434.5,266C435.6,268.2 437.4,270 439.6,271.1C442.1,272.4 445.4,272.4 451.9,272.4H511.9C518.5,272.4 521.8,272.4 524.3,271.1C526.5,270 528.2,268.2 529.4,266C530.6,263.5 530.6,260.2 530.6,253.7V191.1C530.6,187.3 530.6,185.4 530.2,183.6C529.8,182 529.2,180.5 528.3,179.1C527.4,177.5 526,176.2 523.3,173.5L512.6,162.8Z" />
    <path
        android:fillAlpha="0.2"
        android:fillType="evenOdd"
        android:pathData="M512.6,162.8C509.9,160.1 508.6,158.7 507,157.8C505.6,156.9 504.1,156.3 502.5,155.9C500.7,155.5 498.8,155.5 495,155.5H451.9C445.4,155.5 442.1,155.5 439.6,156.8C437.4,157.9 435.6,159.7 434.5,161.9C433.2,164.4 433.2,167.6 433.2,174.2V253.7C433.2,260.2 433.2,263.5 434.5,266C435.6,268.2 437.4,270 439.6,271.1C442.1,272.4 445.4,272.4 451.9,272.4H511.9C518.5,272.4 521.8,272.4 524.3,271.1C526.5,270 528.2,268.2 529.4,266C530.6,263.5 530.6,260.2 530.6,253.7V191.1C530.6,187.3 530.6,185.4 530.2,183.6C529.8,182 529.2,180.5 528.3,179.1C527.4,177.5 526,176.2 523.3,173.5L512.6,162.8Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="481.9"
                android:endY="272.4"
                android:startX="433.2"
                android:startY="155.5"
                android:type="linear">
                <item
                    android:color="#FFFFFFFF"
                    android:offset="0" />
                <item
                    android:color="#00FFFFFF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#00000000"
        android:pathData="M451.9,155.9H495C498.8,155.9 500.7,155.9 502.4,156.3C503.9,156.7 505.4,157.3 506.8,158.1C508.3,159.1 509.6,160.4 512.3,163.1L523,173.8C525.7,176.5 527.1,177.8 528,179.3C528.8,180.7 529.4,182.2 529.8,183.7C530.2,185.5 530.2,187.3 530.2,191.1V253.7C530.2,257 530.2,259.4 530.1,261.3C529.9,263.3 529.6,264.6 529,265.8C527.9,267.9 526.2,269.7 524.1,270.8C522.9,271.4 521.5,271.7 519.6,271.8C517.6,272 515.2,272 511.9,272H451.9C448.6,272 446.2,272 444.3,271.8C442.3,271.7 441,271.4 439.8,270.8C437.7,269.7 435.9,267.9 434.8,265.8C434.2,264.6 433.9,263.3 433.8,261.3C433.6,259.4 433.6,257 433.6,253.7V174.2C433.6,170.9 433.6,168.5 433.8,166.5C433.9,164.6 434.2,163.2 434.8,162C435.9,159.9 437.7,158.2 439.8,157.1C441,156.5 442.3,156.2 444.3,156.1C446.2,155.9 448.6,155.9 451.9,155.9Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.05"
        android:strokeColor="#000000" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M508.8,236.5C508.8,235.7 508.1,235 507.2,235H455.8C454.9,235 454.2,235.7 454.2,236.5V236.5C454.2,237.4 454.9,238.1 455.8,238.1H507.2C508.1,238.1 508.8,237.4 508.8,236.5V236.5Z" />
    <path
        android:fillColor="#ffffff"
        android:fillType="evenOdd"
        android:pathData="M473.6,209.8C473.3,210.1 473.3,210.6 473.6,210.9L478.2,215.5C478.5,215.8 478.9,215.8 479.2,215.5C492.5,202.1 502.8,191.1 500.4,188.7C497.9,186.3 486.9,196.5 473.6,209.8Z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M471.8,212.7C472,212.5 472.1,212.4 472.2,212.4C472.3,212.4 472.4,212.4 472.6,212.4C472.7,212.5 472.8,212.6 473,212.8L476.2,216C476.4,216.2 476.6,216.3 476.6,216.5C476.7,216.6 476.7,216.7 476.6,216.8C476.6,216.9 476.5,217 476.3,217.2L467.6,226C466,227.6 463.2,229.5 461,230.5C460.8,230.6 460.7,230.6 460.6,230.6C460.5,230.6 460.4,230.6 460.3,230.6C460.2,230.5 460.1,230.4 459.9,230.2L458.8,229.1C458.6,228.9 458.5,228.8 458.5,228.7C458.4,228.6 458.4,228.5 458.4,228.4C458.4,228.3 458.4,228.2 458.5,228.1C459.5,225.9 461.5,223.1 463.1,221.4L471.8,212.7Z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M456.9,232.1C456.5,231.7 457.4,230.4 457.9,229.8C458,229.6 458.3,229.6 458.4,229.8L459.3,230.6C459.4,230.8 459.4,231 459.2,231.1C458.6,231.6 457.3,232.5 456.9,232.1Z" />
    <path
        android:fillColor="#2CB52E"
        android:fillType="evenOdd"
        android:pathData="M403.4,162.4C400.7,159.7 399.4,158.3 397.8,157.4C396.4,156.5 394.9,155.9 393.3,155.5C391.5,155.1 389.6,155.1 385.8,155.1H342.7C336.2,155.1 332.9,155.1 330.4,156.3C328.2,157.5 326.4,159.2 325.3,161.4C324,163.9 324,167.2 324,173.8V253.3C324,259.8 324,263.1 325.3,265.6C326.4,267.8 328.2,269.6 330.4,270.7C332.9,272 336.2,272 342.7,272H402.7C409.3,272 412.5,272 415.1,270.7C417.3,269.6 419,267.8 420.2,265.6C421.4,263.1 421.4,259.8 421.4,253.3V190.7C421.4,186.9 421.4,185 421,183.2C420.6,181.6 420,180.1 419.1,178.7C418.2,177.1 416.8,175.8 414.1,173.1L403.4,162.4Z" />
    <path
        android:fillAlpha="0.2"
        android:fillType="evenOdd"
        android:pathData="M403.4,162.4C400.7,159.7 399.4,158.3 397.8,157.4C396.4,156.5 394.9,155.9 393.3,155.5C391.5,155.1 389.6,155.1 385.8,155.1H342.7C336.2,155.1 332.9,155.1 330.4,156.3C328.2,157.5 326.4,159.2 325.3,161.4C324,163.9 324,167.2 324,173.8V253.3C324,259.8 324,263.1 325.3,265.6C326.4,267.8 328.2,269.6 330.4,270.7C332.9,272 336.2,272 342.7,272H402.7C409.3,272 412.5,272 415.1,270.7C417.3,269.6 419,267.8 420.2,265.6C421.4,263.1 421.4,259.8 421.4,253.3V190.7C421.4,186.9 421.4,185 421,183.2C420.6,181.6 420,180.1 419.1,178.7C418.2,177.1 416.8,175.8 414.1,173.1L403.4,162.4Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="372.7"
                android:endY="272"
                android:startX="324"
                android:startY="155.1"
                android:type="linear">
                <item
                    android:color="#FFFFFFFF"
                    android:offset="0" />
                <item
                    android:color="#00FFFFFF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#00000000"
        android:pathData="M342.7,155.5H385.8C389.6,155.5 391.5,155.5 393.2,155.9C394.7,156.3 396.2,156.9 397.6,157.7C399.1,158.6 400.4,159.9 403.1,162.7L413.8,173.4C416.5,176.1 417.9,177.4 418.8,178.9C419.6,180.3 420.2,181.8 420.6,183.3C421,185 421,186.9 421,190.7V253.3C421,256.6 421,259 420.9,260.9C420.7,262.8 420.4,264.2 419.8,265.4C418.7,267.5 417,269.3 414.9,270.3C413.7,270.9 412.3,271.2 410.4,271.4C408.4,271.6 406,271.6 402.7,271.6H342.7C339.4,271.6 337,271.6 335.1,271.4C333.1,271.2 331.8,270.9 330.6,270.3C328.5,269.3 326.7,267.5 325.6,265.4C325,264.2 324.7,262.8 324.6,260.9C324.4,259 324.4,256.6 324.4,253.3V173.8C324.4,170.5 324.4,168 324.6,166.1C324.7,164.2 325,162.8 325.6,161.6C326.7,159.5 328.5,157.8 330.6,156.7C331.8,156.1 333.1,155.8 335.1,155.6C337,155.5 339.4,155.5 342.7,155.5Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.05"
        android:strokeColor="#000000" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M396.3,246.6C395.2,246.6 394.7,246.6 394.2,246.4C393.9,246.2 393.6,245.9 393.4,245.6C393.2,245.2 393.2,244.6 393.2,243.5L393.2,215.7C393.2,214.6 393.2,214 393.4,213.6C393.6,213.2 393.9,212.9 394.2,212.8C394.7,212.5 395.2,212.5 396.3,212.5L398.8,212.5C399.9,212.5 400.5,212.5 400.9,212.8C401.3,212.9 401.5,213.2 401.7,213.6C401.9,214 401.9,214.6 401.9,215.7L401.9,243.5C401.9,244.6 401.9,245.2 401.7,245.6C401.5,245.9 401.3,246.2 400.9,246.4C400.5,246.6 399.9,246.6 398.8,246.6L396.3,246.6Z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M379.7,246.6C378.6,246.6 378.1,246.6 377.7,246.4C377.3,246.2 377,245.9 376.8,245.6C376.6,245.2 376.6,244.6 376.6,243.5L376.6,208.8C376.6,207.8 376.6,207.2 376.8,206.8C377,206.4 377.3,206.1 377.7,205.9C378.1,205.7 378.6,205.7 379.7,205.7L382.3,205.7C383.4,205.7 383.9,205.7 384.3,205.9C384.7,206.1 385,206.4 385.2,206.8C385.4,207.2 385.4,207.8 385.4,208.8L385.4,243.5C385.4,244.6 385.4,245.2 385.2,245.6C385,245.9 384.7,246.2 384.3,246.4C383.9,246.6 383.4,246.6 382.3,246.6L379.7,246.6Z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M360.1,244.7L360.1,221.3A1.9,1.9 0,0 1,362 219.4L366.9,219.4A1.9,1.9 0,0 1,368.8 221.3L368.8,244.7A1.9,1.9 0,0 1,366.9 246.6L362,246.6A1.9,1.9 0,0 1,360.1 244.7z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M343.5,244.7L343.5,236.9A1.9,1.9 0,0 1,345.4 235L350.3,235A1.9,1.9 0,0 1,352.3 236.9L352.3,244.7A1.9,1.9 0,0 1,350.3 246.6L345.4,246.6A1.9,1.9 0,0 1,343.5 244.7z" />
    <path
        android:fillColor="#00000000"
        android:pathData="M993.7,27.4H1036.8C1040.6,27.4 1042.4,27.4 1044.2,27.8C1045.7,28.2 1047.2,28.8 1048.6,29.7C1050.1,30.6 1051.4,31.9 1054.1,34.6L1064.8,45.3C1067.5,48 1068.8,49.3 1069.8,50.9C1070.6,52.2 1071.2,53.7 1071.6,55.2C1072,57 1072,58.8 1072,62.7V125.2C1072,128.5 1072,130.9 1071.9,132.9C1071.7,134.8 1071.4,136.2 1070.8,137.3C1069.7,139.5 1068,141.2 1065.9,142.3C1064.7,142.9 1063.3,143.2 1061.4,143.3C1059.4,143.5 1057,143.5 1053.7,143.5H993.7C990.4,143.5 988,143.5 986.1,143.3C984.1,143.2 982.8,142.9 981.6,142.3C979.5,141.2 977.7,139.5 976.6,137.3C976,136.2 975.7,134.8 975.6,132.9C975.4,130.9 975.4,128.5 975.4,125.2V45.7C975.4,42.4 975.4,40 975.6,38.1C975.7,36.1 976,34.8 976.6,33.6C977.7,31.5 979.5,29.7 981.6,28.6C982.8,28 984.1,27.7 986.1,27.6C988,27.4 990.4,27.4 993.7,27.4Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.2">
        <aapt:attr name="android:strokeColor">
            <gradient
                android:endX="955.3"
                android:endY="76.7"
                android:startX="1084.5"
                android:startY="79.5"
                android:type="linear">
                <item
                    android:color="#00FFFFFF"
                    android:offset="0.4" />
                <item
                    android:color="#93C0C0C0"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#00000000"
        android:pathData="M993.7,155.9H1036.8C1040.6,155.9 1042.4,155.9 1044.2,156.3C1045.7,156.7 1047.2,157.3 1048.6,158.1C1050.1,159.1 1051.4,160.4 1054.1,163.1L1064.8,173.8C1067.5,176.5 1068.8,177.8 1069.8,179.3C1070.6,180.7 1071.2,182.2 1071.6,183.7C1072,185.5 1072,187.3 1072,191.1V253.7C1072,257 1072,259.4 1071.9,261.3C1071.7,263.3 1071.4,264.6 1070.8,265.8C1069.7,267.9 1068,269.7 1065.9,270.8C1064.7,271.4 1063.3,271.7 1061.4,271.8C1059.4,272 1057,272 1053.7,272H993.7C990.4,272 988,272 986.1,271.8C984.1,271.7 982.8,271.4 981.6,270.8C979.5,269.7 977.7,267.9 976.6,265.8C976,264.6 975.7,263.3 975.6,261.3C975.4,259.4 975.4,257 975.4,253.7V174.2C975.4,170.9 975.4,168.5 975.6,166.5C975.7,164.6 976,163.2 976.6,162C977.7,159.9 979.5,158.2 981.6,157.1C982.8,156.5 984.1,156.2 986.1,156.1C988,155.9 990.4,155.9 993.7,155.9Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.2">
        <aapt:attr name="android:strokeColor">
            <gradient
                android:endX="955.3"
                android:endY="205.2"
                android:startX="1084.5"
                android:startY="208"
                android:type="linear">
                <item
                    android:color="#00FFFFFF"
                    android:offset="0.4" />
                <item
                    android:color="#93C0C0C0"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#00000000"
        android:pathData="M234.6,155.5H277.6C281.4,155.5 283.3,155.5 285,155.9C286.6,156.3 288.1,156.9 289.4,157.7C290.9,158.6 292.2,159.9 295,162.7L305.7,173.4C308.4,176.1 309.7,177.4 310.6,178.9C311.5,180.3 312.1,181.8 312.4,183.3C312.9,185 312.9,186.9 312.9,190.7V253.3C312.9,256.6 312.9,259 312.7,260.9C312.5,262.8 312.2,264.2 311.6,265.4C310.6,267.5 308.8,269.3 306.7,270.3C305.5,270.9 304.1,271.2 302.2,271.4C300.3,271.6 297.9,271.6 294.6,271.6H234.6C231.3,271.6 228.8,271.6 226.9,271.4C225,271.2 223.6,270.9 222.4,270.3C220.3,269.3 218.6,267.5 217.5,265.4C216.9,264.2 216.6,262.8 216.4,260.9C216.3,259 216.3,256.6 216.3,253.3V173.8C216.3,170.5 216.3,168 216.4,166.1C216.6,164.2 216.9,162.8 217.5,161.6C218.6,159.5 220.3,157.8 222.4,156.7C223.6,156.1 225,155.8 226.9,155.6C228.8,155.5 231.3,155.5 234.6,155.5Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.08"
        android:strokeColor="#ffffff" />
    <path
        android:fillColor="#00000000"
        android:pathData="M234.6,27.4H277.6C281.4,27.4 283.3,27.4 285,27.8C286.6,28.2 288.1,28.8 289.4,29.7C290.9,30.6 292.2,31.9 295,34.6L305.7,45.3C308.4,48 309.7,49.3 310.6,50.9C311.5,52.2 312.1,53.7 312.4,55.2C312.9,57 312.9,58.8 312.9,62.7V125.2C312.9,128.5 312.9,130.9 312.7,132.9C312.5,134.8 312.2,136.2 311.6,137.3C310.6,139.5 308.8,141.2 306.7,142.3C305.5,142.9 304.1,143.2 302.2,143.3C300.3,143.5 297.9,143.5 294.6,143.5H234.6C231.3,143.5 228.8,143.5 226.9,143.3C225,143.2 223.6,142.9 222.4,142.3C220.3,141.2 218.6,139.5 217.5,137.3C216.9,136.2 216.6,134.8 216.4,132.9C216.3,130.9 216.3,128.5 216.3,125.2V45.7C216.3,42.4 216.3,40 216.4,38.1C216.6,36.1 216.9,34.8 217.5,33.6C218.6,31.5 220.3,29.7 222.4,28.6C223.6,28 225,27.7 226.9,27.6C228.8,27.4 231.3,27.4 234.6,27.4Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.08"
        android:strokeColor="#ffffff" />
    <path
        android:fillColor="#00000000"
        android:pathData="M126.7,155.5H169.8C173.6,155.5 175.5,155.5 177.2,155.9C178.7,156.3 180.2,156.9 181.6,157.7C183.1,158.6 184.4,159.9 187.1,162.7L197.8,173.4C200.5,176.1 201.9,177.4 202.8,178.9C203.6,180.3 204.2,181.8 204.6,183.3C205,185 205,186.9 205,190.7V253.3C205,256.6 205,259 204.9,260.9C204.7,262.8 204.4,264.2 203.8,265.4C202.7,267.5 201,269.3 198.9,270.3C197.7,270.9 196.3,271.2 194.4,271.4C192.4,271.6 190,271.6 186.7,271.6H126.7C123.4,271.6 121,271.6 119.1,271.4C117.1,271.2 115.8,270.9 114.6,270.3C112.5,269.3 110.7,267.5 109.6,265.4C109,264.2 108.7,262.8 108.6,260.9C108.4,259 108.4,256.6 108.4,253.3V173.8C108.4,170.5 108.4,168 108.6,166.1C108.7,164.2 109,162.8 109.6,161.6C110.7,159.5 112.5,157.8 114.6,156.7C115.8,156.1 117.1,155.8 119.1,155.6C121,155.5 123.4,155.5 126.7,155.5Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.2">
        <aapt:attr name="android:strokeColor">
            <gradient
                android:endX="74.5"
                android:endY="204.5"
                android:startX="248"
                android:startY="207"
                android:type="linear">
                <item
                    android:color="#8EFDFDFD"
                    android:offset="0" />
                <item
                    android:color="#16FFFFFF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#00000000"
        android:pathData="M126.7,27.4H169.8C173.6,27.4 175.5,27.4 177.2,27.8C178.7,28.2 180.2,28.8 181.6,29.7C183.1,30.6 184.4,31.9 187.1,34.6L197.8,45.3C200.5,48 201.9,49.3 202.8,50.9C203.6,52.2 204.2,53.7 204.6,55.2C205,57 205,58.8 205,62.7V125.2C205,128.5 205,130.9 204.9,132.9C204.7,134.8 204.4,136.2 203.8,137.3C202.7,139.5 201,141.2 198.9,142.3C197.7,142.9 196.3,143.2 194.4,143.3C192.4,143.5 190,143.5 186.7,143.5H126.7C123.4,143.5 121,143.5 119.1,143.3C117.1,143.2 115.8,142.9 114.6,142.3C112.5,141.2 110.7,139.5 109.6,137.3C109,136.2 108.7,134.8 108.6,132.9C108.4,130.9 108.4,128.5 108.4,125.2V45.7C108.4,42.4 108.4,40 108.6,38.1C108.7,36.1 109,34.8 109.6,33.6C110.7,31.5 112.5,29.7 114.6,28.6C115.8,28 117.1,27.7 119.1,27.6C121,27.4 123.4,27.4 126.7,27.4Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.2">
        <aapt:attr name="android:strokeColor">
            <gradient
                android:endX="74.5"
                android:endY="76.4"
                android:startX="248"
                android:startY="78.9"
                android:type="linear">
                <item
                    android:color="#8EFDFDFD"
                    android:offset="0" />
                <item
                    android:color="#16FFFFFF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#00000000"
        android:pathData="M777.7,155.5H820.8C824.6,155.5 826.5,155.5 828.2,155.9C829.7,156.3 831.2,156.9 832.6,157.7C834.1,158.6 835.4,159.9 838.1,162.7L848.8,173.4C851.5,176.1 852.8,177.4 853.8,178.9C854.6,180.3 855.2,181.8 855.6,183.3C856,185 856,186.9 856,190.7V253.3C856,256.6 856,259 855.9,260.9C855.7,262.8 855.4,264.2 854.8,265.4C853.7,267.5 852,269.3 849.9,270.3C848.7,270.9 847.3,271.2 845.4,271.4C843.4,271.6 841,271.6 837.7,271.6H777.7C774.4,271.6 772,271.6 770.1,271.4C768.1,271.2 766.8,270.9 765.6,270.3C763.5,269.3 761.7,267.5 760.6,265.4C760,264.2 759.7,262.8 759.6,260.9C759.4,259 759.4,256.6 759.4,253.3V173.8C759.4,170.5 759.4,168 759.6,166.1C759.7,164.2 760,162.8 760.6,161.6C761.7,159.5 763.5,157.8 765.6,156.7C766.8,156.1 768.1,155.8 770.1,155.6C772,155.5 774.4,155.5 777.7,155.5Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.08"
        android:strokeColor="#ffffff" />
    <path
        android:fillColor="#00000000"
        android:pathData="M777.7,27.4H820.8C824.6,27.4 826.5,27.4 828.2,27.8C829.7,28.2 831.2,28.8 832.6,29.7C834.1,30.6 835.4,31.9 838.1,34.6L848.8,45.3C851.5,48 852.8,49.3 853.8,50.9C854.6,52.2 855.2,53.7 855.6,55.2C856,57 856,58.8 856,62.7V125.2C856,128.5 856,130.9 855.9,132.9C855.7,134.8 855.4,136.2 854.8,137.3C853.7,139.5 852,141.2 849.9,142.3C848.7,142.9 847.3,143.2 845.4,143.3C843.4,143.5 841,143.5 837.7,143.5H777.7C774.4,143.5 772,143.5 770.1,143.3C768.1,143.2 766.8,142.9 765.6,142.3C763.5,141.2 761.7,139.5 760.6,137.3C760,136.2 759.7,134.8 759.6,132.9C759.4,130.9 759.4,128.5 759.4,125.2V45.7C759.4,42.4 759.4,40 759.6,38.1C759.7,36.1 760,34.8 760.6,33.6C761.7,31.5 763.5,29.7 765.6,28.6C766.8,28 768.1,27.7 770.1,27.6C772,27.4 774.4,27.4 777.7,27.4Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.08"
        android:strokeColor="#ffffff" />
    <path
        android:fillColor="#00000000"
        android:pathData="M885.7,155.5H928.8C932.6,155.5 934.5,155.5 936.2,155.9C937.7,156.3 939.2,156.9 940.6,157.7C942.1,158.6 943.4,159.9 946.1,162.7L956.8,173.4C959.5,176.1 960.8,177.4 961.8,178.9C962.6,180.3 963.2,181.8 963.6,183.3C964,185 964,186.9 964,190.7V253.3C964,256.6 964,259 963.9,260.9C963.7,262.8 963.4,264.2 962.8,265.4C961.7,267.5 960,269.3 957.9,270.3C956.7,270.9 955.3,271.2 953.4,271.4C951.4,271.6 949,271.6 945.7,271.6H885.7C882.4,271.6 880,271.6 878.1,271.4C876.1,271.2 874.8,270.9 873.6,270.3C871.5,269.3 869.7,267.5 868.6,265.4C868,264.2 867.7,262.8 867.6,260.9C867.4,259 867.4,256.6 867.4,253.3V173.8C867.4,170.5 867.4,168 867.6,166.1C867.7,164.2 868,162.8 868.6,161.6C869.7,159.5 871.5,157.8 873.6,156.7C874.8,156.1 876.1,155.8 878.1,155.6C880,155.5 882.4,155.5 885.7,155.5Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.2">
        <aapt:attr name="android:strokeColor">
            <gradient
                android:endX="833.5"
                android:endY="204.5"
                android:startX="1007"
                android:startY="207"
                android:type="linear">
                <item
                    android:color="#16FFFFFF"
                    android:offset="0" />
                <item
                    android:color="#8EFDFDFD"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#00000000"
        android:pathData="M885.7,27.4H928.8C932.6,27.4 934.5,27.4 936.2,27.8C937.7,28.2 939.2,28.8 940.6,29.7C942.1,30.6 943.4,31.9 946.1,34.6L956.8,45.3C959.5,48 960.8,49.3 961.8,50.9C962.6,52.2 963.2,53.7 963.6,55.2C964,57 964,58.8 964,62.7V125.2C964,128.5 964,130.9 963.9,132.9C963.7,134.8 963.4,136.2 962.8,137.3C961.7,139.5 960,141.2 957.9,142.3C956.7,142.9 955.3,143.2 953.4,143.3C951.4,143.5 949,143.5 945.7,143.5H885.7C882.4,143.5 880,143.5 878.1,143.3C876.1,143.2 874.8,142.9 873.6,142.3C871.5,141.2 869.7,139.5 868.6,137.3C868,136.2 867.7,134.8 867.6,132.9C867.4,130.9 867.4,128.5 867.4,125.2V45.7C867.4,42.4 867.4,40 867.6,38.1C867.7,36.1 868,34.8 868.6,33.6C869.7,31.5 871.5,29.7 873.6,28.6C874.8,28 876.1,27.7 878.1,27.6C880,27.4 882.4,27.4 885.7,27.4Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.2">
        <aapt:attr name="android:strokeColor">
            <gradient
                android:endX="833.5"
                android:endY="76.4"
                android:startX="1007"
                android:startY="78.9"
                android:type="linear">
                <item
                    android:color="#16FFFFFF"
                    android:offset="0" />
                <item
                    android:color="#8EFDFDFD"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillAlpha="0.6"
        android:fillColor="#00000000"
        android:pathData="M18.7,27.4H61.8C65.6,27.4 67.5,27.4 69.2,27.8C70.7,28.2 72.2,28.8 73.6,29.7C75.1,30.6 76.4,31.9 79.1,34.6L89.8,45.3C92.5,48 93.8,49.3 94.8,50.9C95.6,52.2 96.2,53.7 96.6,55.2C97,57 97,58.8 97,62.7V125.2C97,128.5 97,130.9 96.9,132.9C96.7,134.8 96.4,136.2 95.8,137.3C94.7,139.5 93,141.2 90.9,142.3C89.7,142.9 88.3,143.2 86.4,143.3C84.4,143.5 82,143.5 78.7,143.5H18.7C15.4,143.5 13,143.5 11.1,143.3C9.1,143.2 7.8,142.9 6.6,142.3C4.5,141.2 2.7,139.5 1.6,137.3C1,136.2 0.7,134.8 0.6,132.9C0.4,130.9 0.4,128.5 0.4,125.2V45.7C0.4,42.4 0.4,40 0.6,38.1C0.7,36.1 1,34.8 1.6,33.6C2.7,31.5 4.5,29.7 6.6,28.6C7.8,28 9.1,27.7 11.1,27.6C13,27.4 15.4,27.4 18.7,27.4Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.12">
        <aapt:attr name="android:strokeColor">
            <gradient
                android:endX="-19.7"
                android:endY="76.7"
                android:startX="109.5"
                android:startY="79.5"
                android:type="linear">
                <item
                    android:color="#93C0C0C0"
                    android:offset="0" />
                <item
                    android:color="#00FFFFFF"
                    android:offset="0.8" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillAlpha="0.6"
        android:fillColor="#00000000"
        android:pathData="M18.7,155.9H61.8C65.6,155.9 67.5,155.9 69.2,156.3C70.7,156.7 72.2,157.3 73.6,158.1C75.1,159.1 76.4,160.4 79.1,163.1L89.8,173.8C92.5,176.5 93.8,177.8 94.8,179.3C95.6,180.7 96.2,182.2 96.6,183.7C97,185.5 97,187.3 97,191.1V253.7C97,257 97,259.4 96.9,261.3C96.7,263.3 96.4,264.6 95.8,265.8C94.7,267.9 93,269.7 90.9,270.8C89.7,271.4 88.3,271.7 86.4,271.8C84.4,272 82,272 78.7,272H18.7C15.4,272 13,272 11.1,271.8C9.1,271.7 7.8,271.4 6.6,270.8C4.5,269.7 2.7,267.9 1.6,265.8C1,264.6 0.7,263.3 0.6,261.3C0.4,259.4 0.4,257 0.4,253.7V174.2C0.4,170.9 0.4,168.5 0.6,166.5C0.7,164.6 1,163.2 1.6,162C2.7,159.9 4.5,158.2 6.6,157.1C7.8,156.5 9.1,156.2 11.1,156.1C13,155.9 15.4,155.9 18.7,155.9Z"
        android:strokeWidth="0.835158"
        android:strokeAlpha="0.12">
        <aapt:attr name="android:strokeColor">
            <gradient
                android:endX="-19.7"
                android:endY="205.2"
                android:startX="109.5"
                android:startY="208"
                android:type="linear">
                <item
                    android:color="#93C0C0C0"
                    android:offset="0" />
                <item
                    android:color="#00FFFFFF"
                    android:offset="0.8" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#2991FF"
        android:fillType="evenOdd"
        android:pathData="M730.2,162.3C727.5,159.6 726.1,158.3 724.6,157.3C723.2,156.5 721.7,155.8 720.1,155.4C718.3,155 716.4,155 712.5,155L669.5,155C662.9,155 659.6,155 657.1,156.3C654.9,157.4 653.1,159.2 652,161.4C650.7,163.9 650.7,167.2 650.7,173.7L650.8,253.3C650.8,259.8 650.8,263.1 652,265.6C653.2,267.8 654.9,269.6 657.1,270.7C659.6,272 662.9,272 669.5,272L729.5,272C736.1,272 739.4,272 741.9,270.7C744.1,269.6 745.8,267.8 747,265.6C748.2,263.1 748.2,259.8 748.2,253.3L748.2,190.7C748.2,186.9 748.2,185 747.8,183.2C747.4,181.6 746.8,180.1 745.9,178.7C745,177.1 743.6,175.7 740.9,173L730.2,162.3Z" />
    <path
        android:fillColor="#00000000"
        android:pathData="M669.5,155.3L712.5,155.3C716.4,155.3 718.2,155.3 720,155.7C721.6,156.1 723.1,156.7 724.4,157.6C726,158.5 727.3,159.8 730,162.5L740.7,173.3C743.4,176 744.7,177.3 745.7,178.8C746.5,180.2 747.1,181.7 747.5,183.2C747.9,185 747.9,186.9 747.9,190.7L748,253.3C748,256.6 748,259 747.8,260.9C747.6,262.9 747.3,264.3 746.7,265.5C745.6,267.6 743.9,269.4 741.7,270.5C740.5,271.1 739.1,271.4 737.2,271.5C735.3,271.7 732.8,271.7 729.5,271.7L669.5,271.7C666.2,271.7 663.7,271.7 661.8,271.6C659.9,271.4 658.5,271.1 657.3,270.5C655.1,269.4 653.4,267.6 652.3,265.5C651.7,264.3 651.4,262.9 651.2,261C651,259 651,256.6 651,253.3L651,173.7C651,170.5 651,168 651.2,166.1C651.4,164.1 651.7,162.7 652.3,161.5C653.4,159.4 655.1,157.6 657.3,156.6C658.5,155.9 659.9,155.6 661.8,155.5C663.7,155.3 666.2,155.3 669.5,155.3Z"
        android:strokeWidth="0.584945"
        android:strokeAlpha="0.05"
        android:strokeColor="#000000" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M669.3,205.7L688.8,205.7A1.9,1.9 0,0 1,690.7 207.7L690.7,211.6A1.9,1.9 0,0 1,688.8 213.5L669.3,213.5A1.9,1.9 0,0 1,667.3 211.6L667.3,207.7A1.9,1.9 0,0 1,669.3 205.7z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M669.3,222.3L708.3,222.3A1.9,1.9 0,0 1,710.2 224.2L710.2,228.1A1.9,1.9 0,0 1,708.3 230.1L669.3,230.1A1.9,1.9 0,0 1,667.3 228.1L667.3,224.2A1.9,1.9 0,0 1,669.3 222.3z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M667.3,242C667.3,240.9 667.3,240.3 667.5,239.9C667.7,239.6 668,239.3 668.4,239.1C668.8,238.9 669.4,238.9 670.4,238.9L728.5,238.9C729.6,238.9 730.2,238.9 730.6,239.1C731,239.3 731.3,239.6 731.5,239.9C731.7,240.3 731.7,240.9 731.7,242L731.7,243.5C731.7,244.6 731.7,245.2 731.5,245.6C731.3,246 731,246.3 730.6,246.4C730.2,246.7 729.6,246.7 728.5,246.7L670.4,246.7C669.4,246.7 668.8,246.7 668.4,246.4C668,246.3 667.7,246 667.5,245.6C667.3,245.2 667.3,244.6 667.3,243.5L667.3,242Z" />
    <path
        android:fillColor="#868EA3"
        android:fillType="evenOdd"
        android:pathData="M729.2,34.3C726.5,31.6 725.2,30.3 723.6,29.3C722.2,28.4 720.7,27.8 719.1,27.4C717.3,27 715.4,27 711.6,27L668.5,27C661.9,27 658.6,27 656.1,28.3C653.9,29.4 652.1,31.2 651,33.4C649.7,35.9 649.7,39.2 649.7,45.7L649.8,125.3C649.8,131.8 649.8,135.1 651,137.6C652.2,139.8 653.9,141.6 656.1,142.7C658.6,144 661.9,144 668.5,144L728.5,144C735.1,144 738.4,144 740.9,142.7C743.1,141.6 744.9,139.8 746,137.6C747.3,135.1 747.3,131.8 747.3,125.3L747.2,62.7C747.2,58.9 747.2,57 746.8,55.2C746.4,53.6 745.8,52.1 744.9,50.7C744,49.1 742.6,47.7 739.9,45L729.2,34.3Z" />
    <path
        android:fillColor="#00000000"
        android:pathData="M668.5,27.3L711.6,27.3C715.4,27.3 717.2,27.3 719,27.7C720.6,28.1 722.1,28.7 723.4,29.6C725,30.5 726.3,31.8 729,34.5L739.7,45.2C742.4,48 743.8,49.3 744.7,50.8C745.5,52.2 746.2,53.7 746.5,55.2C747,57 747,58.9 747,62.7L747,125.3C747,128.6 747,131 746.8,132.9C746.6,134.9 746.3,136.3 745.7,137.5C744.6,139.6 742.9,141.4 740.7,142.5C739.5,143.1 738.1,143.4 736.2,143.6C734.3,143.7 731.8,143.7 728.5,143.7L668.5,143.7C665.2,143.7 662.8,143.7 660.8,143.6C658.9,143.4 657.5,143.1 656.3,142.5C654.1,141.4 652.4,139.6 651.3,137.5C650.7,136.3 650.4,134.9 650.2,133C650,131 650,128.6 650,125.3L650,45.7C650,42.4 650,40 650.2,38.1C650.4,36.1 650.7,34.7 651.3,33.5C652.4,31.4 654.1,29.6 656.3,28.5C657.5,27.9 658.9,27.6 660.8,27.5C662.7,27.3 665.2,27.3 668.5,27.3Z"
        android:strokeWidth="0.585"
        android:strokeAlpha="0.05"
        android:strokeColor="#000000" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M668.3,77.7L728.7,77.7A2,2 0,0 1,730.7 79.7L730.7,83.6A2,2 0,0 1,728.7 85.5L668.3,85.5A2,2 0,0 1,666.3 83.6L666.3,79.7A2,2 0,0 1,668.3 77.7z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M668.3,94.3L728.7,94.3A2,2 0,0 1,730.7 96.2L730.7,100.1A2,2 0,0 1,728.7 102.1L668.3,102.1A2,2 0,0 1,666.3 100.1L666.3,96.2A2,2 0,0 1,668.3 94.3z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M668.3,110.9L714.1,110.9A2,2 0,0 1,716.1 112.8L716.1,116.7A2,2 0,0 1,714.1 118.7L668.3,118.7A2,2 0,0 1,666.3 116.7L666.3,112.8A2,2 0,0 1,668.3 110.9z" />
    <path
        android:fillColor="#ffffff"
        android:pathData="M557,83.4C557,81.2 557,80 557.4,79.2C557.8,78.4 558.4,77.8 559.2,77.4C560,77 561.2,77 563.4,77H616.6C618.8,77 620,77 620.8,77.4C621.6,77.8 622.2,78.4 622.6,79.2C623,80 623,81.2 623,83.4V113.2C623,115.4 623,116.5 622.6,117.4C622.2,118.1 621.6,118.8 620.8,119.1C620,119.6 618.8,119.6 616.6,119.6H563.4C561.2,119.6 560,119.6 559.2,119.1C558.4,118.8 557.8,118.1 557.4,117.4C557,116.5 557,115.4 557,113.2V83.4Z" />
    <path
        android:fillColor="#259357"
        android:pathData="M564,83.1L576,83.1A1,1 45,0 1,577 84.1L577,88.2A1,1 45,0 1,576 89.2L564,89.2A1,1 45,0 1,563 88.2L563,84.1A1,1 45,0 1,564 83.1z" />
    <path
        android:fillColor="#259357"
        android:pathData="M564,95.3L576,95.3A1,1 45,0 1,577 96.3L577,100.3A1,1 45,0 1,576 101.3L564,101.3A1,1 45,0 1,563 100.3L563,96.3A1,1 45,0 1,564 95.3z" />
    <path
        android:fillColor="#259357"
        android:pathData="M564,107.4L576,107.4A1,1 45,0 1,577 108.4L577,112.5A1,1 45,0 1,576 113.5L564,113.5A1,1 45,0 1,563 112.5L563,108.4A1,1 45,0 1,564 107.4z" />
    <path
        android:fillColor="#259357"
        android:pathData="M584,83.1L596,83.1A1,1 45,0 1,597 84.1L597,88.2A1,1 45,0 1,596 89.2L584,89.2A1,1 45,0 1,583 88.2L583,84.1A1,1 45,0 1,584 83.1z" />
    <path
        android:fillColor="#259357"
        android:pathData="M584,95.3L596,95.3A1,1 45,0 1,597 96.3L597,100.3A1,1 45,0 1,596 101.3L584,101.3A1,1 45,0 1,583 100.3L583,96.3A1,1 45,0 1,584 95.3z" />
    <path
        android:fillColor="#259357"
        android:pathData="M584,107.4L596,107.4A1,1 45,0 1,597 108.4L597,112.5A1,1 45,0 1,596 113.5L584,113.5A1,1 45,0 1,583 112.5L583,108.4A1,1 45,0 1,584 107.4z" />
    <path
        android:fillColor="#259357"
        android:pathData="M604,83.1L616,83.1A1,1 45,0 1,617 84.1L617,88.2A1,1 45,0 1,616 89.2L604,89.2A1,1 45,0 1,603 88.2L603,84.1A1,1 45,0 1,604 83.1z" />
    <path
        android:fillColor="#259357"
        android:pathData="M604,95.3L616,95.3A1,1 45,0 1,617 96.3L617,100.3A1,1 45,0 1,616 101.3L604,101.3A1,1 45,0 1,603 100.3L603,96.3A1,1 45,0 1,604 95.3z" />
    <path
        android:fillColor="#259357"
        android:pathData="M604,107.4L616,107.4A1,1 45,0 1,617 108.4L617,112.5A1,1 45,0 1,616 113.5L604,113.5A1,1 45,0 1,603 112.5L603,108.4A1,1 45,0 1,604 107.4z" />
</vector>
