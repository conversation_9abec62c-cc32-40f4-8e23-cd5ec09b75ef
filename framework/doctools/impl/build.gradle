plugins {
    id 'com.android.library'
    id 'kotlin-kapt'
}
apply from: rootProject.file("script/compile.gradle")
apply from: rootProject.file("script/unitTest.gradle")

android {
    namespace 'andes.oplus.documentsreader.doctools.impl'
}

dependencies {
    implementation project(":foundation:common")
    implementation project(':framework:statistics')
    implementation project(":framework:doctools:interface")
    implementation project(':framework:privacy:interface')
    implementation project(':framework:aidepend:interface')
    implementation project(':framework:formatConvert:interface')
    implementation project(':framework:documentapi:interface')
    implementation project(':framework:cloudconfig:interface')

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)

    // Koin
    implementation(libs.koin.android)
    implementation(libs.oplus.assistantscreen.plugin.mic.api)
    implementation(libs.oplus.assistantscreen.plugin.mic.interfaces)
    kapt(libs.oplus.assistantscreen.plugin.mic.processor)

    //base包为必须引用的包，prop_versionName需保持一致
    implementation(libs.coui.appcompat.core)
    implementation(libs.coui.appcompat.recyclerview)
    implementation(libs.coui.appcompat.card)
    implementation(libs.coui.appcompat.preference)
    implementation(libs.coui.appcompat.panel)
    implementation(libs.coui.appcompat.scrollbar)
    implementation(libs.coui.appcompat.toolbar)
    implementation(libs.coui.appcompat.poplist)
    compileOnly(libs.oplus.sdk.addon) { artifact { type = "aar" } }
}