/*********************************************************************************
 * Copyright (C), 2008-2024, Oplus, All rights reserved.
 *
 * File: - <PERSON>AskController.kt
 * Description:
 *     To control AiAskContainer
 *
 * Version: 1.0
 * Date: 2024-11-26
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-11-26   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.search.controller

import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.SdkUtils
import android.view.View
import android.view.ViewTreeObserver
import com.oplus.dmp.sdk.aiask.AIAskState
import com.oplus.dmp.sdk.aiask.data.QueryScope
import com.oplus.support.dmp.aiask.AIAskContainer
import com.oplus.support.dmp.aiask.interfaces.AIAskInterface

/**
 * <<记忆大师——问答[控件/能力]接入手册>>
 * https://odocs.myoas.com/docs/25q5MZOVl6C9o7qD/ 访问密码 vhdrtp
 */
class AiAskController private constructor(
    private val container: AIAskContainer,
    private val searchController: SearchController
) : AIAskInterface {

    private var currentVisibility = container.visibility
    private var aiAskState: AIAskState<*, *>? = null

    private val globalLayoutListener = ViewTreeObserver.OnGlobalLayoutListener {
        if (currentVisibility != container.visibility) {
            visibilityCallback?.invoke(container.visibility)
        }
        currentVisibility = container.visibility
    }

    private var visibilityCallback: ((state: Int) -> Unit)? = null

    init {
        container.register(this)
        container.viewTreeObserver.addOnGlobalLayoutListener(globalLayoutListener)
    }

    fun updateQueryText(query: String?) {
        Log.d(TAG, "updateQueryText: ${query?.run { "length=$length" }}")
        val newText = query.takeUnless { it.isNullOrEmpty() } ?: ""
        container.startAIAsk(newText)
    }

    fun observerVisibilityChange(callback: ((state: Int) -> Unit)) {
        visibilityCallback = callback
    }

    fun isAiAskContainerShowed(): Boolean {
        return container.visibility == View.VISIBLE
    }

    /**
     * 退出搜索界面的时候调用，不然会泄露
     */
    fun release() {
        container.viewTreeObserver.removeOnGlobalLayoutListener(globalLayoutListener)
        visibilityCallback = null
    }

    fun getAIAskState(): AIAskState<*, *>? {
        return aiAskState
    }

    /**
     * AIAskContainer内部会自行根据可用性状态设置visibility，
     * 此处仅输出日志监控状态，或后续执行必要的连带布局适配逻辑等
     */
    override fun onAIAskStateChanged(state: AIAskState<*, *>) {
        Log.d(TAG, "onAIAskStateChanged: $state")
        aiAskState = state
    }

    override fun onQueryChanged(query: String): Boolean {
        Log.d(TAG, "onQueryChanged: ${query.run { "length=$length" }}")
        searchController.setQuery(query, true)
        return super.onQueryChanged(query)
    }

    /*
    override fun getQueryScope(): Array<QueryScope> {
        // 这里可以指定检索范围，除非有产品明确说明，不然不要指定
        return arrayOf(QueryScope.DOCUMENT)
    }
    */

    override fun getPreferScope(): Array<QueryScope> {
        return arrayOf(QueryScope.DOCUMENT)
    }

    companion object {
        private const val TAG = "AiAskController"

        @JvmStatic
        fun loadAiAsk(
            containerStub: View,
            searchController: SearchController
        ): AiAskController? {
            if (!SdkUtils.isAtLeastT()) {
                Log.d(TAG, "loadAiAsk: unsupported sdk level")
                return null
            }
            Log.d(TAG, "loadAiAsk: inflate AiAskContainer")
            return AiAskController(
                container = containerStub as AIAskContainer,
                searchController = searchController
            )
        }
    }
}