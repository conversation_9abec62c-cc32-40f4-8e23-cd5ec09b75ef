/*********************************************************************
 * * Copyright (C), 2010-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : FunctionSwitchConfig
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/7/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   keweiwei                2024/7/27       1      create
 ***********************************************************************/
package andes.oplus.documentsreader.core.cloudconfig.bean

import androidx.annotation.Keep

@Keep
data class FunctionSwitchConfig(
    val functionName: String,
    val appVersion: Long,
    val oplusVersion: Int,
    val enable: Boolean
) {
    /*
     * functionName 是功能名，目前包含以下功能，请看注释，配置字段务必和下面的取值保持一致
     * 功能限定的app版本号，传0，则表示全版本覆盖
     * 功能限定的os版本号，传0，则表示全版本覆盖
     * 是否可用，true为可用，false为不可用，当配置了false，就不管app版本号和OS版本号了
     */
    @Keep
    companion object {
        //pdf转word功能
        const val FUNCTION_PDF2WORD = "pdf2word"

        //pdf转excel功能
        const val FUNCTION_PDF2EXCEL = "pdf2excel"

        //pdf转ppt功能
        const val FUNCTION_PDF2PPT = "pdf2ppt"

        //pages转word功能
        const val FUNCTION_PAGE2WORD = "page2word"

        //number转excel功能
        const val FUNCTION_NUMBER2EXCEL = "number2excel"

        //key转ppt功能
        const val FUNCTION_KEY2PPT = "key2ppt"

        //iwork转pdf
        const val FUNCTION_IWORK2PDF = "iwork2pdf"

        //office转pdf
        const val FUNCTION_OFFICE2PDF = "office2pdf"

        //提取公式功能
        const val FUNCTION_EXTRA_FORMULA = "extraFormula"

        //AI摘要功能
        const val FUNCTION_AI_DOC_SUMMARY = "ai_doc_summary"

        //AI图文摘要功能
        const val FUNCTION_GRAPHICAL_ABSTRACT = "graphical_abstract"

        //全文翻译功能
        const val FUNCTION_AI_DOC_TRANSLATE = "ai_doc_translate"

        //图表提取功能
        const val FUNCTION_AI_DOC_CHART_EXTRACT = "ai_doc_chart_extract"

        //AI改写功能
        const val FUNCTION_AI_DOC_REWRITE = "ai_doc_rewrite"

        //扫描文档
        const val FUNCTION_SCAN_DOC = "scan_doc"

        //还原试卷
        const val FUNCTION_PAPER_RESTORE = "paper_restore"

        //文档问答
        const val FUNCTION_DOC_BREENO_QA = "breeno_qa"

        //AI搜索控件
        const val FUNCTION_AI_ASK = "ai_ask"

        //脑图摘要
        const val FUNCTION_AI_XMIND = "ai_xmind"
    }
}




