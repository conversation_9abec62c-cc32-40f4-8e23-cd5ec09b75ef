package com.soundrecorder.base.utils;


import static org.mockito.Mockito.mock;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.shadows.ShadowFeatureOption;
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.mockito.Mockito;
import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class NetworkUtilsTest {

    private Context mContext;
    private Context mMockContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mMockContext = mock(Context.class);
        Mockito.when(mMockContext.getApplicationContext()).thenReturn(mMockContext);
    }

    @After
    public void tearDown() {
        mContext = null;
        mMockContext = null;
    }

    @Test
    public void should_false_when_isNetworkInvalid() {
        boolean networkInvalid = NetworkUtils.isNetworkInvalid(mContext);
        System.out.println("networkInvalid=" + networkInvalid);
        Assert.assertFalse(networkInvalid);

        networkInvalid = NetworkUtils.isNetworkInvalid(null);
        Assert.assertTrue(networkInvalid);

        ConnectivityManager mockManager = mock(ConnectivityManager.class);
        Mockito.when(mMockContext.getSystemService(Context.CONNECTIVITY_SERVICE)).thenReturn(null, mockManager);
        networkInvalid = NetworkUtils.isNetworkInvalid(mMockContext);
        Assert.assertTrue(networkInvalid);

        Mockito.when(mockManager.getActiveNetworkInfo()).thenReturn(null);
        networkInvalid = NetworkUtils.isNetworkInvalid(mMockContext);
        Assert.assertTrue(networkInvalid);

        NetworkInfo networkInfo = mock(NetworkInfo.class);
        Mockito.when(networkInfo.isAvailable()).thenReturn(false, true, true);
        Mockito.when(mockManager.getActiveNetworkInfo()).thenReturn(networkInfo);
        networkInvalid = NetworkUtils.isNetworkInvalid(mMockContext);
        Assert.assertTrue(networkInvalid);

        Mockito.when(networkInfo.isConnected()).thenReturn(false, true);
        networkInvalid = NetworkUtils.isNetworkInvalid(mMockContext);
        Assert.assertTrue(networkInvalid);
        networkInvalid = NetworkUtils.isNetworkInvalid(mMockContext);
        Assert.assertFalse(networkInvalid);
    }

    @Test
    public void should_equals_network_mobile_when_getNetState() {
        int netState = NetworkUtils.getNetState(mContext);
        Assert.assertEquals(NetworkUtils.NETWORK_MOBILE, netState);

        netState = NetworkUtils.getNetState(null);
        Assert.assertEquals(NetworkUtils.NETWORK_NONE, netState);

        ConnectivityManager mockManager = mock(ConnectivityManager.class);
        Mockito.when(mMockContext.getSystemService(Context.CONNECTIVITY_SERVICE)).thenReturn(null, mockManager, mockManager);
        netState = NetworkUtils.getNetState(mMockContext);
        Assert.assertEquals(NetworkUtils.NETWORK_NONE, netState);

        Mockito.when(mockManager.getActiveNetworkInfo()).thenReturn(null);
        netState = NetworkUtils.getNetState(mMockContext);
        Assert.assertEquals(NetworkUtils.NETWORK_NONE, netState);

        NetworkInfo networkInfo = mock(NetworkInfo.class);
        Mockito.when(networkInfo.isAvailable()).thenReturn(false, true);
        Mockito.when(mockManager.getActiveNetworkInfo()).thenReturn(networkInfo);
        netState = NetworkUtils.getNetState(mMockContext);
        Assert.assertEquals(NetworkUtils.NETWORK_NONE, netState);
        netState = NetworkUtils.getNetState(mContext);
        Assert.assertEquals(NetworkUtils.NETWORK_MOBILE, netState);
    }
}
