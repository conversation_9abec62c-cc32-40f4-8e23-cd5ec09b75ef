/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PermissionUtils.java
 Description:
 Version: 1.0
 Date: 2009-12-15
 Author: zhanghr
 -----------Revision History-----------
 <author> <date> <version> <desc>
 zhanghr 2009-12-15 create
 */

package com.soundrecorder.common.permission;

import static android.content.Intent.FLAG_GRANT_WRITE_URI_PERMISSION;
import static android.content.pm.PackageManager.PERMISSION_GRANTED;
import static android.provider.MediaStore.MediaColumns.OWNER_PACKAGE_NAME;
import static com.soundrecorder.common.db.MediaDBUtils.BASE_URI;
import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.net.Uri;
import android.os.Binder;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.provider.Settings;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.StorageManager;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.OS12FeatureUtil;
import com.soundrecorder.common.R;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.utils.FunctionOption;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class PermissionUtils {
    public static final int SHOULD_SHOW_USER_NOTICE = 0;
    public static final int SHOULD_SHOW_ALL_FILE_PERMISSION = 1;
    public static final int SHOULD_REQUEST_PERMISSIONS = 2;

    public static final int PERMISSION_NOT_APPLY = 0;
    public static final int PERMISSION_APPLIED = 1;

    public static final int REQUEST_CODE_BROWSE_FILE = 101;
    public static final int REQUEST_CODE_RECORDER = 102;
    public static final int REQUEST_CODE_EDIT_RECORD = 103;
    public static final int REQUEST_CODE_PLAY_BACK = 104;
    public static final int REQUEST_CODE_SETTINGS = 105;
    public static final int REQUEST_CODE_TRANSPARENT = 106;
    public static final int REQUEST_CODE_PERMISSION_TRANSPARENT = 107;
    public static final int REQUEST_CODE_MINI_RECORDER = 108;
    //进入录制页申请权限被拒绝退出到首页，同时通知权限页被拒绝,此时在首页展示权限受阻弹窗
    public static final int RESULT_NOTIFICATION_PERMISSION_DENIED = 0x17;


    public static final String ACTION_FILE_PERMISSION = "com.oplus.soundrecorder.action_file_permission";

    public static final String RECORD_AUDIO = Manifest.permission.RECORD_AUDIO;
    public static final String READ_EXTERNAL_STORAGE = Manifest.permission.READ_EXTERNAL_STORAGE;
    public static final String WRITE_EXTERNAL_STORAGE = Manifest.permission.WRITE_EXTERNAL_STORAGE;
    @SuppressLint("InlinedApi")
    public static final String READ_MEDIA_IMAGES = Manifest.permission.READ_MEDIA_IMAGES;
    @SuppressLint("InlinedApi")
    public static final String READ_MEDIA_AUDIO = Manifest.permission.READ_MEDIA_AUDIO;
    @SuppressLint("InlinedApi")
    public static final String POST_NOTIFICATIONS = Manifest.permission.POST_NOTIFICATIONS;
    @SuppressLint("InlinedApi")
    public static final String READ_MEDIA_VISUAL_USER_SELECTED = Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED;
    public static final String[] STORAGE_PERMISSIONS_Q = {Manifest.permission.READ_EXTERNAL_STORAGE, WRITE_EXTERNAL_STORAGE};

    public static final String FIRST_APPLY_STATEMENT_DIALOG = "first_apply_statement_dialog";
    public static final String FIRST_START_APP = "first_app_start";
    public static final String FIRST_APPLY_PERMISSION_CONVERT = "first_apply_permission_convert";
    public static final String FIRST_APPLY_PERMISSION_USER_UPDATE = "first_apply_permission_user_update";
    /*功能介绍弹窗SP*/
    public static final String FIRST_APPLY_FUNCTION_INTRO_DIALOG = "first_apply_function_intro_dialog";
    private static final String TAG = "PermissionUtils";
    private static final String CONVERT_PERMISSION = "convert_permission";
    private static final String FIRST_APPLY_PERMISSION_STORAGE = "first_apply_permission_storage";
    private static final String FIRST_APPLY_PERMISSION_NETWORK = "first_apply_permission_network";
    private static final String FIRST_SHOW_NOTIFICATION_PERMISSION_SNACK_BAR = "first_show_notification_permission_snack_bar";
    private static final String FIRST_SHOW_NOTIFICATION_PERMISSION_DIALOG = "first_show_notification_permission_dialog";
    private static final String FIRST_CHECK_ALL_PERMISSIONS_ON_RESUME_FOR_BROWSE_FILE = "first_check_all_permissions_on_resume_for_browse_file";
    private static final String ACTION_SAFE_CENTER_PERMISSION = "oplus.intent.action.PERMISSION_APP_DETAIL";

    public static String READ_AUDIO_PERMISSION() {
        if (BaseUtil.isAndroidTOrLater()) {
            return READ_MEDIA_AUDIO;
        } else {
            return READ_EXTERNAL_STORAGE;
        }
    }

    public static String READ_IMAGE_PERMISSION() {
        if (BaseUtil.isAndroidTOrLater()) {
            return READ_MEDIA_IMAGES;
        } else {
            return READ_EXTERNAL_STORAGE;
        }
    }

    public static boolean hasStoragePermission() {
        return hasPermissions(STORAGE_PERMISSIONS_Q);
    }
    /**
     * check麦克风权限
     */
    public static boolean hasRecordAudioPermission() {
        Context context = BaseApplication.getAppContext();
        return ContextCompat.checkSelfPermission(context, RECORD_AUDIO) == PERMISSION_GRANTED;
    }

    public static boolean hasReadAudioPermission() {
        return hasReadAudioPermission(BaseApplication.getAppContext());
    }

    /**
     * check是否有READ_AUDIO_PERMISSION()权限
     * 授予了所有文件管理权限，默认未true
     */
    public static boolean hasReadAudioPermission(Context context) {
        if (hasAllFilePermission()) {
            return true;
        }
        if (context == null) {
            return false;
        }
        if (BaseUtil.isAndroidROrLater()) {
            int status = context.checkSelfPermission(READ_AUDIO_PERMISSION());
            return status == PERMISSION_GRANTED;
        } else {
            return hasStoragePermission();
        }
    }

    /**
     * check是否有READ_IMAGE_PERMISSION()权限
     * 授予了所有文件管理权限，默认未true
     */
    public static boolean hasReadImagesPermission() {
        if (hasAllFilePermission()) {
            DebugUtil.i(TAG, "hasAllFilePermission");
            return true;
        }
        boolean flag = ContextCompat.checkSelfPermission(BaseApplication.getAppContext(), READ_IMAGE_PERMISSION()) == PERMISSION_GRANTED;
        DebugUtil.i(TAG, READ_IMAGE_PERMISSION() + ":" + flag);
        return flag;
    }

    public static boolean hasReadVisualUserSelectedPermission() {
        boolean flag = ContextCompat.checkSelfPermission(BaseApplication.getAppContext(), READ_MEDIA_VISUAL_USER_SELECTED) == PERMISSION_GRANTED;
        DebugUtil.i(TAG, READ_MEDIA_VISUAL_USER_SELECTED + ":" + flag);
        return flag;
    }

    public static boolean hasOnlyReadVisualUserSelectedPermission() {
        if (!BaseUtil.isAndroidUOrLater()) {
            return false;
        }
        if (hasAllFilePermission()) {
            DebugUtil.i(TAG, "hasAllFilePermission");
            return false;
        }
        boolean readImagesPermission = ContextCompat.checkSelfPermission(BaseApplication.getAppContext(),
                READ_IMAGE_PERMISSION()) == PERMISSION_GRANTED;
        boolean readVisualUserSelectedPermission = ContextCompat.checkSelfPermission(BaseApplication.getAppContext(),
                READ_MEDIA_VISUAL_USER_SELECTED) == PERMISSION_GRANTED;
        if (!readImagesPermission && readVisualUserSelectedPermission) {
            return true;
        }
        return false;
    }

    /**
     * check AndroidT上READ_MEDIA_IMAGES权限是否未调用过系统requestPermissions
     *
     * @see PermissionUtils#requestPermissions(Activity, String[], int)
     */
    public static boolean shouldFirstRequestReadImagesPermission(@NonNull Activity activity) {
        if (!BaseUtil.isAndroidTOrLater()) {
            return false;
        }
        if (hasReadImagesPermission()) {
            return false;
        }
        return StorageManager.getIntPref(activity, READ_MEDIA_IMAGES, PERMISSION_NOT_APPLY) == PERMISSION_NOT_APPLY;
    }

    /**
     * @param permissions      权限数组
     * @param filterReadImages 是否过滤READ_MEDIA_IMAGES权限
     * @return true表示整个权限数组都授予了权限，反之可能某一个或所有都没授予权限
     */
    public static boolean hasAllPermissions(String[] permissions, boolean filterReadImages) {
        for (String permission : permissions) {
            if (filterReadImages && permission.equals(READ_MEDIA_IMAGES)) continue;
            int status = BaseApplication.getAppContext().checkSelfPermission(permission);
            if (status == PackageManager.PERMISSION_DENIED) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param permissions 权限数组
     * @return true 表示整个权限数组中至少有一个权限用户拒绝了一次，且没有一个权限拒绝了两次
     */
    public static boolean hasPermissionsRationale(Activity activity, String[] permissions) {
        boolean anyRationale = false;
        for (String permission : permissions) {
            boolean hasRequest = hasRequestPermissions(permission);
            boolean hasRationale = ActivityCompat.shouldShowRequestPermissionRationale(activity, permission);
            if (hasRequest && !hasRationale) {
                return false;
            } else {
                if (hasRationale) {
                    anyRationale = true;
                }
            }
        }
        return anyRationale;
    }

    /**
     * 主要用户首页权限空UI，“开启”按钮和”设置“按钮显示
     *
     * @param activity 当前activity容器
     * @return true READ_AUDIO_PERMISSION()权限用户已经拒绝过一个了
     */
    public static boolean hasReadAudioPermissionRationale(@NonNull Activity activity) {
        return activity.shouldShowRequestPermissionRationale(READ_AUDIO_PERMISSION());
    }

    /**
     * 判断权限数组中是否有一个权限用户已经拒绝了两次
     *
     * @param activity    当前的Activity容器
     * @param permissions 权限数组
     * @return true表示再次调用系统的requestPermission请求权限，不会再次显示系统权限弹窗
     */
    public static boolean cannotRequestPermissions(Activity activity, String[] permissions) {
        for (String permission : permissions) {
            boolean hasRequest = hasRequestPermissions(permission);
            boolean hasRationale = ActivityCompat.shouldShowRequestPermissionRationale(activity, permission);
            DebugUtil.i(TAG, "hasRequest:" + hasRequest + ",hasRationale:" + hasRationale);
            if (hasRequest && !hasRationale) {
                return true;
            }
        }
        return false;
    }

    /**
     * 申请权限，并记录每一个权限的已经申请过的状态，sp保存
     *
     * @param activity    当前activity容器
     * @param permissions 权限数组
     * @param requestCode 申请权限的requestCode
     */
    public static void requestPermissions(final @NonNull Activity activity,
                                          final @NonNull String[] permissions, final @IntRange(from = 0) int requestCode) {
        ActivityCompat.requestPermissions(activity, permissions, requestCode);
    }

    /**
     * 使用SP保存每一个权限申请过的状态
     *
     * @param permissions 权限数组
     */
    public static void putRequestPermissions(String[] permissions) {
        for (String permission : permissions) {
            StorageManager.setIntPrefApply(BaseApplication.getAppContext(), permission, PERMISSION_APPLIED);
        }
    }

    public static boolean hasRequestPermissions(String permission) {
        Context context = BaseApplication.getAppContext();
        int def = PERMISSION_NOT_APPLY;
        //兼容AndroidT权限适配需求之前的逻辑
        if (READ_AUDIO_PERMISSION().equals(permission)) {
            def = StorageManager.getIntPref(context, FIRST_APPLY_PERMISSION_STORAGE, PERMISSION_NOT_APPLY);
        }
        def = StorageManager.getIntPref(context, permission, def);
        return def == PERMISSION_APPLIED;
    }

    /**
     * 非AndroidT申请单个存储权限
     * AndroidT申请单个音乐和其他音频权限
     * androidQ申请存储权限
     *
     * @param activity BrowseFile
     */
    public static void requestReadAudioPermissionForBrowseFile(Activity activity) {
        String[] permissions = BaseUtil.isAndroidROrLater() ? new String[]{READ_AUDIO_PERMISSION()} : STORAGE_PERMISSIONS_Q;
        requestPermissions(activity, permissions, PermissionUtils.REQUEST_CODE_BROWSE_FILE);
    }

    /**
     * 是否已经申请过通知权限
     */
    public static boolean hasRequestNotificationPermission(Context context) {
        return StorageManager.getIntPref(context, POST_NOTIFICATIONS, PERMISSION_NOT_APPLY) == PERMISSION_APPLIED;
    }

    /**
     * 判断是否需要显示受阻弹窗
     * 没有通知权限 && 已经申请过权限 && 没有显示过snackBar 都满足 return true
     */
    public static boolean isNeedShowNotificationPermissionSnackBar(Context context) {
        //已经申请过通知权限了，但是被拒绝了，并且没有显示过snackBar
        return PermissionUtils.hasRequestNotificationPermission(context) && !PermissionUtils.hasNotificationPermission()
                && !hasShowNotificationPermissionSnackBar(context);
    }

    /**
     * 判断是否显示过通知权限受阻弹窗
     */
    public static boolean hasShowNotificationPermissionSnackBar(Context context) {
        return StorageManager.getBooleanPref(context, PermissionUtils.FIRST_SHOW_NOTIFICATION_PERMISSION_SNACK_BAR, false);
    }

    /**
     * 设置已经显示过通知权限受阻弹窗
     */
    public static void setHasShowNotificationPermissionSnackBar(Context context) {
        StorageManager.setBooleanPref(context, PermissionUtils.FIRST_SHOW_NOTIFICATION_PERMISSION_SNACK_BAR, true);
    }

    /**
     * 判断是否显示过通知权限受阻弹窗-mutidialog
     */
    public static boolean hasShowNotificationPermissionDialog(Context context) {
        return StorageManager.getBooleanPref(context, PermissionUtils.FIRST_SHOW_NOTIFICATION_PERMISSION_DIALOG, false);
    }

    /**
     * 设置已经显示过通知权限受阻弹窗-dialog
     */
    public static void setHasShowNotificationPermissionDialog(Context context) {
        StorageManager.setBooleanPref(context, PermissionUtils.FIRST_SHOW_NOTIFICATION_PERMISSION_DIALOG, true);
    }

    /**
     * 检查是否有通知权限，
     */
    public static void checkNotificationPermission(Activity activity) {
        if (activity == null) {
            return;
        }
        if (hasNotificationPermission()) {
            DebugUtil.i(TAG, "有通知权限");
        } else {
            if (!BaseUtil.isAndroidTOrLater()) {
                return;
            }
            if (hasRequestNotificationPermission(activity)) {
                DebugUtil.d(TAG, "已经申请过通知权限，不再申请！");
            } else {
                requestPermissions(activity, new String[]{PermissionUtils.POST_NOTIFICATIONS}, PermissionUtils.REQUEST_CODE_PERMISSION_TRANSPARENT);
            }
        }
    }

    public static boolean hasNotificationPermission() {
        return hasPermissions(new String[]{POST_NOTIFICATIONS});
    }

    public static boolean hasPermissions(String[] permissions) {
        for (String permission : permissions) {
            Context context = BaseApplication.getAppContext();
            int status = context.checkSelfPermission(permission);
            if (status == PackageManager.PERMISSION_DENIED) {
                return false;
            }
        }
        return true;
    }

    public static void goToAppSettingConfigurePermissions(Activity activity) {
        try {
            Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK & Intent.FLAG_ACTIVITY_CLEAR_TASK);
            Uri uri = Uri.fromParts("package", activity.getPackageName(), null);
            intent.setData(uri);
            activity.startActivity(intent);
        } catch (Exception e) {
            DebugUtil.e(TAG, "goToAppSettingConfigurePermissions, e=" + e);
        }
    }

    public static void goToAppSettingConfigurePermissions(Activity activity, ArrayList<String> permissionList) {
        if (BaseUtil.isEXP() || permissionList == null || !OS12FeatureUtil.isColorOS11Point3Later()) {
            // 外销/OS小于等于11.3走老方法
            goToAppSettingConfigurePermissions(activity);
        } else {
            // 内销权限受阻后跳转至权限管控接口
            goToAuthorityControlSettingConfigurePermissions(activity, permissionList);
        }
    }

    /**
     * 权限受阻后跳转至权限管控接口设置受阻权限（带权限设置提示功能，方便用户快速找到需要设置的权限）
     *
     * @param activity
     * @param permissionList
     */
    private static void goToAuthorityControlSettingConfigurePermissions(Activity activity, ArrayList<String> permissionList) {
        try {
            DebugUtil.d(TAG, "goToAuthorityControlSettingConfigurePermissions permissionList =" + permissionList);
            //指定Action
            Intent intent = new Intent(ACTION_SAFE_CENTER_PERMISSION);

            Bundle bundle = new Bundle();
            bundle.putStringArrayList("permissionList", permissionList);
            //传入待修改权限的包名
            bundle.putString("packageName", BaseUtil.getPackageName());
            //传入待修改权限的应用名（app_name）
            bundle.putString("packageLabel", activity.getString(R.string.app_name_main));
            intent.putExtras(bundle);
            activity.startActivity(intent);
        } catch (Exception e) {
            DebugUtil.e(TAG, "goToAuthorityControlSettingConfigurePermissions, e=" + e);
            // 拉起失败降级执行老方法
            goToAppSettingConfigurePermissions(activity);
        }
    }

    public static void goToAppAllFileAccessConfigurePermissions(@NonNull Activity activity) {
        try {
            Intent intent = new Intent();
            intent.setAction(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
            intent.setData(Uri.parse("package:" + activity.getPackageName()));
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            activity.startActivity(intent);
        } catch (Exception e) {
            DebugUtil.e(TAG, "goToAppAllFileAccessConfigurePermissions, e=" + e);
        }
    }

    public static int getNextAction() {
        Context ctx = BaseApplication.getApplication();
        //卖场模式，默认不显示用户需知
        int defaultValue = (BaseUtil.isEXP() || FunctionOption.isSupportSellMode()) ? SHOULD_SHOW_ALL_FILE_PERMISSION : SHOULD_SHOW_USER_NOTICE;
        return StorageManager.getIntPref(ctx, PermissionUtils.FIRST_APPLY_STATEMENT_DIALOG, defaultValue);
    }

    public static void setNextActionForRequestPermission(@NonNull Context context) {
        StorageManager.setIntPrefApply(context.getApplicationContext(), PermissionUtils.FIRST_APPLY_STATEMENT_DIALOG, PermissionUtils.SHOULD_REQUEST_PERMISSIONS);
    }

    public static void setNextActionForShowAllFileDialog(@NonNull Context context) {
        StorageManager.setIntPrefApply(context.getApplicationContext(), PermissionUtils.FIRST_APPLY_STATEMENT_DIALOG, PermissionUtils.SHOULD_SHOW_ALL_FILE_PERMISSION);
    }

    public static void setStatementUpdateStatus(@NonNull Context context) {
        StorageManager.setIntPrefApply(context.getApplicationContext(), PermissionUtils.FIRST_APPLY_PERMISSION_USER_UPDATE, PermissionUtils.PERMISSION_APPLIED);
    }

    public static boolean checkPermissionUpdateAlreadyApply(@NonNull Context context) {
        //卖场模式，默认已显示过云同步升级弹窗
        boolean hasSellMode = FunctionOption.isSupportSellMode();
        int defaultValue = hasSellMode ? PermissionUtils.PERMISSION_APPLIED : PermissionUtils.PERMISSION_NOT_APPLY;
        Context appContext = context.getApplicationContext();
        return StorageManager.getIntPref(appContext, PermissionUtils.FIRST_APPLY_PERMISSION_USER_UPDATE, defaultValue) == PermissionUtils.PERMISSION_APPLIED;
    }

    /**
     * 设置已弹过功能介绍弹窗
     * @param context
     */
    public static void setFirstApplyFunctionIntroDialog(@NonNull Context context) {
        StorageManager.setIntPrefApply(context.getApplicationContext(),
                PermissionUtils.FIRST_APPLY_FUNCTION_INTRO_DIALOG, PermissionUtils.PERMISSION_APPLIED);
    }

    /**
     * 获取是否已经弹过功能介绍弹窗
     * @param context
     * @return true：已经弹过了
     */
    public static boolean checkFunctionIntroDialogAlreadyApply(@NonNull Context context) {
        //卖场模式，默认已显示过云同步升级弹窗
        boolean hasSellMode = FunctionOption.isSupportSellMode();
        int defaultValue = hasSellMode ? PermissionUtils.PERMISSION_APPLIED : PermissionUtils.PERMISSION_NOT_APPLY;
        Context appContext = context.getApplicationContext();
        return StorageManager.getIntPref(appContext, PermissionUtils.FIRST_APPLY_FUNCTION_INTRO_DIALOG, defaultValue)
                == PermissionUtils.PERMISSION_APPLIED;
    }


    public static boolean isStatementConvertGranted(@NonNull Context context) {
        //卖场模式，默认授予转文本权限
        boolean hasSellMode = FunctionOption.isSupportSellMode();
        int defaultValue = hasSellMode ? PermissionUtils.PERMISSION_APPLIED : PermissionUtils.PERMISSION_NOT_APPLY;
        Context appContext = context.getApplicationContext();
        return PermissionUtils.PERMISSION_APPLIED == StorageManager.getIntPref(appContext, PermissionUtils.FIRST_APPLY_PERMISSION_CONVERT, defaultValue);
    }

    /**
     * 判断是否有网络权限
     * 如果有写过网络权限sp的值 {@link #FIRST_APPLY_PERMISSION_NETWORK}，则使用对应的值
     * 如果没有写过网络权限sp的值，由于有撤销转文本网络权限的功能，因此不能直接使用转文本网络权限，
     * 而是将转文本网络权限的值 {@link #FIRST_APPLY_PERMISSION_CONVERT}写入到网络权限sp中
     *
     * @see #isStatementConvertGranted(Context)
     */
    public static boolean isNetWorkGranted(@NonNull Context context) {
        //卖场模式，默认授予网络权限
        boolean hasSellMode = FunctionOption.isSupportSellMode();
        int defaultValue = hasSellMode ? PermissionUtils.PERMISSION_APPLIED : PermissionUtils.PERMISSION_NOT_APPLY;
        Context appContext = context.getApplicationContext();
        boolean hasNetWork = StorageManager.containsKey(appContext, PermissionUtils.FIRST_APPLY_PERMISSION_NETWORK);
        boolean isGranted;
        if (hasNetWork) {
            // 如果sp有写过网络权限，则用网络权限的值
            isGranted = PermissionUtils.PERMISSION_APPLIED == StorageManager.getIntPref(appContext, PermissionUtils.FIRST_APPLY_PERMISSION_NETWORK, defaultValue);
        } else {
            //如果没有写过，则将转文本网络权限的sp值写入网络权限中，防止撤销时转文本网络权限被删除
            isGranted = isStatementConvertGranted(appContext);
            setNetWorkGrantedStatus(appContext, isGranted);
        }
        //DebugUtil.i(TAG, "isNetWorkGranted hasNetWork = " + hasNetWork + " granted:" + isGranted);
        return isGranted;
    }

    public static void setConvertGrantedStatus(@NonNull Context context) {
        StorageManager.setIntPrefApply(context.getApplicationContext(), PermissionUtils.FIRST_APPLY_PERMISSION_CONVERT, PermissionUtils.PERMISSION_APPLIED);
        boolean grant = isStatementConvertGranted(BaseApplication.getAppContext());
        DebugUtil.e(CONVERT_PERMISSION, "setConvertGrantedStatus in " + LocalDateTime.now() + ", and convert permission is open" + grant);

    }

    public static void clearConvertGrantedStatus() {
        StorageManager.setIntPrefApply(BaseApplication.getAppContext(), PermissionUtils.FIRST_APPLY_PERMISSION_CONVERT, PermissionUtils.PERMISSION_NOT_APPLY);
        boolean grant = isStatementConvertGranted(BaseApplication.getAppContext());
        DebugUtil.e(CONVERT_PERMISSION, "clearConvertGrantedStatus in " + LocalDateTime.now() + ", and convert permission is open" + grant);
    }


    /**
     * 设置网络权限的值
     *
     * @param apply true 设置 {@link #PERMISSION_APPLIED} ，false 设置{@link #PERMISSION_NOT_APPLY}
     */
    public static void setNetWorkGrantedStatus(@NonNull Context context, boolean apply) {
        StorageManager.setIntPrefApply(context, PermissionUtils.FIRST_APPLY_PERMISSION_NETWORK, apply ? PermissionUtils.PERMISSION_APPLIED : PermissionUtils.PERMISSION_NOT_APPLY);
    }

    public static boolean hasFilePermissionCompat() {
        if (BaseUtil.isAndroidROrLater()) {
            return hasAllFilePermission();
        } else {
            return hasStoragePermission();
        }
    }

    public static void sendFilePermissionBroadcast(Context context) {
        Intent intent = new Intent(ACTION_FILE_PERMISSION);
        intent.setPackage(context.getPackageName());
        context.sendBroadcast(intent);
    }

    public static boolean hasAllFilePermission() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                return Environment.isExternalStorageManager();
            } else {
                return false;
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "hasAllFilePermission error", e);
            return false;
        }
    }

    private static boolean hasOwnerPackageName(long id) {
        boolean hasOwnerPackageName = false;
        Cursor cursor = null;
        try {
            ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
            String where = MediaStore.Audio.Media._ID + "=?";
            cursor = resolver.query(BASE_URI, new String[]{OWNER_PACKAGE_NAME}, where, new String[]{String.valueOf(id)}, null);
            if ((cursor != null) && cursor.moveToFirst()) {
                String string = cursor.getString(cursor.getColumnIndexOrThrow(OWNER_PACKAGE_NAME));
                hasOwnerPackageName = BaseApplication.getAppContext().getPackageName().equals(string);
            }
        } catch (Throwable ignored) {
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return hasOwnerPackageName;
    }

    public static boolean checkUriPermission(long id) {
        if (hasOwnerPackageName(id)) {
            return true;
        }
        try {
            int uid = Binder.getCallingUid();
            int pid = Binder.getCallingPid();
            int mode = BaseApplication.getAppContext().checkUriPermission(MediaDBUtils.genUri(id), pid, uid, FLAG_GRANT_WRITE_URI_PERMISSION);
            return mode == PERMISSION_GRANTED;
        } catch (Throwable e) {
            DebugUtil.e(TAG, "checkUriWithIdPermission", e);
            return false;
        }
    }

    public static boolean checkUriPermissions(List<Long> recordIds) {
        for (Long recordId : recordIds) {
            if (!checkUriPermission(recordId)) {
                return false;
            }
        }
        return true;
    }

    public static boolean hasFirstCheckAllPermissionsOnResumeForBrowseFile() {
        Context context = BaseApplication.getAppContext();
        int apply = StorageManager.getIntPref(context, FIRST_CHECK_ALL_PERMISSIONS_ON_RESUME_FOR_BROWSE_FILE, PERMISSION_NOT_APPLY);
        boolean notChecked = apply == PERMISSION_NOT_APPLY;
        if (notChecked) {
            StorageManager.setIntPref(context, FIRST_CHECK_ALL_PERMISSIONS_ON_RESUME_FOR_BROWSE_FILE, PERMISSION_APPLIED);
        }
        return notChecked;
    }
}
