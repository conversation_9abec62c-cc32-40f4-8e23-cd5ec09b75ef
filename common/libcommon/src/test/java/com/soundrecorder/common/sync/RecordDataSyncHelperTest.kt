package com.soundrecorder.common.sync

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.modulerouter.cloudkit.CloudSyncAction
import com.soundrecorder.modulerouter.cloudkit.tipstatus.CloudTipManagerAction
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito.*
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class, ShadowOS12FeatureUtil::class])
class RecordDataSyncHelperTest {
    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @After
    fun release() {
        mContext = null
    }

    @Test
    fun when_checkLastEnterInOneHour() {
        val lastEnterInOneHour = RecordDataSyncHelper.chekLastRecordSyncOverOneHour()
        Assert.assertFalse(lastEnterInOneHour)
        RecordDataSyncHelper.updateMediaCompareTime(mContext!!)
        val lastEnterInOneHour1 = RecordDataSyncHelper.chekLastRecordSyncOverOneHour()
        Assert.assertFalse(lastEnterInOneHour1)
    }

    @Test
    fun verify_when_trigRecordSync() {
        val mockStaticCloudAction = mockStatic(CloudTipManagerAction::class.java)
        val mockStaticSyncAction = mockStatic(CloudSyncAction::class.java)
        mockStaticCloudAction.`when`<Boolean> { CloudTipManagerAction.isCloudSwitchOn() }.thenReturn(true, false)

        RecordDataSyncHelper.trigRecordSync(mContext!!)
        mockStaticSyncAction.verify { CloudSyncAction::trigCloudSync }

        RecordDataSyncHelper.trigRecordSync(mContext!!)

        mockStaticCloudAction.close()
        mockStaticSyncAction.close()
    }

    @Test
    fun when_updateMediaCompareTime() {
        RecordDataSyncHelper.updateMediaCompareTime(mContext!!)
    }
}