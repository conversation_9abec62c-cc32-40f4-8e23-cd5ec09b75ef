<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.coui.appcompat.preference.COUIPreferenceCategory app:top_margin_type="none">
        <com.soundrecorder.playback.audio.setting.MySwitchPreference
            android:layout_marginStart="@dimen/dp16"
            android:clickable="false"
            android:defaultValue="false"
            android:focusable="false"
            android:key="play_setting_switch_preference"
            android:title="@string/skip_mute"
            app:isDataReady="false" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.soundrecorder.playback.view.PlaySettingFooterPreference
            android:key="play_setting_footer_preference"
            android:selectable="false"
            android:layout="@layout/play_setting_dialog_footer" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <com.coui.appcompat.preference.COUIPreferenceCategory app:top_margin_type="none">

        <com.coui.appcompat.preference.COUIMarkPreference
            android:defaultValue="false"
            android:key="play_setting_speed_0_5_preference"
            android:persistent="false"
            android:title="@string/speed_half" />
        <com.coui.appcompat.preference.COUIMarkPreference
            android:defaultValue="true"
            android:key="play_setting_speed_1_preference"
            android:persistent="false"
            android:title="@string/speed_normal" />
        <com.coui.appcompat.preference.COUIMarkPreference
            android:defaultValue="false"
            android:key="play_setting_speed_1_0_5_preference"
            android:persistent="false"
            android:title="@string/speed_one_point_5" />
        <com.coui.appcompat.preference.COUIMarkPreference
            android:defaultValue="false"
            android:key="play_setting_speed_2_preference"
            android:persistent="false"
            android:title="@string/speed_two" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.soundrecorder.playback.view.PlaySettingFooterPreference
            android:selectable="false"
            android:layout="@layout/padding_view" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
</PreferenceScreen>