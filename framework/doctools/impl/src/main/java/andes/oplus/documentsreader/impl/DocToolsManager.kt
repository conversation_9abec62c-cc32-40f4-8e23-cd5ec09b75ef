/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: DocToolsManager
 * * Description: DocToolsManager
 * * Version: 1.0
 * * Date : 2024/07/03
 * * Author:ztf
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *        ztf           2024/07/03       1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.impl

import andes.oplus.documentsreader.core.common.uiconfig.type.IUIConfig
import andes.oplus.documentsreader.impl.enumer.EnumId
import andes.oplus.documentsreader.impl.helper.ForActivityResultHelper
import andes.oplus.documentsreader.impl.tool.ScanDocTool
import andes.oplus.documentsreader.impl.tool.YoZoDocTool
import andes.oplus.documentsreader.impl.ui.AllToolsActivity
import andes.oplus.documentsreader.impl.ui.AllToolsFragment
import andes.oplus.documentsreader.impl.util.SelectFileUtil
import andes.oplus.documentsreader.impl.view.DocToolView
import andes.oplus.documentsreader.impl.view.PanelToolView
import andes.oplus.documentsreader.interfaces.IDocToolsManager
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner

class DocToolsManager : IDocToolsManager {

    companion object {
        private const val SUMMARY_TYPE = "summary"
        private const val TRANSLATE_TYPE = "translate"
        private const val CONVERTPDF_TYPE = "convertPDF"
        private const val CONVERTWORD_TYPE = "convertWord"
        private const val CONVERTPPT_TYPE = "convertPPT"
        private const val CONVERTEXCEL_TYPE = "convertExcel"
    }

    override fun loadDocToolView(
        activity: ComponentActivity,
        lifecycleOwner: LifecycleOwner,
        resultCodeLauncher: ActivityResultLauncher<Intent>,
        callback: (view: View, size: Int) -> Unit?
    ) {
        DocToolView(activity, lifecycleOwner, resultCodeLauncher, callback)
    }

    override fun getPanelToolView(
        activity: ComponentActivity,
        lifecycleOwner: LifecycleOwner,
        resultCodeLauncher: ActivityResultLauncher<Intent>,
        callback: (size: Int) -> Unit?
    ): View {
        return PanelToolView(activity, lifecycleOwner, resultCodeLauncher, callback)
    }

    override fun openTool(context: Context?, bundle: Bundle?) {
        ForActivityResultHelper.openTool(context, bundle)
    }

    override fun startAllToolActivity(activity: Activity) {
        AllToolsActivity.start(activity)
    }

    override fun setDialogState(view: View?, callback: () -> Unit?) {
        if (view is PanelToolView) {
            view.dismissDialogCallBack(callback)
        }
    }

    override fun openDocTranslation(context: Context?, bundle: Bundle?) {
        YoZoDocTool.pairYozo = Pair(YoZoDocTool.PREVIEW_TYPE_DOC_TRANSLATION, "")
        SelectFileUtil.selectIdEnum = EnumId.ID_DOC_TRANSLATION
        openTool(context, bundle)
    }

    override fun onWindowInsetsCallback(docToolView: View) {
        (docToolView as? DocToolView)?.onWindowInsetsCallback()
    }

    override fun invalidateData(docToolView: View) {
        (docToolView as? DocToolView)?.invalidateData()
    }

    override fun wrapDeepLinkIntent(
        context: Context,
        intent: Intent,
        title: String,
        idEnumString: String
    ) {
        var idEnum: EnumId = EnumId.ID_INIT
        when (idEnumString) {
            SUMMARY_TYPE -> idEnum = EnumId.ID_DOC_SUMMARY
            TRANSLATE_TYPE -> idEnum = EnumId.ID_DOC_TRANSLATION
            CONVERTPDF_TYPE -> idEnum = EnumId.ID_CONVERT_TO_PDF
            CONVERTWORD_TYPE -> idEnum = EnumId.ID_CONVERT_TO_WORD
            CONVERTPPT_TYPE -> idEnum = EnumId.ID_CONVERT_TO_PPT
            CONVERTEXCEL_TYPE -> idEnum = EnumId.ID_CONVERT_TO_EXCEL
        }
        if (idEnum == EnumId.ID_INIT) return
        SelectFileUtil.wrapDeepLinkIntent(context, intent, title, idEnum)
    }

    override fun getFragment(activity: Activity, category: Int): Fragment {
        return AllToolsFragment()
    }

    override fun onResumeLoadData(fragment: Fragment) {
        if (fragment is AllToolsFragment) {
            fragment.onResumeLoadData()
        }
    }

    override fun onCreateOptionsMenu(fragment: Fragment, menu: Menu, inflater: MenuInflater) {
        fragment.onCreateOptionsMenu(menu, inflater)
    }

    override fun onOptionsMenuItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        if (fragment is AllToolsFragment) {
            return fragment.onOptionsItemSelected(item)
        }
        return false
    }

    override fun pressBack(fragment: Fragment): Boolean {
        return false
    }

    override fun fromSelectPathResult(fragment: Fragment, requestCode: Int, path: String?) {}

    override fun backToTop(fragment: Fragment) {}

    override fun setIsHalfScreen(fragment: Fragment, category: Int, isHalfScreen: Boolean) {
        if (fragment is AllToolsFragment) {
            fragment.setIsHalfScreen(isHalfScreen)
        }
    }

    override fun onUIConfigChanged(fragment: Fragment, configList: MutableCollection<IUIConfig>) {
        if (fragment is AllToolsFragment) {
            fragment.onUIConfigChanged(configList)
        }
    }

    override fun onWindowInsetsCallback(
        fragment: Fragment,
        showNavigationBar: Boolean,
        systemBarInsetsBottom: Int
    ) {}

    override fun openDrawer(fragment: Fragment, fragmentWidth: Int) {
        if (fragment is AllToolsFragment) {
            fragment.openDrawer(fragmentWidth)
        }
    }

    override fun closeDrawer(fragment: Fragment, fragmentWidth: Int) {
        if (fragment is AllToolsFragment) {
            fragment.closeDrawer(fragmentWidth)
        }
    }

    override fun notifyDrawerRight(fragment: Fragment, right: Int) {
        if (fragment is AllToolsFragment) {
            fragment.notifyDrawerRight(right)
        }
    }

    override fun checkEnableForScanTools(context: Context, action: String): Boolean {
        return ScanDocTool.checkScanToolsEnable(context, action)
    }
}