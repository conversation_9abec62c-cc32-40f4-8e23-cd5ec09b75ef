/*********************************************************************
 * * Copyright (C), 2024, OPlus. All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/6/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.launcher.recent

import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.KtConstants.LIST_NORMAL_MODE
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.loader.LoaderController
import andes.oplus.documentsreader.core.common.loader.LoadingLoaderListener
import andes.oplus.documentsreader.core.common.loader.UriLoadResult
import andes.oplus.documentsreader.core.common.selection.BaseStateModel
import andes.oplus.documentsreader.core.common.selection.BaseUiModel
import andes.oplus.documentsreader.core.common.selection.SelectionViewModel
import andes.oplus.documentsreader.core.common.wrapper.MediaFileWrapper
import andes.oplus.documentsreader.launcher.recent.filter.FilterCondition
import andes.oplus.documentsreader.launcher.recent.filter.FilterItem
import andes.oplus.documentsreader.launcher.utils.RecentStatisticsUtils
import android.os.Bundle
import androidx.lifecycle.MutableLiveData
import com.documentsreader.documentapi.IDocumentExtensionType
import com.oplus.dropdrag.SelectionTracker
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

open class RecentDocViewModel : SelectionViewModel<MediaFileWrapper, BaseUiModel<MediaFileWrapper>>() {
    companion object {
        private const val TAG = "RecentDocViewModel"
        const val RESTORE_BUNDLE_DATA = "restore_bundle_data"
        const val SELECT_MODE = "select_mode"
        const val SELECT_LIST = "select_list"
        const val FILTER_SELECT = "filter_select"
    }

    val mModeState = BaseStateModel(MutableLiveData<Int>(LIST_NORMAL_MODE))
    val filterSelectedDataModel = MutableLiveData<List<FilterItem>>()

    var filterCondition = FilterCondition().parseFilterItems()
    var restoreSelect: ArrayList<Int>? = null

    private val recentDocCallBack = RecentDocLoaderCallBack(this)

    fun initLoader(loaderController: LoaderController, tabPosition: Int) {
        Log.d(TAG, "initLoader")
        val filterList = createFilterList()
        if (recentDocCallBack.getLoader() == null) {
            if (filterList.isNotEmpty()) {
                recentDocCallBack.updateFilter(filterList)
            }
            loaderController.initLoader(tabPosition, recentDocCallBack)
        } else {
            if (filterList.isNotEmpty()) {
                recentDocCallBack.getLoader()?.updateFilter(filterList)
            }
            loadData()
        }
    }

    private fun createFilterList(): ArrayList<String> {
        val filterList = ArrayList<String>()
        val items = filterSelectedDataModel.value ?: return filterList
        val intList = items.filter { it.isSelect }.map { it.id }
        val stringList = Injector.injectFactory<IDocumentExtensionType>()?.filterMapToSelectionList(intList) ?: return filterList
        return stringList
    }

    fun updateSelectFilter(filterItemList: List<FilterItem>) {
        filterSelectedDataModel.postValue(filterItemList)
    }

    override fun getRealFileSize(): Int {
        return uiState.value?.fileList?.size ?: 0
    }

    fun hasRealFile(): Boolean {
        return getRealFileSize() > 0
    }

    override fun loadData() {
        recentDocCallBack.getLoader()?.forceLoad()
    }

    fun updateFilterSuffix(filterList: ArrayList<String>) {
        recentDocCallBack.getLoader()?.apply {
            updateFilter(filterList)
            forceLoad()
        }
    }

    open fun clickToolbarSelectAll() {
        if (getRealFileSize() == uiState.value?.selectedList?.size) {
            uiState.value?.selectedList?.clear()
        } else {
            uiState.value?.selectedList?.clear()
            uiState.value?.fileList?.let {
                for (baseFileBean in it) {
                    baseFileBean.id?.let { it1 -> uiState.value?.selectedList?.add(it1) }
                }
            }
        }
        uiState.value = uiState.value
    }

    override fun getRecyclerViewScanMode(): SelectionTracker.LAYOUT_TYPE {
        return SelectionTracker.LAYOUT_TYPE.LIST
    }

    open fun obtainResultList(result: UriLoadResult<Int, MediaFileWrapper>?): MutableList<MediaFileWrapper>? {
        return result?.resultList
    }

    fun restoreDataBundle(data: Bundle) {
        val selectMode = data.getInt(SELECT_MODE, LIST_NORMAL_MODE)
        if (mModeState.listModel.value != selectMode) {
            mModeState.initState = true
            mModeState.listModel.value = selectMode
        }
        val selectList = data.getIntegerArrayList(SELECT_LIST) ?: arrayListOf()
        restoreSelect = ArrayList()
        restoreSelect?.clear()
        restoreSelect?.addAll(selectList)
        //过滤文件
        val filterIds = data.getIntegerArrayList(FILTER_SELECT) ?: arrayListOf()
        if (filterIds.isNotEmpty()) {
            val list = ArrayList<FilterItem>()
            filterCondition.items?.apply {
                list.addAll(this)
                list.forEach { item ->
                    filterIds.forEach { item.isSelect = (it == item.id) }
                }
            }
            filterSelectedDataModel.value = list
        }
        Log.i(TAG, "restoreDataBundle selectMode = $selectMode, selectList = $selectList, filterIds = $filterIds")
    }

    fun storeDataBundle(): Bundle {
        return Bundle().apply {
            putInt(SELECT_MODE, mModeState.listModel.value ?: LIST_NORMAL_MODE)
            putIntegerArrayList(SELECT_LIST, uiState.value?.selectedList)
            val filterIds = ArrayList(filterSelectedDataModel.value?.filter { it.isSelect }?.map { it.id } ?: arrayListOf())
            putIntegerArrayList(FILTER_SELECT, filterIds)
            Log.i(TAG, "storeDataBundle selectMode = ${mModeState.listModel.value}, " +
                    "selectList = ${uiState.value?.selectedList}, filterIds = $filterIds")
        }
    }

    class RecentDocLoaderCallBack(viewModel: RecentDocViewModel) :
        LoadingLoaderListener<RecentDocViewModel, RecentDocLoader, UriLoadResult<Int, MediaFileWrapper>>(
            viewModel,
            viewModel.dataLoadState
        ) {

        private var docArray = ArrayList<String>()

        fun updateFilter(filterList: ArrayList<String>) {
            docArray.clear()
            docArray.addAll(filterList)
        }

        override fun onCreateLoader(viewModel: RecentDocViewModel?): RecentDocLoader? {
            return if (viewModel != null) {
                RecentDocLoader(ContextGetter.context, null, null).apply {
                    updateFilter(docArray)
                }
            } else null
        }

        override fun onLoadComplete(
            viewModel: RecentDocViewModel?,
            result: UriLoadResult<Int, MediaFileWrapper>?
        ) {
            Log.d(TAG, "DocumentLoaderCallBack onLoadComplete size ${result?.resultList?.size}")
            val recentViewModel = viewModel ?: return
            val loadResult = result ?: return
            val resultList = recentViewModel.obtainResultList(loadResult)
            resultList?.let {
                recentViewModel.mModeState.initState = true
                recentViewModel.launch {
                    Log.d(TAG, "DocumentLoaderCallBack onLoadComplete restoreSelect = ${recentViewModel.restoreSelect?.size}" +
                            ",selectedList = ${recentViewModel.uiState.value?.selectedList?.size}")
                    val selectedList = ArrayList<Int>()
                    val existSelectList = recentViewModel.restoreSelect ?: recentViewModel.uiState.value?.selectedList
                    if (existSelectList?.isNotEmpty() == true) {
                        withContext(Dispatchers.IO) {
                            existSelectList.forEach { selectedFile ->
                                if (loadResult.resultMap.containsKey(selectedFile)) {
                                    selectedList.add(selectedFile)
                                }
                            }
                        }
                    }
                    recentViewModel.restoreSelect?.clear()
                    recentViewModel.restoreSelect = null
                    if (it.isEmpty() && (recentViewModel.mModeState.listModel.value == KtConstants.LIST_SELECTED_MODE)) {
                        Log.d(TAG, "onLoadComplete mResultList is empty change to normal mode")
                        recentViewModel.mModeState.listModel.value = LIST_NORMAL_MODE
                    }
                    recentViewModel.uiState.postValue(BaseUiModel(it, recentViewModel.mModeState, selectedList, loadResult.resultMap))
                    RecentStatisticsUtils.onUploadDocumentAssets(ContextGetter.context, it)
                    RecentStatisticsUtils.onStatisticsDocumentAdded(ContextGetter.context, it)
                }
            }
        }
    }
}