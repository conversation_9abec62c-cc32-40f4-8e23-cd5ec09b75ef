/*********************************************************************
 ** Copyright (C), 2024-2034 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BackupAndRestoreInterface
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/7/17 14:46
 ** Author      : 80262777
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 ** 80262777        2024/7/17       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.backuprestore

import android.content.Context
import androidx.annotation.Keep
import java.io.FileDescriptor

@Keep
interface BackupAndRestoreInterface {
    /**
     * 返回需要备份的内容，备份的内容分为纯文本和文件 Pair.first=数据version
     * 纯文本情况：
     * Triple.first=待备份文本
     * Triple.third表示需要写入的文件名称，每个业务需要自己定义名称比如最近任务，恢复时需要使用
     * Triple.third= RecentFile/recentfile.json
     *
     * 直接备份文件的情况
     * Triple.first=null
     * Triple.second=待备份的文件全路径
     * Triple.third表示需要写入的文件名称，每个业务需要自己定义名称比如最近删除，恢复时需要使用
     * Triple.third="RecycleBin" + File.separator + File(it.recyclePath).name
     *
     */
    fun getBackupData(context: Context): Pair<String, List<Triple<String?, String?, String?>>>

    /**
     * 进行数据恢复，通过备份是写入的文件路径获取对应的数据并进行恢复
     * 比如最近记录恢复：
     * 1.获取数据getFileDescriptor(RecentFile/recentfile.json)
     * 2.解析文件中数据写入数据库
     * 恢复文件情况：
     * 1.获取数据getFileDescriptor("RecycleBin" + File.separator + File(it.recyclePath).name)
     * 2.将文件写入实际的位置
     */
    fun restoreData(
        context: Context,
        version: String,
        getFileDescriptor: (key: String) -> FileDescriptor,
        registerCancel: (cancel: () -> Unit) -> Unit
    )
}