/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.search
 * * Version     : 1.0
 * * Date        : 2020/7/21
 * * Author      : w9007122
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.search.manager.history

import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.search.database.SearchHistoryDBHelper
import andes.oplus.documentsreader.search.ui.GlobalSearchViewModel


object GlobalSearchHistoryManager {

    @JvmStatic
    fun loadHistory(): MutableList<GlobalSearchViewModel.SearchHistoryModel> {
        val history = mutableListOf<GlobalSearchViewModel.SearchHistoryModel>()
        SearchHistoryDBHelper(ContextGetter.context).getAllSearchHistory()?.forEach {
            history.add(
                GlobalSearchViewModel.SearchHistoryModel(
                    it.mId!!, it.mSearchContent, it.mSearchTime
                )
            )
        }
        return history
    }

    @JvmStatic
    fun addHistory(txt: String): Long {
        return SearchHistoryDBHelper(ContextGetter.context).addSearchHistory(txt) ?: -1L
    }

    @JvmStatic
    fun removeHistory(list: List<GlobalSearchViewModel.SearchHistoryModel>): Int {
        if (list.isEmpty()) {
            return -1
        }
        return SearchHistoryDBHelper(ContextGetter.context).deleteSearchHistory(list.map { it.mId })
    }

    @JvmStatic
    fun updateHistory(model: GlobalSearchViewModel.SearchHistoryModel): Int {
        return SearchHistoryDBHelper(ContextGetter.context).updateSearchHistory(model.mId, model.mTime)
    }

    @JvmStatic
    fun clearHistory(): Int {
        return SearchHistoryDBHelper(ContextGetter.context).clearHistory()
    }
}