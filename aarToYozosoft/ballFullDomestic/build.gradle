plugins {
    id 'com.android.library'
}
apply from: rootProject.file("script/compile.gradle")
apply from: rootProject.file("appLib/koin/script/autoKoinRegister.gradle")
apply from: rootProject.file("script/projectEmbed.gradle")
apply from: rootProject.file("script/aarMetadata.gradle")
apply from: rootProject.file("script/pipelineConfig.gradle")

android {
    // 与其它渠道的aarToYozosoft的namespace相同以做到不同版本永中aar互斥
    namespace 'andes.oplus.documentsreader.merged'

    buildFeatures {
        viewBinding = false
        buildConfig = false
        aidl = false
    }

    defaultConfig {
        // embedProjects配置块中有更多consumerProguardFiles配置
    }

    buildTypes {
        release {
            minifyEnabled true
            // embedProjects配置块中有更多ProguardFile配置
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt')
        }

        debug {
            //config enable proGuard
            minifyEnabled false
            //proGuard rules files
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt')
        }
    }
}

/**
 * 全渠道永中aar均集成的在以下统一配置gradle里配置:
 *     aarToYozosoft/buildConfigs/embedCommon.gradle
 */
apply from: rootProject.file("aarToYozosoft/buildConfigs/embedCommon.gradle")

/**
 * 以下仅内销永中aar集成的配置：
 */
embedProjects {
    // 在deployEmbedConfigs之前配置集成内容
    //中子搜索
    embedKoin ':framework:dmp:impl'
    //云控内销
    embedKoin ":framework:cloudconfig:impl"
    //sau
    embedKoin ":framework:sau:impl"
    //专业格式打开和转化
    embedKoin ":framework:andesformat:impl"
    //专业格式webView打开
    embedKoin ":framework:opendirect:impl"
    //备案号
    embedKoin ":framework:icpregistration:impl"
    //隐私相关
    embedKoin ':framework:privacy:impl'
    //格式转化和公式提取
    embedKoin ":framework:formatConvert:impl"
    //云盘
    embedKoin ":framework:clouddrive:impl"
    //打开专业格式
    embed ":business:openfile"
    //文件预览
    embed ":business:filepreview"
    embed ":business:filepreviewbase"
    embedKoin ":framework:textointent:implbase"
    embed ":framework:textointent:impldomestic"
    //自动跳转，内销集成
    embedKoin ":framework:simulate:impl"
    //feedback帮助与反馈
    embedKoin ":framework:feedback:impl"

    embedCommonRes rootProject.file("res-strings/res-domestic-strings")

    deployEmbedConfigs()
}

dependencies {
    implementation libs.com.heytap.log.domin.cn
    implementation(libs.com.heytap.log)
    implementation(libs.com.alibaba.fastjson)
}

autoKoinRegister {
    // 当前AAR的渠道类型
    brandType = "ball"
    productType = "full"
    regionType = "domestic"

    applyProguardRulesTo project
    deployAutoRegister()
}

/**
 * 内销版永中aar的生成配置
 */
aarPipeline {
    groupId = prop_archivesGroupName
    artifactId = prop_artifactId_full_domestic
    versionName = "${mainVersionName}" + "${versionSuffix}"
    variantType = autoKoinRegister.variantType
}

apply from: rootProject.file("script/pipelinePublish.gradle")
apply from: rootProject.file("script/aarCompile.gradle")