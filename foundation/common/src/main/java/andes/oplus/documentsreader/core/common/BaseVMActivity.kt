/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BaseVMActivity
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/6/25 15:31
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/6/25       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.core.common

import andes.oplus.documentsreader.core.common.controller.PermissionController
import andes.oplus.documentsreader.core.common.parentchild.IMainViewApi
import andes.oplus.documentsreader.core.common.thread.BaseThreadTask
import andes.oplus.documentsreader.core.common.thread.FileRunnable
import andes.oplus.documentsreader.core.common.thread.ThreadManager
import andes.oplus.documentsreader.core.common.thread.ThreadPriority
import andes.oplus.documentsreader.core.common.thread.ThreadType
import andes.oplus.documentsreader.core.common.uiconfig.UIConfigMonitor
import andes.oplus.documentsreader.core.common.uiconfig.type.IUIConfig
import andes.oplus.documentsreader.core.common.uiconfig.type.ScreenFoldConfig
import andes.oplus.documentsreader.core.common.uiconfig.type.ScreenOrientationConfig
import andes.oplus.documentsreader.core.common.uiconfig.type.ZoomWindowConfig
import andes.oplus.documentsreader.core.common.utils.KtViewUtils
import andes.oplus.documentsreader.core.common.view.NavigationView
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.view.DragEvent
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.ColorRes
import androidx.annotation.Keep
import androidx.annotation.LayoutRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.marginBottom
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.statusbar.COUIStatusBarResponseUtil
import com.coui.appcompat.theme.COUIThemeOverlay
import com.oplus.content.OplusIntent.ACTION_SKIN_CHANGED
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import java.util.function.Consumer

@Keep
abstract class BaseVMActivity : AppCompatActivity(), CoroutineScope by CoroutineScope(Dispatchers.Default),
    PermissionController.OnRequestPermissionListener, UIConfigMonitor.OnUIConfigChangeListener,
    COUIStatusBarResponseUtil.StatusBarClickListener, ActivityResultCallback<ActivityResult> {

    companion object {
        private const val TAG = "BaseVMActivity"
    }

    interface PermissionCallBack {
        fun handleNoStoragePermission()
    }

    private var mPermissionController: PermissionController? = null
    private var dragEventHash: Int = 0
    private var mStatusBarResponse: COUIStatusBarResponseUtil? = null
    private val mVMChangedReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            Log.d(TAG, "mVMChangedReceiver onReceive action = $action")
            onVMChangedReceiver(action, intent)
            if (ACTION_SKIN_CHANGED == action) {
                onSkinChangerReceiver(action, intent)
            }
        }
    }

    private lateinit var activityLauncher: ActivityResultLauncher<Intent>
    private var activityResultConsumer: Consumer<ActivityResult>? = null
    var showNavigationBar: Boolean = false
    var systemBarInsetsBottom: Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        registerUIConfigChange()
        mStatusBarResponse = COUIStatusBarResponseUtil(this)
        mStatusBarResponse?.setStatusBarClickListener(this)
        //should be called before setContentView
        StatusBarUtils.setStatusBarTransparentAndBlackFont(this)
        WindowUtils.initWindowInsets(this)
        COUIThemeOverlay.getInstance().applyThemeOverlays(this)
        activityLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult(), this)
        val layoutResId = getLayoutResId()
        if (layoutResId != 0) {
            setContentView(layoutResId)
        }
        initView()
        initNavigationBar()
        startObserve()
        initData()
        if (isAdaptNavigationBar()) {
            updateWindowInsets()
        }
        Injector.injectFactory<IMainViewApi>()?.registerDragListener(this)
        registerDragFileChangeReceiver()
    }

    override fun onDestroy() {
        super.onDestroy()
        WindowUtils.removeInsetsListener(this)
        unregisterDragFileChangeReceiver()
    }

    override fun onResume() {
        super.onResume()
        runCatching {
            mStatusBarResponse?.onResume()
        }.onFailure { e ->
            Log.e("TAG", "Exception in onResume: ${e.message}", e)
        }
    }

    override fun onPause() {
        super.onPause()
        runCatching {
            mStatusBarResponse?.onPause()
        }.onFailure { e ->
            Log.e("TAG", "Exception in onPause: ${e.message}", e)
        }
    }

    protected open fun registerUIConfigChange() {
        UIConfigMonitor.instance.also {
            it.attachActivity(this)
            it.addOnUIConfigChangeListener(this)
            KtViewUtils.updateUIOrientation(this, getScreenOrientation())
        }
    }

    protected open fun getScreenOrientation(): Int? {
        return null
    }

    /**
     * Return activity layout red id.
     */
    @LayoutRes
    abstract fun getLayoutResId(): Int

    /**
     *  Init view.
     */
    abstract fun initView()

    /**
     * Start observe.
     */
    abstract fun startObserve()

    /**
     * Init data.
     */
    abstract fun initData()

    open fun refreshCurrentPage(action: String? = null, data: String? = null) {
    }

    protected open fun onVMChangedReceiver(action: String?, intent: Intent) {
    }

    protected open fun onSkinChangerReceiver(action: String?, intent: Intent) {
        if (ThreadManager.sThreadManager.containThread(ACTION_SKIN_CHANGED).not()) {
            ThreadManager.sThreadManager.execute(ClearAllCacheTask())
        }
    }

    open fun getWindowWidth(category: Int): Int {
        return 0
    }

    internal class ClearAllCacheTask :
        BaseThreadTask<Void>(FileRunnable({}, ACTION_SKIN_CHANGED), null, null) {

        override fun getUniqueFlag(): String {
            return ACTION_SKIN_CHANGED
        }

        override fun getThreadType(): ThreadType {
            return ThreadType.NORMAL_THREAD
        }

        override fun getPriority(): ThreadPriority {
            return ThreadPriority.BACKGROUND
        }
    }

    /**
     * 是否适配OS12的底部导航栏，底部有Tab（如MainActivity）的不需要适配
     * 适配的效果为，当是手势导航并且是上下滚动页面时，最底部的导航条后面可看到页面内容
     */
    open fun isAdaptNavigationBar(): Boolean = true

    open fun initNavigationBar() {
        StatusBarUtils.setNavigationBarColor(this)
    }

    override fun onPermissionSuccess() {
        mPermissionController?.removePermissionEmptyView(getRootView())
    }

    open fun checkStoragePermission() {
        Log.d(TAG, "checkStoragePermission ")
        if (mPermissionController == null) {
            mPermissionController = PermissionController.create(this.lifecycle, this)
        }
        if (mPermissionController!!.getWaitPermissionGrantResult()) {
            mPermissionController!!.setWaitPermissionGrantResult(false)
        } else {
            mPermissionController!!.checkPermission(this)
        }
    }

    override fun onPermissionReject(alwaysReject: Boolean) {
        if (alwaysReject) { // If always reject, show a full screen permission request UI
            Log.d(TAG, "onPermissionReject: permission always rejected")
            mPermissionController?.showPermissionEmptyView(this, getRootView())
        } else {
            Log.d(TAG, "onPermissionReject: activity finished after permission reject")
            finish()
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        mPermissionController?.onPermissionsResultReturn(this, requestCode, permissions, grantResults)
    }

    protected open fun getRootView(): ViewGroup? {
        val view = window.decorView
        return if (view is ViewGroup) view else null
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        UIConfigMonitor.instance.onActivityConfigChanged(newConfig)
    }

    override fun onMultiWindowModeChanged(isInMultiWindowMode: Boolean, newConfig: Configuration) {
        super.onMultiWindowModeChanged(isInMultiWindowMode, newConfig)
        UIConfigMonitor.instance.onMultiWindowModeChanged(isInMultiWindowMode)
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        configList.forEach {
            if ((it is ScreenFoldConfig) || (it is ScreenOrientationConfig)) {
                KtViewUtils.updateUIOrientation(this, getScreenOrientation())
                return
            } else if (it is ZoomWindowConfig && !it.windowShow) {
                KtViewUtils.resetTabletUIOrientation(this)
            }
        }
    }

    private fun updateWindowInsets() {
        updateDecorFitsSystemWindows()
        findViewById<View>(android.R.id.content)?.let {
            val navigationBarHelper = NavigationBarHelper()
            navigationBarHelper.addInsetsCallback(it, window) { _, insets, showNavigationBar ->
                val systemBarInsetsBottom = insets.getInsets(WindowInsetsCompat.Type.systemBars()).bottom
                val isGestureNavMode = StatusBarUtils.checkIsGestureNavMode(this)
                val isInMultiWindowMode = FlexibleWindowUtil.isInMultiWindowMode(this)
                val isZoomWindowMode = FlexibleWindowUtil.isZoomWindowMode(this)
                Log.d(TAG, "onApplyInsets showNavigationBar: $showNavigationBar, " +
                        "systemBarInsetsBottom = $systemBarInsetsBottom, " +
                        "isGestureNavMode = $isGestureNavMode, " +
                        "isInMultiWindowMode = $isInMultiWindowMode, " +
                        "isZoomWindowMode = $isZoomWindowMode")
                if (systemBarInsetsBottom == 0 && !isGestureNavMode && !isInMultiWindowMode && !isZoomWindowMode) {
                    return@addInsetsCallback
                }
                onWindowInsetsCallback(showNavigationBar, systemBarInsetsBottom)
            }
        }
    }

    private val dragFileChangeReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            val dragAppPackage = intent.getStringExtra(KtConstants.DRAG_APP_PACKAGE)
            Log.d(TAG, "dragFileChangeReceiver onReceive action = $action dragAppPackage = $dragAppPackage")
            if (action == KtConstants.ACTION_DRAG_FILE_CHANGED && dragAppPackage != ContextGetter.application.packageName) {
                onRefreshData()
            }
        }
    }

    private fun registerDragFileChangeReceiver() {
        val intentFilter = IntentFilter()
        intentFilter.addAction(KtConstants.ACTION_DRAG_FILE_CHANGED)
        runCatching {
            if (SdkUtils.isAtLeastT()) {
                ContextGetter.context.registerReceiver(dragFileChangeReceiver, intentFilter, KtConstants.PROTECT_PERMISSION, null, RECEIVER_EXPORTED)
            } else {
                ContextGetter.context.registerReceiver(dragFileChangeReceiver, intentFilter)
            }
        }.onFailure {
            Log.e(TAG, "registerDragFileChangeReceiver $it")
        }
    }

    private fun unregisterDragFileChangeReceiver() {
        runCatching {
            ContextGetter.context.unregisterReceiver(dragFileChangeReceiver)
        }.onFailure {
            Log.e(TAG, "unregisterDragFileChangeReceiver $it")
        }
    }

    /**
     * 更新导航栏颜色
     * @param showNavigationBar 是否显示导航栏
     * @param showNavigationView 是否显示底部菜单栏
     */
    fun updateNavigationBarColor(
        context: Context,
        window: Window,
        showNavigationBar: Boolean,
        showNavigationView: Boolean,
        @ColorRes colorRes: Int = R.color.coui_color_background_with_card
    ) {
        Log.d(TAG, "updateNavigationBarColor showNavigationBar $showNavigationBar showNavigationView $showNavigationView")
        /*
        1,显示菜单栏，跟随菜单栏颜色
        2,没有菜单栏，就跟随传入的颜色
        3,其他情况的就透明
         */
        when {
            showNavigationView -> {
                window.isNavigationBarContrastEnforced = true
                window.navigationBarColor = context.getColor(R.color.oplusdoc_navigation_bar_color)
            }
            showNavigationBar -> {
                window.isNavigationBarContrastEnforced = true
                window.navigationBarColor = context.getColor(colorRes)
            }
            else -> {
                window.isNavigationBarContrastEnforced = false
                window.navigationBarColor = Color.TRANSPARENT
            }
        }
    }

    fun setNavigationHeight(navigationView: NavigationView?, systemBarInsetsBottom: Int) {
        navigationView?.let { navigation ->
            Log.d(TAG, "setNavigationHeight: ${navigation.measuredHeight}," +
                    "${navigation.bottomPadding},${navigation.marginBottom}")
            //切换导航栏高度时更新navigation_tab的bottomMargin
            if (navigation.visibility == View.VISIBLE && navigation.marginBottom < 0
                && navigation.bottomPadding != systemBarInsetsBottom
            ) {
                navigation.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                    bottomMargin = -(navigation.measuredHeight - navigation.bottomPadding + systemBarInsetsBottom)
                }
            }
            navigation.updatePaddingBottom(systemBarInsetsBottom)
        }
    }

    private fun updateDecorFitsSystemWindows() {
        //小屏、分屏、虚拟按键导航且导航栏和界面在一边的时候，设置为非沉浸式
        this.let {
            val isMiddleAndLargeScreen = WindowUtils.isMiddleAndLargeScreen(it)
            val checkIsGestureNavMode = StatusBarUtils.checkIsGestureNavMode(it)
            val isDisplayInPrimaryScreen = COUIPanelMultiWindowUtils.isDisplayInPrimaryScreen(it)
            val deviceRotation = UIConfigMonitor.instance.getDeviceRotation(it)
            val isNavigationBarTogether =
                (deviceRotation == UIConfigMonitor.ROTATION_ONE && isDisplayInPrimaryScreen.not()) ||
                        (deviceRotation == UIConfigMonitor.ROTATION_THREE && isDisplayInPrimaryScreen)
            Log.d(
                TAG, "updateDecorFitsSystemWindows " +
                        "isInMultiWindowMode ${it.isInMultiWindowMode}, " +
                        "isMiddleAndLargeScreen $isMiddleAndLargeScreen, " +
                        "checkIsGestureNavMode $checkIsGestureNavMode, " +
                        "isDisplayInPrimaryScreen $isDisplayInPrimaryScreen, " +
                        "isNavigationBarTogether $isNavigationBarTogether"
            )
            if (it.isInMultiWindowMode && isMiddleAndLargeScreen.not() && checkIsGestureNavMode.not() && isNavigationBarTogether) {
                WindowCompat.setDecorFitsSystemWindows(window, true)
                return
            }
        }
        WindowCompat.setDecorFitsSystemWindows(window, false)
    }

    open fun onWindowInsetsCallback(showNavigationBar: Boolean, systemBarInsetsBottom: Int) {
        this.showNavigationBar = showNavigationBar
        this.systemBarInsetsBottom = systemBarInsetsBottom
        updateNavigationBarColor(this, this.window, showNavigationBar, false)
    }

    open fun onRefreshData() {
        Log.d(TAG, "onRefreshData")
    }

    fun setDragEvent(dragEvent: DragEvent) {
        this.dragEventHash = dragEvent.hashCode()
    }

    fun getDragEventHash(): Int {
        return dragEventHash
    }

    protected fun registerVmChangedReceiver(actions: Array<String>?) {
        val intentFilter = IntentFilter()
        intentFilter.addAction(ACTION_SKIN_CHANGED)
        if (actions != null) {
            for (action in actions) {
                intentFilter.addAction(action)
            }
        }
        runCatching {
            ContextGetter.context.registerExportedReceiver(mVMChangedReceiver, intentFilter)
        }.onFailure {
            Log.e(TAG, "registerVmChangedReceiver " + it.message)
        }
    }

    protected fun unregisterVmChangedReceiver() {
        runCatching {
            ContextGetter.context.unregisterReceiver(mVMChangedReceiver)
        }.onFailure {
            Log.e(TAG, "unregisterVmChangedReceiver " + it.message)
        }
    }

    override fun onActivityResult(result: ActivityResult) {
        Log.d(TAG, "onActivityResult result = $result")
        this.activityResultConsumer?.accept(result)
    }

    /**
     * 启动Activity,类似于startActivityForResult
     * @param intent 跳转的intent
     * @param callback onActivityResult的回调
     */
    fun launchActivityForResult(intent: Intent, callback: Consumer<ActivityResult>?) {
        this.activityResultConsumer = callback
        activityLauncher.launch(intent)
    }

    override fun onStatusBarClicked() {
        backtoTop()
    }

    protected open fun backtoTop() {
        Log.d(TAG, "backtoTop")
    }

    fun showSettingGuildDialog() {
        if (mPermissionController == null) {
            mPermissionController = PermissionController.create(this.lifecycle, this)
        }
        mPermissionController?.showSettingGuildDialog(activity = this)
    }
}