/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - CleanObsoleteRecycleBinAction.kt
 * Description:
 *     The action to remove overdue recycled files
 *
 * Version: 1.0
 * Date: 2024-07-01
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-07-01   1.0    Create this module
 *********************************************************************************/
package andes.oplus.documentsreader.recycle.actions

import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.recycle.database.RecycleBinDBHelper
import andes.oplus.documentsreader.recycle.database.data.RecycleFileEntity
import andes.oplus.documentsreader.recycle.store.IRecycleBinFile
import andes.oplus.documentsreader.recycle.store.RecycleBinStore
import android.content.Context
import androidx.annotation.VisibleForTesting
import kotlinx.coroutines.CoroutineScope
import java.io.File
import java.io.FileNotFoundException

internal class CleanObsoleteRecycleBinAction(actionScope: CoroutineScope) :
    AbsRecycleFileAction<Unit, Unit>(actionScope), IUnitRecycleFileAction {

    override val actionName: String = TAG

    override suspend fun onExecuteAction(context: Context, param: Unit) {
        val currentDate = System.currentTimeMillis()
        val dbHelper = RecycleBinDBHelper(context)
        val allRecords = dbHelper.getAllRecycledFileList()
        Log.d(TAG, "onExecuteAction: currentDate=$currentDate, allRecords=${allRecords.size}")
        val toObsoleted = mutableListOf<RecycleFileEntity>()
        val allFiles = mutableListOf<RecycleBinFileWrapper>()
        allRecords.forEach {
            val recycleBinFile = RecycleBinFileWrapper(it)
            if (recycleBinFile.isUnavailable) {
                toObsoleted.add(it)
            } else {
                allFiles.add(recycleBinFile)
            }
        }
        val recycleStore = RecycleBinStore(context, emptyList())
        val clearedFiles = recycleStore.clearObsoleteRecycledFiles(allFiles, currentDate)
        clearedFiles.map { it.record }.takeIf { it.isNotEmpty() }?.let(toObsoleted::addAll)
        Log.d(TAG, "onExecuteAction: cleared=${clearedFiles.size}, obsoleted=${toObsoleted.size}")
        if (toObsoleted.isNotEmpty()) {
            val removed = dbHelper.removeRecycledFileList(toObsoleted)
            Log.d(TAG, "onExecuteAction: remove from db: $removed")
        }
    }

    @VisibleForTesting
    internal companion object {
        private const val TAG = "CleanObsoleteRecycleBinAction"

        @VisibleForTesting
        @JvmStatic
        fun constructFile(path: String): File = File(path)
    }

    private class RecycleBinFileWrapper(val record: RecycleFileEntity) : IRecycleBinFile {
        override val recycledFile: File
            get() = _recycledFile ?: throw FileNotFoundException("${record.recyclePath} not exists")
        override val recycleDate: Long
            get() = record.recycleDate
        val isUnavailable: Boolean
            get() = _recycledFile == null

        private val _recycledFile: File? = record.recyclePath
            .takeIf { it.isNotEmpty() }
            ?.let { constructFile(it) }
            ?.takeIf { it.exists() }
    }
}