/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/06/13, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package andes.oplus.documentsreader.preview.ui.interfaze

import andes.oplus.documents.preview.interfaces.data.PreviewErrorCode
import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.FileTypeUtils
import andes.oplus.documentsreader.core.common.FileUtils
import andes.oplus.documentsreader.core.common.KtAppUtils
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.MiddleMultilineTextView
import andes.oplus.documentsreader.core.common.MimeTypeHelper
import andes.oplus.documentsreader.core.common.StatusBarUtils
import andes.oplus.documentsreader.core.common.Utils
import andes.oplus.documentsreader.core.common.compat.FeatureCompat
import andes.oplus.documentsreader.core.common.compat.compat30.FeatureCompatR
import andes.oplus.documentsreader.core.common.controller.PermissionStorageController
import andes.oplus.documentsreader.core.common.stringResource
import andes.oplus.documentsreader.interfaces.CollectPrivacyUtils
import andes.oplus.documentsreader.openfile.R
import android.app.Dialog
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ScrollView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleCoroutineScope
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.toolbar.COUIToolbar
import com.google.android.material.appbar.COUIDividerAppBarLayout

interface IUiCommon {

    companion object {
        private const val TAG = "UiCommon"
        private const val UPDATE_MULTI_TEXT_DELAY = 10L
        private const val EXTRACHAR = 4
    }

    var permissionDialog: Dialog?
    var storageLimitDialog: Dialog?
    var hasShownPermissionDialog: Boolean

    fun getAttachActivity(): FragmentActivity

    fun initToolbarCommon(toolbar: COUIToolbar?, appbar: COUIDividerAppBarLayout?, scrollView: ScrollView?) {
        toolbar?.apply {
            this.title = ""
            this.isTitleCenterStyle = false
            (getAttachActivity() as? AppCompatActivity)?.let {
                it.setSupportActionBar(this)
                it.supportActionBar?.setDisplayHomeAsUpEnabled(true)
            }
        }
        fun getViewTopPadding(): Int {
            return StatusBarUtils.getStatusBarHeight(ContextGetter.context) + (toolbar?.height
                ?: 0) + ContextGetter.context.resources.getDimensionPixelSize(R.dimen.sort_list_padding_top)
        }
        appbar?.setPadding(
            0, StatusBarUtils.getStatusBarHeight(ContextGetter.context) + ContextGetter.context.resources.getDimensionPixelOffset(
                R.dimen.tab_searchview_margin_top
            ), 0, 0
        )
        toolbar?.post {
            scrollView?.apply {
                setPadding(paddingLeft, getViewTopPadding(), paddingRight, 0)
            }
        }
    }

    fun getContentHeight(activity: FragmentActivity?, rootView: View?): Int {
        //直接获取ScrollView的高度，在切换暗色模式或语言时，会获取到错误的高度，直接使用父布局减去appBar的高度，此情况下不会变
        activity?.let { act ->
            val rooViewHeight = (rootView?.parent as? FrameLayout)?.height ?: 0
            val appbarHeight = act.window?.decorView?.findViewById<View>(R.id.appbar)?.height ?: 0
            var screenHeight = rooViewHeight - appbarHeight
            //兼容taskbar 全屏，手势导航,非浮窗的时候减去taskbar高度
            if (FeatureCompat.isSupportTaskbar && StatusBarUtils.isDisplayTaskbar(
                    act.window.decorView
                ) && !StatusBarUtils.checkNotDisplayTaskbarHeight(act)
            ) {
                Log.d(TAG, "support task bar and gesture mode ShowGestureNavBar and not zoom window")
                screenHeight -= act.resources.getDimensionPixelSize(R.dimen.navigation_gesture_taskbar_height)
            }
            return screenHeight
        }
        return 0
    }

    fun displayIconByFileType(context: Context?, filePath: String?, fileIcon: ImageView?) {
        context?.let { context ->
            val drawable = when (MimeTypeHelper.getTypeFromExtension(FileTypeUtils.getExtension(filePath))) {
                MimeTypeHelper.KEYNOTE_TYPE -> ContextCompat.getDrawable(context, R.drawable.preview_keynote)
                MimeTypeHelper.PAGES_TYPE -> ContextCompat.getDrawable(context, R.drawable.preview_pages)
                MimeTypeHelper.NUMBERS_TYPE -> ContextCompat.getDrawable(context, R.drawable.preview_numbers)
                MimeTypeHelper.MARKDOWN_TYPE -> ContextCompat.getDrawable(context, R.drawable.preview_md)
                MimeTypeHelper.DWG_TYPE, MimeTypeHelper.DWT_TYPE, MimeTypeHelper.DXF_TYPE -> {
                    ContextCompat.getDrawable(context, R.drawable.preview_cad)
                }

                MimeTypeHelper.PSD_TYPE -> ContextCompat.getDrawable(context, R.drawable.preview_psd)
                MimeTypeHelper.AI_TYPE -> ContextCompat.getDrawable(context, R.drawable.preview_ai)
                MimeTypeHelper.VSDX_TYPE, MimeTypeHelper.VSDM_TYPE, MimeTypeHelper.VSTX_TYPE,
                MimeTypeHelper.VSTM_TYPE, MimeTypeHelper.VSSX_TYPE, MimeTypeHelper.VSSM_TYPE,
                MimeTypeHelper.VSD_TYPE, MimeTypeHelper.VSS_TYPE, MimeTypeHelper.VST_TYPE,
                MimeTypeHelper.VDW_TYPE -> ContextCompat.getDrawable(context, R.drawable.preview_visio)

                else -> ContextCompat.getDrawable(context, R.drawable.preview_keynote)
            }
            fileIcon?.setImageDrawable(drawable)
        }
    }

    fun displayMultipleTitleName(
        shouldDelay: Boolean = true,
        title: MiddleMultilineTextView?,
        fileSize: TextView?,
        filePath: String?,
        lifecycleScope: LifecycleCoroutineScope
    ) {
        refreshMiddleMargin(title)
        refreshMiddleMargin(fileSize)
        //显示双行文本，此处需要一个延时，否则titleView的宽度不正确，计算的文本显示双行时未打点
        title?.post {
            title.text = Utils.setDisplayNameTextByLine(FileUtils.getName(filePath), title, EXTRACHAR) ?: ""
        }
    }

    fun showErrorPage(code: Int, force: Boolean = false) {
        Log.d(TAG, "showErrorPage -> code = $code")
        showDeviceStorageLimitDialogIfNeed(code)
        showPermissionDialogIfNeed(code, force)
    }

    private fun showDeviceStorageLimitDialogIfNeed(code: Int) {
        val activity = getAttachActivity()
        if (code == PreviewErrorCode.DEVICE_STORAGE_LIMIT.code) {
            //设备内存不足,弹内在不足提示
            releaseStorageLimitDialog()
            storageLimitDialog =
                COUIAlertDialogBuilder(activity).setTitle(stringResource(R.string.phone_storage_can_not_save))
                    .setBlurBackgroundDrawable(true)
                    .setPositiveButton(R.string.garbage_cleanup) { dialog, _ ->
                        dialog.dismiss()
                        KtAppUtils.startPhoneManager(activity)
                        CollectPrivacyUtils.collectPackage(FeatureCompatR.PKG_NAME_PHONE_MANAGER)
                        activity.finish()
                    }
                    .setNegativeButton(R.string.alert_dialog_cancel) { dialog, _ ->
                        dialog.dismiss()
                        activity.finish()
                    }
                    .create()
            storageLimitDialog?.setCanceledOnTouchOutside(false)
            storageLimitDialog?.show()
        }
    }

    private fun showPermissionDialogIfNeed(code: Int, force: Boolean) {
        if (code == PreviewErrorCode.NO_STORAGE_PERMISSION.code && (force || !hasShownPermissionDialog)) {
            releasePermissionDialog()
            permissionDialog = PermissionStorageController.createPermissionDialog(getAttachActivity())
            permissionDialog?.show()
            hasShownPermissionDialog = true
        }
    }

    fun releasePermissionDialog() {
        permissionDialog?.cancel()
        permissionDialog = null
        hasShownPermissionDialog = false
    }

    fun releaseStorageLimitDialog() {
        storageLimitDialog?.cancel()
        storageLimitDialog = null
    }

    /**
     * 原因：预加载页面中间的文本，小屏左右margin是60，大屏margin是160
     * 折叠屏时，先打开大屏，切换成小屏，屏幕宽度变化，但margin仍然是160的值，需要重置
     */
    private fun refreshMiddleMargin(view: View?) {
        view ?: return
        view.updateLayoutParams<ViewGroup.MarginLayoutParams> {
            val margin = if (!fromQuickView()) {
                ContextGetter.context.resources?.getDimensionPixelSize(R.dimen.preview_desc_margin_middle) ?: 0
            } else {
                ContextGetter.context.resources?.getDimensionPixelSize(R.dimen.quick_preview_desc_margin_middle) ?: 0
            }
            this.marginStart = margin
            this.marginEnd = margin
        }
    }

    fun fromQuickView(): Boolean {
        return false
    }
}