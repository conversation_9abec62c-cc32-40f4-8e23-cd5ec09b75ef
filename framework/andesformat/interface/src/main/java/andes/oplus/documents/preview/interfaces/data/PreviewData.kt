/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : PreviewData
 * * Description :  preview data
 * * Version     : 1.0
 * * Date        : 2022/12/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   hank.zhou                2022/12/13       1      create
 ***********************************************************************/
package andes.oplus.documents.preview.interfaces.data

import andes.oplus.documentsreader.core.common.FileUtils
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.database.data.PreviewDataEntity
import android.text.TextUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.util.concurrent.TimeUnit

class PreviewData {

    companion object {
        private const val TAG = "PreviewData"
        private const val LENGTH = "length"
        const val STATE_IDLE = 0
        const val STATE_PREPARE = 1
        const val STATE_UPLOADING = 2
        const val STATE_CONVERTING = 3
        const val STATE_CONVERTED = 4
        const val STATE_ERROR = 5
        const val STATE_DOWNLOADED = 6
        const val STATE_CANCELING = 7
        const val STATE_WAITING = 8
        const val STATE_PAUSING = 9

        const val SOURCE_KEY = "source_key"

        fun map(entity: PreviewDataEntity): PreviewData {
            val data = PreviewData()
            data.indexId = entity.id
            data.filePath = entity.filePath
            data.fileName = FileUtils.getName(entity.filePath)
            data.taskId = entity.taskId
            data.status = entity.status
            data.errorCode = entity.errorCode
            data.uploadProgress = entity.uploadProgress
            data.convertProgress = entity.convertProgress
            data.url = entity.convertUrl
            val mapStr = entity.convertMap
            if (mapStr != null) {
                val resultMap = hashMapOf<String, String>()
                data.map = Gson().fromJson<Map<String, String>>(mapStr, resultMap.javaClass)
            }
            data.convertedTime = entity.convertedTime
            data.downloadProgress = entity.downloadProgress
            data.savePath = entity.savePath
            data.fileMd5 = entity.fileMd5
            data.fileModifyTime = entity.fileModifyTime ?: ""
            data.fileSize = entity.fileSize
            data.fileConvertSize = kotlin.runCatching { data.map?.get(LENGTH)?.toLong() }.getOrDefault(0L) ?: 0L
            return data
        }

        fun map(data: PreviewData): PreviewDataEntity {
            val entity = PreviewDataEntity(id = data.indexId, filePath = data.filePath ?: "")
            entity.taskId = data.taskId
            entity.status = data.status
            entity.errorCode = data.errorCode
            entity.uploadProgress = data.uploadProgress
            entity.convertProgress = data.convertProgress
            entity.convertUrl = data.url
            val map = data.map
            if (map?.isNotEmpty() == true) {
                val type = object : TypeToken<Map<String, String>>() {}.type
                entity.convertMap = Gson().toJson(map, type)
            }
            entity.convertedTime = data.convertedTime
            entity.downloadProgress = data.downloadProgress
            entity.savePath = data.savePath
            entity.fileMd5 = data.fileMd5
            entity.fileModifyTime = data.fileModifyTime
            entity.fileSize = data.fileSize
            return entity
        }
    }

    var fileMd5: String? = null
    var fileName: String? = null
    var filePath: String? = null
    var status = STATE_IDLE
    var errorCode = 0
    var url: String? = null
    var map: Map<String, String>? = null
    var convertedTime: Long? = null // 转换完成的时间
    var taskId: String? = null
    var uploadProgress = 0
    var convertProgress = 0
    var downloadProgress = 0
    var savePath: String? = null
    var isNeedUpdate = false
    var indexId = 0L
    var fileModifyTime = ""
    var lastStatus = STATE_IDLE   //最后记录的状态，在暂停加载时，需要记录最后的状态，然后继续的时候再继上
    var fileSize: Long = 0L
    var fileConvertSize: Long = 0L

    fun isUploading(): Boolean {
        return status == STATE_UPLOADING
    }

    fun isPausing(): Boolean {
        return status == STATE_PAUSING
    }

    fun isConverting(): Boolean {
        return status == STATE_CONVERTING && TextUtils.isEmpty(taskId).not()
    }

    fun isConvertCompleted(): Boolean {
        return status == STATE_CONVERTED && TextUtils.isEmpty(url).not() && true == map?.isNotEmpty()
    }

    fun isConvertExpired(): Boolean {
        val result = status == STATE_CONVERTED
        val convertedTime = convertedTime ?: 0L
        val current = System.currentTimeMillis()
        return result && (current - convertedTime >= TimeUnit.DAYS.toMillis(1))
    }

    fun isDownload(): Boolean {
        return status == STATE_CONVERTED && TextUtils.isEmpty(savePath).not()
    }

    fun isError(): Boolean {
        return status == STATE_ERROR
    }

    fun reset() {
        fileMd5 = null
        fileName = null
        filePath = null
        status = STATE_IDLE
        errorCode = 0
        url = null
        map = null
        convertedTime = 0L // 转换完成的时间
        taskId = null
        uploadProgress = 0
        convertProgress = 0
        downloadProgress = 0
        savePath = null
        isNeedUpdate = false
        indexId = 0L
        fileModifyTime = ""
        fileSize = 0L
        fileConvertSize = 0L
    }

    fun resetConvertExpired() {
        this.url = null
        this.map = null
        this.convertedTime = null
        this.convertProgress = 0
    }

    override fun toString(): String {
        return "PreviewData(indexId=$indexId, taskId=$taskId, status=$status, errorCode=$errorCode, " +
                "url=(${Log.desensitizeUrl(url)}), lastStatus=$lastStatus , " +
                "uploadProgress=$uploadProgress , fileModifyTime=$fileModifyTime, fileSize=$fileSize)"
    }

    fun switchStatus(status: Int) {
        if (isPausing()) {
            lastStatus = status
        } else {
            this.status = status
        }
    }

    /**
     * 因SDK返回的当前进度，可能小于已经显示的进度
     * 所以当要更新的进度大于当前进度时，才刷新上传进度
     */
    fun refreshUploadProgressSafe(progress: Int) {
        if (progress > uploadProgress) {
            uploadProgress = progress
        }
    }
}