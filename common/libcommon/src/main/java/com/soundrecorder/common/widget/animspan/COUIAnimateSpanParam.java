/*********************************************************************
 ** Copyright (C), 2024-2034 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : COUIAnimateTextView
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/7/17 14:46
 ** Author      : 80342011
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 ** 80262777        2024/7/17       1.0      create
 ***********************************************************************/
package com.soundrecorder.common.widget.animspan;

public class COUIAnimateSpanParam {
    public long duration;
    public long delay;
    public float textSize;
    public float offset;
    public int startColor;
    public int endColor;
    public int stableColor;
    public Runnable rundRunnable;
}
