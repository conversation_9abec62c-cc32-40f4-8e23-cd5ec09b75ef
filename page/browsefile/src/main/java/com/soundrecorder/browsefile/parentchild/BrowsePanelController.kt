/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: BrowsePanelController
 * Description:
 * Version: 1.0
 * Date: 2022/12/22
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/12/22 1.0 create
 */

package com.soundrecorder.browsefile.parentchild

import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.view.animation.PathInterpolator
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.Guideline
import androidx.core.view.isInvisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.commit
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.findFragment
import com.soundrecorder.base.ext.hideFragment
import com.soundrecorder.base.ext.removeFragment
import com.soundrecorder.base.ext.removeFragmentCustomAnimations
import com.soundrecorder.base.ext.replaceFragmentByTag
import com.soundrecorder.base.ext.showFragment
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.home.BrowseFragment
import com.soundrecorder.browsefile.home.call.CallRecordListFragment
import com.soundrecorder.browsefile.parentchild.BrowseAnimUtil.Companion.GUIDE_PERCENT_0
import com.soundrecorder.browsefile.parentchild.BrowseAnimUtil.Companion.GUIDE_PERCENT_0_7
import com.soundrecorder.browsefile.parentchild.BrowseAnimUtil.Companion.GUIDE_PERCENT_1
import com.soundrecorder.browsefile.parentchild.BrowseAnimUtil.Companion.GUIDE_PERCENT_2
import com.soundrecorder.browsefile.parentchild.BrowseAnimUtil.Companion.GUIDE_PERCENT_NEGATIVE_0_3
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.databean.CallGroupModel
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.modulerouter.playback.PlaybackFragmentAction
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class BrowsePanelController(
    val activity: AppCompatActivity,
    private val mBrowseFileActivityViewModel: BrowseFileActivityViewModel?
) {
    private val logTag = "BrowsePanelController"

    companion object {
        const val TAG_LEFT_FRAGMENT = "left"
        const val TAG_RIGHT_FRAGMENT = "right"
        const val TAG_PLAYBACK_EMPTY = "right_empty"
        /*父子级结构下，左侧默认占屏幕的0.4*/
        const val PARENT_DEFAULT_WIDTH_PERCENT = 0.4F
        const val CHILD_ANIM_RUN_DELAY = 20L
        const val TAG_LEFT_CALL_FRAGMENT = "CallRecordListFragment"
        private const val DEFAULT_MASK_DISAPPEAR_DURATION = 270L
        private const val DEFAULT_MASK_APPEAR_DURATION = 400L
        private val DEFAULT_MASK_ANIMATION_PATH_INTERPOLATOR = PathInterpolator(0.3f, 0f, 1f, 1f)
    }

    private var parentDividerLine: View? = null
    private var parentStartLine: Guideline? = null
    private var parentEndLine: Guideline? = null
    private var childStartLine: Guideline? = null
    private var childEndLine: Guideline? = null
    private var childView: ViewGroup? = null
    private var parentView: ViewGroup? = null
    private var mAnimUtils: BrowseAnimUtil? = null
    private var maskViewStub: ViewStub? = null
    private var maskView: View? = null
    var lowMemoryReCreate = false

    init {
        parentDividerLine = activity.findViewById(R.id.view_subwindow_line)
        parentStartLine = activity.findViewById(R.id.parent_start_guide_line)
        parentEndLine = activity.findViewById(R.id.parent_end_guide_line)
        childStartLine = activity.findViewById(R.id.child_start_guide_line)
        childEndLine = activity.findViewById(R.id.child_end_guide_line)
        parentView = activity.findViewById(R.id.fl_left_container)
        childView = activity.findViewById(R.id.fl_right_container)
        maskViewStub = activity.findViewById<ViewStub>(R.id.view_right_mask)
        mBrowseFileActivityViewModel?.mCurrentPlayRecordData?.observe(activity) {
            //逻辑上是观察到播放内容有变化，然后往下处理。
            onPlayRecordChanged(it)
        }
        mBrowseFileActivityViewModel?.windowType?.observe(activity) { windowType ->
            DebugUtil.i(logTag, "windowType changed $windowType")
            updateLayoutWhenWindowTypeChanged(windowType)
            showEmptyPlaybackFragmentIfNecessary()
        }
        mBrowseFileActivityViewModel?.allRecordCount?.observe(activity) {
            DebugUtil.i(logTag, "allRecordCount value: $it")
            showEmptyPlaybackFragmentIfNecessary()
        }
        mBrowseFileActivityViewModel?.mCallGroupMoreData?.observe(activity) {
            showCallGroupMoreFragmentIfNecessary(it)
        }
        mBrowseFileActivityViewModel?.isGroupListShow?.observe(activity) {
            showRightMaskView(it)
        }
        maskViewStub?.setOnInflateListener { _, inflated ->
            inflated.setOnClickListener {
                if (activity.supportFragmentManager.backStackEntryCount > 0) {
                    activity.onBackPressed()
                }
            }
        }
    }

    private fun showRightMaskView(isShow: Boolean) {
        if (isShow) {
            showMaskAnimation(true)
        } else {
            hideMaskAnimation(true)
        }
    }

    /**
     * 蒙层显示动效
     *
     * @param normal 动效时长
     */
    @Suppress("MagicNumber")
    private fun showMaskAnimation(normal: Boolean = false) {
        inflateMaskView()
        maskView?.apply {
            alpha = 0f
            visibility = View.VISIBLE

            animate().apply {
                if (normal) {
                    duration = 350
                    interpolator = COUIMoveEaseInterpolator()
                } else {
                    duration = DEFAULT_MASK_APPEAR_DURATION
                    interpolator = DEFAULT_MASK_ANIMATION_PATH_INTERPOLATOR
                }
                alpha(1f)
            }.start()
        }
    }

    /**
     * 蒙层消失动效
     *
     * @param normal 动效时长
     */
    @Suppress("MagicNumber")
    private fun hideMaskAnimation(normal: Boolean = false) {
        maskView?.apply {
            visibility = View.VISIBLE
            alpha = 1f

            animate().apply {
                if (normal) {
                    duration = 350
                    interpolator = COUIMoveEaseInterpolator()
                } else {
                    duration = DEFAULT_MASK_DISAPPEAR_DURATION
                    interpolator = DEFAULT_MASK_ANIMATION_PATH_INTERPOLATOR
                }
                alpha(0f)
            }.withEndAction {
                visibility = View.GONE
            }.start()
        }
    }

    private fun inflateMaskView() {
        if (maskView == null) {
            runCatching {
                val viewStubContainer = maskViewStub?.inflate()
                maskView = viewStubContainer?.findViewById<View>(R.id.mask_view)
            }
        }
    }

    private fun showCallGroupMoreFragmentIfNecessary(callGroupModel: CallGroupModel?) {
        DebugUtil.i(logTag, "showCallGroupMoreFragmentIfNecessary: $callGroupModel")
        if (callGroupModel == null) {
            removeCallRecordListFragment()
        } else {
            showCallGroupMoreFragment(callGroupModel)
        }
    }

    private fun showCallGroupMoreFragment(mode: CallGroupModel) {
        val oldPlayFragment = activity.supportFragmentManager.findFragmentByTag(TAG_LEFT_CALL_FRAGMENT)
        activity.supportFragmentManager.commit(true) {
            if (mBrowseFileActivityViewModel?.isSmallWindow() == true) {
                setCustomAnimations(com.support.appcompat.R.anim.coui_open_slide_enter,
                    com.support.appcompat.R.anim.coui_open_slide_exit)
            } else {
                setCustomAnimations(R.anim.detail_fragment_alpha_in, R.anim.detail_fragment_alpha_out)
            }
            CallRecordListFragment.newInstance(mode.groupInfo, mode.callerName, mode.avatarColor).let {
                add(R.id.fl_left_container, it, TAG_LEFT_CALL_FRAGMENT)
            }
            oldPlayFragment?.let {
                remove(it)
            }
        }
    }

    private fun removeCallRecordListFragment() {
        if (mBrowseFileActivityViewModel?.isSmallWindow() == true) {
            activity.removeFragmentCustomAnimations(getCallRecordListFragment())
        } else {
            activity.removeFragment(getCallRecordListFragment())
        }
    }

    /**
     * windowType改变，动态更新布局
     */
    private fun updateLayoutWhenWindowTypeChanged(windowType: WindowType) {
        parentDividerLine?.isInvisible = windowType == WindowType.SMALL
        val hasPlayPage = mBrowseFileActivityViewModel?.hasPlayPageData() ?: false
        if (windowType == WindowType.SMALL) {
            if (hasPlayPage) {
                updatePercentHasChildInSmallWindow()
                hideBrowseFragment()
            } else {
                updatePercentNoChildInSmallWindow()
            }
        } else {
            // 分屏模式下，小屏进入播放详情，跳转动画增加了maskView，其他应用切换为浮窗，录音不会重建，走onLayoutChange，如果再这里remove掉会crash，所以通过设置visible的形式规避该问题
            mAnimUtils?.hideParentMaskView()
            updatePercentInOverSmallWindow()
            showBrowseFragment()
        }
    }

    private fun showBrowseFragment(forceShow: Boolean = true) {
        if (forceShow || (mBrowseFileActivityViewModel?.hasPlayPageData() != true) || !mBrowseFileActivityViewModel.isSmallWindow()) {
            activity.showFragment(getBrowseFragment(), true)
        }
    }

    private fun hideBrowseFragment(forceHide: Boolean = true) {
        if (forceHide || (mBrowseFileActivityViewModel?.hasPlayPageData() == true && mBrowseFileActivityViewModel.isSmallWindow())) {
            activity.hideFragment(getBrowseFragment(), true)
        }
    }

    /**
     * 小屏有播放详情
     */
    fun updatePercentHasChildInSmallWindow() {
        /*-0.3 - 0.7*/
        if (parentStartLine.guidePercent() != GUIDE_PERCENT_NEGATIVE_0_3) {
            parentStartLine?.setGuidelinePercent(GUIDE_PERCENT_NEGATIVE_0_3)
            parentEndLine?.setGuidelinePercent(GUIDE_PERCENT_0_7)
        }

        if (childStartLine.guidePercent() != GUIDE_PERCENT_0) { // 0 - 1
            childStartLine?.setGuidelinePercent(GUIDE_PERCENT_0)
            childEndLine?.setGuidelinePercent(GUIDE_PERCENT_1)
        }
    }

    /**
     * 小屏无播放详情
     */
    fun updatePercentNoChildInSmallWindow() {
        /*0 - 1*/
        parentStartLine?.setGuidelinePercent(GUIDE_PERCENT_0)
        parentEndLine?.setGuidelinePercent(GUIDE_PERCENT_1)

        // 1 - 2
        childStartLine?.setGuidelinePercent(GUIDE_PERCENT_1)
        childEndLine?.setGuidelinePercent(GUIDE_PERCENT_2)
    }

    fun updatePercentInOverSmallWindow() {
        parentStartLine?.setGuidelinePercent(GUIDE_PERCENT_0)
        // 父子级结构下，左侧默认占屏幕的0.4,最大不超过 360dp
        var centerLineRadioOnMiddleWindow = PARENT_DEFAULT_WIDTH_PERCENT
        val parentMaxWidth = activity.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.sub_window_parent_max_width)
        val screenWidth = ScreenUtil.getRealScreenWidth()
        if ((screenWidth * PARENT_DEFAULT_WIDTH_PERCENT) > parentMaxWidth) {
            centerLineRadioOnMiddleWindow = parentMaxWidth.toFloat() / screenWidth
        }
        DebugUtil.d(logTag, "updatePercentInOverSmallWindow centerLinePercent = $centerLineRadioOnMiddleWindow")
        parentEndLine?.setGuidelinePercent(centerLineRadioOnMiddleWindow)
        childStartLine?.setGuidelinePercent(centerLineRadioOnMiddleWindow)
        childEndLine?.setGuidelinePercent(GUIDE_PERCENT_1)
    }

    /**
     * 播放内容发生变化
     */
    private fun onPlayRecordChanged(playModel: StartPlayModel?) {
        DebugUtil.i(logTag, "onPlayRecordChanged $playModel")
        if (playModel == null) {
            // 无播放数据，移除播放fragment，中大屏下显示空页面
            removePlayBackFragment()
            showEmptyPlaybackFragmentIfNecessary()
        } else {
            showPlayBackFragment(playModel)
        }
        mBrowseFileActivityViewModel?.lastRecordId = playModel?.mediaId ?: -1
    }

    /**
     * 显示未选中录音的空页面
     * 满足条件：
     * 1.中大屏设备
     * 2.列表所有录音有音频数据
     * 3.无播放项
     */
    private fun showEmptyPlaybackFragmentIfNecessary() {
        /*小屏不显示; 有选中音频不显示,音频数量为0不显示*/
        val canLoadEmpty = ((mBrowseFileActivityViewModel?.windowType?.value != WindowType.SMALL)
                && (mBrowseFileActivityViewModel?.noPlayPageData() != false)
                && ((mBrowseFileActivityViewModel?.allRecordCount?.value ?: 0) > 0))
        val emptyFragment = getPlaybackEmptyFragment()
        if (canLoadEmpty && (emptyFragment == null)) {
            activity.replaceFragmentByTag(R.id.fl_right_container, PlaybackFragmentAction.newPlaybackEmptyFragment(), TAG_PLAYBACK_EMPTY)
        } else if (!canLoadEmpty && (emptyFragment != null)) {
            removeEmptyFragment(emptyFragment)
        }
    }

    private fun removeEmptyFragment(fragment: Fragment? = null) {
        (fragment ?: getPlaybackEmptyFragment())?.let {
            activity.removeFragment(it)
        }
    }

    /**
     * 加载播放fragment
     */
    private fun showPlayBackFragment(playModel: StartPlayModel) {
        if ((playModel.mediaId == mBrowseFileActivityViewModel?.lastRecordId) && (getPlaybackContainerFragment() != null)) {
            return
        }

        // 小屏手机+child容器不在屏幕里才需要做动画(如：中大屏左右结构、小屏重建-关权限、低内存，不需要动画)
        val isNeedEnterAnimOnSmallWindow =
            !lowMemoryReCreate && (mBrowseFileActivityViewModel?.isSmallWindow() == true) && (childStartLine?.guidePercent() != GUIDE_PERCENT_0)

        if (isNeedEnterAnimOnSmallWindow) {
            mBrowseFileActivityViewModel?.childAnimRunning?.value = true // 需要show动画，标记动画开始，详情拿到数据后不急着显示内容
        }

        // 低内存进入，windowType的观察已经结束，拿到播放数据后，矫正控件位置
        if (lowMemoryReCreate && (mBrowseFileActivityViewModel?.isSmallWindow() == true)) {
            updatePercentHasChildInSmallWindow()
        }
        val oldPlayFragment = activity.supportFragmentManager.findFragmentByTag(TAG_RIGHT_FRAGMENT)
        PlaybackFragmentAction.newPlaybackFragment(checkDecodeAmpOverLimit(playModel))?.let {
            activity.supportFragmentManager.commit(true) {
                if (mBrowseFileActivityViewModel?.isSmallWindow() != true) {
                    setCustomAnimations(R.anim.detail_fragment_alpha_in, R.anim.detail_fragment_alpha_out)
                }
                add(R.id.fl_right_container, it, TAG_RIGHT_FRAGMENT)
                oldPlayFragment?.let {
                    remove(it)
                }
            }
            removeEmptyFragment()
        }

        if (isNeedEnterAnimOnSmallWindow) {
            initAnimUtil()
            activity.lifecycleScope.launch {
                // 延迟动画执行，避免同时 加载fragmentTransition和动画，导致动画卡顿
                delay(CHILD_ANIM_RUN_DELAY)
                if (mBrowseFileActivityViewModel?.hasPlayPageData() == true) {
                    mAnimUtils?.runShowDetailAnim() {
                        mBrowseFileActivityViewModel.childAnimRunning.value = false //动画执行结束，标记为false，通知详情隐藏loading，显示内容
                        /*小屏下播放页面完全进入后，隐藏下面的列表页，避免页面过度绘制*/
                        hideBrowseFragment(false)
                    }
                }
            }
        }
        lowMemoryReCreate = false
    }

    private fun initAnimUtil() {
        if (mAnimUtils == null) {
            mAnimUtils = BrowseAnimUtil(
                parentStartLine,
                parentEndLine,
                childStartLine,
                childEndLine,
                parentView,
                childView)
        }
    }

    /**
     * 预估加载波形时间是否超过 400ms
     * 1. 波形已在本地，判断为 400ms内,由于设备性能不一，若超过2小时，判断为 400ms+
     * 2.波形未解析，超过1分钟，则认为需要耗时 400ms+
     */
    private fun checkDecodeAmpOverLimit(playModel: StartPlayModel): Boolean {
        if (playModel.duration > Constants.TIME_ONE_MINUTE * NumberConstant.NUM_120) {
            return true
        }
        val hasAmp = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).checkLocalHasAmp(playModel.playPath)
        if (!hasAmp) {
            return playModel.duration > Constants.TIME_ONE_MINUTE
        }
        return false
    }

    /**
     * 移除子fragment-播放详情
     */
    private fun removePlayBackFragment() {
        /*移除播放页面前,先将列表页面显示出来*/
        showBrowseFragment()
        val removeFunc = {
            activity.removeFragment(getPlaybackContainerFragment())
        }
        if ((mBrowseFileActivityViewModel?.isSmallWindow() == true)) {
            initAnimUtil()
            // 延迟20ms，避免返回首页，首页闪白
            mAnimUtils?.runRemoveDetailAnim(CHILD_ANIM_RUN_DELAY, removeFunc)
        } else {
            removeFunc.invoke()
        }
    }

    /**
     * 暂停播放
     * @param clearNotification 是否清除通知栏播放卡片 true：要清除
     */
    fun pausePlayDetail(clearNotification: Boolean) {
        getPlaybackContainerFragment()?.let {
            PlaybackFragmentAction.pausePlay(it, clearNotification)
        }
    }

    fun getPlaybackContainerFragment(): Fragment? = activity.findFragment(TAG_RIGHT_FRAGMENT)

    fun getBrowseFragment(): BrowseFragment? = activity.findFragment(TAG_LEFT_FRAGMENT)

    fun getPlaybackEmptyFragment(): Fragment? = activity.findFragment(TAG_PLAYBACK_EMPTY)

    fun getCallRecordListFragment(): CallRecordListFragment? = activity.findFragment(TAG_LEFT_CALL_FRAGMENT)

    fun onDestroy() {
        mAnimUtils?.release()
        mAnimUtils = null
    }
}