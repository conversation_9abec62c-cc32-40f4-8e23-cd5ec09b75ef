/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.module.operation
 * * Version     : 1.0
 * * Date        : 2020/2/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.core.common.sort

import andes.oplus.documentsreader.core.common.BaseFileBean
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.MimeTypeHelper
import andes.oplus.documentsreader.core.common.R
import androidx.annotation.VisibleForTesting
import java.util.Collections
import kotlin.collections.ArrayList

object SortHelper {
    private const val TAG = "SortHelper"

    /**
     * Sort by file name.
     */
    const val FILE_NAME_ORDER = 0

    /**
     * Sort by file extension.
     */
    const val FILE_TYPE_ORDER = 2

    /**
     * Sort by file size is counting the folder.
     */
    const val FILE_SIZE_SUMDIR_REVERSE_ORDER = 7

    /**
     * Sort by file last modify time.
     */
    const val FILE_TIME_REVERSE_ORDER = 9

    /**
     * Sort by file last open time of file.
     */
    const val FILE_LAST_OPEN_TIME_ORDER = 10

    /**
     * Recycle Bin Sort
     * Sort by file delete time.(DESC)
     */
    const val FILE_TIME_DELETE_ORDER = 99
    const val RECYCLE_BIN_DEFAULT_ORDER = FILE_TIME_DELETE_ORDER


    val ITEM_IDS = intArrayOf(
        R.string.folder, R.string.string_photos, R.string.string_videos,
        R.string.string_audio, R.string.string_documents, R.string.string_apk, R.string.string_compress, R.string.string_other
    )

    fun sortCategoryFiles(files: List<BaseFileBean>, order: Int, category: Int, isDesc: Boolean) {
        try {
            Collections.sort(files, getComparatorCategory(order, category, isDesc))
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, e.message)
        }
    }

    fun sortByNameWhenSameTime(list: MutableList<BaseFileBean>) {
        list.sortWith(object : Comparator<BaseFileBean> {
            override fun compare(file1: BaseFileBean, file2: BaseFileBean): Int {
                var result = LastModifiedComparatorGenerator.REVERSE_COMPARATOR.compare(file1, file2)
                // If both modify time are same, compare name
                if (result == 0) {
                    return NameComparatorGenerator.COMPARATOR_CATEGORY.compare(file1, file2)
                }
                return result
            }
        })
    }

    fun sortFileIgnoreHeadLabel(files: List<BaseFileBean>, order: Int, lastMode: Int, isDesc: Boolean) {
        try {
            Log.d(TAG, "sortFileIgnoreHeadLabel -> order = $order ; ${files is ArrayList}")
            if ((order == FILE_TYPE_ORDER) && (files is ArrayList)) {
                Collections.sort(files, getComparator(FILE_NAME_ORDER, lastMode, isDesc))
                val folderFiles = ArrayList<BaseFileBean>()
                val imageFiles = ArrayList<BaseFileBean>()
                val videoFiles = ArrayList<BaseFileBean>()
                val audioFiles = ArrayList<BaseFileBean>()
                val docFiles = ArrayList<BaseFileBean>()
                val appFiles = ArrayList<BaseFileBean>()
                val compressFiles = ArrayList<BaseFileBean>()
                val otherFiles = ArrayList<BaseFileBean>()
                files.forEach {
                    classifyFileByMimeType(it, folderFiles, imageFiles, videoFiles, audioFiles, docFiles,
                        appFiles, compressFiles, otherFiles)
                }
                files.clear()
                val dataList = arrayOf(
                    folderFiles, imageFiles, videoFiles,
                    audioFiles, docFiles, appFiles,
                    compressFiles, otherFiles)
                val size = dataList.size
                for (index in dataList.indices) {
                    val i = getDescIndex(index, size, isDesc)
                    if (dataList[i].size > 0) {
                        files.addAll(dataList[i])
                        dataList[i].clear()
                    }
                }
            } else {
                Collections.sort(files, getComparator(order, lastMode, isDesc))
            }
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, e.message)
        }
    }

    fun sortFiles(
        files: List<BaseFileBean>,
        order: Int,
        lastMode: Int,
        hideHeadLabel: Boolean = false,
        isDesc: Boolean,
        includeOtherDocType: Boolean = false
    ) {
        try {
            if ((order == FILE_TYPE_ORDER) && (files is ArrayList)) {
                Collections.sort(files, getComparator(FILE_NAME_ORDER, lastMode, isDesc))
                if (!hideHeadLabel) {
                    val folderFiles = ArrayList<BaseFileBean>()
                    val imageFiles = ArrayList<BaseFileBean>()
                    val videoFiles = ArrayList<BaseFileBean>()
                    val audioFiles = ArrayList<BaseFileBean>()
                    val docFiles = ArrayList<BaseFileBean>()
                    val appFiles = ArrayList<BaseFileBean>()
                    val compressFiles = ArrayList<BaseFileBean>()
                    val otherFiles = ArrayList<BaseFileBean>()
                    files.forEach {
                        classifyFileByMimeType(it, folderFiles, imageFiles, videoFiles, audioFiles, docFiles,
                                appFiles, compressFiles, otherFiles)
                    }
                    if (includeOtherDocType) {
                        classifyOtherDocFile(docFiles, otherFiles)
                    }
                    files.clear()

                    var fileRelatedHeader: BaseFileBean?
                    val dataList = arrayOf(
                            folderFiles, imageFiles, videoFiles,
                            audioFiles, docFiles, appFiles,
                            compressFiles, otherFiles)
                    val size = dataList.size
                    for (index in dataList.indices) {
                        val i = getDescIndex(index, size, isDesc)
                        if (dataList[i].size > 0) {
                            fileRelatedHeader = BaseFileBean()
                            fileRelatedHeader.mFileWrapperLabel = ITEM_IDS[i]
                            fileRelatedHeader.mFileWrapperViewType = BaseFileBean.TYPE_LABEL_FILE
                            fileRelatedHeader.mFileWrapperTypeNum = dataList[i].size
                            files.add(fileRelatedHeader)
                            files.addAll(dataList[i])
                            dataList[i].clear()
                        }
                    }
                }
            } else {
                Collections.sort(files, getComparator(order, lastMode, isDesc))
            }
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, e.message)
        }
    }

    fun <T : BaseFileBean> classifyFileByMimeType(file: T, folderFiles: ArrayList<T>,
                                                  imageFiles: ArrayList<T>,
                                                  videoFiles: ArrayList<T>,
                                                  audioFiles: ArrayList<T>,
                                                  docFiles: ArrayList<T>,
                                                  appFiles: ArrayList<T>,
                                                  compressFiles: ArrayList<T>,
                                                  otherFiles: ArrayList<T>) {
        when {
            file.mLocalType == MimeTypeHelper.DIRECTORY_TYPE -> {
                folderFiles.add(file)
            }
            file.mLocalType == MimeTypeHelper.IMAGE_TYPE -> {
                imageFiles.add(file)
            }
            file.mLocalType == MimeTypeHelper.VIDEO_TYPE -> {
                videoFiles.add(file)
            }
//            MimeTypeHelper.isAudioType(file.mLocalType) -> {
//                audioFiles.add(file)
//            }
            MimeTypeHelper.isDocType(file.mLocalType) -> {
                docFiles.add(file)
            }
            file.mLocalType == MimeTypeHelper.APPLICATION_TYPE -> {
                appFiles.add(file)
            }
            file.mLocalType == MimeTypeHelper.COMPRESSED_TYPE -> {
                compressFiles.add(file)
            }
            else -> {
                otherFiles.add(file)
            }
        }
    }

    /**
     * 将其他文件中是扩展doc的文件，放到docFiles中
     */
    fun <T : BaseFileBean> classifyOtherDocFile(docFiles: ArrayList<T>, otherFiles: ArrayList<T>) {
        val tempOthers = ArrayList<T>()
        for (file in otherFiles) {
            if (MimeTypeHelper.isDocType(file.mLocalType) || MimeTypeHelper.isOtherDocType(file.mLocalType)) {
                docFiles.add(file)
            } else {
                tempOthers.add(file)
            }
        }
        otherFiles.clear()
        otherFiles.addAll(tempOthers)
    }


    fun <T : BaseFileBean> classifyFileByDOCType(file: T,folderFiles: ArrayList<T>,
                                                 docFiles: ArrayList<T>, xlsFiles: ArrayList<T>, pptFiles: ArrayList<T>,
                                                 pdfFiles: ArrayList<T>, ofdFiles: ArrayList<T>, iworkFiles: ArrayList<T>,
                                                 xmindFiles: ArrayList<T>, visioFiles: ArrayList<T>, txtFiles: ArrayList<T>,
                                                 cadFiles: ArrayList<T>, psdFiles: ArrayList<T>, aiFiles: ArrayList<T>,
                                                 mdFiles: ArrayList<T> ,otherFiles: ArrayList<T>
    ) {
        when {
            file.mLocalType == MimeTypeHelper.DIRECTORY_TYPE ->
                folderFiles.add(file)

            file.mLocalType == MimeTypeHelper.DOC_TYPE ->
                docFiles.add(file)

            file.mLocalType == MimeTypeHelper.DOCX_TYPE ->
                docFiles.add(file)

            file.mLocalType == MimeTypeHelper.XLSX_TYPE ->
                xlsFiles.add(file)

            file.mLocalType == MimeTypeHelper.XLSX_TYPE ->
                xlsFiles.add(file)

            file.mLocalType == MimeTypeHelper.PPTX_TYPE ->
                pptFiles.add(file)

            file.mLocalType == MimeTypeHelper.PPT_TYPE ->
                pptFiles.add(file)

            file.mLocalType == MimeTypeHelper.PDF_TYPE ->
                pdfFiles.add(file)

            file.mLocalType == MimeTypeHelper.OFD_TYPE ->
                ofdFiles.add(file)

            MimeTypeHelper.isIworkType(file.mLocalType) ->
                iworkFiles.add(file)

            file.mLocalType == MimeTypeHelper.XMIND_TYPE ->
                xmindFiles.add(file)

            MimeTypeHelper.isVisioType(file.mLocalType) ->
                visioFiles.add(file)

            file.mLocalType == MimeTypeHelper.TXT_TYPE ->
                txtFiles.add(file)

            MimeTypeHelper.isCADType(file.mLocalType) ->
                cadFiles.add(file)

            file.mLocalType == MimeTypeHelper.PSD_TYPE ->
                psdFiles.add(file)

            file.mLocalType == MimeTypeHelper.AI_TYPE ->
                aiFiles.add(file)

            file.mLocalType == MimeTypeHelper.MARKDOWN_TYPE -> mdFiles.add(file)
            else -> otherFiles.add(file)
        }
    }

    fun getComparator(order: Int, lastMode: Int, isDesc: Boolean = true): Comparator<BaseFileBean>? {
        Log.d(TAG, "getComparator order = $order isDesc = $isDesc")
        when (order) {
            FILE_NAME_ORDER -> {
                return NameComparatorGenerator.getNameComparator(isDesc)
            }
            FILE_TYPE_ORDER -> {
                ExtensionComparatorGenerator.setLastSortMode(lastMode)
                ExtensionComparatorGenerator.setCategoryType(-1)
                return ExtensionComparatorGenerator.getComparator(isDesc)
            }
            FILE_SIZE_SUMDIR_REVERSE_ORDER -> {
                SizeComparatorGenerator.setLastSortMode(lastMode)
                return SizeComparatorGenerator.getComparator(isDesc)
            }
            FILE_TIME_REVERSE_ORDER -> {
                return LastModifiedComparatorGenerator.getComparator(isDesc)
            }
        }
        return null
    }

    @VisibleForTesting
    fun getComparatorCategory(order: Int, category: Int, isDesc: Boolean = true): Comparator<BaseFileBean>? {
        Log.d(TAG, "getComparatorCategory order = $order isDesc = $isDesc")
        when (order) {
            FILE_NAME_ORDER -> {
                return if (category >= 0) {
                    NameComparatorGenerator.getCategoryComparator(isDesc)
                } else {
                    NameComparatorGenerator.getNameComparator(isDesc)
                }
            }
            FILE_TYPE_ORDER -> {
                if (category > 0) {
                    ExtensionComparatorGenerator.setCategoryType(category)
                }
                return ExtensionComparatorGenerator.getComparator(isDesc)
            }
            FILE_SIZE_SUMDIR_REVERSE_ORDER -> {
                return SizeComparatorGenerator.getComparator(isDesc)
            }
            FILE_TIME_REVERSE_ORDER -> {
                return LastModifiedComparatorGenerator.getComparator(isDesc)
            }
            FILE_LAST_OPEN_TIME_ORDER -> return LastOpenTimeComparatorGenerator.getComparator(isDesc)
        }
        return null
    }

    /**
     * 根据顺序、倒序获取对应的index
     * @param index 当前index
     * @param size 列表的size
     * @param isDesc 顺序或者倒序
     */
    fun getDescIndex(index: Int, size: Int, isDesc: Boolean): Int {
        return if (isDesc) {
            index
        } else {
            size - index - 1
        }
    }
}