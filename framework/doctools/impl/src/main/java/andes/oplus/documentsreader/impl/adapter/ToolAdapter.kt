/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: ToolAdapter
 * * Description: ToolAdapter
 * * Version: 1.0
 * * Date : 2024/07/03
 * * Author:ztf
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *        ztf           2024/07/03       1.0            create
 ****************************************************************/
package andes.oplus.documentsreader.impl.adapter

import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.doctools.impl.R
import andes.oplus.documentsreader.impl.bean.ToolBean
import andes.oplus.documentsreader.impl.bean.ToolContentBean
import andes.oplus.documentsreader.impl.bean.ToolTitleBean
import andes.oplus.documentsreader.impl.enumer.EnumLayout
import andes.oplus.documentsreader.impl.enumer.EnumWay
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.textview.COUITextView

class ToolAdapter(
    private val context: Context,
    private val wayEnum: EnumWay = EnumWay.WAY_INIT,
    private val clickItemListener: ((position: Int, data: ToolContentBean?) -> Unit)? = null
) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TAG = "ToolAdapter"
        private const val LINE_WRAP_TEXT_SIZE_CHANGE = 13f
    }

    var listData: MutableList<ToolBean> = mutableListOf()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {

        return when (viewType) {
            EnumLayout.LAYOUT_CONTENT_SMALL.layout -> {
                ContentSmallViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(R.layout.oplus_doctools_item_view_content_small, parent, false)
                )
            }

            EnumLayout.LAYOUT_CONTENT_LARGE.layout -> {
                ContentLargeViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(R.layout.oplus_doctools_item_view_content_large, parent, false)
                )
            }

            EnumLayout.LAYOUT_TITLE_PANEL.layout -> {
                TitlePanelViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(R.layout.oplus_doctools_item_view_title_panel, parent, false)
                )
            }

            else -> {
                TitleViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(R.layout.oplus_doctools_item_view_title, parent, false)
                )
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is ContentSmallViewHolder -> {
                holder.notifyData(context, listData[position], wayEnum)
                holder.itemView.setOnClickListener {
                    val bean = listData[position]
                    if (bean is ToolContentBean) {
                        clickItemListener?.invoke(position, bean)
                    }
                }
            }

            is ContentLargeViewHolder -> {
                holder.notifyData(context, listData[position], wayEnum)
                holder.itemView.setOnClickListener {
                    val bean = listData[position]
                    if (bean is ToolContentBean) {
                        clickItemListener?.invoke(position, bean)
                    }
                }
            }

            is TitlePanelViewHolder -> holder.notifyData(listData[position])

            is TitleViewHolder -> holder.notifyData(listData[position])

            else -> {
                Log.d(TAG, "do nothing")
            }
        }
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemCount(): Int {
        return when (wayEnum) {
            EnumWay.WAY_PANEL -> {
                if (listData.size > 5) {
                    5
                } else {
                    listData.size
                }
            }

            else -> {
                listData.size
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (listData[position].layoutEnum) {
            EnumLayout.LAYOUT_CONTENT_SMALL -> {
                EnumLayout.LAYOUT_CONTENT_SMALL.layout
            }

            EnumLayout.LAYOUT_CONTENT_LARGE -> {
                EnumLayout.LAYOUT_CONTENT_LARGE.layout
            }

            EnumLayout.LAYOUT_TITLE_PANEL -> EnumLayout.LAYOUT_TITLE_PANEL.layout

            else -> {
                EnumLayout.LAYOUT_TITLE.layout
            }
        }
    }

    class TitleViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private var iconTitle: ImageView = itemView.findViewById(R.id.icon_title)
        private var title: COUITextView = itemView.findViewById(R.id.tv_title)
        private var tvLookAll: COUITextView = itemView.findViewById(R.id.tv_look_all)

        init {
            tvLookAll.isVisible = false
        }

        fun notifyData(item: ToolBean) {
            if (item !is ToolTitleBean) {
                return
            }
            if (item.iconTitle == 0) {
                iconTitle.isVisible = false
            } else {
                iconTitle.isVisible = true
                iconTitle.setImageResource(item.iconTitle)
            }
            title.text = item.title
        }
    }

    class TitlePanelViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private var title: COUITextView = itemView.findViewById(R.id.tv_title_panel)

        fun notifyData(item: ToolBean) {
            if (item !is ToolTitleBean) {
                return
            }
            title.text = item.title
        }
    }

    class ContentSmallViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private var iconSmall: ImageView = itemView.findViewById(R.id.icon_small)
        private var tvTitleMainSmall: COUITextView = itemView.findViewById(R.id.tv_title_main_small)
        private var itemSmall: COUICardListSelectedItemLayout = itemView.findViewById(R.id.item_small)

        fun notifyData(context: Context, item: ToolBean, wayEnum: EnumWay) {
            if (item !is ToolContentBean) {
                return
            }
            iconSmall.setImageResource(item.iconContent)
            tvTitleMainSmall.text = item.contentMainTitle
            if (wayEnum == EnumWay.WAY_HOME) {
                itemSmall.refreshCardBg(context.resources.getColor(item.bgColor, context.theme))
            } else {
                Log.d(TAG, "ContentSmallViewHolder refreshCardBg")
                val colorDefault =
                    COUIContextUtil.getAttrColor(context, R.attr.couiColorCardBackground)
                itemSmall.refreshCardBg(colorDefault)
            }
            tvTitleMainSmall.post {
                if (tvTitleMainSmall.layout.lineCount > 1) {
                    tvTitleMainSmall.textSize = LINE_WRAP_TEXT_SIZE_CHANGE
                }
            }
        }
    }

    class ContentLargeViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private var iconLarge: ImageView = itemView.findViewById(R.id.icon_large)
        private var tvTitleMainLarge: COUITextView = itemView.findViewById(R.id.tv_title_main_large)
        private var tvTitleSubLarge: COUITextView = itemView.findViewById(R.id.tv_title_sub_large)
        private var itemLarge: COUICardListSelectedItemLayout = itemView.findViewById(R.id.item_large)

        fun notifyData(context: Context, item: ToolBean, wayEnum: EnumWay) {
            if (item !is ToolContentBean) {
                return
            }
            iconLarge.setImageResource(item.iconContent)
            tvTitleMainLarge.text = item.contentMainTitle
            tvTitleSubLarge.text = item.contentSubTitle
            if (wayEnum == EnumWay.WAY_HOME) {
                itemLarge.refreshCardBg(context.resources.getColor(item.bgColor, context.theme))
            } else {
                Log.d(TAG, "ContentLargeViewHolder refreshCardBg")
                val colorDefault =
                    COUIContextUtil.getAttrColor(context, R.attr.couiColorCardBackground)
                itemLarge.refreshCardBg(colorDefault)
            }
        }
    }
}