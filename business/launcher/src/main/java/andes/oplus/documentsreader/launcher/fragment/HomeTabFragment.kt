/**
 * **********************************************************
 *  * * Copyright (C), 2010 - 2030 Oplus. All rights reserved..
 *  * * File: HomeTabFragment
 *  * * Description:
 *  * * Version: 1.0
 *  * * Date : 2024/6/17
 *  * * Author: hades 80241271
 *  * *
 *  * * ---------------------Revision History: ---------------------
 *  * *     <author>          <data>      <version >        <desc>
 *  * *  hades 80241271    2024/6/17       1.0               create
 *  ***************************************************************
 *
 */
package andes.oplus.documentsreader.launcher.fragment

import andes.oplus.documentsreader.core.common.CategoryHelper
import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.FileManagerDefaultItemAnimator
import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.KtConstants.MY_DOCUMENTS_PATH
import andes.oplus.documentsreader.core.common.KtConstants.PARAMETER_DEFAULT_SAVE_PATH
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.OfficeDocBean
import andes.oplus.documentsreader.core.common.PermissionUtils
import andes.oplus.documentsreader.core.common.Utils
import andes.oplus.documentsreader.core.common.compat.FeatureCompat
import andes.oplus.documentsreader.core.common.interfaces.OnBackPressed
import andes.oplus.documentsreader.core.common.interfaces.OpeateNavigationInterface
import andes.oplus.documentsreader.core.common.interfaces.TabActivityListener
import andes.oplus.documentsreader.core.common.loader.LoaderViewModel
import andes.oplus.documentsreader.core.common.parentchild.IMainViewApi
import andes.oplus.documentsreader.core.common.selection.RecyclerSelectionVMFragment
import andes.oplus.documentsreader.core.common.uiconfig.type.IUIConfig
import andes.oplus.documentsreader.core.common.utils.ToolbarUtil
import andes.oplus.documentsreader.core.common.view.FeedbackFloatingButton
import andes.oplus.documentsreader.core.common.wrapper.MediaFileWrapper
import andes.oplus.documentsreader.core.fileoperate.interfaces.INormalFileOperateController
import andes.oplus.documentsreader.core.statistics.StaticConstants.OPEN_FILE_SOURCE_RECENT
import andes.oplus.documentsreader.core.statistics.StaticsUploadUtil
import andes.oplus.documentsreader.interfaces.IDocToolsManager
import andes.oplus.documentsreader.interfaces.IUserStatementManager
import andes.oplus.documentsreader.launcher.R
import andes.oplus.documentsreader.launcher.adapter.HomeTabAdapter
import andes.oplus.documentsreader.launcher.controller.RecentFilterController
import andes.oplus.documentsreader.launcher.controller.SearchFilterClickListener
import andes.oplus.documentsreader.launcher.recent.RecentDocViewModel
import andes.oplus.documentsreader.launcher.recent.filter.FilterCondition
import andes.oplus.documentsreader.launcher.recent.filter.FilterItem
import andes.oplus.documentsreader.launcher.view.HomeTabLayoutManager
import andes.oplus.documentsreader.launcher.view.SearchEntryView
import andes.oplus.documentsreader.launcher.viewmodel.HomeTabViewModel
import andes.oplus.documentsreader.setting.ISetting
import android.annotation.SuppressLint
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.Toolbar.OnMenuItemClickListener
import androidx.core.view.children
import androidx.core.view.marginEnd
import androidx.core.view.marginStart
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.progressbar.COUICompProgressIndicator
import com.documentsreader.documentapi.IDocumentExtensionType
import com.documentsreader.newdocpanel.INewDocumentPanelDialog
import com.documentsreader.newdocpanel.IOfficeItemClick
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.dropdrag.recycleview.ItemDetailsLookup
import org.koin.android.ext.android.inject
import org.koin.core.parameter.parametersOf
import kotlin.math.max

class HomeTabFragment : RecyclerSelectionVMFragment<HomeTabViewModel>(), TabActivityListener<MediaFileWrapper>,
    OnBackPressed, NavigationBarView.OnItemSelectedListener, SearchFilterClickListener {
    companion object {
        private const val TAG = "HomeTabFragment"
        private const val MSG_DISABLE_LOADING = 1
        private const val DISABLE_LOADING_DELAY_TIME = 600L
    }

    private var mCreateDocFloatingButton: FeedbackFloatingButton? = null
    private var mCreateFileFabLayout: View? = null
    private var mSearchView: SearchEntryView? = null
    private var homeTabAdapter: HomeTabAdapter? = null

    private var aiToolContainer: FrameLayout? = null
    private var filterLayout: RelativeLayout? = null
    private var footLayout: View? = null
    private var stickFilterLayout: RelativeLayout? = null
    private var loadingView: View? = null
    private var loadingProgress: COUICompProgressIndicator? = null
    private var showLoadingTime: Long = 0L

    private val filterController by lazy { RecentFilterController(lifecycle) }

    private var mMenuItem: MenuItem? = null

    private var homeTabViewModel: HomeTabViewModel? = null
    private val userStatementManager by lazy { Injector.injectFactory<IUserStatementManager>() }
    private val fileOperateController: INormalFileOperateController by inject { parametersOf(lifecycle, fragmentViewModel) }

    private val searchViewOffset = ContextGetter.context.resources.getDimensionPixelSize(R.dimen.dimen_180dp)
    private val menuItemOffset = ContextGetter.context.resources.getDimensionPixelSize(R.dimen.dimen_16dp)
    private var menuViewEndWidth = ContextGetter.context.resources.getDimensionPixelSize(R.dimen.dimen_42dp)

    private var stickFilterLayoutShowed = false
    private var searchBarViewOriginWidth = -1

    private val newDocumentPanelDialog by lazy {
        Injector.injectFactory<INewDocumentPanelDialog>()
    }
    private val docToolsManager by lazy {
        Injector.injectFactory<IDocToolsManager>()
    }
    private val setting by lazy {
        Injector.injectFactory<ISetting>()
    }
    private val handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                MSG_DISABLE_LOADING -> disableLoading(true)
            }
        }
    }

    override fun createViewModel(): HomeTabViewModel {
        val vm = ViewModelProvider(requireActivity())[HomeTabViewModel::class.java]
        homeTabViewModel = vm
        restoreDataBundle()
        return vm
    }

    fun changeListModelState(state: Int) {
        fragmentViewModel?.mModeState?.listModel?.postValue(state)
    }

    private fun restoreDataBundle() {
        val restoreData = arguments?.getBundle(RecentDocViewModel.RESTORE_BUNDLE_DATA)
        if (restoreData != null) {
            homeTabViewModel?.restoreDataBundle(restoreData)
            arguments?.putBundle(RecentDocViewModel.RESTORE_BUNDLE_DATA, null)
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_tab_home
    }

    override fun initView(view: View) {
        initRootLayout(view)
        initLoadingView()
        initSearchView(view)
        initContentRecyclerView(view)
        initAiToolContainer()
        initFilterLayout(view)
        initFootLayout()
        initCreateFileFabLayout(view)
        updateLayout()
    }

    private fun initRootLayout(view: View) {
        rootView = view.findViewById(R.id.coordinator_layout)
        rootView?.apply {
            val statusHeight = COUIPanelMultiWindowUtils.getStatusBarHeight(context)
            setPadding(paddingLeft, statusHeight, paddingRight, paddingBottom)
        }
    }

    private fun initLoadingView() {
        loadingView = LayoutInflater.from(requireContext())
            .inflate(R.layout.oplus_doc_home_tab_loading_layout, rootView, false)
        rootView?.addView(loadingView)
        loadingView?.bringToFront()
        loadingProgress = loadingView?.findViewById(R.id.loading_progress_bar)
        loadingProgress?.animationView?.resumeAnimation()
        showLoadingTime = System.currentTimeMillis()
        Log.d(TAG, "initLoadingView showLoadingTime = $showLoadingTime")
    }

    private fun initSearchView(view: View) {
        appBarLayout = view.findViewById(R.id.appBarLayout)
        toolbar = view.findViewById(R.id.toolbar)
        mSearchView = view.findViewById(R.id.searchView)
    }

    private fun initContentRecyclerView(view: View) {
        val activity = activity ?: return
        fragmentRecyclerView = view.findViewById(R.id.content_recycler_view)
        homeTabAdapter = HomeTabAdapter(activity, this)
        fragmentRecyclerView?.layoutManager = HomeTabLayoutManager(activity)
        fragmentRecyclerView?.adapter = homeTabAdapter
        val itemAnimator = FileManagerDefaultItemAnimator()
        itemAnimator.supportsChangeAnimations = false
        fragmentRecyclerView?.itemAnimator = null
    }

    @SuppressLint("InflateParams")
    private fun initAiToolContainer() {
        val aiToolContainer = LayoutInflater.from(requireContext())
            .inflate(R.layout.oplus_doc_ai_tool_container, fragmentRecyclerView, false) as FrameLayout
        this.aiToolContainer = aiToolContainer
        homeTabAdapter?.setAiToolContainer(aiToolContainer)
    }

    @SuppressLint("InflateParams")
    private fun initFilterLayout(view: View) {
        val filterLayout = LayoutInflater.from(requireContext())
            .inflate(R.layout.oplus_doc_recent_filter_layout, fragmentRecyclerView, false) as RelativeLayout
        this.filterLayout = filterLayout
        homeTabAdapter?.setFilterLayout(filterLayout)

        //吸顶的过滤layout
        stickFilterLayout = view.findViewById(R.id.stick_filter_layout)
        stickFilterLayout?.setOnClickListener { /*do nothing */ }
    }

    private fun initFootLayout() {
        val footLayout = LayoutInflater.from(requireContext())
            .inflate(R.layout.oplus_doc_home_tab_foot_layout, fragmentRecyclerView, false)
        this.footLayout = footLayout
        homeTabAdapter?.setFootLayout(footLayout)
    }

    private fun initCreateFileFabLayout(view: View) {
        val createFileFabLayout = view.findViewById<View>(R.id.create_file_fab_layout)
        mCreateFileFabLayout = createFileFabLayout
        (parentFragment as? MainViewFragment)?.setAliasAnimView(createFileFabLayout)
        mCreateDocFloatingButton = view.findViewById(R.id.create_file_fab)
    }

    private fun updateLayout() {
        val navigationTab = (parentFragment as? MainViewFragment)?.navigationTab
        navigationTab?.viewTreeObserver?.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                val navigationTabHeight = navigationTab.measuredHeight
                Log.d(TAG, "updateLayout navigationTabHeight = $navigationTabHeight, $systemBarInsetsBottom")
                footLayout?.updateLayoutParams<ViewGroup.LayoutParams> {
                    height = navigationTabHeight + navigationTab.resources.getDimensionPixelSize(R.dimen.dimen_30dp)
                }
                mCreateFileFabLayout?.updateLayoutParams<MarginLayoutParams> {
                    bottomMargin = navigationTabHeight
                }
                navigationTab.viewTreeObserver?.removeOnGlobalLayoutListener(this)
            }
        })
    }

    override fun initData(savedInstanceState: Bundle?) {
        initToolbar()
        initFloatingButton()
        setScrollOnOffsetChangedListener()
        initFilterController(false)
    }

    private val resultCodeLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            val resultCode = it.resultCode
            Log.d(TAG, "registerForActivityResult resultCode:$resultCode")
            when (resultCode) {
                Activity.RESULT_OK -> { //工具选择文件
                    docToolsManager?.openTool(activity, it.data?.extras)
                }
            }
        }

    private fun setScrollOnOffsetChangedListener() {
        fragmentRecyclerView?.setOnScrollListener(object : OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val recyclerViewScrollY = (recyclerView.layoutManager as? HomeTabLayoutManager)?.getScrollYOffset() ?: 0
                updateAIToolState(recyclerViewScrollY, searchViewOffset, menuItemOffset)
                //设置吸顶view可见或者不可见
                updateStickLayout(recyclerViewScrollY)
            }
        })
    }

    private fun updateAIToolState(scrollY: Int, searchViewOffset: Int, menuItemOffset: Int) {
        val aiToolContainerHeight = aiToolContainer?.measuredHeight ?: 0
        val toolbarHeight = appBarLayout?.measuredHeight ?: 0
        val aiDataSize = homeTabAdapter?.getAiToolItemSize() ?: 0
        if (aiToolContainerHeight <= 0 || aiDataSize <= 0 || toolbarHeight <= 0) {
            mMenuItem?.icon?.alpha = 0
            menuToolVisible(false)
            return
        }
        val absVerticalOffset = (aiToolContainerHeight - toolbarHeight - scrollY).coerceAtLeast(0)
        if (absVerticalOffset <= searchViewOffset) {
            val percentForSearchView = 1f - absVerticalOffset / searchViewOffset.toFloat()
            val newWidth = searchBarViewOriginWidth - (percentForSearchView * menuViewEndWidth).toInt()
            updateSearchViewWidth(newWidth)
        } else {
            updateSearchViewWidth(searchBarViewOriginWidth)
        }
        if (absVerticalOffset <= menuItemOffset) {
            val percentForMenuItem = 1f - absVerticalOffset / menuItemOffset.toFloat()
            mMenuItem?.icon?.alpha = (percentForMenuItem * KtConstants.ALPHA_255).toInt()
            menuToolVisible(true)
        } else {
            mMenuItem?.icon?.alpha = 0
            menuToolVisible(false)
        }
    }

    private fun menuToolVisible(visible: Boolean) {
        if (mMenuItem?.isVisible == !visible) {
            toolbar?.post {
                mMenuItem?.isVisible = visible
            }
        }
    }

    private fun updateSearchViewWidth(width: Int) {
        val layoutParams = mSearchView?.layoutParams
        layoutParams?.width = width
        mSearchView?.layoutParams = layoutParams
    }

    private fun updateSearchBraViewOriginWidth() {
        toolbar?.let {
            val toolbarWidth = it.measuredWidth
            val toolbarMarginStart = it.marginStart
            val toolbarMarginEnd = it.marginEnd
            val toolbarPaddingStart = it.paddingStart
            val toolbarPaddingEnd = it.paddingEnd
            val moreButtonWidth = it.overFlowMenuButton.measuredWidth
            val moreButtonMarginStart = it.overFlowMenuButton.marginStart
            val moreButtonMarginEnd = it.overFlowMenuButton.marginEnd
            val spaceToolbarWidth = toolbarMarginStart + toolbarMarginEnd + toolbarPaddingStart + toolbarPaddingEnd
            val spaceMoreButtonWidth = moreButtonWidth + moreButtonMarginStart + moreButtonMarginEnd
            val spaceWidth = spaceToolbarWidth + spaceMoreButtonWidth
            searchBarViewOriginWidth = toolbarWidth - spaceWidth
            Log.d(TAG, "updateSearchViewOriginWidth toolbarWidth = $toolbarWidth, spaceWidth = $spaceWidth, " +
                    "searchBarViewOriginWidth = $searchBarViewOriginWidth")
        }
    }

    private fun updateStickLayout(scrollY: Int) {
        val aiToolContainerHeight = aiToolContainer?.measuredHeight ?: 0
        val toolbarHeight = appBarLayout?.measuredHeight ?: 0
        if (aiToolContainerHeight == 0 || toolbarHeight == 0) {
            Log.i(TAG, "updateStickLayout aiToolHeight = $aiToolContainerHeight, toolbarHeight = $toolbarHeight")
            return
        }
        val currentState = stickFilterLayoutShowed
        if (scrollY > aiToolContainerHeight - toolbarHeight) {
            stickFilterLayout?.visibility = View.VISIBLE
            stickFilterLayoutShowed = true
        } else {
            stickFilterLayout?.visibility = View.GONE
            stickFilterLayoutShowed = false
        }
        if (currentState != stickFilterLayoutShowed) {
            initFilterController(stickFilterLayoutShowed)
        }
    }

    private fun initFilterController(isStick: Boolean) {
        val activity = requireActivity()
        val filterCondition = homeTabViewModel?.filterCondition ?: FilterCondition().parseFilterItems()
        val panelRootView = activity.window.decorView as ViewGroup
        val filterLayout = if (isStick) stickFilterLayout else this.filterLayout
        filterLayout ?: return
        val parentLayout = if (isStick) rootView else fragmentRecyclerView
        filterController.init(
            activity,
            panelRootView,
            filterLayout,
            filterLayout.findViewById(R.id.filter_layout),
            parentLayout,
            false,
            filterCondition,
            anchorTargetViewDown = true
        )
        filterController.setFilterClickListener(this)
        filterController.updateButtonExpend()
    }

    private fun initFloatingButton() {
        mCreateDocFloatingButton?.mainFloatingButton?.contentDescription =
            ContextGetter.context.resources.getString(R.string.yozo_res_oppo_common_new)
        mCreateDocFloatingButton?.setButtonBackgroundDrawable(R.drawable.fb_create_doc_bg)
        mCreateDocFloatingButton?.setFloatingButtonClickListener {
            if (!Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                baseVMActivity?.apply {
                    newDocumentPanelDialog?.createAndShow(this, resultCodeLauncher)
                    newDocumentPanelDialog?.setOfficeItemClick(object : IOfficeItemClick {
                        override fun onItemClick(officeBean: OfficeDocBean) {
                            Log.d(TAG, "setOfficeItemClick")
                            try {
                                val intent = Intent(officeBean.action)
                                intent.putExtra(PARAMETER_DEFAULT_SAVE_PATH, MY_DOCUMENTS_PATH)
                                startActivity(intent)
                            } catch (e: ActivityNotFoundException) {
                                Log.e(TAG, e.message)
                            }
                        }
                    })
                }
            }
        }
        mCreateDocFloatingButton?.visibility = View.VISIBLE
        mCreateDocFloatingButton?.show()
    }

    override fun startObserve() {
        fragmentRecyclerView?.post {
            if (isAdded) {
                startListSelectModeObserver()
                startUIDataStateObserver()
                startFilterSelectObserver()
            }
        }
    }

    private fun startListSelectModeObserver() {
        val viewModule = fragmentViewModel ?: return
        viewModule.mModeState.listModel.observe(this, object : Observer<Int> {
            override fun onChanged(value: Int) {
                if (!viewModule.mModeState.initState) {
                    return
                }
                Log.d(TAG, "startListSelectModeObserver onChanged value = $value")
                val selectModel = (value == KtConstants.LIST_SELECTED_MODE)
                updateViewsStateWhenSelectedChange(selectModel)
                toolbar?.let {
                    changeActionModeAnim(it, {
                        if (selectModel) {
                            initToolbarSelectedMode(
                                true,
                                viewModule.getRealFileSize(),
                                viewModule.uiState.value?.selectedList?.size ?: 0,
                                viewModule.getSelectItems()
                            )
                        } else {
                            val empty = viewModule.isFileListEmpty()
                            initToolbarNormalMode(true, empty)
                        }
                    }, (it.getTag(R.id.toolbar_animation_id) == true), doChildrenAnimation = true)
                    it.setTag(R.id.toolbar_animation_id, true)
                }
                //刷新下
                val groupFileList = viewModule.uiState.value?.fileList as? ArrayList<MediaFileWrapper> ?: ArrayList()
                val selectList = viewModule.uiState.value?.selectedList ?: ArrayList()
                homeTabAdapter?.setData(groupFileList, selectList)
            }
        })
    }

    private fun updateViewsStateWhenSelectedChange(selectModel: Boolean) {
        //设置各种view可用/不可用
        homeTabAdapter?.setSelectEnabled(selectModel)
        mCreateDocFloatingButton?.isEnabled = selectModel.not()

        stickFilterLayout?.children?.forEach { childView ->
            childView.isEnabled = selectModel.not()
            childView.isFocusable = selectModel.not()
            childView.alpha = if (selectModel) {
                KtConstants.ALPHA_PERCENT_30
            } else {
                KtConstants.ALPHA_PERCENT_100
            }
        }

        //显示或者消失工具条
        val navigationOperate = parentFragment as? OpeateNavigationInterface
        navigationOperate?.let {
            if (selectModel) {
                it.showNavigation()
            } else {
                it.hideNavigation()
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun startUIDataStateObserver() {
        val viewModule = fragmentViewModel ?: return
        viewModule.uiState.observe(this) { uiModel ->
            val fileSize = viewModule.getRealFileSize()
            val selectState = uiModel.stateModel.listModel.value
            Log.d(TAG, "startUIDataStateObserver mUiState: total=$fileSize" +
                    ", select=${uiModel.selectedList.size}" +
                    ", keyword=${uiModel.keyWord}" +
                    ", selectState = $selectState")
            val selectMode = selectState == KtConstants.LIST_SELECTED_MODE
            if (selectMode) {
                initToolbarSelectedMode(
                    false, fileSize,
                    uiModel.selectedList.size,
                    viewModule.getSelectItems()
                )
                if (uiModel.fileList is ArrayList<MediaFileWrapper>) {
                    val groupFileList = uiModel.fileList as ArrayList<MediaFileWrapper>
                    homeTabAdapter?.setData(groupFileList, uiModel.selectedList)
                }
                val navigationOperate = parentFragment as? OpeateNavigationInterface
                navigationOperate?.setNavigateItemAble(uiModel.selectedList.isNotEmpty())
            } else {
                val empty = viewModule.isFileListEmpty()
                initToolbarNormalMode(false, empty)
                if (uiModel.fileList is ArrayList<MediaFileWrapper>) {
                    val groupFileList = uiModel.fileList as ArrayList<MediaFileWrapper>
                    homeTabAdapter?.setData(groupFileList, uiModel.selectedList)
                    //如果为空需要重新load一下，播放empty动画
                    if (empty) {
                        homeTabAdapter?.notifyDataSetChanged()
                    }
                }
            }
        }
    }

    private fun startFilterSelectObserver() {
        fragmentViewModel?.filterSelectedDataModel?.observe(this) { items ->
            updateFilterSelectChanged(items)
            items?.apply {
                val intList = this.filter { it.isSelect }.map { it.id }
                val stringList = Injector.injectFactory<IDocumentExtensionType>()?.filterMapToSelectionList(intList)
                stringList?.let {
                    fragmentViewModel?.updateFilterSuffix(it)
                }
            }
        }
    }

    override fun onResumeLoadData() {
        val activity = baseVMActivity
        if (!isAdded || activity == null) {
            Log.e(TAG, "onResumeLoadData fragment don't add or activity is null, return !")
            return
        }
        var hasShowStatement = userStatementManager?.checkShowStatement(activity) ?: false
        if (FeatureCompat.sIsExpRom) {
            hasShowStatement = true
        }
        if (hasShowStatement && PermissionUtils.hasStoragePermission()) {
            fragmentViewModel?.initLoader(LoaderViewModel.getLoaderController(activity), 0)
        } else {
            fragmentViewModel?.loadWithNoPermission()
        }
    }

    fun setData() {
    }

    override fun backToTop() {
        fragmentRecyclerView?.fastSmoothScrollToTop()
    }

    private fun initToolbar() {
        toolbar?.apply {
            menu.clear()
            isTitleCenterStyle = false
            inflateMenu(R.menu.oplus_doc_home_with_search_menu)
            title = ""
            mSearchView?.visibility = View.VISIBLE
            mSearchView?.setUseResponsivePadding(false)
            setOnMenuItemClickListener(menuItemClickListener)
        }
    }

    private val menuItemClickListener = object : OnMenuItemClickListener {
        override fun onMenuItemClick(item: MenuItem): Boolean {
            if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return false
            }
            return when (item.itemId) {
                R.id.action_edit -> {
                    if (filterController.mIsShow) {
                        filterController.showOrHideFilterPanel()
                    }
                    fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                    true
                }

                R.id.action_select_all -> {
                    fragmentViewModel?.clickToolbarSelectAll()
                    true
                }

                R.id.action_select_cancel -> {
                    if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                        fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                    }
                    true
                }

                else -> false
            }
        }
    }

    override fun onItemClick(item: ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        fragmentViewModel?.uiState?.value?.let { uiModel ->
            if (uiModel.stateModel.listModel.value != KtConstants.LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            val baseFile = uiModel.keyMap[item.selectionKey].apply {
                Log.d(TAG, "onItemClick baseFile=$this")
            } ?: return true
            activity?.let {
                fileOperateController.onFileClick(it, baseFile, e)
                upLoadOpenFileStatics(item, baseFile)
            }
        }
        return true
    }

    private fun upLoadOpenFileStatics(
        item: ItemDetailsLookup.ItemDetails<Int>,
        baseFile: MediaFileWrapper
    ) {
        val position = item.position + 1
        val sourceType = OPEN_FILE_SOURCE_RECENT
        val isDirectory = baseFile.mIsDirectory
        StaticsUploadUtil.clickOpenFile(sourceType, baseFile.mData, isDirectory, position)
    }

    override fun initToolbarSelectedMode(needInit: Boolean, realFileSize: Int, selectedFileSize: Int, selectItems: ArrayList<MediaFileWrapper>) {
        toolbar?.apply {
            if (needInit) {
                menu.clear()
                isTitleCenterStyle = true
                inflateMenu(R.menu.oplus_doc_menu_edit_mode)
                mSearchView?.visibility = View.GONE
                setOnMenuItemClickListener(menuItemClickListener)
            }
            val isSelectAll = (realFileSize == selectedFileSize)
            ToolbarUtil.updateToolbarTitle(this, selectedFileSize, isSelectAll)
            (parentFragment as? OpeateNavigationInterface)?.setNavigateItemAble(selectedFileSize > 0)
        }
        (parentFragment as? MainViewFragment)?.setViewPagerInputEnabled(false)
    }

    override fun initToolbarNormalMode(needInit: Boolean, empty: Boolean) {
        toolbar?.apply {
            if (needInit) {
                menu.clear()
                isTitleCenterStyle = false
                inflateMenu(R.menu.oplus_doc_home_with_search_menu)
                title = ""
                mSearchView?.visibility = View.VISIBLE
                setOnMenuItemClickListener(menuItemClickListener)
            }
            menu.findItem(R.id.action_edit)?.isVisible = empty.not()
            menu.findItem(R.id.action_setting)?.setOnMenuItemClickListener {
                setting?.startSettingActivity(requireActivity())
                true
            }
            mMenuItem = menu.findItem(R.id.action_tool)
            mMenuItem?.let {
                updateOffsetLayout()
                it.setOnMenuItemClickListener {
                    val aiDataSize = homeTabAdapter?.getAiToolItemSize() ?: 0
                    if (aiDataSize > KtConstants.MAGIC_VALUE_4) {
                        val mainViewApi = Injector.injectFactory<IMainViewApi>()
                        baseVMActivity?.let { activity ->
                            mainViewApi?.enterNextFragment(activity, CategoryHelper.CATEGORY_DOC_TOOL)
                        }
                    } else {
                        fragmentRecyclerView?.fastSmoothScrollToTop()
                    }
                    true
                }
            }
            (parentFragment as? MainViewFragment)?.setViewPagerInputEnabled(true)
        }
    }

    override fun pressBack(): Boolean {
        //#7724393,体验单修改，如果点击返回先收起这个
        if (filterController.mIsShow) {
            filterController.hideFilterPanel()
            return true
        }
        return homeTabViewModel?.pressBack() ?: false
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        if (pause) {
            Log.d(TAG, "already pause, return")
            return false
        }
        return fileOperateController.onNavigationItemSelected(requireActivity(), item)
    }

    override fun onUpdateFilter(filterItems: List<FilterItem>?) {
        filterItems?.forEach {
            Log.d(TAG, "onFilterClick: ${it.desc} isSelect = ${it.isSelect}")
        }
        filterItems?.apply {
            fragmentViewModel?.updateSelectFilter(this)
        }
    }

    override fun onChangeFilterPanel() {
        Log.d(TAG, "onChangeFilterPanel")
        fragmentRecyclerView?.let {
            if (it.scrollState != RecyclerView.SCROLL_STATE_IDLE) {
                Log.d(TAG, "stop recyclerView scroll")
                it.stopScroll()
            }
        }
        filterController.showOrHideFilterPanel()
    }

    override fun onItemSelected() {
        Log.d(TAG, "onItemSelected")
        filterController.showOrHideFilterPanel()
    }

    override fun fromSelectPathResult(requestCode: Int, path: String?) {
        Log.d(TAG, "fromSelectPathResult requestCode:$requestCode path:$path")
        fileOperateController.onSelectPathReturn(requireActivity(), requestCode, path)
        fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        fragmentViewModel?.loadData()
    }

    private fun updateFilterSelectChanged(filterList: List<FilterItem>) {
        filterController.updateFilterSelectChanged(filterList)
    }

    fun hideRecentDocFilterPanel(ev: MotionEvent? = null) {
        if (filterController.mIsShow) {
            filterController.hideFilterPanel(ev)
        }
    }

    fun closeFilterPanel() {
        filterController.removeFilterPanel()
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy")
        super.onDestroy()
        disableLoading(true)
        homeTabAdapter?.onDestroy()
        homeTabAdapter = null
        showLoadingTime = 0
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        if (filterController.mIsShow) {
            withBaseVMActivity {
                filterController.updatePanelPosition(it)
            }
        }
        homeTabAdapter?.updateEmptyContainerIfNeed()
    }

    override fun onWindowInsetsCallback(showNavigationBar: Boolean, systemBarInsetsBottom: Int) {
        super.onWindowInsetsCallback(showNavigationBar, systemBarInsetsBottom)
        rootView?.post { updateLayoutInsetsCallback() }
        homeTabAdapter?.onWindowInsetsCallback()
        updateOffsetLayout()
    }

    private fun updateOffsetLayout() {
        fragmentRecyclerView?.post {
            if (isAdded.not()) return@post
            val recyclerViewScrollY = (fragmentRecyclerView?.layoutManager as? HomeTabLayoutManager)?.getScrollYOffset() ?: 0
            Log.d(TAG, "recyclerViewScrollY = $recyclerViewScrollY")
            updateSearchBraViewOriginWidth()
            updateAIToolState(recyclerViewScrollY, searchViewOffset, menuItemOffset)
            //设置吸顶view可见或者不可见
            updateStickLayout(recyclerViewScrollY)
        }
    }

    private fun updateLayoutInsetsCallback() {
        if (isAdded.not()) return
        val navigationTabHeight = (parentFragment as? MainViewFragment)?.navigationTab?.measuredHeight ?: 0
        val navigationToolHeight = (parentFragment as? MainViewFragment)?.navigationTool?.measuredHeight ?: 0
        Log.d(TAG, "updateLayoutInsetsCallback navigationTabHeight = $navigationTabHeight, " +
                "navigationToolHeight = $navigationToolHeight, $systemBarInsetsBottom")
        val footLayoutHeight = max(navigationTabHeight, navigationToolHeight)
        footLayout?.updateLayoutParams<ViewGroup.LayoutParams> {
            height = footLayoutHeight + resources.getDimensionPixelSize(R.dimen.dimen_30dp)
        }
        mCreateFileFabLayout?.updateLayoutParams<MarginLayoutParams> {
            bottomMargin = if (homeTabViewModel?.isInSelectMode() == true) {
                -(mCreateFileFabLayout?.measuredHeight ?: 0)
            } else {
                footLayoutHeight
            }
        }
    }

    fun disableLoading(immediately: Boolean = false) {
        handler.removeMessages(MSG_DISABLE_LOADING)
        val currentTime = System.currentTimeMillis()
        val gap = currentTime - showLoadingTime
        Log.d(TAG, "disableLoading currentTime = $currentTime, gap: $gap, $immediately")
        if (gap > DISABLE_LOADING_DELAY_TIME || immediately) {
            loadingProgress?.animationView?.pauseAnimation()
            loadingView?.visibility = View.GONE
            rootView?.removeView(loadingView)
        } else {
            handler.sendMessageDelayed(handler.obtainMessage(MSG_DISABLE_LOADING), DISABLE_LOADING_DELAY_TIME - gap)
        }
    }
}