/***********************************************************
 * * Copyright (C), 2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:COUISnackBarUtils.kt
 * * Description:com.coloros.filemanager.filerefactor.utils
 * * Version:1.0
 * * Date :2021.3.25
 * * Author:W9001165
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package andes.oplus.documentsreader.core.common.utils

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.snackbar.COUISnackBar
import andes.oplus.documentsreader.core.common.R

object COUISnackBarUtils {
    private const val DURATION = 2000

    @JvmStatic
    fun show(context: Activity, textDescription: String, clickListener: View.OnClickListener) {
        if (context.isFinishing.not() && context.isDestroyed.not() && (context.window != null)) {
            context.findViewById<ViewGroup>(android.R.id.content)?.let {
                it.clipChildren = false
                val snackBar = COUISnackBar.make(it, textDescription, DURATION)
                snackBar.setOnAction(R.string.result_view, clickListener)
                snackBar.show()
            }
        }
    }

    @JvmStatic
    fun show(context: Activity, textDescriptionId: Int, clickListener: View.OnClickListener) {
        show(context, context.getString(textDescriptionId), clickListener)
    }

    @JvmStatic
    fun show(context: Activity, textDescriptionId: Int, textBtn: Int, clickListener: View.OnClickListener) {
        if (context.isFinishing.not() && context.isDestroyed.not() && (context.window != null)) {
            context.findViewById<ViewGroup>(android.R.id.content)?.let {
                it.clipChildren = false
                val snackBar = COUISnackBar.make(it, context.getString(textDescriptionId), DURATION)
                snackBar.setOnAction(textBtn, clickListener)
                snackBar.show()
            }
        }
    }
}