/*********************************************************************
 * * Copyright (C), 2024, OPlus. All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/7/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package andes.oplus.documentsreader.core.cloudconfig.bean

import androidx.annotation.Keep
import com.heytap.nearx.cloudconfig.anotation.FieldIndex

@Keep
data class BlackListEntity(
    @FieldIndex(index = 1)
    val path: String = ""
)
