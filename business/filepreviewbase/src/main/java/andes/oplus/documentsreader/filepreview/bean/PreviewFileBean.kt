/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileBean
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/2/21 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  wanglei        2024/2/21       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.filepreview.bean

import andes.oplus.documentsreader.core.common.KtConstants.PREVIEW_NO_ERROR
import andes.oplus.documentsreader.core.common.KtConstants.PREVIEW_TYPE_DEFAULT
import android.net.Uri
import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

/**
 *
 * @property fileOriUri Uri
 * @property filePath String
 * @property fileUri Uri
 * @property fileName String
 * @property fileSize Long
 * @property fileMimeType String
 * @property fileModifyTime Long
 * @property fromPackageName String
 * @property previewType String 音视频、图片、压缩包等
 * @property previewErrorType Int 拷贝失败、文件异常、文件过大
 * @constructor
 */
@Keep
@Parcelize
data class PreviewFileBean(
    val fileOriUri: Uri,
    val filePath: String,
    val fileUri: Uri,
    val fileName: String,
    val fileSize: Long,
    val fileMimeType: String,
    var fileModifyTime: Long,
    val fromPackageName: String,
    val previewType: String = PREVIEW_TYPE_DEFAULT,
    var previewErrorType: Int = PREVIEW_NO_ERROR,
    val panelShowInfo: String = ""
) : Parcelable {
    override fun toString(): String {
        return "PreviewFileBean = (fileName:$fileName, " +
            "fileSize:$fileSize, " +
            "fileMimeType:$fileMimeType, " +
            "fileModifyTime:$fileModifyTime, " +
            "fromPackageName:$fromPackageName, " +
            "previewType:$previewType, " +
            "errorType:$previewErrorType"
    }
}
