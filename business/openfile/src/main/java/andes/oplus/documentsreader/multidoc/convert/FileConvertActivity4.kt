/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/06/18, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package andes.oplus.documentsreader.multidoc.convert

import andes.oplus.documentsreader.filepreview.multidoc.ActivityConstant
import andes.oplus.documentsreader.preview.ui.FilePreviewActivity

class FileConvertActivity4 : FilePreviewActivity() {

    override fun getActivityName(): String {
        return ActivityConstant.FILE_CONVERT_ACTIVITY_4
    }

    override fun getProcessName(): String {
        return ActivityConstant.FILE_CONVERT_PROCESS_NAME_4
    }
}