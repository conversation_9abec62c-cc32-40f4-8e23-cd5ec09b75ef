plugins {
    id 'com.android.library'
}
apply from: rootProject.file("script/compile.gradle")
apply from: rootProject.file("script/unitTest.gradle")
apply from: rootProject.file("script/aarMetadata.gradle")
apply from: rootProject.file("script/pipelineConfig.gradle")

android {
    namespace 'com.oplus.documentsreader.thumbnail.wrapper'

    buildFeatures {
        aidl = true
        viewBinding = false
        buildConfig = false
    }
}

dependencies {
    implementation libs.androidx.annotation
    implementation libs.kotlinx.coroutines.android

    // thumbnailWrapper为独立出包的SDK Module，禁止导入其它非独立出包的Module作为依赖
}

aarPipeline {
    groupId = prop_exported_archivesGroupName
    artifactId = prop_exported_artifactId_thumbnail_wrapper
    versionName = "${mainVersionName}" + "${versionSuffix}"
    variantType = "all"
}

apply from: rootProject.file("script/pipelinePublish.gradle")
apply from: rootProject.file("script/aarCompile.gradle")