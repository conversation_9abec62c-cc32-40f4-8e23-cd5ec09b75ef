/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileOpenViewModel
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/7/11 10:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/7/11       1.0      create
 ***********************************************************************/
package andes.oplus.documentsreader.preview.ui.open

import andes.oplus.documents.preview.interfaces.ISaveAsNotificationManager
import andes.oplus.documents.preview.interfaces.data.PreviewData
import andes.oplus.documentsreader.bean.AppInfo
import andes.oplus.documentsreader.bean.SheetsObject
import andes.oplus.documentsreader.core.common.ContextGetter
import andes.oplus.documentsreader.core.common.FileUtils
import andes.oplus.documentsreader.core.common.GsonUtil
import andes.oplus.documentsreader.core.common.Injector
import andes.oplus.documentsreader.core.common.KtAppUtils
import andes.oplus.documentsreader.core.common.KtConstants
import andes.oplus.documentsreader.core.common.Log
import andes.oplus.documentsreader.core.common.MimeTypeHelper
import andes.oplus.documentsreader.core.common.PreferencesUtils
import andes.oplus.documentsreader.core.database.PreviewCacheDBHelper
import andes.oplus.documentsreader.core.database.PreviewDataDBHelper
import andes.oplus.documentsreader.core.database.data.PreviewCacheEntity
import andes.oplus.documentsreader.filepreview.util.OpenAnyUtil
import andes.oplus.documentsreader.interfaces.IOpenDirectManager
import andes.oplus.documentsreader.util.OpenFileUtil
import android.content.ComponentName
import android.os.Bundle
import android.os.LocaleList
import android.os.Process
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import java.io.File
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Locale

class FileOpenViewModel : ViewModel() {

    var cadAppState = MutableLiveData<KtAppUtils.ConnectionResult>()
    var xMindAppState = MutableLiveData<KtAppUtils.ConnectionResult>()
    var otherAppState = MutableLiveData<Boolean>()

    private val extList = ArrayList<String>()

    //通知用ID
    private var notifyBaseId = NOTIFICATION_ID_BASE + Process.myPid()
    private val notifyIdMap = HashMap<String, Int>()

    var url: String? = null
    var fileSize = 0L
    var fileConvertSize = 0L

    var isLoadedCacheFile = false   //已经加载完缓存文件（PDF）
    var isShareCacheFile = false    //是否要分享缓存的文件（PDF）
    var isSaveCacheFile = false     //是否要保存缓存的文件（PDF）
    var isSavingFile = false        //正在保存中，不可以再次点击打开文管选择文件夹
    var isLoadedSheetIds = false        //已经加载完有画板或画布的的sheetId,未加载完无法保存及分享
    var isSaveSheetsFileToPng = false         //是否要保存有画板或画布的文件为PNG格式
    var isShareSheetsFileByPng = false        //是否要分享有画板或画布的的图片
    var selectedSheet: SheetsObject? = null     //xmind,visio,psd,ai当前Sheet
    var canvasSheets = MutableLiveData<List<SheetsObject>>()
    var isPanelShowing = false
    var isShowSaveForLoadSheetIds = false       //未加载完sheetIds的时候点击保存按钮，显示Loading加载框，加载到后打开保存按钮
    var isNeedFinish = false                    //保存缓存文件时，不可关闭Activity，执行moveToTask()方法，缓存完成后关闭Activity
    var isFirstShowTips = true

    private val saveAsNotificationManager: ISaveAsNotificationManager? by lazy {
        Injector.injectFactory<ISaveAsNotificationManager>()
    }
    private val openDirectManager = Injector.injectFactory<IOpenDirectManager>()

    fun getCADRecommendApp(): AppInfo {
        val appLists = OpenFileUtil.getSpecialWhiteApp(DOC_TYPE_DWG).toMutableList()
        if (appLists.isEmpty()) {
            Log.d(TAG, "cloud config app is empty, use default app:$WHITE_CAD_NAME")
            return AppInfo(WHITE_CAD_NAME, null, WHITE_CAD_PACKAGE)
        }
        appLists.sortWith(compareBy({ it.priority }, { it.appName }))
        return appLists[0]
    }

    fun getCADAppName(): String {
        return if (isChineseLanguage()) {
            WHITE_CAD_NAME
        } else {
            WHITE_CAD_NAME_US
        }
    }

    fun updateCADAppState() {
        viewModelScope.launch {
            val result = withContext(Dispatchers.Default) {
                KtAppUtils.checkAppForceEnabled(WHITE_CAD_PACKAGE)
            }
            Log.d(TAG, "updateCadAppState -> result = $result")
            if (cadAppState.value != result) {
                cadAppState.value = result
            }
        }
    }

    fun getXMindRecommendApp(): AppInfo {
        val appLists = OpenFileUtil.getSpecialWhiteApp(DOC_TYPE_X_MIND).toMutableList()
        if (appLists.isEmpty()) {
            Log.d(TAG, "cloud config app is empty, use default app:$WHITE_X_MIND_NAME")
            return AppInfo(WHITE_X_MIND_NAME, null, WHITE_X_MIND_PACKAGE)
        }
        appLists.sortWith(compareBy({ it.priority }, { it.appName }))
        return appLists[0]
    }

    fun getXMindAppName(): String {
        return WHITE_X_MIND_NAME
    }

    fun updateXMindAppState() {
        viewModelScope.launch {
            val result = withContext(Dispatchers.Default) {
                KtAppUtils.checkAppForceEnabled(WHITE_X_MIND_PACKAGE)
            }
            Log.d(TAG, "updateCadAppState -> result = $result")
            if (xMindAppState.value != result) {
                xMindAppState.value = result
            }
        }
    }

    fun updateOtherAppState(filePath: String?) {
        viewModelScope.launch {
            val result = withContext(Dispatchers.Default) {
                OpenAnyUtil.hasMoreOpenApp(filePath)
            }
            Log.d(TAG, "updateOtherAppState -> result = $result")
            if (otherAppState.value != result) {
                otherAppState.value = result
            }
        }
    }

    fun updateExpiredUrl(filePath: String, key: String) {
        val previewData = PreviewCacheDBHelper.obtainCacheDataByPath(filePath)
        if (previewData == null) {
            PreviewCacheDBHelper.insertPreviewCacheData(PreviewCacheEntity(filePath, key, System.currentTimeMillis()))
        } else {
            previewData.cacheKey = key
            previewData.cacheTime = System.currentTimeMillis()
            previewData.deleteFlag = 0
            PreviewCacheDBHelper.updatePreviewCacheData(previewData)
        }
    }

    fun fileCachedValid(filePath: String): Boolean {
        val previewData = PreviewCacheDBHelper.obtainCacheDataByPath(filePath)
        return previewData?.isFileCacheValid() ?: false
    }

    fun markCacheDelete(filePath: String) {
        val cachedPath = PreviewCacheDBHelper.obtainCacheDataByPath(filePath)
        Log.d(TAG, "markDelete cachedPath: $cachedPath")
        cachedPath?.let {
            if (!File(it.filePath).exists() || cacheOverDue(cachedPath.cacheTime)) {
                PreviewCacheDBHelper.markCacheDelete(filePath)
            }
        }
    }

    @VisibleForTesting
    fun cacheOverDue(cacheTime: Long): Boolean {
        val now = now()
        return cacheTime != 0L && (now - cacheTime) > FileOpenFragment.OVER_DUE_PERIOD
    }

    @VisibleForTesting
    fun now(): Long {
        return System.currentTimeMillis()
    }

    private fun isChineseLanguage(): Boolean {
        var country = ""
        val localeList: LocaleList = ContextGetter.context.resources.configuration.locales
        if (localeList.isEmpty.not()) {
            val locale: Locale? = localeList[0]
            country = locale?.country ?: ""
        }
        return country.isNotEmpty() && CN.equals(country, ignoreCase = true)
    }

    fun findSupportSaveTypeByExt(filePath: String): ArrayList<String> {
        if (extList.size > 0) {
            return extList
        }
        val ext = FileUtils.getExtension(filePath)
        val typeList = getCloudConfigDataByExt(ext)
        if (typeList.isNullOrEmpty()) {
            //未配置云控配置，只取文件源文件后缀
            extList.add(FileUtils.EXTENSION_SEPARATOR + ext)
            return extList
        }
        Log.d(TAG, "typeList :$typeList")
        typeList.forEach { type ->
            extList.add(FileUtils.EXTENSION_SEPARATOR + type)
        }
        return extList
    }

    @Suppress("TooGenericExceptionCaught")
    private fun getCloudConfigDataByExt(ext: String): List<String>? {
        try {
            val cloudConfigJson = PreferencesUtils.getString(
                KtConstants.OPEN_ANY_CONFIG_SAVE_TYPE_SUPPORT_KEY, KtConstants.OPEN_ANY_CONFIG_SAVE_TYPE_SUPPORT, ""
            ).toString()
            if (cloudConfigJson.isEmpty()) {
                return null
            }
            Log.d(TAG, "getCloudConfigDataByExt cloudConfigJson :$cloudConfigJson")
            val configData = GsonUtil.fromJson(cloudConfigJson, HashMap::class.java)
            val supportTypes = configData[ext] as? String
            supportTypes?.apply {
                return@getCloudConfigDataByExt split(",")
            }
        } catch (e: Exception) {
            Log.e(TAG, "getCloudConfigDataByExt e:$e")
        }
        return null
    }

    fun sendNotificationBySavingCache(filePath: String, savePath: String, componentName: ComponentName, title: String) {
        var notifyId = notifyIdMap[savePath] ?: 0
        if (notifyId == 0) {
            notifyId = notifyBaseId++
            notifyIdMap[savePath] = notifyId
        }
        sendNotification(filePath, componentName, title, notifyId, true)
    }

    fun sendNotificationBySaved(filePath: String, savePath: String, componentName: ComponentName, title: String, jumpToRecent: Boolean = false) {
        val notifyId = notifyIdMap[savePath] ?: notifyBaseId
        sendNotification(filePath, componentName, title, notifyId, false, jumpToRecent)
    }

    fun sendCancelNotification(selectedDestPath: String, notifyId: Int, savePath: String?) {
        viewModelScope.launch(Dispatchers.Default) {
            val notifyOldId = notifyIdMap[selectedDestPath] ?: notifyBaseId
            if (notifyOldId == notifyId) {
                //缓存PDF的，判断通知栏的ID，重置保存文件按钮，让当前保存按钮可点击
                isSaveCacheFile = false
                isSaveSheetsFileToPng = false
                isSavingFile = false
            } else if (savePath != null && FileUtils.getBaseName(savePath) == FileUtils.getBaseName(selectedDestPath)) {
                //Web端处理的文件，收到取消通知，让当前保存按钮可点击
                isSavingFile = false
            }
        }
    }

    private fun sendNotification(
        filePath: String,
        componentName: ComponentName,
        title: String,
        notifyId: Int,
        showCancel: Boolean,
        jumpToRecent: Boolean = false
    ) {
        viewModelScope.launch(Dispatchers.Default) {
            val previewData = PreviewDataDBHelper.getPreviewDataByFilePath(filePath)?.let { PreviewData.map(it) }
            if (previewData == null) {
                val tempData = PreviewData()
                tempData.filePath = filePath
                tempData.fileName = FileUtils.getName(filePath)
                tempData.url = url
                tempData.fileSize = fileSize
                saveAsNotificationManager?.sendSavingNotification(tempData, componentName, notifyId, title, showCancel, jumpToRecent)
            } else {
                saveAsNotificationManager?.sendSavingNotification(previewData, componentName, notifyId, title, showCancel, jumpToRecent)
            }
        }
    }

    fun initFileData(arguments: Bundle?, filePath: String) {
        url = if (MimeTypeHelper.isOpenDirect(filePath)) {
            openDirectManager?.genUrl(filePath) ?: ""
        } else {
            arguments?.getString(KtConstants.FILE_URI) ?: ""
        }
        fileSize = arguments?.getLong(KtConstants.FILE_SIZE, 0L) ?: 0L
        fileConvertSize = arguments?.getLong(KtConstants.FILE_CONVERT_SIZE, 0L) ?: 0L
    }

    companion object {
        private const val TAG = "FileOpenViewModel"

        const val WHITE_CAD_PACKAGE = "com.gstarmc.android"
        const val WHITE_CAD_NAME = "CAD看图王"
        const val WHITE_X_MIND_PACKAGE = "net.xmind.doughnut"
        const val WHITE_X_MIND_NAME = "Xmind"
        private const val WHITE_CAD_NAME_US = "DWG FastView"
        private const val DOC_TYPE_DWG = ".dwg"
        private const val DOC_TYPE_X_MIND = ".xmind"

        private const val NOTIFICATION_ID_BASE = 200000

        private const val CN = "CN"
    }
}